<%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())
%>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<lottie-player src="https://assets3.lottiefiles.com/packages/lf20_OiqxPlq97j.json" id="celebrationAnim" style="z-index: 0 !important;display: none;"  background="transparent"  speed="1"></lottie-player>
<section class="page-main-wrapper mdl-js pt-5 shopping_cart">
    <div class="container">
        <h3>Cart <small>(<span id="cartBooksCount">0</span>)</small></h3>

        <div id="showEmptyCart" class="d-none">
            <div class="empty_cart text-center shadow-sm px-5 py-4">
                <lottie-player src="https://assets3.lottiefiles.com/datafiles/vhvOcuUkH41HdrL/data.json" id="emptyCartBox" background="transparent" speed="1" loop autoplay></lottie-player>
                <h6>Your cart is empty.</h6>
                <%if("true".equals(session["commonWhiteLabel"])){%>
                <a href="/sp/<%= session["siteName"] %>/store" class="btn btn-lg shopnow_btn">Go to Store</a>
                <%}else if("true".equals(session["prepjoySite"])){%>
                <a href="/<%= session["entryController"] %>/eBooks" class="btn btn-lg shopnow_btn">Go to Store</a>
                <%}else {%>
                <a href="/<%= session["entryController"] %>/store" class="btn btn-lg shopnow_btn">Go to Store</a>
                <%}%>
            </div>
        </div>
    </div>
    <div class="container d-flex flex-lg-row flex-column">
        <div class="main d-flex flex-column col-lg-8 px-0 px-lg-3">
            <div id="displayCartItems">
                <div id="cartItems" class="cart_items w-100"></div>
            </div>

            <g:render template="/whitelabel/billingShipping"></g:render>
        </div>
        <div class="aside col-lg-4 d-none" id="cartSummaryCard">
            <div class="card border-0 p-3 cart_checkout">
                <div class="order_summary">
                    <h5 class="mb-3 pb-3">Order Summary <i id="closeOrderSummary" class="material-icons-round d-md-none">close</i></h5>
                    <div class="d-flex align-items-center justify-content-between">
                        <p class="col-8 p-0">Subtotal</p>
                        <p id="subTotal" class="col-4 p-0 text-right">Loading..</p>
                    </div>
                    <div class="d-flex align-items-center justify-content-between">
                        <p class="col-8 p-0" style="display: none" id="displayLabel">Discount</p>
                        <p id="discountTotal" class="col-4 p-0 text-right" style="display: none" id="displayValue">&#x20b9 0</p>
                    </div>
                    <div class="d-flex align-items-center justify-content-between">
                        <p class="col-8 p-0">Shipping charges</p>
                        <p id="shippingTotal" class="col-4 p-0 text-right">-</p>
                    </div>
                    <div class="d-flex align-items-center justify-content-between">
                        <p class="col-8 p-0" id="payableTotalLabel">Total</p>
                        <p id="payableTotal" class="col-4 p-0 text-right">Loading..</p>
                    </div>
                </div>

                <!-- Coupon Code Section -->
                <div class="coupon_section mb-3">
                    <div class="d-flex justify-content-center">
                        <a href="javascript:toggleCouponSection();" class="btn btn-link text-decoration-none p-0" id="couponToggleText">Have Coupon Code?</a>
                    </div>
                    <div id="couponSection" class="mt-2" style="display: none;">
                        <div class="input-group">
                            <input type="text" class="form-control" id="couponCodeInput" placeholder="Enter coupon code" maxlength="50">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button" id="applyCouponBtn" onclick="applyCouponCode()">Apply</button>
                            </div>
                        </div>
                        <small id="couponMessage" class="form-text"></small>
                    </div>
                </div>

                <div class="cart_buttons">
                    <div class="mobile_summary">
                        <p class="mobile_total d-md-none"><span>Total</span><br> &#x20b9 144</p>
                        <p class="total_discount_price d-none">
                            <span id="discountPriceFinal"></span>
                            <lottie-player src="https://assets5.lottiefiles.com/packages/lf20_94094xlb.json" id="totalDiscountPercent" background="transparent" speed="1" loop autoplay></lottie-player>
                        </p>
                        <p class="mobile_view_summary d-md-none">
                            <a href="javascript:openOrderSummary()" class="open_order_summary">View<br>Summary</a>
                        </p>
                    </div>
                    <a href="javascript:proceedToBuy();" class="btn btn-lg proceed_btn" id="proceedToBuyButton">Proceed to Buy</a>
                    <%if("true".equals(session["commonWhiteLabel"])){%>
                    <a href="/sp/<%= session["siteName"] %>/store" class="btn continue_shop_btn border mt-2">Add more Books</a>
                    <%}else if("true".equals(session['prepjoySite'])){%>
                    <a href="/<%= session["entryController"] %>/eBooks" class="btn continue_shop_btn border mt-2">Add more Books</a>
                    <%}else{%>
                    <a href="/<%= session["entryController"] %>/store" class="btn continue_shop_btn border mt-2">Add more Books</a>
                    <%}%>
                </div>
            </div>

        </div>

    </div>
</section>

<div class="modal fade book-cart-modal modal-modifier" id="deleteCartBookModal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center">
                <h5 class="mt-3">Are you sure?</h5>
                <p>You want to remove this book from cart?</p>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-danger btn-danger-modifier btn-shadow border-0 col-5 col-md-4 ml-2" id="deleteCartBookBtn">Yes, Remove</button>
                </div>
            </div>

        </div>
    </div>
</div>
