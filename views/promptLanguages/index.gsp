<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h2 class="page-header">Prompts Language Manager</h2>

                    <g:if test="${flash.message}">
                        <div class="alert alert-success">${flash.message}</div>
                    </g:if>

                <!-- Form for selecting language -->
                    <form method="get" action="${createLink(controller:'promptLanguages', action:'index')}">
                        <div class="form-group">
                            <label for="language">Select Language:</label>
                            <select name="language" id="language" class="form-control">
                                <option value="">--Select--</option>
                                <g:each in="${languages}" var="lang">
                                    <option value="${lang}" ${lang == selectedLanguage ? 'selected="selected"' : ''}>${lang}</option>
                                </g:each>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Load</button>
                    </form>

                    <hr/>

                    <!-- Form for displaying and saving translations -->
                    <form method="post" action="${createLink(controller:'promptLanguages', action:'saveTranslations')}">
                        <input type="hidden" name="language" value="${selectedLanguage}" />
                        <table class="table table-bordered table-responsive">
                            <thead>
                            <tr>
                                <th>Prompt Type</th>
                                <th>English Prompt</th>
                                <th>Translated Prompt (${selectedLanguage ?: 'N/A'})</th>
                            </tr>
                            </thead>
                            <tbody>
                            <g:each in="${defaultPrompts}" var="p">
                                <tr>
                                    <td>
                                        <input type="text" name="promptType" value="${p.promptType}" class="form-control" readonly />
                                    </td>
                                    <td>${p.promptLabel}</td>
                                    <td>
                                        <input type="text" name="promptLabel" class="form-control"
                                             size="100"  value="${translationsMap[p.promptType]}"
                                            ${!selectedLanguage ? 'readonly' : ''}/>
                                    </td>
                                </tr>
                            </g:each>
                            </tbody>
                        </table>

                        <g:if test="${selectedLanguage}">
                            <button type="submit" class="btn btn-success">Save Translations</button>
                        </g:if>
                    </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
