<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<style>
body {
    padding: 20px;
}
.container {
    margin-top: 20px;
}
.category-select, .subcategory-select {
    margin-bottom: 20px;
}
.add-btn {
    margin-left: 10px;
}
.table-responsive {
    margin-top: 20px;
}
.modal .modal-header {
    background-color: #007bff;
    color: white;
}
.modal .modal-footer button {
    min-width: 100px;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Digital Library Manager</h3>
                    <div class="container">
                        <h2>Digital Library Admin</h2>
                        <div class="form-group category-select">
                            <label for="categorySelect">Select Reference Section</label>
                            <div class="input-group">
                                <select class="form-control" id="referenceSectionId" onchange="referenceSectionChanged()">
                                    <option value="">-- Select Category --</option>
                                    <g:each in="${referencesections}" var="referenceSection">
                                        <option value="${referenceSection.id}">${referenceSection.name}</option>
                                    </g:each>
                                </select>
                                 </div>
                        </div>
                        <div class="form-group category-select">
                            <label for="categorySelect">Select Category</label>
                            <div class="input-group">
                                <select class="form-control" id="categorySelect">
                                    <option value="">-- Select Category --</option>

                                </select>
                                <button type="button" class="btn btn-primary add-btn" data-toggle="modal" data-target="#addCategoryModal">Add Category</button>
                            </div>
                        </div>

                        <div class="form-group subcategory-select" style="display:none;">
                            <label for="subcategorySelect">Select Subcategory</label>
                            <div class="input-group">
                                <select class="form-control" id="subcategorySelect">
                                    <option value="">-- Select Subcategory --</option>
                                </select>
                                <button type="button" class="btn btn-primary add-btn" data-toggle="modal" data-target="#addSubcategoryModal">Add Subcategory</button>
                            </div>
                        </div>

                        <div id="resourcesSection" style="display:none;">
                            <button type="button" class="btn btn-success" data-toggle="modal" data-target="#addResourceModal">Add Resource</button>
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped" id="resourcesTable">
                                    <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Link</th>
                                        <th>Icon</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <!-- Resources will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Modals -->
                    <!-- Add Category Modal -->
                    <div class="modal fade" id="addCategoryModal" tabindex="-1" role="dialog" aria-labelledby="addCategoryModalLabel">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title" id="addCategoryModalLabel">Add Category</h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span>&times;</span></button>
                                </div>
                                <div class="modal-body">
                                    <form id="addCategoryForm">
                                        <div class="form-group">
                                            <label for="categoryName">Name</label>
                                            <input type="text" class="form-control" id="categoryName" name="name" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="categoryIcon">Icon</label>
                                            <input type="text" class="form-control" id="categoryIcon" name="icon">
                                        </div>
                                        <div class="form-group">
                                            <label for="categoryColor">Color</label>
                                            <input type="text" class="form-control" id="categoryColor" name="color">
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                    <button id="saveCategoryBtn" type="button" class="btn btn-primary">Save Category</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Add Subcategory Modal -->
                    <div class="modal fade" id="addSubcategoryModal" tabindex="-1" role="dialog" aria-labelledby="addSubcategoryModalLabel">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title" id="addSubcategoryModalLabel">Add Subcategory</h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span>&times;</span></button>
                                </div>
                                <div class="modal-body">
                                    <form id="addSubcategoryForm">
                                        <div class="form-group">
                                            <label for="subcategoryName">Name</label>
                                            <input type="text" class="form-control" id="subcategoryName" name="name" required>
                                        </div>
                                        <!-- Hidden field to store selected category -->
                                        <input type="hidden" id="subcategoryCategoryId" name="categoryId">
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                    <button id="saveSubcategoryBtn" type="button" class="btn btn-primary">Save Subcategory</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Add Resource Modal -->
                    <div class="modal fade" id="addResourceModal" tabindex="-1" role="dialog" aria-labelledby="addResourceModalLabel">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title" id="addResourceModalLabel">Add Resource</h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span>&times;</span></button>
                                </div>
                                <div class="modal-body">
                                    <form id="addResourceForm">
                                        <div class="form-group">
                                            <label for="resourceName">Name</label>
                                            <input type="text" class="form-control" id="resourceName" name="name" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="resourceLink">Link</label>
                                            <input type="text" class="form-control" id="resourceLink" name="link" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="resourceIcon">Icon</label>
                                            <input type="text" class="form-control" id="resourceIcon" name="icon">
                                        </div>
                                        <!-- Hidden field to store selected subcategory -->
                                        <input type="hidden" id="resourceSubcategoryId" name="subcategoryId">
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                    <button id="saveResourceBtn" type="button" class="btn btn-primary">Save Resource</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Edit Resource Modal -->
                    <div class="modal fade" id="editResourceModal" tabindex="-1" role="dialog" aria-labelledby="editResourceModalLabel">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title" id="editResourceModalLabel">Edit Resource</h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span>&times;</span></button>
                                </div>
                                <div class="modal-body">
                                    <form id="editResourceForm">
                                        <input type="hidden" id="editResourceId" name="id">
                                        <div class="form-group">
                                            <label for="editResourceName">Name</label>
                                            <input type="text" class="form-control" id="editResourceName" name="name" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="editResourceLink">Link</label>
                                            <input type="text" class="form-control" id="editResourceLink" name="link" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="editResourceIcon">Icon</label>
                                            <input type="text" class="form-control" id="editResourceIcon" name="icon">
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                    <button id="updateResourceBtn" type="button" class="btn btn-primary">Update Resource</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script>
    $(document).ready(function() {

    $('#categorySelect').change(function() {
            var categoryId = $(this).val();
            if (categoryId) {
                // Store categoryId in hidden field
            $('#subcategoryCategoryId').val(categoryId);
                // Load subcategories
            $.ajax({
                    url: '${createLink(controller: 'digitalLibrary', action: 'getSubcategories')}',
                    data: {categoryId: categoryId},
                    success: function(data) {
                    $('#subcategorySelect').html('<option value="">-- Select Subcategory --</option>' + data);
                            $('.subcategory-select').show();
                            $('#resourcesSection').hide();
                    }
                });
            } else {
            $('.subcategory-select').hide();
                    $('#resourcesSection').hide();
            }
        });

            $('#subcategorySelect').change(function() {
            var subcategoryId = $(this).val();
            if (subcategoryId) {
                // Store subcategoryId in hidden field
            $('#resourceSubcategoryId').val(subcategoryId);
                // Load resources
            $.ajax({
                    url: '${createLink(controller: 'digitalLibrary', action: 'getResources')}',
                    data: {subcategoryId: subcategoryId},
                    success: function(data) {
                    $('#resourcesTable tbody').html(data);
                            $('#resourcesSection').show();
                    }
                });
            } else {
            $('#resourcesSection').hide();
            }
        });

            $('#saveCategoryBtn').click(function() {
            var formData = $('#addCategoryForm').serialize();
            //add reference section id
            formData += '&referenceSectionId=' + $('#referenceSectionId').val();
                $.ajax({
                type: 'POST',
                url: '${createLink(controller: 'digitalLibrary', action: 'addCategory')}',
                data: formData,
                success: function(response) {
                    alert('Category added successfully');
                    location.reload();
                },
                error: function() {
                    alert('Failed to add category');
                }
            });
        });

            $('#saveSubcategoryBtn').click(function() {
            var formData = $('#addSubcategoryForm').serialize();
                $.ajax({
                type: 'POST',
                url: '${createLink(controller: 'digitalLibrary', action: 'addSubcategory')}',
                data: formData,
                success: function(response) {
                    alert('Subcategory added successfully');
                    // Reload subcategories
                    var categoryId = $('#categorySelect').val();
                        $.ajax({
                        url: '${createLink(controller: 'digitalLibrary', action: 'getSubcategories')}',
                        data: {categoryId: categoryId},
                        success: function(data) {
                        $('#subcategorySelect').html('<option value="">-- Select Subcategory --</option>' + data);
                                $('.subcategory-select').show();
                        }
                    });
                        $('#addSubcategoryModal').modal('hide');
                        $('#addSubcategoryForm')[0].reset();
                },
                error: function() {
                    alert('Failed to add subcategory');
                }
            });
        });

            $('#saveResourceBtn').click(function() {
            var formData = $('#addResourceForm').serialize();
                $.ajax({
                type: 'POST',
                url: '${createLink(controller: 'digitalLibrary', action: 'addResource')}',
                data: formData,
                success: function(response) {
                    alert('Resource added successfully');
                    // Reload resources
                    var subcategoryId = $('#subcategorySelect').val();
                        $.ajax({
                        url: '${createLink(controller: 'digitalLibrary', action: 'getResources')}',
                        data: {subcategoryId: subcategoryId},
                        success: function(data) {
                        $('#resourcesTable tbody').html(data);
                        }
                    });
                        $('#addResourceModal').modal('hide');
                        $('#addResourceForm')[0].reset();
                },
                error: function() {
                    alert('Failed to add resource');
                }
            });
        });

        // Edit resource
    $(document).on('click', '.edit-resource-btn', function() {
            var resourceId = $(this).data('id');
            // Load resource details
        $.ajax({
                url: '${createLink(controller: 'digitalLibrary', action: 'getResource')}',
                data: {id: resourceId},
                success: function(data) {
                $('#editResourceId').val(data.id);
                        $('#editResourceName').val(data.name);
                        $('#editResourceLink').val(data.link);
                        $('#editResourceIcon').val(data.icon);
                        $('#editResourceModal').modal('show');
                }
            });
        });

            $('#updateResourceBtn').click(function() {
            var formData = $('#editResourceForm').serialize();
                $.ajax({
                type: 'POST',
                url: '${createLink(controller: 'digitalLibrary', action: 'editResource')}',
                data: formData,
                success: function(response) {
                    alert('Resource updated successfully');
                    // Reload resources
                    var subcategoryId = $('#subcategorySelect').val();
                        $.ajax({
                        url: '${createLink(controller: 'digitalLibrary', action: 'getResources')}',
                        data: {subcategoryId: subcategoryId},
                        success: function(data) {
                        $('#resourcesTable tbody').html(data);
                        }
                    });
                        $('#editResourceModal').modal('hide');
                },
                error: function() {
                    alert('Failed to update resource');
                }
            });
        });

        // Delete resource
    $(document).on('click', '.delete-resource-btn', function() {
            if (confirm('Are you sure you want to delete this resource?')) {
                var resourceId = $(this).data('id');
                    $.ajax({
                    type: 'POST',
                    url: '${createLink(controller: 'digitalLibrary', action: 'deleteResource')}',
                    data: {id: resourceId},
                    success: function(response) {
                        alert('Resource deleted successfully');
                        // Reload resources
                        var subcategoryId = $('#subcategorySelect').val();
                            $.ajax({
                            url: '${createLink(controller: 'digitalLibrary', action: 'getResources')}',
                            data: {subcategoryId: subcategoryId},
                            success: function(data) {
                            $('#resourcesTable tbody').html(data);
                            }
                        });
                    },
                    error: function() {
                        alert('Failed to delete resource');
                    }
                });
            }
        });

    });

    function referenceSectionChanged(){
        var referenceSectionId = $('#referenceSectionId').val();
        if (referenceSectionId) {
            // Load categories
            $.ajax({
                url: '${createLink(controller: 'digitalLibrary', action: 'getCategories')}',
                data: {referenceSectionId: referenceSectionId},
                success: function(data) {
                    // Clear category select box
                    $('#categorySelect').html('<option value="">-- Select Category --</option>');
                    //data.categories has categories list. Use id and name from it and populate the select box
                    for(var i=0; i<data.categories.length; i++){
                        $('#categorySelect').append('<option value="'+data.categories[i].id+'">'+data.categories[i].name+'</option>');
                    }

                    $('.category-select').show();
                    $('.subcategory-select').hide();
                    $('#resourcesSection').hide();




                }
            });
        } else {
            $('.category-select').hide();
            $('.subcategory-select').hide();
            $('#resourcesSection').hide();
        }
    }
</script>

</body>
</html>
