<!DOCTYPE html>
<g:render template="/wonderpublish/loginChecker"></g:render>

<g:render template="/${session['entryController']}/navheader_new"></g:render>


<asset:stylesheet href="imageoverlay.css"/>
<asset:stylesheet href="bootstrap-select.css"/>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>

<link href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css" rel="stylesheet">
<div class="row">
    <div class="col-md-12 text-center">
        <h3>${resourceDtl.resourceName}</h3>
    </div>
</div>

<div class="container lightblue_bg border rounded my-5">
   <form method="post" action="/resourceCreator/updateSections" id="sectionForm">
       <div class="row">
           <div class="col-md-8">
               <b>AutoFill</b> &nbsp;<input type="radio" name="autoFill" value="on">On&nbsp;&nbsp;&nbsp;<input type="radio" name="autoFill"  value="off" checked>Off
           </div>
           <br><br>
       </div>
    <div class="list-chapters" id="chaptersList">
        <%for(int i=0;i<mcqs.size();i++){%>
        <div class="row">
        <div class="col-md-8">
            ${(i+1)}. ${mcqs[i].question}


        </div>
        <div class="col-md-1"></div>
        <div class="col-md-3"><select name="quiz_${mcqs[i].id}" id="quiz_${i}" onchange="subjectChanged(${i},${mcqs[i].id})">
            <option value="">Select</option>
            <%if(examDtl!=null){
                for(int j=0;j<examDtl.size();j++){%>
             <option value="${examDtl[j].subject}" <%=examDtl[j].subject.equals(mcqs[i].subject)?"selected":""%>>${examDtl[j].subject}</option>
            <%}}%>
        </select>



        </div>
    </div>
        <%}%>

    </div>
       <input type="hidden" name="bookId" value="${params.bookId}">
       <input type="hidden" name="resId" value="${params.resId}">
       <input type="hidden" name="chapterId" value="${params.chapterId}">

   </form>
    <div class="d-flex my-2 justify-content-center">

        <button class="btn btn-sm btn-primary" onclick="updateSections();">Update Sections</button>

    </div>
</div>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<script src="/assets/katex.min.js"></script>
<script src="/assets/auto-render.min.js"></script>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<asset:javascript src="bootstrap-select.js"/>
<asset:javascript src="generic.js"/>
<asset:javascript src="topic.js"/>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>

    function updateSections(){
        document.getElementById("sectionForm").submit();
         }


    $(document).ready(function(){
        renderMathInElement(document.body);
    });

    function subjectChanged(index,objId){
        if(document.querySelector('input[name="autoFill"]:checked').value=="on") {
            var selectedIndex = document.getElementById("quiz_" + index).selectedIndex;
            for (var k = (index + 1); k <${mcqs.size()}; k++) {
                document.getElementById("quiz_" + k).selectedIndex = selectedIndex;

            }
        }
    }

</script>
