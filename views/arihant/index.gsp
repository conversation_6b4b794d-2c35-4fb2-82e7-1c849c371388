<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Required meta tags -->
  <title>Buy Books Online from Arihant Store</title>

  <meta charset="utf-8">
  <meta name="keywords" content="buy books online, shop books online, online shopping for books, book shop, bookstore, online bookstore, online book shop india, books online, online book store, online bookstore india, Competitive Exam Book." />
  <meta name="description" content="Buy Competitive Exams and school exams books online at Arihant store. Find large collection of Entrance Exam Books for engineering, medical, Banking, school and other Exam." />
  <meta name="generator" content="ArihantBook" />
  <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="author" content="webmaster - Arihant Publication India Limited">
  <meta name="Arihant Publication India Limited" content=" https://www.arihantbooks.com">
  <meta name="subject" content="Book Store Online : Buy Books Online from Arihant Store">
  <meta name="keyphrase" content="Medical Exam Books, Board exams books, CBSE Exam Books, UGC Net, Air Force Books, state exam books, Govt Exam Books, NDA Exam Books, Bank Po Books, Entrance Exam Books, Engineering Books, Exam Books, General Books, General English Books, General Knowledge Books, NDA & CDS Books, SBI exam books, competition books in Hindi, ssc competition books, civil service books, banking Exams books, Gate, Teacher exam Books, buy books online, shop books online, online shopping for books, book shop, bookstore, online bookstore, online book shop india, books online, online book store, online bookstore india, Competitive Exam Book.">
  <meta name="abstract" content="Find large collection of Entrance Exam Books for engineering, medical, Banking, school and other Exam.">
  <link rel="shortcut icon" href="${assetPath(src: 'arihant/favicon.png')}" type="image/x-icon"/>
  <link rel="icon"  href="${assetPath(src: 'arihant/favicon.png')}" type="image/x-icon">
  <link rel="android-touch-icon" href="${assetPath(src: 'arihant/footer-icon-responsive.png')}"/>
  <link rel="windows-touch-icon" href="${assetPath(src: 'arihant/footer-icon-responsive.png')}" />

  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" async>

  <!-- Bootstrap CSS -->

  <asset:stylesheet href="arihant/style.css"/>
  <asset:stylesheet href="arihant/responsive.css" />
  <asset:stylesheet href="font-awesome.min.css"/>
  <asset:stylesheet href="arihant/animate.css"/>
  <asset:stylesheet href="landingpage/fonts/flaticon.css"/>
  <asset:stylesheet href="/assets/katex.min.css"/>

  <asset:stylesheet href="wonderslate/material.css" async="true"/>
  <asset:stylesheet href="landingpage/bootstrap.min.css" async="true"/>
  <asset:stylesheet href="wonderslate/headerws.css" async="true"/>
  <asset:stylesheet href="landingpage/slick.min.css" async="true"/>
  <asset:stylesheet href="landingpage/slick.theme.css" async="true"/>
  <asset:stylesheet href="landingpage/homepageStyle.css" async="true" data-role="baseline" />
  <asset:stylesheet href="arihant/arihantStyles.css" async="true"/>

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,700|Oswald:200,300,400,500,600,700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Playfair+Display:400,400i&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Fira+Sans:300,400,500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&family=Rubik:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

  <asset:javascript src="jquery-1.11.2.min.js"/>
  <script src="/assets/katex.min.js"></script>
  <script src="/assets/auto-render.min.js"></script>
  <!-- General styles for admin & user pages -->
  <%if("true".equals(commonTemplate)){%>
  <asset:stylesheet href="arihant/arihantTemplate.css" async="true"/>
  <% }%>

</head>
<body class="arihant">
<sec:ifNotLoggedIn>
  <g:render template="/books/signIn"></g:render>
</sec:ifNotLoggedIn>

<div class="loading-icon hidden">
  <div class="loader-wrapper">
    <div class="loader">Loading</div>
  </div>
</div>

<div id="wrapper" class="index-page">

  <%if(params.tokenId==null&&session["appType"]==null){%>
  <header>

    <div class="menu-main-min-height">
      <div class="main-menu-wrp" id="navbarIndex">

        <div class="container-fluid">
          <div class="row">
            <div class="pl-3 posi-static-respons">
              <div class="user-menu-wrp">
                <div class="wrp-new-posi-changes-arihant">
                  <a href="javascript:void(0)" class="menu-dots-img-wrp">
                    <img src="${assetPath(src: 'arihant/menu-dots.svg')}" class="menu-dots-img">
                    <img src="${assetPath(src: 'arihant/menu-close-menuss.png')}" class="menu-close-btn-menub" />
                  </a>
                </div>
                <div class="main-menu-wrp-arihant-big">
                  <div class="container-fluid">
                    <div class="row">
                      <div class="col-12 col-md-3 col-xl-2 background-white-main">
                        <div class="manage-logo-big-menu-arihant">

                          <img src="${assetPath(src: 'arihant/logo-big-menu.png')}">
                        </div>
                        <div class="main-side-bar-side-menu-big-wrp-wala-arihant">
                          <ul class="this-is-side-wrp-ul-big-menu-arihant">
                            <li class="active-menuss"><a href="javascript:void(0)">Books</a></li>
                          </ul>
                          <ul class="this-is-side-wrp-ul-big-menu-arihant">
                            <sec:ifNotLoggedIn>
                              <li><a href="javascript:loginOpen()">My Account</a></li>
                            </sec:ifNotLoggedIn>
                            <sec:ifLoggedIn>
                              <li><a href="/creation/userProfile">My Account</a></li>
                              <li><a href="javascript:logout()">Logout</a></li>
                            </sec:ifLoggedIn>
                          <!--<li><a href="contact-us.html">Contact Us</a></li>-->
                          </ul>

                        </div>
                      </div>
                      <div class="col-md-9 col-xl-10 arihant-big-menu-side-wrp">
                        <div class="book-wrapper-sec-part-main-menu-big">
                          <div class="row" id="topMenuItems">






                          </div><!--row-->
                        <!--row-->
                        </div><!--===Book Wrapper===-->
                      </div>
                    </div>
                  </div>
                </div>
                <div class="menu-overlay-big-menus"></div>

              </div>
              <div class="logo-white-small-wrp">
                <a href="/arihant/index" class="white-logo-anchor-white">
                  <img src="${assetPath(src: 'arihant/logo-white-small.png')}">
                </a>
              </div>

              <div class="overlay-menu-close"></div>
              <div class="clearfix"></div>
            </div>

            <div class="d-flex justify-content-start global-search pr-0 pr-md-3 col">
              <form class="form-inline rounded rounded-modifier col-12 col-lg-10 col-xl-8 pl-0 px-md-3">
                <input type="text" class="form-control form-control-modifier border-0 typeahead w-100" name="search" id="search-book-header" autocomplete="off" placeholder="Search title, subject, author, ISBN, language etc.">
                <button type="button" class="btn bg-transparent text-primary text-primary-modifier" onclick="submitSearchHeader()" id="search-btn-header"><i class="material-icons">search</i></button>
              </form>
            </div>

            <div class="pr-md-3">
              <div class="menu-wrp-all-users-com">
                <ul>
                  <li class="link-menu-li d-none d-md-block"><a href="/arihant/store" class="menu-link-anchor"><span>Store</span></a></li>
                  <li class="link-menu-li navbar_cart"><a href="/wsshop/cart" class="menu-link-anchor mobile_cart_icon"><img src="${assetPath(src: 'arihant/shopping-cart.png')}" alt="" title="" /><span class="hide_mobile">Cart</span> <span class="cart_count" id="navbarCartCount">0</span></a></li>
                  <sec:ifNotLoggedIn>
                    <li class="none-add-class-responsive"><a href="javascript:loginOpen()"><img src="${assetPath(src: 'arihant/user-login.png')}" alt="" title="" /><span class="hide_mobile">Login</span></a></li>
                  </sec:ifNotLoggedIn>
                  <sec:ifLoggedIn>
                    <li class="none-add-class-responsive d-none d-md-block"><a href="/wsLibrary/myLibrary"><img src="${assetPath(src: 'arihant/user-login.png')}" alt="" title="" /><span class="hide_mobile">My Books</span></a></li>
                  </sec:ifLoggedIn>

                  <sec:ifAllGranted roles="ROLE_FINANCE">
                    <li class="header-menu-item d-none d-md-block">
                      <a href="/publishing-sales" class="header-menu-item-link">Sales</a>
                    </li>
                  </sec:ifAllGranted>
                  <sec:ifAllGranted roles="ROLE_CLIENT_ORDER_MANAGER">
                    <li class="header-menu-item d-none d-md-block">
                      <a href="/wsshop/orderManagement" class="header-menu-item-link">Order Management</a>
                    </li>
                  </sec:ifAllGranted>
                  <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
                    <li class="nav-item dropdown d-none d-md-block">
                      <a class="nav-link dropdown-toggle" href="#" id="publishing" data-toggle="dropdown">
                        Publishing
                      </a>
                      <div class="dropdown-menu">
                        <a class="dropdown-item" href="/publishing-desk">Publishing Desk</a>
                        <a class="dropdown-item" href="/wonderpublish/manageTabs">Manage Tags</a>
                        <a class="dropdown-item" href="/wonderpublish/manageExams">Manage MCQs Templates</a>
                      </div>
                    </li>
                  </sec:ifAllGranted>
                  <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_WS_CONTENT_ADMIN,ROLE_NOTIFICATION,ROLE_USER_LOGIN_RESET_MANAGER,ROLE_APP_ADMIN,ROLE_INSTITUTE_REPORT_MANAGER,ROLE_WEB_CHAT,ROLE_WS_EDITOR,ROLE_LIBRARY_ADMIN,ROLE_MASTER_LIBRARY_ADMIN">

                    <li class="nav-item dropdown d-none d-md-block">
                      <a class="nav-link dropdown-toggle" href="#" id="ad" data-toggle="dropdown">
                        Admin
                      </a>
                      <div class="dropdown-menu dropdown-menu-right">
                        <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                          <a class="dropdown-item" href="/admin/priceList">Price List</a>
                        </sec:ifAllGranted>

                        <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                          <a class="dropdown-item" href="/admin/discountManager">Discount Management</a>
                        </sec:ifAllGranted>

                        <sec:ifAllGranted roles="ROLE_DELETE_USER">
                          <a class="dropdown-item" href="/admin/deleteuser">Delete User</a>
                        </sec:ifAllGranted>
                        <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                          <a class="dropdown-item" href="/institute/admin">eClass+</a>
                        </sec:ifAllGranted>
                        <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                          <a class="dropdown-item" href="/institute/libAdmin">Library Management</a>
                        </sec:ifAllGranted>
                        <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_INSTITUTE_REPORT_MANAGER">
                          <a class="dropdown-item" href="/reports/instituteReport">Institute reports</a>
                        </sec:ifAnyGranted>
                        <sec:ifAnyGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                          <a class="dropdown-item" href="/institute/usageReportInstituteAdmin">Usage Statistics Report</a>
                        </sec:ifAnyGranted>
                        <sec:ifAllGranted roles="ROLE_PUBLISHER">
                          <a class="dropdown-item" href="/log/quizissues">Quiz Issues</a>
                        </sec:ifAllGranted>
                        <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                          <a class="dropdown-item" href="/log/notification">Notification</a>
                        </sec:ifAllGranted>
                        <sec:ifAllGranted roles="ROLE_USER_LOGIN_RESET_MANAGER">
                          <a class="dropdown-item" href="/log/userManagement">Force Logout</a>
                        </sec:ifAllGranted>

                        <sec:ifAllGranted roles="ROLE_ACCESS_CONTROLL">
                          <a class="dropdown-item" href="/log/userAccess">User Access</a>
                        </sec:ifAllGranted>

                        <sec:ifAllGranted roles="ROLE_WS_EDITOR">
                          <a class="dropdown-item" href="/wonderpublish/wseditor">WS Editor</a>
                        </sec:ifAllGranted>
                        <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                          <a class="dropdown-item" href="/log/notificationManagement">Notification Management</a>
                        </sec:ifAllGranted>
                        <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN,ROLE_BOOK_CREATOR">
                          <a class="dropdown-item" href="/admin/managePublishers">Publisher Management</a>
                        </sec:ifAllGranted>
                        <%if(session["userdetails"].publisherId!=null){%>
                        <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                          <a class="dropdown-item" href="/publisherManagement/publisherReport">Publisher Report</a>
                        </sec:ifAllGranted>
                        <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                          <a class="dropdown-item" href="/admin/accessCodeUsage">Access Code Report</a>
                        </sec:ifAllGranted>
                        <%}%>
                        <sec:ifAllGranted roles="ROLE_LIBRARY_ADMIN">
                          <a class="dropdown-item" href="/institute/userManagement">Library Management</a>
                          <a class="dropdown-item" href="/institute/manageInstitutePage">Institute Page Management</a>
                          <a class="dropdown-item" href="/institute/usageReportInstituteAdmin">Usage Statistics Report</a>
                        </sec:ifAllGranted>
                        <sec:ifAnyGranted roles="ROLE_BOOK_CREATOR,ROLE_DIGITAL_MARKETER">
                          <a class="dropdown-item" href="/wonderpublish/bannerManagement">Banner Management</a>
                        </sec:ifAnyGranted>
                      </div>
                    </li>
                  </sec:ifAnyGranted>
                %{--                <li class="none-add-class-responsive"><a class="search-btn-click" href="/arihant/store"><img src="${assetPath(src: 'arihant/search-icon.png')}" alt="" title="" />Search</a></li>--}%
                  <div class="clearfix"></div>
                </ul>
                <div class="clearfix"></div>
              </div>
            </div>

          </div><!--row End-->
          <div class="row search-item-rows">
            <div class="this-div-use-box-search">
              <div class="search-view-bar-arihant">
                <form class="form-search-box-arihant">
                  <div class="wrapper-form-header-search-input">
                    <input class="input-box-search-in-header" type="text" placeholder="Search arihantbooks.com">
                    <button class="btn-in-open-search-header-search"></button>
                  </div>

                  <button class="close-all-searchdiv-box" type="button">
                    <span class="wrapper-close-btn-search-header-all">
                      <span class="close-btn-search-header-one-part"></span>
                      <span class="close-btn-search-header-second-part"></span>
                    </span>
                  </button>
                </form>
              </div>
              <aside class="search-result-arihant">
                <section class="search-result-in-wrapper-one">
                  <div class="search-result-arihant-one-on">
                    <h3 class="search-result-quick-disc">Quick Links</h3>
                    <ul class="arihant-all-list-view-serach-result">
                      <li class="result-all-item-here-animated"><a href="javascript:void(0)">New Release</a></li>
                      <li class="result-all-item-here-animated"><a href="javascript:void(0)">Best Selling Books</a></li>
                      <li class="result-all-item-here-animated"><a href="javascript:void(0)">Engineering Entrances</a></li>
                      <li class="result-all-item-here-animated"><a href="javascript:void(0)">Medical Entrances</a></li>
                      <li class="result-all-item-here-animated"><a href="javascript:void(0)">School Curriculum</a></li>
                      <li class="result-all-item-here-animated"><a href="javascript:void(0)">Government Exams</a></li>
                      <li class="result-all-item-here-animated"><a href="javascript:void(0)">ITI & Polytechnic</a></li>
                    </ul>
                  </div>
                </section>
              </aside>
            </div>
          </div><!--row end-->
        </div>

      </div>
    </div>
    <div class="header-wrapper">
    <div class="bg-wrapper-sec">

      <div id="slider-desktop" class="carousel slide this-is-a-web-view-slider" data-ride="carousel">
        <ol class="carousel-indicators" id="slider-desktop-indicators"></ol>
        <div class="carousel-inner" id="slider-desktop-views"></div>
      </div>

      <div id="bindAllItem">
        <div id="slider-mobile" class="carousel slide this-is-a-responsive-view-slider" data-ride="carousel">
          <ol class="carousel-indicators" id="slider-mobile-indicators"></ol>
          <div class="carousel-inner" id="slider-mobile-views"></div>
        </div>
        <div class="header-menu-wrapper">
          <div class="logo-wrapper">
            <a href="/arihant/index">
              <img src="${assetPath(src: 'arihant/logo.png')}" alt="Arihant Logo"/>
            </a>
          </div>
          <div class="menu-bar-wrp">
            <ul>
              <li class="facebook"><a href="https://www.facebook.com/arihantpub" target="_blank">facebook</a></li>
              <li class="twitter"><a href="https://twitter.com/arihantpub" target="_blank">twitter</a></li>
              <li class="Instagram"><a href="https://www.instagram.com/arihantpub/" target="_blank">instagram</a></li>
              <li class="youtube"><a href="https://www.youtube.com/user/arihantpublications" target="_blank">youtube</a></li>
              <div class="clearfix"></div>
            </ul>
          </div>
        </div>
      </div>

    </div>
  </div>
  </header>
  <% }%>

  <div class="overlay-wrp-s"></div>

  <div class="categories-section">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-10 col-lg-7">
          <div class="text-center border-title-categories">
            <h2 class="title-categories-section">eBook Categories</h2>
          </div>
        </div>
      </div>

      <div id="ebookCategories">
        <div class="category_list">

        </div>
      </div>
    </div>
  </div>

  <div class="journey-sec-wrp">

    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-10 col-lg-7">
          <div class="text-center border-title-journey">
            <div class="the-img-icon-journey">
              <img src="${assetPath(src: 'arihant/jounery-icon.png')}" alt="" title="" />
            </div>
            <h2 class="title-journey-section">The Journey</h2>
          </div>
        </div>
        <div class="col-md-10 col-lg-9 text-center">
          <p class="journey-section-disc-title-pera">From one Man's dream to passion of thousands of individuals, measure of our success <br/> has always been your achievements. Since 1997, we have come far and feel the journey has just begun.</p>
        </div>
      </div>

      <div class="row manage-margin-wrp-journey">

        <div class="col-lg-3 col-sm-6 col-6">
          <div class="manage-count-wrp-box">
            <h2><span class="counter">20</span>+</h2>
            <span>Years of Existence</span>
          </div>
        </div>

        <div class="col-lg-3 col-sm-6 col-6">
          <div class="manage-count-wrp-box">
            <h2><span class="counter">23</span></h2>
            <span>Offices Pan India</span>
          </div>
        </div>

        <div class="col-lg-3 col-sm-6 col-6">
          <div class="manage-count-wrp-box">
            <h2><span class="counter">5000</span>+</h2>
            <span>Retailers & Distributors</span>
          </div>
        </div>

        <div class="col-lg-3 col-sm-6 col-6">
          <div class="manage-count-wrp-box brdr-right-nn">
            <h2><span class="counter">6000</span>+</h2>
            <span>Working Professionals</span>
          </div>
        </div>

      </div>
    </div>

  </div>

  <div class="connect-section">
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-2 display-none-responsive">
          <div class="img-wrapper-connect">
            <img src="${assetPath(src: 'arihant/connect-circul-user.png')}" alt="" title="" />
          </div>
        </div>
        <div class="col-lg-8">
          <div class="row justify-content-center">
            <div class="col-lg-9 col-sm-10">
              <div class="text-center border-title-coonect">
                <h2 class="title-connect-section">Connect</h2>
              </div>
            </div>
            <div class="col-lg-9 col-sm-9">
              <p class="connect-sec-wrp-disc-pera">'Customer Experience always comes first', we genuinely believe in this and would  always love to hear from you, be it related to books, orders, content or business. <br/> Get in touch and follow us to stay updated.</p>
            </div>
          </div>


          <div class="row mrgn-top-connect-sec">
            <div class="col-lg-6 col-sm-12 center-responsive-div">
              <ul class="social-icon-wrp-connect">
                <li><a href="https://www.facebook.com/arihantpub" target="_blank"><i class="fa fa-facebook"></i></a></li>
                <li><a href="https://twitter.com/arihantpub" target="_blank"><i class="fa fa-twitter"></i></a></li>
                <li><a href="https://www.instagram.com/arihantpub/" target="_blank"><i class="fa fa-instagram"></i></a></li>
                <li><a href="https://www.youtube.com/user/arihantpublications" target="_blank"><i class="fa fa-youtube-play"></i></a></li>
                <div class="clearfix"></div>
              </ul>
            </div>

            <div class="col-lg-6 col-sm-12 responsive-center-text">
              <div class="img-wrp-call-in">
                <img src="${assetPath(src: 'arihant/email-icon.png')}" align="" title="" />
              </div>
              <h3 class="call-here-number"><a href="tel:+91-11-40546380">+91-11-40546380</a></h3>
              <div class="responsive-view-icon-this">
                <img src="${assetPath(src: 'arihant/email-icon.png')}" align="" title="" />
              </div>
            </div>

          </div>
        </div>
        <div class="col-lg-2 display-none-responsive">
          <div class="img-wrapper-connect phone-connect-img-here">
            <img src="${assetPath(src: 'arihant/connect-phone-icon.png')}" alt="" title="" />
          </div>
        </div>
        <div class="dispay-set-resposive-img-phone">
          <img src="${assetPath(src: 'arihant/connect-phone-icon.png')}" alt="" title="" />
        </div>

      </div>
    </div>
  </div>

</div>

<script>
  var activeCategories = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
  var activeCategoriesSyllabus = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
  const groupedData = activeCategoriesSyllabus.reduce((acc, obj) => {
    const key = obj.level;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(obj);
    return acc;
  }, {});

  let html = '';

  for (const key in groupedData) {
    const group = groupedData[key];
    html += "<div class='category_level'>"+
            "<h4><a href='/${session["entryController"]}/store?level="+encodeURIComponent(key)+"' style='font-size:inherit;color: inherit;font-weight: inherit;'>"+key+"</a></h4>";
    html += "<div class='category_cards'>";
    for (const obj of group) {
      html +="<a href='/${session["entryController"]}/store?level="+encodeURIComponent(key)+"&syllabus="+encodeURIComponent(obj.syllabus)+"' class='category_card'>"+
              "<div class='category_card-title'>"+
              "<p>"+obj.syllabus+"</p>"+
              "</div>"+
              "</a>";
    }
    html += '</div>'+
            "</div>";
  }

  document.querySelector(".category_list").innerHTML=html;

  function logout(){
    setCookie("level","");
    setCookie("syllabus","");
    setCookie("grade","");
    setCookie("subject","");
    window.location.href = '/logoff';
  }

  var startProductBarPos=-1;
  window.onscroll=function(){
    var bar = document.getElementById('navbarIndex');
    if(startProductBarPos<0)startProductBarPos=findPosY(bar);

    if(pageYOffset>startProductBarPos){
      bar.style.position='fixed';
      bar.style.top=0;
    }else{
      bar.style.position='relative';
    }

  };

  function findPosY(obj) {
    var curtop = 0;
    if (typeof (obj.offsetParent) != 'undefined' && obj.offsetParent) {
      while (obj.offsetParent) {
        curtop += obj.offsetTop;
        obj = obj.offsetParent;
      }
      curtop += obj.offsetTop;
    } else if (obj.y)
      curtop += obj.y;
    return curtop;
  }
</script>

<g:render template="/arihant/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js"></script>

<script>
  var siteId = "${session["siteId"]}";
  var checkDescBanners = false;
  var checkMobBanners = false;
  var templateDesktop = "";
  var templateMobile = "";
  var defaultDeskImageUrl = "/assets/arihant/bg-banner-arihant.jpg";
  var defaultMobImageUrl = "/assets/arihant/bg-mobile-slide.png";

  function showBanners(data) {
    setTimeout(function () {
      $(".loading-icon").addClass("hidden");
    },2000);
    if(data.status=="OK") {
      var banners = data.banners.reverse();
      $('#slider-desktop-views,#slider-mobile-views').empty();

      // Banner slider
      $.each(banners, function (i, v) {
        var item = v;
        var htmlStr = '';
        var htmlStrMobile = '';
        var indicatorStr = '';
        var indicatorStrMobile = '';
        var imageDescUrl = "/wonderpublish/showImage?id=" + v.id + "&fileName=" + v.imagePath + "&imgType=webp";
        var imageMobUrl = "/wonderpublish/showImage?id="+v.id+"&fileName="+v.imagePathMobile + "&imgType=webp";
        if(v.bookTitle) {
          var bookTitle = "/" + replaceAll(replaceAll(v.bookTitle, ' ', '-').toLowerCase(), '\'', '');
        }
        var bookHyperLink = bookTitle + "/ebook-details?siteName=${session['entryController']}&bookId=" + v.bookId + "&preview=true";

        var actionLink = v.action //action field link
        var serverURL =  window.location.origin //getting base url
        actionLink = serverURL+'/'+actionLink;

        indicatorStr += "<li data-target='#slider-desktop' class='border-light' data-slide-to='" + i + "'></li>";
        indicatorStrMobile += "<li data-target='#slider-mobile' class='border-light' data-slide-to='" + i + "'></li>";
        if(v.bookId) {
          htmlStr += '<div class="carousel-item">' +
                  '<a href="' + bookHyperLink + '" target="_blank" class="d-block">' +
                  '<img src="'+imageDescUrl+'">' +
                  '</a>' +
                  '</div>';
          htmlStrMobile += '<div class="carousel-item">' +
                  '<a href="' + bookHyperLink + '" target="_blank" class="d-block">' +
                  '<img src="'+imageMobUrl+'">' +
                  '</a>' +
                  '</div>';
        } else if (v.action) {
          htmlStr += '<div class="carousel-item">' +
                  '<a href="' + actionLink + '" target="_blank" class="d-block">' +
                  '<img src="'+imageDescUrl+'">' +
                  '</a>' +
                  '</div>';
          htmlStrMobile += '<div class="carousel-item">' +
                  '<a href="' + actionLink + '" target="_blank" class="d-block">' +
                  '<img src="'+imageMobUrl+'">' +
                  '</a>' +
                  '</div>';
        }else{
          htmlStr += '<div class="carousel-item">' +
                  '<img src="'+imageDescUrl+'">' +
                  '</div>';
          htmlStrMobile += '<div class="carousel-item">' +
                  '<img src="'+imageMobUrl+'">' +
                  '</div>';
        }

        // If desktop banners are available
        if(v.imagePath) {
          checkDescBanners = true;
          $('#slider-desktop-views').append(htmlStr).find('.carousel-item:first-child').addClass('active');
          $('#slider-desktop-indicators').append(indicatorStr).find('li:first-child').addClass('active');
        }

        // If mobile banners are available
        if(v.imagePathMobile) {
          checkMobBanners = true;
          $('#slider-mobile-views').append(htmlStrMobile).find('.carousel-item:first-child').addClass('active');
          $('#slider-mobile-indicators').append(indicatorStrMobile).find('li:first-child').addClass('active');
        }

      });

    } else if(data.status=="Nothing Present") {
      checkDescBanners = false; checkMobBanners = false;
    }

    // Showing empty banners based on condition
    if(!checkDescBanners && !checkMobBanners) {
      templateDesktop += emptyDesktopBannerUI(defaultDeskImageUrl);
      $('#slider-desktop-views').append(templateDesktop);
      templateMobile += emptyMobileBannerUI(defaultMobImageUrl);
      $('#slider-mobile-views').append(templateMobile);
    } else if(!checkMobBanners) {
      templateMobile += emptyMobileBannerUI(defaultMobImageUrl);
      $('#slider-mobile-views').append(templateMobile);
    }

  }

  // If desktop banner images are empty calling this function
  function emptyDesktopBannerUI(defaultImage) {
    var emptyBannerDesk = '<div class="carousel-item active">' +
            '<img src="'+defaultImage+'">' +
            '</div>';
    return emptyBannerDesk;
  }

  // If mobile banner images are empty calling this function
  function emptyMobileBannerUI(defaultImage) {
    var emptyBannerMob = '<div class="carousel-item active">' +
            '<img src="'+defaultImage+'">' +
            '</div>';
    return emptyBannerMob;
  }

  function getBannerDetails(siteId) {
    $(".loading-icon").removeClass("hidden");
    <g:remoteFunction controller="wonderpublish" action="getBannerdetails" params="'siteId='+siteId" onSuccess="showBanners(data);"/>
  }

  getBannerDetails(siteId);

  function replaceAll(str, find, replace) {
    if (str == undefined) return str
    else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
  }

  function escapeRegExp(str) {
    return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
  }

  if($.browser.platform == "win") {
    $("html").addClass("windows");
  }

  if($.browser.name == "chrome") {
    $("html").addClass("chrome");
  } else if($.browser.name == "mozilla") {
    $("html").addClass("mozilla");
  } else if($.browser.name == "safari") {
    $("html").addClass("safari");
    if($(window).width() < 767){
      $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
        $(this).css('background','#eee');
        $('.btn-primary-modifier').css('background-color','#A81175 !important');
        $('.btn-success-modifier').css('background-color','#27AE60 !important');
        $('.btn-secondary-modifier').css('background-color','#8E8E8E !important');
        $('.btn-danger-modifier').css('background-color','#FF4B33 !important');
        $('.btn-warning-modifier').css('background-color','#FFD602 !important');
      });
      document.addEventListener('touchmove', function (event) {
        if (event.scale !== 1) { event.preventDefault(); }
      }, { passive: false });
    }
  } else {
    $("html").addClass("others");
    if($(window).width() < 767){
      $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
        $(this).css('background','#eee');
      });
      document.addEventListener('touchmove', function (event) {
        if (event.scale !== 1) { event.preventDefault(); }
      }, { passive: false });
    }
  }
</script>

</body>
</html>
