%{--<g:render template="/books/wsabout"></g:render>--}%
<g:render template="/wonderpublish/wsFooter"></g:render>
<asset:stylesheet href="wonderslate/footer.css" async="true"/>
<asset:javascript src="moment.min.js"/>

<script>
    var userLoggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        userLoggedIn=true;
    </script>
</sec:ifLoggedIn>

<%if(params.tokenId==null&&session["appType"]==null && !isBookPage){%>

<footer class="footer-menus py-3 mt-5">
    <div class="container-fluid footer-fluid">
        <div class="row mx-0">
            <div class="col-lg-5">
                <div class="row mx-0">
                    <div class="col-md-6">
                        <ul class="footer_logo_wrapper">
                            <li>
                                <div class="logo-wrapper-new">
                                    <img class="footer-logo" src="${assetPath(src: 'ws/footer-logo.svg')}" alt="wonderslate">
                                </div>
                            </li>
                            <li class="desktop mt-3 mt-md-0">
                                <a href="https://play.google.com/store/apps/details?id=com.wonderslate.wonderpublish&utm_source=signup%20mailer&utm_medium=email&utm_campaign=Wonderslate_App&utm_term=IITJEE" target="_blank">
                                    <button class="download-btns">
                                        <img class="footer-logo" src="${assetPath(src: 'ws/playstore.svg')}" alt="wonderslate">
                                    </button>
                                </a>
                                <a href="https://apps.apple.com/in/app/wonderslate/id1438381878" target="_blank">
                                    <button class="download-btns">
                                        <img class="footer-logo" src="${assetPath(src: 'ws/appstore.svg')}" alt="wonderslate">
                                    </button>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6 explore-section pl-0">
                        <div class="widget-ftr ipadVis">
                            <div id="vl"></div>
                            <div id="explore">
                                <h4>Explore</h4>
                                <ul>
                                    <sec:ifNotLoggedIn>
                                        <li>
                                            <a href="javascript:loginOpen();">Home</a>
                                        </li>
                                    </sec:ifNotLoggedIn>
                                    <sec:ifLoggedIn>
                                        <li>
                                            <a href="/books/home" >Home</a>
                                        </li>
                                    </sec:ifLoggedIn>
                                    <li><a href="/books/publishersProduct">Our Products</a> </li>
                                    <li><a href="https://blog.wonderslate.com/" target="_blank">Blog</a> </li>
                                    <li><a href="/books/faq">FAQs</a> </li>
                                    <li><a href="/funlearn/termsandconditions">Terms & Conditions</a> </li>
                                    <li><a href="/funlearn/privacy">Privacy Policy</a> </li>
                                </ul>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="col-lg-7">
                <div class="row mx-0">
                    <div class="col-md-4 pl-0">
                        <div class="widget-ftr">
                            <h4>Trending Categories</h4>
                            <div class="w-100" id="trendingCategories"></div>
                        </div>
                    </div>
                    <div class="col-md-4 pl-0">
                        <div class="widget-ftr">
                            <h4>Featured Publishers</h4>
                            <div class="w-100" id="trendingPublishers"></div>
                        </div>
                    </div>
                    <div class="col-md-4 pl-0">
                        <div class="widget-ftr">
                            <h4>Connect</h4>
                            <ul>
                                <li><a href="tel:+91-8088443860">+91-8088443860</a> </li>
                                <li><a href="mailto:<EMAIL>"><EMAIL></a> </li>
                            </ul>
                            <ul class="social-icons mb-0">
                                <li><a href="https://www.facebook.com/wonderslatesmartbooks" target="_blank"><i class="fb flaticon-facebook-logo"></i> </a> </li>
                                <li><a href="https://www.instagram.com/wonderslatesmartbooks/" target="_blank"><img src="${assetPath(src:'wonderslate/instagram.svg')}"/></a></li>
                                <li><a href="https://www.youtube.com/channel/UCMJv3HwAgBCwqWsZ63wIWwA" target="_blank"><i class="yt flaticon-youtube"></i> </a> </li>
                                <li><a href="https://wa.me/918088443860" target="_blank"><img src="${assetPath(src:'wonderslate/whatsapp.svg')}"/></a> </li>
                                <li><a href="https://t.me/wonderslate" target="_blank"><img src="${assetPath(src:'wonderslate/telegram.svg')}"/></a> </li>
                            </ul>
                            <ul>
                                <li>
                                    <button class="digitalLib-btn rounded w-100"><a href="/wsLibrary/index" target="_blank">Digital Library</a></button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 accept-payments text-center">
                <img  src="${assetPath(src:'wonderslate/accept-payments.svg')}"/>
            </div>
        </div>
        <div class="row footer-secRow">
            <div class="col-md-6 copyright-footer"></div>
            <div class="col-md-6 company-name">Wonderslate Technologies Pvt Ltd.</div>
        </div>
    </div>
</footer>

<%if(!"true".equals(session["appInApp"])){%>
<div class="mobile-footer-nav d-md-none" id="mobile-footer-nav"></div>
<%}%>

<%}%>
<div class="modal fade book-cart-modal modal-modifier" id="bookCartModal">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center">
                <div id="addedIconAnimation" class="scaleAnimation" style="display: none;">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div id="existIconAnimation" class="f-modal-alert" style="display: none;">
                    <div class="f-modal-icon f-modal-warning scaleAnimation">
                        <span class="f-modal-body pulseAnimationIns"></span>
                        <span class="f-modal-dot pulseAnimationIns"></span>
                    </div>
                </div>
                <h5 id="cartModalText" class="mt-3">eBook is added to your cart!</h5>

                <div id="cartModalBtns" class="d-none justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-6 col-md-5 mr-2" data-dismiss="modal" aria-label="Close">Continue Browsing</button>
                    <button type="button" onclick="goCartPage();" class="btn btn-lg btn-success btn-shadow border-0 col-6 col-md-5 ml-2" data-dismiss="modal" aria-label="Close">Go to Cart</button>
                </div>
                <div id="cartModalLibBtn" class="d-none justify-content-center py-3 col-12">
                    <button type="button" onclick="goLibraryPage();" class="btn btn-lg btn-success btn-shadow border-0 col-6 col-md-5 ml-2" data-dismiss="modal" aria-label="Close">Go to My eBooks</button>
                </div>
            </div>

        </div>
    </div>
</div>



<asset:javascript src="landingpage/popper.min.js"/>
<asset:javascript src="landingpage/bootstrap.min.js"/>

<asset:javascript src="landingpage/jquery.shorten.js" />
<asset:javascript src="wonderslate/material.min.js"/>
<asset:javascript src="landingpage/slick.js"/>

<asset:javascript src="browser-deeplink.js"/>

%{-- JS Libraries --}%
<asset:javascript src="wonderslate/vendors.min.js"/>

<sec:ifNotLoggedIn>
    <g:render   template="/creation/register"></g:render>
</sec:ifNotLoggedIn>
<g:render template="/books/pomodoro"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
<script>

    //Dynamic Year in Footer
    var strDate = new Date();
    var shortYear = strDate.getFullYear();
    $('.copyright-footer').html('&copy; Wonderslate ' + shortYear + " All rights reserved.");


    var mobileFooterNav =
        '        <div class="d-flex row justify-content-around align-items-center w-100">\n' +
        '<sec:ifNotLoggedIn>\n' +
        '            <a href="javascript:loginOpen()" class="home-menu d-flex align-items-center col">\n' +
        '                <img class="mb-1 inactive" src="${assetPath(src: 'ws/icon-mobile-home.svg')}">\n' +
        '                <img class="mb-1 active d-none" src="${assetPath(src: 'ws/icon-home-filled.svg')}">\n' +
        '                <p>My Library</p>\n' +
        '            </a>\n' +
        '</sec:ifNotLoggedIn>'+
        '<sec:ifLoggedIn>'+
        '            <a href="/wsLibrary/myLibrary" class="home-menu d-flex align-items-center col">\n' +
        '                <img class="mb-1 inactive" src="${assetPath(src: 'ws/icon-mobile-home.svg')}">\n' +
        '                <img class="mb-1 active d-none" src="${assetPath(src: 'ws/icon-home-filled.svg')}">\n' +
        '                <p>My Library</p>\n' +
        '            </a>\n' +
        <%if(session["userInstitutes"]!=null&&session["userInstitutes"].size()>0){%>
        '            <a href="/institute/home?instituteId=${session["userInstitutes"][0].id}" class="institute-menu d-flex align-items-center col">\n' +
        '                <img class="mb-1 inactive" src="${assetPath(src: 'ws/icon-mobile-institute.svg')}">\n' +
        '                <img class="mb-1 active d-none" src="${assetPath(src: 'ws/icon-institute-filled.svg')}">\n' +
        '                <p><span class="institute-menu-text">${session["userInstitutes"][0].name}</span></p>\n' +
        '            </a>\n' +
        <%}%>
        '</sec:ifLoggedIn>'+
        '            <a href="/ebooks" class="ebooks-menu d-flex align-items-center col">\n' +
        '                <img class="mb-1 inactive" src="${assetPath(src: 'ws/icon-mobile-library.svg')}">\n' +
        '                <img class="mb-1 active d-none" src="${assetPath(src: 'ws/icon-ebooks-filled.svg')}">\n' +
        '                <p>eBooks Store</p>\n' +
        '            </a>\n' +
        %{--        <%if(session["userdetails"]==null||session["userdetails"].username.indexOf("1_cookie_")!=0){%>--}%
        %{--        '<sec:ifLoggedIn>'+--}%
        %{--        '            <a href="javascript:checkLoginAndProceed(\'/groups/index\');" class="groups-menu d-flex align-items-center col">\n' +--}%
        %{--        '                <img class="mb-1 inactive" src="${assetPath(src: 'ws/icon-mobile-study-groups.svg')}">\n' +--}%
        %{--        '                <img class="mb-1 active d-none" src="${assetPath(src: 'ws/icon-mobile-study-groups.svg')}">\n' +--}%
        %{--        '                <p>Study Groups</p>\n' +--}%
        %{--        '            </a>\n' +--}%
        %{--        '</sec:ifLoggedIn>'+--}%
        %{--        <%}%>--}%
        '        </div>';

    $(document).ready(function(){
        document.getElementById('mobile-footer-nav') ? document.getElementById('mobile-footer-nav').innerHTML = mobileFooterNav : null;

        var url = window.location.href;
        if(url.indexOf("/funlearn/quiz") != -1){
            $('.mobile-footer-nav').addClass('hide-menus');
        } else if(url.indexOf("/dashboard") != -1 || url.indexOf("/books/home") != -1){
            $('.mobile-footer-nav .home-menu').addClass('active-menu');
            $('.mobile-footer-nav .home-menu .active').removeClass('d-none');
            $('.mobile-footer-nav .home-menu .inactive').addClass('d-none');
            $('.mobile-footer-nav .ebooks-menu, .mobile-footer-nav .institute-menu, .mobile-footer-nav .groups-menu').addClass('common-footer-nav');
        } else if(url.indexOf("/ebooks") != -1 || url.indexOf("/books/store") != -1){
            $('.mobile-footer-nav .ebooks-menu').addClass('active-menu');
            $('.mobile-footer-nav .ebooks-menu .active').removeClass('d-none');
            $('.mobile-footer-nav .ebooks-menu .inactive').addClass('d-none');
            $('.mobile-footer-nav .home-menu, .mobile-footer-nav .institute-menu, .mobile-footer-nav .groups-menu').addClass('common-footer-nav');
        }
            // else if(url.indexOf("/groups/index") != -1){
            //     $('.mobile-footer-nav .groups-menu').addClass('active-menu');
            //     $('.mobile-footer-nav .groups-menu .active').removeClass('d-none');
            //     $('.mobile-footer-nav .groups-menu .inactive').addClass('d-none');
            //     $('.mobile-footer-nav .home-menu, .mobile-footer-nav .institute-menu, .mobile-footer-nav .ebooks-menu').addClass('common-footer-nav');
        // }
        else if(url.indexOf("/instituteHome") != -1 || url.indexOf("/institute/home") != -1){
            $('.mobile-footer-nav .institute-menu').addClass('active-menu');
            $('.mobile-footer-nav .institute-menu .active').removeClass('d-none');
            $('.mobile-footer-nav .institute-menu .inactive').addClass('d-none');
            $('.mobile-footer-nav .home-menu, .mobile-footer-nav .ebooks-menu, .mobile-footer-nav .groups-menu').addClass('common-footer-nav');
        } else {
            $('.mobile-footer-nav a').addClass('common-footer-nav');
        }

    });

</script>

<script>

    var serverPath= "${request.contextPath}";

    function showregister(registerType){
        $("#registerModal").modal("show");
        document.getElementById('forgotPassword').style.display = 'none';
        document.getElementById('tandc').style.display = 'none';

        if("login"==registerType) {
            document.getElementById('signup').style.display = 'none';
            document.getElementById('login').style.display = 'block';
            document.getElementById('loginFailed').style.display = 'none';
        }
        else{
            document.getElementById('login').style.display = 'none';
            document.getElementById('signup').style.display = 'block';
        }

    }


    var syllabusType="${syllabusType}";

    var country ="${country}";
    var noOfNotifications=0;
    var messagetype="message";
    var logOffMode=true;
    var onceLoggedOut=false;
    function getTopicsMap(mode){

        <g:remoteFunction controller="funlearn" action="topicsMap"  onSuccess='initializeDataIndex(data);'
                params="'country='+country+'&mode='+mode" />
    }
    <%if("true".equals(showDiscover)||"true".equals(homeDiscover)){%>
    getTopicsMap('topic');
    <%}%>


    function getLocalDate(dateStr){
        var newDateStr = dateStr.substr(0,dateStr.length-18)+" "+dateStr.substr(dateStr.length-5);
        return newDateStr;
    }

    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }

    function setSelectedValue(selectObj, valueToSet) {
        for (var i = 0; i < selectObj.options.length; i++) {
            if (selectObj.options[i].text.replace(/\W+/g, '') == valueToSet.replace(/\W+/g, '')) {
                selectObj.options[i].selected = true;
                return;
            }
        }
    }

    function updateQuizPoints(quizId,correctAnswers) {
        <g:remoteFunction controller="funlearn" action="addQuizPoints"
			params="'quizId='+quizId+'&correctAnswers='+correctAnswers"></g:remoteFunction>
    }

</script>

<script>
    window.addEventListener('keydown', function(event) {
        if (event.keyCode === 80 && (event.ctrlKey || event.metaKey) && !event.altKey && (!event.shiftKey || window.chrome || window.opera)) {
            event.preventDefault();
            if (event.stopImmediatePropagation) {
                event.stopImmediatePropagation();
            } else {
                event.stopPropagation();
            }
            return;
        }
    }, true);
    $('#quizQuestionSection').bind('copy paste', function(e) {
        e.preventDefault();
    });
</script>



<script>
    function visitPage(){
        var userAgent = navigator.userAgent || navigator.vendor || window.opera;
        var actionMode="test";
        if (/iPad|iPhone|iPod/.test(userAgent)) {
            window.location="https://apps.apple.com/in/app/wonderslate/id1438381878";
        }
        else{
            window.location="https://play.google.com/store/apps/details?id=com.wonderslate.wonderpublish&hl=en&gl=US";
        }

        <g:remoteFunction controller="log" action="smartbannerdltadd"  params="'actionMode='+actionMode" />
    }
</script>
<script>

    <sec:ifNotLoggedIn>
    $(window).on('load',function() {
        var userCookie = getCookie("userCookie");
        var forceLogin = false;
        <%if(forceLogin!=null&&"true".equals(forceLogin)){%>
        forceLogin=true;
        <%}%>


    });
    </sec:ifNotLoggedIn>
    <%if(session.getAttribute('instituteUrlName')==null){%>
    deleteCookie("instituteUrlName");
    <%}%>


    <%if(session["userdetails"]==null||session["userdetails"].username.indexOf("1_cookie_")==0){
    if(!"true".equals(session["sessionCountUpdated"])){%>
    <g:remoteFunction controller="usermanagement" action="updateSessionCount"  onSuccess='userSessionCountUpdated(data);'/>
    <% } %>

    function userSessionCountUpdated(data){
        var numberOfSessions = parseInt(data.sessionCount);
        if(numberOfSessions > 3){
            //check for forcing signins
            signupModal();

        }
    }
    <% }%>

    if($.browser.platform == "win") {
        $("html").addClass("windows");
    }

    if($.browser.name == "chrome") {
        $("html").addClass("chrome");
    } else if($.browser.name == "mozilla") {
        $("html").addClass("mozilla");
    } else if($.browser.name == "safari") {
        $("html").addClass("safari");
        if($(window).width() < 767){
            $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
                $(this).css('background','#eee');
                $('.btn-primary-modifier').css('background-color','#A81175 !important');
                $('.btn-success-modifier').css('background-color','#27AE60 !important');
                $('.btn-secondary-modifier').css('background-color','#8E8E8E !important');
                $('.btn-danger-modifier').css('background-color','#FF4B33 !important');
                $('.btn-warning-modifier').css('background-color','#F79420 !important');
            });
            document.addEventListener('touchmove', function (event) {
                if (event.scale !== 1) { event.preventDefault(); }
            }, { passive: false });
        }
    } else {
        $("html").addClass("others");
        if($(window).width() < 767){
            $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
                $(this).css('background','#eee');
            });
            document.addEventListener('touchmove', function (event) {
                if (event.scale !== 1) { event.preventDefault(); }
            }, { passive: false });
        }
    }

</script>


<script>
    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
    function openMenu() {
        $('.right-menu').css('display','flex');
    }
    function closeMenu() {
        $('.right-menu').hide();
    }

    // Only for ebook page top navigation
    <%if(session["userInstitutes"]!=null&&session["userInstitutes"].size()>0){%>
    $("header.mdl-layout__header").addClass('show-institute-menu');
    <%}%>

    $(document).ready(function(){
        // Only for form element with new styles
        $(".form-control-modifier").on('focus', function (){
            $(this).parent().addClass('is-focused');
        }).on('blur', function () {
            $(this).parent().removeClass('is-focused');
        });

        <%if(session["sessionCount"]!=null&&session["sessionCount"].intValue()>3&&session["userdetails"]!=null&&session["userdetails"].username.indexOf("1_cookie_")==0){%>
        signupModal();
        <%}%>
    });

    if (window.performance && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD) {
        $('.loading-icon').addClass('hidden');
    }

    <%if(session["userdetails"]!=null&&session["userdetails"].username.indexOf("1_cookie_")==0){%>
    function addNotification() {
        $(".mdl-js-layout #top-navbar").before($("<div class='col-12 guest-user-alert'><span>You are in guest mode <a href='javascript:signupModal()'>Sign-up</a></span></div>"));
    }
    addNotification();
    $("body").addClass('guest-mode');
    <%}%>
</script>

<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<script>
    <%if(params.tokenId==null&&session["appType"]==null){%>
    $(window).on('load',function() {
        $('.loading-icon').removeClass('hidden');
    });
    <%}%>
    $('#search-book-header').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }

                    }));
                    searchDataValues = data.searchValues;
                    searchStringValues = data.searchList;
                }
            })
        },
        afterSelect: function(){
            submitSearchHeader();
        }
    });
    $(document).ready(function(){
        $('#search-btn-header').attr('disabled',true);
        $('#search-book-header').keyup(function(){
            if($(this).val().length !=0)
                $('#search-btn-header').attr('disabled', false);
            else
                $('#search-btn-header').attr('disabled',true);
        })
    });
    $(document).on("keypress", "#search-book-header", function(e) {
        if( e.which === 32 && this.value === '' ) {
            return false;
        } else if (e.which == 13) {
            if (e.keyCode == 13) {
                // to prevent submitting of the page
                e.preventDefault();
                submitSearchHeader();
            }
        }
    });

    function submitSearchHeader(){
        var searchString = document.getElementById("search-book-header").value;

        if(searchString.length == 0) {

        } else {
            $('.navbar-search ul.typeahead').css('display','none');
            $('.loading-icon').removeClass('hidden');
            if (typeof submitSearchTop === 'function') {
                submitSearchTop();
                $('html, body').animate({scrollTop: $('#filters').offset().top - 77 }, 'slow');
            }else{
                if(searchStringValues!=null&&searchStringValues!=""){

                    //use the filters
                    for(var i=0;i<searchStringValues.length;i++){
                        if(searchStringValues[i]==searchString){
                            var searchData = searchDataValues[i];
                            var requestList = "";
                            //callRemoteSearch = populateFromFilters(searchDataValues[i]);

                            if(searchData.hasOwnProperty("publisherId")){
                                requestList += "&publisherId="+searchData.publisherId;
                            }
                            if(searchData.hasOwnProperty("level")){
                                requestList += "&level="+searchData.level;
                            }
                            if(searchData.hasOwnProperty("syllabus")){
                                requestList += "&syllabus="+searchData.syllabus;
                            }
                            if(searchData.hasOwnProperty("grade")){
                                requestList += "&grade="+searchData.grade;
                            }
                            if(searchData.hasOwnProperty("subject")){
                                requestList += "&subject="+searchData.subject;
                            }
                            break
                        }
                    }
                }
                window.location.href="/<%= session["entryController"] %>/ebooks?searchString="+encodeURIComponent(searchString)+requestList;
            }
        }
    }

    var activeCategoriesSyllabus = [];
    if("${session["activeCategoriesSyllabus_"+session["siteId"]]}" != "") {
        activeCategoriesSyllabus = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
    }

    var displayLevel = "";
    var categoryChanged = true;
    var categorySyllabusHtml="";
    for (var i = 0; i < activeCategoriesSyllabus.length; i++) {
        if (activeCategoriesSyllabus[i].level != displayLevel) {
            categoryChanged = true;
            displayLevel = activeCategoriesSyllabus[i].level;
            //closing tag logic
            if (displayLevel != "") {
                categorySyllabusHtml += " </ul>\n" +
                    "  </div>";

            }
            categorySyllabusHtml += "<div class=\"card border-0 mega_menu__links\">\n" +
                "          <ul class=\"list-inline p-0\">\n" +
                "          <h5>" + activeCategoriesSyllabus[i].level + "</h5>";
        }
        if ("Medical Entrances" == activeCategoriesSyllabus[i].level && "NEET" == activeCategoriesSyllabus[i].syllabus) continue;
        categorySyllabusHtml += "<li><a href='/ebooks?level=" + replaceAll(activeCategoriesSyllabus[i].level.replace('&', '~'),' ','-') + "&syllabus=" + replaceAll(activeCategoriesSyllabus[i].syllabus.replace('&', '~'),' ','-') + "&grade=null'>" + activeCategoriesSyllabus[i].syllabus + "</a></li>";
    }
    categorySyllabusHtml +=" </ul>\n" +
        "  </div>";

    document.getElementById("topMenuItems").innerHTML=categorySyllabusHtml;


    function getFeaturedCategoriesAndPublishers() {
        <g:remoteFunction controller="wsshop" action="getFeaturedPublishersAndCategories"  onSuccess='getCategoriesAndPublishers(data);' />
    }

    var trendingCategories="";
    var trendingPublishers = "";
    function getCategoriesAndPublishers(data) {
        var featuredCategories = JSON.parse(data.featuredCategories);
     var featuredPublishers = JSON.parse(data.featuredPublishers);
        trendingCategories += "<ul>";
     for (var i = 0; i < featuredCategories.length; i++){
         trendingCategories += "<li><a href='/ebooks?level=" +replaceAll(featuredCategories[i].level,' ','-') + "&syllabus=" + replaceAll(featuredCategories[i].syllabus,' ','-') + "&grade=null'>" + featuredCategories[i].syllabus + "</a></li>";

     }
        trendingCategories+=" </ul>";
        document.getElementById("trendingCategories").innerHTML=trendingCategories;

        trendingPublishers += "<ul>";
     for (var i = 0; i < featuredPublishers.length; i++){
         var publisherName = featuredPublishers[i].publisher;
             publisherName = publisherName.split(" ").join("-");
             trendingPublishers += "<li><a href='/ebooks?publisher=" + publisherName + "'>" + featuredPublishers[i].publisher + "</a></li>\n";
     }
            trendingPublishers+="</ul>";
     document.getElementById("trendingPublishers").innerHTML=trendingPublishers;
     $('.loading-icon').addClass('hidden');
    }

    if ($(window).width() >= 992) {
        $(".parallax").parallaxie({
            //speed value btw (-1 to 1)
            speed: 0.55,
            offset: 0,
        });
        $(".parallax.parallax-slow").parallaxie({
            speed: 0.31,
        });
    }

    // Initiate animations
    AOS.init();

    // Shopping cart functions
    var cartItemsCount;
    function getCartCount() {
        <g:remoteFunction controller="wsshop" action="getUsersCartCount" onSuccess='showCartCount(data);'/>
    }

    function showCartCount(data) {
        cartItemsCount = data.userCartCount;
        $("#navbarCartCount").html(cartItemsCount);
    }

    function addToCart(bookId) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsshop" action="addBookToCart" params="'bookId='+bookId" onSuccess='booksAddedInCart(bookId, data);'/>
        $("#addToCartBtn"+bookId).html("Adding..");
    }

    function booksAddedInCart(id, data) {
        $('.loading-icon').addClass('hidden');
        if (data.status == "OK") {
            $("#bookCartModal").modal('show');
            $("#cartModalText").html("This eBook has been added to your cart.");
            $("#addedIconAnimation").show();
            $("#existIconAnimation").hide();
            var ct = Number($('#navbarCartCount').text()) + 1;
            $('#navbarCartCount').text(ct);
            $("#cartModalBtns").removeClass('d-none').addClass('d-flex');
            $("#cartModalLibBtn").removeClass('d-flex').addClass('d-none');
            $("#addToCartBtn"+id).html("Add to Cart");
        } else if (data.status == "Already exist") {
            $("#bookCartModal").modal('show');
            $("#cartModalText").html("This eBook is already present in your cart.");
            $("#existIconAnimation").show();
            $("#addedIconAnimation").hide();
            $("#cartModalBtns").removeClass('d-none').addClass('d-flex');
            $("#cartModalLibBtn").removeClass('d-flex').addClass('d-none');
            $("#addToCartBtn"+id).html("Add to Cart");
        } else {
            $("#bookCartModal").modal('show');
            $("#cartModalText").html("This eBook is already purchased.");
            $("#existIconAnimation").show();
            $("#addedIconAnimation").hide();
            $("#cartModalBtns").removeClass('d-flex').addClass('d-none');
            $("#cartModalLibBtn").removeClass('d-none').addClass('d-flex');
            $("#addToCartBtn"+id).html("Add to Cart");
        }
    }

    function goCartPage() {
        window.open('/books/shoppingCart', '_blank');
    }

    function goLibraryPage() {
        window.open('/wsLibrary/myLibrary?mode=mybooks', '_blank');
    }

    // For book detail and preview pages
    function addToCartFromDtl(bookId) {
        if(userLoggedIn){
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="wsshop" action="addBookToCart" params="'bookId='+bookId" onSuccess='redirectToCartPage(bookId, data);'/>
            $("#buyNow").html("Adding..");
        } else {
            loginOpen();
        }
    }

    function redirectToCartPage(id, data) {
        $("#buyNow").html("Add to Cart");
        if (data.status == "OK") {
            window.location.href = "/books/shoppingCart";
            var ct = Number($('#navbarCartCount').text()) + 1;
            $('#navbarCartCount').text(ct);
        } if (data.status == "Already exist") {
            window.location.href = "/books/shoppingCart";
        }
    }

    <%if(params.tokenId==null&&session["appType"]==null){%>
    window.addEventListener( "pageshow", function () {
        $(".loading-icon").addClass("hidden");
    }, false);
    <%}%>

    if(userLoggedIn){
        getCartCount();
    }
    getFeaturedCategoriesAndPublishers();
</script>
