<g:render template="/${session['entryController']}/navheader_new"></g:render>
<div class="custom_container_new">
    <h4 class="text-center my-4">MCQs upload manager</h4>
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 20px auto; float: none; padding: 15px;">
            <div id="content-books">
            <textarea id="questionsTextArea" rows="10" cols="50" placeholder="Paste your questions here..." style="display: none">${extractedContent}</textarea>
            <div style="display: none">
                Format text: <input type="checkbox" id="formatText" value="format" checked>
            </div>

            <div id="status" class="my-3"></div>
            <div id="formattedQuestions" style="display: none; overflow-x: auto;">Separating questions ....</div>
            <div id="viewMCQButton" style="display: none" class="mt-3">   <button class="btn btn-primary" onclick="viewMCQs()">View MCQs</button></div>

            </div>
        </div>
        </div>
</div>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
        <div id="loadingText" style="color: #ff2a21">Extracting questions ....</div>
    </div>

</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>

<script>
    $('.loading-icon').removeClass('hidden');
    const formattedQuestionsArray = [];
    var jsonData = [];
    var rowData = {};
    var resId = ${resId};
    var rowIndex=0;
    function formatQuestions() {
        jsonData =[];
        const questionsTextArea = document.getElementById("questionsTextArea");
        const formattedQuestions = document.getElementById("formattedQuestions");
        const statusDiv = document.getElementById("status");

        const inputText = questionsTextArea.value;
        const questionRegex = /(Q\d+)\.\s(.*?)(A\.)(.*?)(B\.)(.*?)(C\.)(.*?)(D\.)(.*?)(Answer:)(.*?)(Explanation:)(.*?)(Marks:)(.*?)(Negative Marking:)(.{5})/gs
        const justNumber = /\d+(\.\d+)?/

        // Initialize error tracking
        let parsingErrors = [];
        let invalidQuestions = [];

        let match;
        formattedQuestionsArray.length = 0; // Clear the array before formatting

        // First pass - check if we can extract any questions at all
        const testMatch = questionRegex.exec(inputText);
        if (testMatch === null) {
            statusDiv.innerHTML = "<div class='alert alert-danger'>No questions could be extracted. Please ensure your questions follow the required format:<br>" +
                "<pre>Q1. [Question text]<br>A. [Option A]<br>B. [Option B]<br>C. [Option C]<br>D. [Option D]<br>" +
                "Answer: [Correct answer]<br>Explanation: [Explanation text]<br>Marks: [Marks value]<br>Negative Marking: [Value]</pre></div>";

            // Hide the loading icon
            $('.loading-icon').addClass('hidden');
            return;
        }

        // Reset regex index
        questionRegex.lastIndex = 0;

        while ((match = questionRegex.exec(inputText)) !== null) {
            try {
                const questionNumber = match[1];
                const questionText = match[2];
                const options = [match[4], match[6], match[8], match[10],``];

                // Validate each component
                let questionErrors = [];

                if (!questionText || questionText.trim() === "") {
                    questionErrors.push("Question text is missing");
                }

                for (let i = 0; i < 4; i++) {
                    if (!options[i] || options[i].trim() === "") {
                        questionErrors.push("Option " + String.fromCharCode(65 + i) + " is missing");
                    }
                }

                const correctAnswer = match[12];
                if (!correctAnswer || correctAnswer.trim() === "") {
                    questionErrors.push("Correct answer is missing");
                }

                const answerExplanation = match[14];
                if (!answerExplanation || answerExplanation.trim() === "") {
                    questionErrors.push("Explanation is missing");
                }

                let marks, negativeMarking;
                try {
                    const marksMatch = justNumber.exec(match[16]);
                    marks = marksMatch ? marksMatch[0] : null;
                    if (!marks) questionErrors.push("Marks value is invalid or missing");
                } catch (e) {
                    questionErrors.push("Marks value is invalid or missing");
                    marks = "0";
                }

                try {
                    const negativeMarkingMatch = justNumber.exec(match[18]);
                    negativeMarking = negativeMarkingMatch ? negativeMarkingMatch[0] : null;
                    if (!negativeMarking) questionErrors.push("Negative marking value is invalid or missing");
                } catch (e) {
                    questionErrors.push("Negative marking value is invalid or missing");
                    negativeMarking = "0";
                }

                // Store question with any errors
                formattedQuestionsArray.push({
                    questionNumber,
                    questionText,
                    options,
                    correctAnswer,
                    answerExplanation,
                    marks: marks || "0",
                    negativeMarking: negativeMarking || "0",
                    errors: questionErrors
                });

                if (questionErrors.length > 0) {
                    invalidQuestions.push(questionNumber);
                }
            } catch (e) {
                console.error("Error parsing question:", e);
                parsingErrors.push("Failed to parse question starting with: " + match[0].substring(0, 50) + "...");
            }
        }

        var currentQuestionNumber= Number('${startQuestionNo}');
        var endQuestionNumber = Number('${endQuestionNo}')
        var noOfWrongquestions =0;
        var wrongQuestions="";
        var bgColor="white";
        var regexPattern = /\[IAS\s\d{4}\]/g;
        var option5=""
        var option5Regex = /(.+)(E\.)(.+)/gs;
        var wrongQuestions="";
        let str = "abc123xyz456";
        let numbers = str.match(/\d+/);


        if (formattedQuestionsArray.length > 0) {
            // Display the formatted questions in a table
            let tableHtml = '<table class="table table-bordered" style="min-width: 1200px;">';
            tableHtml += '<tr><th style="width: 50px;">No</th><th style="width: 20%;">Question</th><th style="width: 10%;">Option A</th><th style="width: 10%;">Option B</th><th style="width: 10%;">Option C</th><th style="width: 10%;">Option D</th><th style="width: 10%;">Option E</th><th style="width: 5%;">Answer</th><th style="width: 10%;">Explanation</th><th style="width: 15%;">Status</th></tr>';
            formattedQuestionsArray.forEach((formattedQuestion) => {
                // Set background color based on errors
                bgColor = formattedQuestion.errors && formattedQuestion.errors.length > 0 ? "#ffeeee" : "white";

                var questionNumMatch = formattedQuestion.questionNumber ? formattedQuestion.questionNumber.match(/\d+/) : null;
                const extractedNum = questionNumMatch ? Number(questionNumMatch[0]) : null;
                console.log(currentQuestionNumber+" "+(extractedNum || 'unknown'));
                if(extractedNum !== null && Number(extractedNum) !== Number(currentQuestionNumber))
                {
                    noOfWrongquestions++;
                    wrongQuestions +=""+(Number(currentQuestionNumber))+",";
                }

                //logic to see if option E is present and separate them
                formattedQuestion.options[4]="";
                while ((match = option5Regex.exec(formattedQuestion.options[3])) !== null) {
                    formattedQuestion.options[3]=match[1];
                    formattedQuestion.options[4] = match[3];
                }

                // Create status cell content
                let statusCell = "";
                if (formattedQuestion.errors && formattedQuestion.errors.length > 0) {
                    statusCell = "<div class='text-danger' style='min-width: 150px;'><strong>Issues:</strong><ul style='padding-left: 20px;'>";
                    formattedQuestion.errors.forEach(error => {
                        statusCell += "<li>" + error + "</li>";
                    });
                    statusCell += "</ul></div>";
                } else {
                    statusCell = "<span class='text-success'>✓ Valid</span>";
                }

                // Safe access to properties with null checks
                const safeOption3 = formattedQuestion.options[3] ? formattedQuestion.options[3].replace(regexPattern, '') : "";
                const safeOption4 = formattedQuestion.options[4] ? formattedQuestion.options[4].replace(regexPattern, '') : "";

                tableHtml += "<tr bgcolor='"+bgColor+"'>\n     " +
                    "               <td>"+(formattedQuestion.questionNumber || "")+"</td>\n   " +
                    "                     <td>"+(formattedQuestion.questionText || "")+"</td>\n           " +
                    "             <td>"+(formattedQuestion.options[0] || "")+"</td>\n                " +
                    "        <td>"+(formattedQuestion.options[1] || "")+"</td>\n           " +
                    "             <td>"+(formattedQuestion.options[2] || "")+"</td>\n          " +
                    "              <td>"+safeOption3+"</td>\n           " +
                    "              <td>"+safeOption4+"</td>\n           " +
                    "             <td>"+(formattedQuestion.correctAnswer || "")+"</td>\n           " +
                    "             <td>"+(formattedQuestion.answerExplanation || "")+"</td>\n           " +
                    "             <td>"+statusCell+"</td>\n  " +
                    "                  </tr>";
               questionNumMatch = formattedQuestion.questionNumber ? formattedQuestion.questionNumber.match(/\d+/) : null;
              currentQuestionNumber = questionNumMatch ? Number(questionNumMatch[0]) + 1 : currentQuestionNumber + 1;

                var questionString = formattedQuestion.questionText || "";
                if (questionString) {
                    questionString = questionString.replace("(A)","<br>(A)");
                    questionString = questionString.replace("(B)","<br>(B)");
                    questionString = questionString.replace("(C)","<br>(C)");
                    questionString = questionString.replace("(D)","<br>(D)");
                    questionString = questionString.replace("(E)","<br>(E)");
                }
                rowData = {
                    "Question": questionString,
                    "option1": formattedQuestion.options[0] || "",
                    "option2": formattedQuestion.options[1] || "",
                    "option3": formattedQuestion.options[2] || "",
                    "option4": formattedQuestion.options[3] || "",
                    "option5": formattedQuestion.options[4] || "",
                    "correctAnswer": formattedQuestion.correctAnswer ? formattedQuestion.correctAnswer.replace("null","").replace("<br>","").trim() : "",
                    "answerDescription": formattedQuestion.answerExplanation || "",
                    "marks": formattedQuestion.marks || "0",
                    "negativeMarks": formattedQuestion.negativeMarking || "0",
                    "ignoreFormula": "true"
                }
                jsonData.push(rowData);
            });
            if(currentQuestionNumber<=endQuestionNumber){
                noOfWrongquestions++;
                wrongQuestions +=""+endQuestionNumber;
            }
            tableHtml += '</table>';
            formattedQuestions.innerHTML = tableHtml;
            // Count questions with validation errors
            const questionsWithErrors = formattedQuestionsArray.filter(q => q.errors && q.errors.length > 0).length;

            // Create summary message
            let statusMessage = "";
            if (questionsWithErrors > 0 || noOfWrongquestions > 0) {
                statusMessage = "<div class='alert alert-warning'><strong>Issues Found:</strong><ul>";

                if (questionsWithErrors > 0) {
                    statusMessage += "<li>" + questionsWithErrors + " question(s) have formatting or content issues (highlighted in red)</li>";
                }

                if (noOfWrongquestions > 0) {
                    statusMessage += "<li>Problem with question numbering sequence: " + wrongQuestions + "</li>";
                }

                statusMessage += "</ul>Please fix these issues and try again.</div>";
                statusMessage += "<button id='toggleQuestionsBtn' class='btn btn-info mt-2 mb-2'>Hide Questions Table</button>";
                document.getElementById("status").innerHTML = statusMessage;

                // Show the questions table when there are errors
                document.getElementById("formattedQuestions").style.display = "block";

                // Add click handler for the toggle button
                document.getElementById("toggleQuestionsBtn").onclick = function() {
                    const questionsTable = document.getElementById("formattedQuestions");
                    if (questionsTable.style.display === "none") {
                        questionsTable.style.display = "block";
                        this.innerHTML = "Hide Questions Table";
                    } else {
                        questionsTable.style.display = "none";
                        this.innerHTML = "Show Questions Table";
                    }
                };

                // Hide the loading icon
                $('.loading-icon').addClass('hidden');
            } else {
                document.getElementById("status").innerHTML = "<div class='alert alert-success'>All questions created correctly</div>";
                uploadToServer();
            }
        } else {
            console.log("not even one")
            document.getElementById("status").innerHTML = "<div class='alert alert-danger'><strong>No questions found!</strong><br>Please ensure your questions follow the required format:<br>" +
                "<pre>Q1. [Question text]<br>A. [Option A]<br>B. [Option B]<br>C. [Option C]<br>D. [Option D]<br>" +
                "Answer: [Correct answer]<br>Explanation: [Explanation text]<br>Marks: [Marks value]<br>Negative Marking: [Value]</pre></div>";

            // Hide the loading icon
            $('.loading-icon').addClass('hidden');
        }
    }

    function uploadToServer(){
        console.log("number of rows="+jsonData.length);
        rowIndex=0
        callServerAPI(rowIndex)

    }

    function callServerAPIInternal(rowData) {
       // Make an AJAX request or use fetch() to call your server API
        // Adjust the URL and method as per your server implementation
        var url = '/excel/processMCQ';
        // Get a reference to the radio buttons using their name
        // Get a reference to the radio buttons using their name
        rowData["chapterId"]="${params.chapterId}";
        rowData["resId"] = resId;
        return fetch(url, {
            method: 'POST',
            body: JSON.stringify(rowData),
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(response => response.json());
    }

    function callServerAPI(rowIndex) {
        document.getElementById("status").innerHTML="<div class='alert alert-info'>Adding question number "+(rowIndex+1)+"</div>";
        document.getElementById("loadingText").innerHTML="Adding question number "+(rowIndex+1);
        // Stop the process if the rowIndex exceeds the jsonData length
        if (rowIndex >= jsonData.length) {
            document.getElementById("status").innerHTML="<div class='alert alert-success'>All questions added successfully. Total questions: "+rowIndex+"</div>";
            $("#viewMCQButton").show();
        }
        // Call the server API with the row data
        rowData = jsonData[rowIndex]
        callServerAPIInternal(rowData)
            .then(response => {
                // Handle the server API response as needed
                // Update the row status based on the API response
                resId = response.additionalInformation.resId;

                callServerAPI(rowIndex + 1);
            })
            .catch(error => {
                // Handle any errors that occur during the API call
                console.error('Error:', error);

                callServerAPI(rowIndex + 1);
            });
    }

    function exportToExcel() {
        if (formattedQuestionsArray.length > 0) {
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet([
                ["Question", "option1", "option2", "option3", "option4","option5", "correctAnswer", "answerDescription"]
            ]);
            var questionText="";
            var answerDescription="";
            formattedQuestionsArray.forEach((formattedQuestion) => {
                if(formattedQuestion.questionText.indexOf("math")==-1&&formattedQuestion.questionText.indexOf("hline")==-1
                    &&formattedQuestion.questionText.indexOf("begin")==-1&&formattedQuestion.questionText.indexOf("times")==-1&&document.getElementById("formatText").checked)
                {
                    questionText = formattedQuestion.questionText.replace(/\n/g, "<br>");
                    console.log("question text="+questionText);
                    if(questionText.indexOf("<br>")==-1){
                        questionText = questionText.replace(/(\d+)\./g, '<br>$1.');
                    }

                }
                else {

                    if(document.getElementById("formatText").checked)
                        questionText = formattedQuestion.questionText;
                    else
                    {
                        questionText = formattedQuestion.questionText.replace(/\n/g, " ");
                        questionText = questionText.replace(/(\d+)\./g, '<br>$1.');
                    }
                }

                if(formattedQuestion.answerExplanation.indexOf("math")==-1&&formattedQuestion.answerExplanation.indexOf("hline")==-1
                    &&formattedQuestion.answerExplanation.indexOf("begin")==-1&&formattedQuestion.answerExplanation.indexOf("times")==-1&&document.getElementById("formatText").checked)
                {
                    answerDescription = formattedQuestion.answerExplanation.replace(/\n/g, "<br>");
                    if(answerDescription.indexOf("<br>")==-1){
                        answerDescription = answerDescription.replace(/(\d+)\./g, '<br>$1.');
                    }
                }
                else {
                    if(document.getElementById("formatText").checked)
                        answerDescription = formattedQuestion.answerExplanation;
                    else {
                        answerDescription = formattedQuestion.answerExplanation.replace(/\n/g, " ");
                        answerDescription = answerDescription.replace(/(\d+)\./g, '<br>$1.');
                    }
                }

                XLSX.utils.sheet_add_aoa(ws, [
                    [questionText, ...formattedQuestion.options, formattedQuestion.correctAnswer, answerDescription]
                ], { origin: -1 });
            });

            XLSX.utils.book_append_sheet(wb, ws, "Questions");
            XLSX.writeFile(wb, "formatted_questions.xlsx");
        }
    }

    <%if(extractedContent!=null){%>

    formatQuestions();
    <%}%>

    function viewMCQs(){
        window.open("/resources/editQuiz?page=notes&resId="+resId+"&chapterId=${params.chapterId}&bookId=${params.bookId}&resourceType=Multiple Choice Questions&useType=quiz&mode=edit");
    }
</script>
</body>
</html>
