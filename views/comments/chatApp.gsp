
<g:render template="/eutkarsh/navheader_new"></g:render>
<style>
    html{
        margin: 0;
    }
    .btn-blocks{
        background: transparent;
        border: none;
        outline: 0;
        margin-top: 0.5rem;
    }
    .btn-blocks:focus{
        outline: 0;
    }
    .btn-blocks:active{
        outline: 0;
    }
    .username{
       padding-top: 0.5rem;
    }
    #inputFocus:focus{
        outline: none !important;
        border:none;
        box-shadow: none;
    }
    #webChat{
        display: none!important;
    }
    .dropdown-menu .media-body{
        background: transparent;
    }
</style>
<body>
<base href="/comments/chatApp">

<script>
    if(global == undefined){
        var global = window;
    }
</script>
<app-root></app-root>
<div class="toast shadow-lg " id="myToast"   style="position: fixed; top: 0; right: 0;opacity: 1;display: none;z-index: 999;">
    <div class="toast-header">
        <strong class="mr-auto"><i class="fa fa-grav"></i> New message!</strong>
%{--        <small>11 mins ago</small>--}%
        <button type="button" class="ml-2 mb-1 close" data-dismiss="toast">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div onclick="autoScrollClicked()" style="cursor: pointer" class="toast-body">
        <div>Click Here to scroll to new messages.</div>
    </div>
</div>
<asset:javascript src="runtime.js"/>
<asset:javascript src="polyfills-es5.js"/>
<asset:javascript src="polyfills.js"/>
<asset:javascript src="styles.js"/>
<asset:javascript src="scripts.js"/>
<asset:javascript src="vendor.js"/>
<asset:javascript src="main.js"/>
<asset:javascript src="moment.min.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
<script>
// $(".message-wrapper").on('DOMSubtreeModified',function () {
//     var myDiv = $('.media').last();
//     $('.message-wrapper').animate({
//         scrollTop:$($('#demo')).offset().top
//     },100)
// });

function getAllUserComments() {
    var resId = localStorage.getItem('resId');
    <g:remoteFunction controller="comments" action="getAllUserComments" onSuccess='displayComments(data)'
        params="'siteId=1&resId='+resId"/>
    <g:remoteFunction controller="comments" action="getResourcedetailsByResId" onSuccess='displayResName(data)'
        params="'siteId=1&resId='+resId"/>
}
function displayComments(data){
    var commentsArr = data['comments'];
    if(commentsArr != undefined){

        for(var i= commentsArr.length-1 ; i >= 0 ; i-- ){
            if(document.getElementById("inputFocus") == null || document.getElementById("inputFocus") == undefined){
                var str = '<input id="inputFocus" style="width: 0px;height: 0px;border: none">';
                $(str).insertAfter($('.media').last());
            }
       try{
           if(commentsArr[i]['adminUser'] == "true"){
            var str = '<div id="'+commentsArr[i]['messageId']+'" class="media mt-4">\n' +
                '    <img src="/assets/eutkarsh/utkarsh-logo.svg" class="mr-3" alt="...">\n' +
                "<div class=\"dropdown\">\n" +
                "  <button type=\"button\" class=\"btn-blocks\" data-toggle=\"dropdown\">\n" +
                "<i class=\"material-icons\">\n" +
                "more_vert\n" +
                "</i>"+
                "  </button>\n" +
                "  <div class=\"dropdown-menu\">\n" +
                // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','block','"+commentsArr[i]['messageId']+"')\" >Block user</a>\n" +
                // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','unblock','"+commentsArr[i]['messageId']+"')\" >Unblock user</a>\n" +
                "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','delete','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Delete comment</a>\n" +
                "  </div>\n" +
                "</div>"+
                '    <div  class="media-body shadow-sm">\n' +
                '\n' + decodeURI(commentsArr[i]['message']) +
                ' <p style="float: right"> '+moment.utc(commentsArr[i]['dateCreated']).local().format("YYYY-MM-DD h:mm a")+'</p> ' +
                '    </div>\n' +
                '  </div>';
            $(str).insertAfter($('.media').last());
        }else{
            var str = ' <div id="'+commentsArr[i]['messageId']+'" class="media mt-4">\n' +
                '    <div  class="media-body shadow-sm you">\n' +
                '\n'+ decodeURI(commentsArr[i]['message']) +
                ' <p style="float: right"> '+moment.utc(commentsArr[i]['dateCreated']).local().format("YYYY-MM-DD h:mm a")+'</p> ' +
                '    </div>\n' +
                    "<div class=\"dropdown\">\n" +
                "  <button type=\"button\" class=\"btn-blocks\" data-toggle=\"dropdown\">\n" +
                   "<i class=\"material-icons\">\n" +
                "more_vert\n" +
                "</i>"+
                "  </button>\n" +
                "  <div class=\"dropdown-menu\">\n" +
                "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','block','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Block user</a>\n" +
                "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','unblock','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Unblock user</a>\n" +
                "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','delete','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Delete comment</a>\n" +
                "  </div>\n" +
                "</div>"+
                '    <p class="username">' +
                '\n' + commentsArr[i]['sender'] +
                '</p>\n' +
                '  </div>';
            $(str).insertAfter($('.media').last());
        }
       }catch (e) {
           continue;
       }
        }
        $( "#inputFocus" ).focus();
    }
}
getAllUserComments();
function scrollToFun(){
    var myDiv = $('.media').last();
    $('.message-wrapper').animate({
        scrollTop:$($('#demo')).offset().top
    },100)
}

function userAction(userName,action,messageId,admin) {
    var url = "";
    if(action == 'block') url = "/comments/blockUserComments";
    else if(action == 'unblock') url = "/comments/unblockUserComments";
    else if(action == 'delete') url = "/comments/removeQuestion"
    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
        if (this.readyState == 4 && this.status == 200) {
            // document.getElementById("demo").innerHTML = this.responseText;
            if(action == 'delete'){
                if(admin == "true"){
                    var str = '' +
                        // '    <img src="/assets/eutkarsh/utkarsh-logo.svg" class="mr-3" alt="...">\n' +
                        // "<div class=\"dropdown\">\n" +
                        // "  <button type=\"button\" class=\"btn-blocks\" data-toggle=\"dropdown\">\n" +
                        // "<i class=\"material-icons\">\n" +
                        // "more_vert\n" +
                        // "</i>"+
                        // "  </button>\n" +
                        // "  <div class=\"dropdown-menu\">\n" +
                        // // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','block','"+commentsArr[i]['messageId']+"')\" >Block user</a>\n" +
                        // // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','unblock','"+commentsArr[i]['messageId']+"')\" >Unblock user</a>\n" +
                        // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','delete','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Delete Message</a>\n" +
                        // "  </div>\n" +
                        // "</div>"+
                        '    <div  style="color: gray">\n' +
                        '\n' + decodeURI("This comment is deleted") +
                        // ' <p style="float: right"> '+moment.utc(commentsArr[i]['dateCreated']).local().format("YYYY-MM-DD h:mm a")+'</p> ' +
                        '    </div>\n' +
                        '  ';
                    $('#'+messageId).html(str)
                }else{
                    var str = '' +
                        '    <div  style="color: gray">\n' +
                        '\n'+ decodeURI("This comment is deleted") +
                        // ' <p style="float: right"> '+moment.utc(commentsArr[i]['dateCreated']).local().format("YYYY-MM-DD h:mm a")+'</p> ' +
                        '    </div>\n' +
                        // "<div class=\"dropdown\">\n" +
                        // "  <button type=\"button\" class=\"btn-blocks\" data-toggle=\"dropdown\">\n" +
                        // "<i class=\"material-icons\">\n" +
                        // "more_vert\n" +
                        // "</i>"+
                        // "  </button>\n" +
                        // "  <div class=\"dropdown-menu\">\n" +
                        // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','block','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Block user</a>\n" +
                        // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','unblock','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Unblock user</a>\n" +
                        // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','delete','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Delete Message</a>\n" +
                        // "  </div>\n" +
                        // "</div>"+
                        // '    <p class="username">' +
                        // '\n' + commentsArr[i]['sender'] +
                        // '</p>\n' +
                        '';
                    $(str).insertAfter($('.media').last());
                }
            }
        }
    };
    xhttp.open("POST",url , true);
    xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhttp.send("username="+userName+"&messageId="+messageId);
}

function autoScrollClicked() {
    $( "#inputFocus" ).focus();
}

function displayResName(data) {
    if(data['data'] == null || data['data'] == 'null' || data['data'] == undefined || data['data'] == 'undefined' || data['data'] == ''){
        alert("Please enter valid RES-ID");
        var url = "/comments/chatLogin";
        window.location.href=url
    }
    $("#resName").text(data['data']['resourceName']);
}
</script>
</body>
<g:render template="/eutkarsh/footer_new"></g:render>
