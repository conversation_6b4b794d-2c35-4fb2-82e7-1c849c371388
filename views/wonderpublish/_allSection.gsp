<div class="modal fade" data-backdrop="static" data-keyboard="false" id="hostedVideoModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered text-center">
        <div class="modal-content">
            <div class="modal-body">
                <div class="col-md-12 text-right">
                    <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal"  aria-label="Close" id="videoClose"> <span aria-hidden="true">&times;</span></button>
                </div>
                <video width="320" height="240" controls id="hostedVideo">

                </video>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" data-backdrop="static" data-keyboard="false" id="hostedAudioModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered text-center">
        <div class="modal-content">
            <div class="modal-body">
                <div class="col-md-12 text-right">
                    <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal"  aria-label="Close" id="audioClose"> <span aria-hidden="true">&times;</span></button>
                </div>
                <audio width="320" height="240" controls id="hostedAudio" controlsList="nodownload">

                </audio>
            </div>
        </div>
    </div>
</div>
<script>
    var appInApp="${session["appInApp"]}";
    var chapterDetailsData;
    var chapterName;
    var chapterGrade;
    var chapterSyllabus;
    var chapterDesc;
    var chapterSubject;
    var chapterLevel;
    var firstReadId=-1;
    var expiryDate;
    var suggestedVideos;
    var masterChapterID;
    var sageFileOpened=false;
    function chapterDetailsRecieved(data){
        chapterDetailsData = data.results;
        seenResources = data.seenResouces;
        suggestedVideos = JSON.parse(data.suggestedVideos);
        expiryDate=data.expiryDate;
        masterChapterID = data.chapterId;
         if(expiryDate!=null) {
             document.getElementById('chapter-text').innerText ="";
             $('#chapter-text').hide();
            document.getElementById('expiry-date').innerText = "Expires on " + moment.utc(expiryDate,'YYYY/MM/DD').local().format("DD-MM-YYYY");
            $('#expiry-date').show();
         }
        populateAllSection(chapterDetailsData);

         if (sageOnly!="true"){
             var chpList =  document.querySelectorAll('.read-book-chapters-wrapper li');

             chpList.forEach(item=>{
                 item.classList.remove('orangeText');
             });
             document.getElementById("chapterName"+data.chapterId).classList.add('orangeText');
         }else{
             displayResource(data);
         }
    }
    function populateAllSection(cData){
        firstReadId=-1;
         chapterReadPresent=true;
        //remove the chapter name highlighting as it should be section name
        $("#chapterName"+previousChapterId).children('a').removeClass('orangeText');
        var cStr = "<div class=\"container\">\n" +

            "<div class=\"all-container\">";
        for (var i = 0; i < cData.length; ++i) {
            var showShare = false;
            var data = cData[i];
            if(i==0){
                chapterName = data.topicName;
                chapterGrade = data.grade;
                chapterSyllabus = data.syllabus;
                chapterSubject = data.subject;
                chapterLevel = data.level;
                chapterDesc = data.chapterDesc;
            }
            cStr +="<div class=\"container-wrapper\">\n" +
                "<div class='d-flex justify-content-between align-items-center'>";
            if(sageOnly=="true")
                cStr +="<p class='quiz-number'>" + (i + 1) + "</p>";

            if (showShare) {

                cStr += "<div class=\"dropdown\"> <a class=\"dropdown-toggle\" id=\"android-menu\" data-toggle=\"dropdown\" aria-haspopup=\"true\"" +
                    " aria-expanded=\"true\" style=\"cursor:pointer;\">" +

                    "<i class=\"material-icons\">more_vert</i></a> <div class=\"dropdown-menu\" aria-labelledby=\"android-menu\"> <a href=\"javascript:showShareOptions("+data.id+",false);\" class='dropdown-item'>Share</a> " +
                    "<a href=\"javascript:showShareOptions(" + data.id + ",true,'nonquiz');\" class='dropdown-item'>Share as assignment</a>"+

                    "</div> </div></div>";
            }else{
                cStr +="</div>";
            }
            cStr += "        <div class=\"media\">\n" ;
            if(data.resType=="Notes") {
                cStr +="            <i class=\"align-self-center material-icons\">\n" +
                    "                import_contacts\n" +
                    "            </i>\n";
            }
            else   if(data.resType=="Videos" || data.resType=="Reference Videos" ) {
                cStr +="<i class=\"align-self-center yt flaticon-youtube\"></i>";
            }
            else   if(data.resType=="Reference Web Links") {
                cStr +="            <i class=\"align-self-center material-icons blue\">\n" +
                    "                link\n" +
                    "            </i>\n";
            }
            else   if(data.resType=="QA") {
                cStr +="            <i class=\"align-self-center material-icons violet\">\n" +
                    "              question_answer\n" +
                    "            </i>\n";
            }
            else   if(data.resType=="KeyValues") {
                cStr +="            <i class=\"align-self-center material-icons yellow\">\n" +
                    "                note\n" +
                    "            </i>\n";
            }
            else{
                cStr +="            <i class=\"align-self-center material-icons green\">\n" +
                    "                book\n" +
                    "            </i>\n";
            }

            cStr += "            <div class=\"media-body\">\n";
            if(siteId!=28) {
                cStr +="<span class=\"date mt-0\">"+moment.utc(data.dateCreated).local().format("MMM Do YYYY h:mm a")+"</span>\n";
            }
            cStr +="                <p class=\"title\">"+data.resName+"</p>\n" +
                "                <div class=\"d-flex align-items-center justify-content-start\">\n" ;
            //reading section
            if(data.resType=="Notes") {
                if(data.sharing==null) {
                    if(firstReadId==-1) {
                        firstReadId = data.id;
                    }
                    if (data.resLink.includes(".pdf")) {
                        cStr += "                         <a class=\"mb-0 readnow\" href='javascript:displayPdfReadingMaterial(\"" + data.resLink + "\",\""+data.videoPlayer+"\",\""+data.id+"\",\""+data.zoomLevel+"\",\""+data.resName+"\")'>Read now</a>\n";
                    } else {
                        cStr += "                         <a class=\"mb-0 readnow\" href='javascript:displayReadingMaterial(" + data.id + ")'>Read Now</a>\n";
                    }
                }else{
                    if (data.quizMode == "file") {
                        if (data.resLink.includes(".pdf"))
                            cStr += "<a href='javascript:displayPDFNotes(" + data.id + ",\"" + data.resName + "\")' class=\"mb-0 readnow\">Show Notes</a>";
                        else
                            cStr += "<a href='javascript:downloadFile( " + data.id + ")' class=\"mb-0 readnow\">Download Notes</a>";

                    } else {
                        cStr += "<a href='javascript:getNotesData(" + data.id + ")' class=\"mb-0 readnow\">Show Notes</a>";
                    }
                }

            }
            else   if(data.resType=="Videos" || data.resType=="Reference Videos" ) {   //videos

                if (data.resLink == "") {
                        cStr += "<a class=\"mb-0 readnow\" href='javascript:playSmartVideo(\"" + data.secureVideoLink + "\"," + data.id + ")'>Watch</a>\n";

                }else {
                    //check the different types of it
                    if (siteId == 21) {
                        cStr += '<p class="m-0" style="color:red;">Videos of this book are available in app only</p>';
                    } else{
                        if ("audio" == data.quizMode) {
                            cStr += "<a class=\"mb-0 readnow\" href='javascript:playAudio(" + data.id + ")'>Play</a>\n";
                        } else if ("video" == data.quizMode) {
                            cStr += "<a class=\"mb-0 readnow\" href='javascript:playS3Video(" + data.id + ",\"" + data.resLink + "\")'>Watch</a><a href='javascript:playAudiOnly(\"" + data.resLink + "\"," + data.id + ")' id='audioPlayButton' class='listen-btn readnow'>Listen</a>\n";
                        } else {
                            cStr += "<a class=\"mb-0 readnow\" href='javascript:playVideo(\"" + data.resLink + "\"," + data.id + ")'>Watch</a><a href='javascript:playAudiOnly(\"" + data.resLink + "\"," + data.id + ")' id='audioPlayButton' class='listen-btn readnow'>Listen</a>\n";
                        }
                    }
                }
            }
            else if(data.resType=="Uploaded Media"  ) {   //videos

                if((""+data.resLink).indexOf(".mp4")>-1) {
                        cStr += "<a class=\"mb-0 readnow\" href='javascript:playHostedMediaVideo(" + data.id + ")'>Watch</a>\n";
                        cStr += "<a class=\"mb-0 readnow\" href='javascript:playHostedMediaVideo(" + data.id + ")'>";

                }else{
                    cStr += "<a class=\"mb-0 readnow\" href='javascript:playHostedMediaAudio(" + data.id + ")'>Play</a>\n";
                }
            }
            else   if(data.resType=="Reference Web Links") {   //weblinks
                cStr += "                    <a class=\"mb-0 readnow\" href='javascript:openWebRef(" + data.id + ",\""+data.resLink.replace('#', ':')+"\")'>Open</a>\n";
            }
            else   if(data.resType=="QA") {   //Question and Answers
                cStr += "                    <a class=\"mb-0 readnow\" href='javascript:showQAndA(" + data.resLink + ")'>Show Question and Answers</a>\n";
            }
            else   if(data.resType=="KeyValues") {   //Question and Answers
                cStr += "                     <a class=\"mb-0 readnow\" href='javascript:reviseNow(" + data.id + ",\""+data.resName.replace(/'/g,"&#39;")+"\")'>Revise</a>\n";
            }else{


                if(data.testStartDate){
                    testSeriesQuiz=true;
                    var displayName="Take test";
                    var testAlreadyTaken=false;
                    if(seenResources.includes(","+data.id+",")) {
                        console.log("test already taken");
                        testAlreadyTaken=true;
                        displayName="Re attempt";
                    }

                    if("true"==data.testEnded) cStr += "<p class='testStarts'>Test already ended</p>";
                    else if("true"==data.testStarted){
                        cStr += "<a href='/funlearn/quiz?fromMode=book&quizMode=practice&quizId=" + data.resLink + "&resId=" + data.id + "' class='btn quiz-practice-btn waves-effect waves-ripple'>"+displayName+"</a>";

                    }else cStr += "<p class='testStarts'> Test Starts On </p>"+
                        "<p>"+
                        "<span class='testDate'>"+moment.utc(data.testStartDate).format("D MMM YYYY")+"</span>"+
                        "<span class='seperator'>|</span>"+
                        "<span class='testTime'>"+moment.utc(data.testStartDate).format("h:mm a")+"</span>"+
                        "</p>";
                    if(data.testResultAnnounced=="true")
                        cStr += "<a href='javascript:getRankDetailsForTestSeries(" + data.id + ")' class='btn btn-block showRank waves-effect waves-ripple'>Show Rank</a>";
                    else if(data.testResultDate){
                        cStr += "<p class='testStarts'> Results on </p>"+
                            "<p>"+
                            "<span class='testDate'>"+moment.utc(data.testResultDate).format("D MMM YYYY")+"</span>"+
                            "<span class='seperator'>|</span>"+
                            "<span class='testTime'>"+moment.utc(data.testResultDate).format("h:mm a")+"</span>"+
                            "</p>";
                    }
                }else {
                    if(appInApp=="true") {
                        cStr += "<a class=\"mb-0 readnow\" href='/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&quizId=" + data.resLink + "&resId=" + data.id + "&siteName=${session['entryController']}'>Take Quiz</a>" +
                            "<a class=\"mb-0 readnow\" href='/funlearn/quiz?fromMode=book&quizMode=learn&fromTab=all&viewedFrom=quiz&quizId=" + data.resLink + "&resId=" + data.id + "&siteName=${session['entryController']}'>Learn</a>\n";
                    } else {
                        cStr += "                    <a class=\"mb-0 readnow\" href='/prepjoy/prepJoyGame?quizId=" + data.resLink + "&resId=" + data.id + "&quizType=practice" + "&source=web" + "&learn=false&pubDesk=false&dailyTest=false"  + "&siteName=${session['entryController']}' target='_blank'>Practice</a>" +
                            "&nbsp;&nbsp;&nbsp; <a class=\"mb-0 readnow\" href='/prepjoy/prepJoyGame?quizId=" + data.resLink + "&resId=" + data.id + "&quizType=testSeries" + "&source=web" + "&learn=false&pubDesk=false&dailyTest=false" + "&siteName=${session['entryController']}' target='_blank'>Test</a>" +
                            "&nbsp;&nbsp;&nbsp;   <a class=\"mb-0 readnow\" href='/funlearn/quiz?fromMode=book&quizMode=learn&quizId=" + data.resLink + "&resId=" + data.id + "'" + "&siteName=${session['entryController']}'>Study</a>\n";
                    }
                }

            }
            cStr += "                </div>\n";

            cStr += "            </div>\n" +
                "        </div>\n" +
                "    </div>\n";

        }
        // add adding video thingy first.
        if(sageOnly=="true") {
            if ("books" == defaultSiteName && !"true"==appInApp) {
                cStr += "<div class=\"container-wrapper\">\n" +
                    "<div class='d-flex justify-content-between align-items-center'>" +
                    "<p class='quiz-number'>" + (cData.length + 1) + "</p>";
                cStr += "</div>";
                cStr += "        <div class=\"media\">\n";
                cStr += "<i class=\"align-self-center yt flaticon-youtube\"></i>";
                cStr += "            <div class=\"media-body\">\n" +
                    "                <span class=\"date mt-0\"></span>\n" +
                    "                <p class=\"title\">Related videos</p>\n" +
                    "                <div class=\"d-flex align-items-center justify-content-between\">\n";
                if(siteId == 21)
                {
                    cStr += "<a class=\"mb-0\"  style='color:red;'>Videos of this book are available in app only</a>\n";
                }
                else {
                    cStr += "<a class=\"mb-0 readnow\" href='javascript:searchRelatedVideos();'>Show them</a>\n";
                }

                cStr += "                </div>\n" +
                    "            </div>\n" +
                    "        </div>\n" +
                    "    </div>\n";

                //add the web links solutions
                cStr += "<div class=\"container-wrapper\">\n" +
                    "<div class='d-flex justify-content-between align-items-center'>" +
                    "<p class='quiz-number'>" + (cData.length + 2) + "</p>";
                cStr += "</div>";
                cStr += "        <div class=\"media\">\n";
                cStr += "            <i class=\"align-self-center material-icons blue\">\n" +
                    "                link\n" +
                    "            </i>\n";
                cStr += "            <div class=\"media-body\">\n" +
                    "                <span class=\"date mt-0\"></span>\n" +
                    "                <p class=\"title\">Solutions</p>\n" +
                    "                <div class=\"d-flex align-items-center justify-content-between\">\n";
                cStr += "                    <a class=\"mb-0 readnow\" href='javascript:googleSearch(\"solutions\")'>Open</a>\n";

                cStr += "                </div>\n" +
                    "            </div>\n" +
                    "        </div>\n" +
                    "    </div>\n";
                //add the web worksheets
                cStr += "<div class=\"container-wrapper\">\n" +
                    "<div class='d-flex justify-content-between align-items-center'>" +
                    "<p class='quiz-number'>" + (cData.length + 3) + "</p>";
                cStr += "</div>";
                cStr += "        <div class=\"media\">\n";
                cStr += "            <i class=\"align-self-center material-icons blue\">\n" +
                    "                link\n" +
                    "            </i>\n";
                cStr += "            <div class=\"media-body\">\n" +
                    "                <span class=\"date mt-0\"></span>\n" +
                    "                <p class=\"title\">Worksheets</p>\n" +
                    "                <div class=\"d-flex align-items-center justify-content-between\">\n";
                cStr += "                    <a class=\"mb-0 readnow\" href='javascript:googleSearch(\"worksheets\")'>Open</a>\n";

                cStr += "                </div>\n" +
                    "            </div>\n" +
                    "        </div>\n" +
                    "    </div>\n";

                //add the web links previous year question papers
                cStr += "<div class=\"container-wrapper\">\n" +
                    "<div class='d-flex justify-content-between align-items-center'>" +
                    "<p class='quiz-number'>" + (cData.length + 4) + "</p>";
                cStr += "</div>";
                cStr += "        <div class=\"media\">\n";
                cStr += "            <i class=\"align-self-center material-icons blue\">\n" +
                    "                link\n" +
                    "            </i>\n";
                cStr += "            <div class=\"media-body\">\n" +
                    "                <span class=\"date mt-0\"></span>\n" +
                    "                <p class=\"title\">Previous year question papers</p>\n" +
                    "                <div class=\"d-flex align-items-center justify-content-between\">\n";
                cStr += "                    <a class=\"mb-0 readnow\" href='javascript:googleSearch(\"previous year question papers\")'>Open</a>\n";

                cStr += "                </div>\n" +
                    "            </div>\n" +
                    "        </div>\n" +
                    "    </div>\n";
            }
        }

        if (siteId != 9) {
        if(suggestedVideos.length > 0) {
            cStr += "<div class='whitelabel suggested-videos mt-4 pt-2'><h6>Suggested Videos</h6>";
            for (let j = 0; j < suggestedVideos.length; ++j) {
                let videos = suggestedVideos[j];
                let imgSrc = "https://i.ytimg.com/vi/" + videos.videoId + "/mqdefault.jpg";
                cStr += "<div class='video-wrapper'>" +
                            "<div class='d-flex'>" +
                                "<a href='javascript:playVideo(\"" + videos.videoId + "\"," + 1 + ")'>" +
                                    "<div class='video-img-wrapper'>" +
                                        "<img src='" + imgSrc + "' class='video-img' alt=''/>" +
                                    "</div>" +
                                    "<p>" + videos.videoTitle + "</p>" +
                                "</a>" +
                            "</div>" +
                        "</div>";
            }
            cStr += "</div>";
        }
        }

        cStr +=     "        </div>\n" +
            "      </div>" ;

        document.getElementById('content-data-all').innerHTML = cStr;
        //add adding video thingy

        $("#content-data-all").show();
        $('html, body').animate({scrollTop:0}, 'slow');

      //  hideTextFormatter("something");
        $('.loading-icon').addClass('hidden');
        if(cData.length==1&&firstReadId>-1) {
            if(sageOnly=="true") {
                if (data.resLink.includes(".pdf")) {
                    displayPdfReadingMaterial(data.resLink, data.videoPlayer, +data.id, data.zoomLevel)
                } else {
                    displayReadingMaterial(firstReadId);
                    sageFileOpened=true;
                }
            }
        }
    }


</script>

<script type="text/javascript" src="https://www.youtube.com/iframe_api"></script>

<script>
    function playAudiOnly(videoLink,id) {
        var htmlStr = "<div style=\"display:flex;justify-content:center;align-items:center;\">\n" +
            "    <div>\n" +
            "        <div data-video=\""+videoLink+"\" data-autoplay=\"0\" data-loop=\"1\" data-controls='1' id=\"youtube-audio\"></div>\n" +
            "        <div style=\"clear:both;margin:10px;text-align:center\">\n" +
            // "            <p>The audio player is created with the YouTube API.</p>\n" +
            // "            <p>Read Tutorial: <a href=\"http://www.labnol.org/internet/youtube-audio-player/26740/\">YouTube Audio Player</a></p>\n" +
            "        </div>\n" +
            "    </div>\n" +
            "</div>\n"
        $("#LoadingAudio").html(htmlStr);
        $("#PlayAudiOnlyModal").modal('show');
        onYouTubeIframeAPIReady();
    }

    var player;
    function onYouTubeIframeAPIReady() {

        var ctrlq = document.getElementById("youtube-audio");
        ctrlq.innerHTML = '<i class="material-icons-round" id="youtube-icon" style="font-size: 3rem"></i><div id="youtube-player"></div>';
        ctrlq.style.cssText = 'margin:1.4em auto;cursor:pointer;cursor:hand;display:none';
        ctrlq.onclick = toggleAudio;

        player = new YT.Player('youtube-player', {
            height: '0',
            width: '0',
            videoId: ctrlq.dataset.video,
            playerVars: {
                autoplay: ctrlq.dataset.autoplay,
                loop: ctrlq.dataset.loop,
                controls: '1',
            },
            events: {
                'onReady': onPlayerReady,
                'onStateChange': onPlayerStateChange
            }
        });
    }

    function togglePlayButton(play) {
        var iconElement = document.getElementById("youtube-icon");
        play ? iconElement.innerHTML = 'pause' : iconElement.innerHTML = 'play_arrow';
    }

    function toggleAudio() {
        if ( player.getPlayerState() == 1 || player.getPlayerState() == 3 ) {
            player.pauseVideo();
            togglePlayButton(false);
        } else {
            player.playVideo();
            togglePlayButton(true);
        }
    }

    function onPlayerReady(event) {
        player.setPlaybackQuality("small");
        document.getElementById("youtube-audio").style.display = "block";
        togglePlayButton(player.getPlayerState() !== 5);
    }

    function onPlayerStateChange(event) {
        if (event.data === 0) {
            togglePlayButton(false);
        }
    }

    $(document).ready(function () {
        $("#PlayAudiOnlyModal").click(function(ev){
            if(ev.target != this) return;
            $('#PlayAudiOnlyModal').modal('hide');
            // document.getElementById('PlayingAudiOnly').pause();
            player.pauseVideo();
            togglePlayButton(false);
        });
    });



</script>

<script>
    <sec:ifLoggedIn>
    function updateUserView(id,fromTab,viewedFrom){
        <g:remoteFunction controller="log" action="updateUserView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }

    function updateUserViewChapter(id,fromTab,viewedFrom,action){
        <g:remoteFunction controller="log" action="updateUserView" params="'chapterId='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom+'&actionName='+action" />
    }




    </sec:ifLoggedIn>

    function updateView(id,fromTab,viewedFrom){

        <g:remoteFunction controller="log" action="updateView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }
    function updateViewChapter(id,fromTab,viewedFrom,action){
        <g:remoteFunction controller="log" action="updateView" params="'chapterId='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom+'&actionName='+action" />
    }

    function initCallGoogle(id,loadedType){


        var bookTitle = "${bookName}";
        var seoFriendlyTitle = "${title}";
        <%if("books".equals(grailsApplication.config.grails.appServer.default)&&"1".equals(""+session["siteId"])) {%>
        var currentUrl = window.location.href;
        //add the resId
        if(currentUrl.indexOf("&resId")==-1)
            currentUrl=currentUrl+"&resId="+id;
        else{
            currentUrl = currentUrl.substr(0,currentUrl.indexOf("&resId"))+"&resId="+id;
        }
        history.pushState({
            id: 'homepage'
        }, '', currentUrl);
        if(seoFriendlyTitle.indexOf("Wonderslate")>0) {
            var wonderslateIndex =   seoFriendlyTitle.indexOf(" - Wonderslate");
            seoFriendlyTitle = seoFriendlyTitle.substr(0,wonderslateIndex)+": "+loadedType+" - Wonderslate";
            document.title = seoFriendlyTitle;
        }

        callGoogle(window.location.pathname+"/"+window.location.search);

        <%}
       %>

    }

    function closeResourceScreen(){
        $("#content-data-all").show();
        $("#htmlreadingcontent").hide();
        $('#allAddButton').show();
        if($(window).width()<767) {
            $('.contentEdit').width('0');
        }
        $('.shadowHeader').height('0');
        $(".price-wrapper").show();
        $('.prevnextbtn').addClass('d-none').removeClass('d-flex');
        $('#overlay,.export-notes').addClass('d-none');
    }

    function openResourceScreen(){
        $('.loading-icon').removeClass('hidden');
        $("#htmlreadingcontent").show();
        $("#content-data-all").hide();

    }

    function setBackButton(){
        var backString ="<a href='javascript:closeResourceScreen()' class='pr-back-btn'><i class=\"material-icons\">\n" +
            "keyboard_arrow_left\n" +
            "</i>Back</a>";

        return backString;
    }
    function getRankDetailsForTestSeries(resId){
        <g:remoteFunction controller="wonderpublish" action="getRankDetails" onSuccess='displayRankDetails(data)'
                params="'testSeriesBook=true&resId='+resId" />
    }
    function displayRankDetails(data){
        var htmlStr="";
        if("ok"==data.status){
            var rankDetails = data.testRanks;
            var noOfRanks=0;
            if(rankDetails.length>10) noOfRanks=10;
            else noOfRanks=rankDetails.length;
            for(i=0;i<noOfRanks;i++){
                htmlStr +="<tr>\n" +
                    "                        <td>"+rankDetails[i].rank+"</td>\n" +
                    "                        <td>"+rankDetails[i].name+"</td>\n" +
                    "                        <td>"+ parseFloat(""+rankDetails[i].marks).toFixed(2) +"</td>\n" +
                    "                    </tr>";
            }


        } else {
            htmlStr="<tr><td colspan='3'>Results are not yet announced</td></tr>";
        }

        document.getElementById("rankDetails").innerHTML=htmlStr;
        document.getElementById("totalParticipants").innerHTML = "Attempted by <b>" + (data.totalParticipants==null||data.totalParticipants=="null"?"0":data.totalParticipants)+ "</b> students";
        if(data.userRank!=null&&!data.userRank=="") {
            document.getElementById("userRank").innerHTML = data.userRank==null||data.userRank=="null"?"-NA-":data.userRank;
            document.getElementById("userScore").innerHTML = "<span>" + parseFloat(""+data.userScore).toFixed(2) +"</span>";
            if(data.userAnswers!=null&&!(""==data.userAnswers)&&!("null"==data.userAnswers)){
                document.getElementById("detailedResults").innerHTML="<a href='/funlearn/quiz?fromMode=library&quizMode=results&resId="+data.resId+"&quizRecorderId="+data.quizRecorderId+"'>Show Detailed Result</a>";
            }

        }
        $('#rank-dialog').modal('show');
    }

    function playHostedMediaVideo(id){
        var video = document.getElementById('hostedVideo');
        video.src='/funlearn/getMedia?id='+id;

        video.play();
        $("#hostedVideoModal").modal('show');
    }
    function playHostedMediaAudio(id){
        var audio = document.getElementById('hostedAudio');
        audio.src= '/funlearn/getMedia?id='+id;
        audio.play();
        $("#hostedAudioModal").modal('show');
    }

    $("#videoClose").click(function () {
        var vid = document.getElementById("hostedVideo");
        vid.pause();
    });
    $("#audioClose").click(function () {
        var aid = document.getElementById("hostedAudio");
        aid.pause();
    });


    function displayResource(data){
        var lastReadPDF = localStorage.getItem('lastReadPDF');
        chapterIdForPDF = data.chapterId;

        if (lastReadPDF!=null && lastReadPDF!=undefined && lastReadPDF!=""){
            lastReadPDF = JSON.parse(lastReadPDF)
        }
        var response = data.results;
        chapterResponse = response;

        var chpList =  document.querySelectorAll('.read-book-chapters-wrapper li');

        chpList.forEach(item=>{
            item.classList.remove('orangeText');
        });
        document.getElementById("chapterName"+chapterIdForPDF).classList.add('orangeText');
        var notesCount=0;
        var notesObjArr = [];
        var chapterId = data.chapterId;
        response.map(item=>{
            if(item.resType=='Notes'){
                notesCount++;
                notesObjArr.push(item);
            }
        });
        if (notesCount===1){
            notesObjArr.map(item=>{
                if(item.sharing==null) {
                    if (item.resLink.includes(".pdf")) {
                        displayPdfReadingMaterial(item.resLink ,item.videoPlayer,item.id,item.zoomLevel,item.resName);
                    } else {
                        if (!sageFileOpened){
                            displayReadingMaterial(item.id);
                        }
                    }
                }else{
                    if (item.quizMode == "file") {
                        if (item.resLink.includes(".pdf")) {
                            displayPDFNotes(item.id,item.resName);
                        }else {
                            downloadFile(item.id);
                        }
                    } else {
                        if (!sageFileOpened){
                            displayReadingMaterial(item.id);
                        }
                    }
                }

                if(siteId!=12 && siteId !=23 && siteId !=24){
                    setTimeout(function (){
                        $('.bookTemplate,header').hide();
                    },1000);
                }

            })
        }else if(notesCount>1 && lastReadPDF!=null){
            notesObjArr.map(item=>{
                if (item.id == lastReadPDF.resId){
                    if(item.sharing==null) {
                        if (item.resLink.includes(".pdf")) {
                            displayPdfReadingMaterial(item.resLink ,item.videoPlayer,item.id,item.zoomLevel,item.resName);
                        } else {
                            if (!sageFileOpened){
                                displayReadingMaterial(item.id);
                            }
                        }
                    }else{
                        if (item.quizMode == "file") {
                            if (item.resLink.includes(".pdf")) {
                                displayPDFNotes(item.id,item.resName);
                            }else {
                                downloadFile(item.id);
                            }
                        } else {
                            if (!sageFileOpened){
                                displayReadingMaterial(item.id);
                            }
                        }
                    }
                    if(siteId!=12 && siteId !=23 && siteId !=24){
                        $('.bookTemplate,header').hide();
                    }
                }
            });
        }
    }
</script>
