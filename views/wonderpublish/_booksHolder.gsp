<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<asset:javascript src="moment.min.js"/>
<script language="JavaScript">
    var formattedTopicMapIndex;
    var syllabus;
    var grade;
    var subject;
    var level;
    dropdownMode = "books";
    var indexLevel="Competitive Exams";
    <%if(categories!=null&&categories.startsWith("School")){%>
    indexLevel = "School";
    <%}else if(categories!=null&&categories.startsWith("College")){%>
    indexLevel = "College";
    <%}else if(categories!=null&&categories.startsWith("Healthcare")){%>
    indexLevel = "Healthcare";
    <%}%>

    var booksData;
    var indexBoard="";
    var indexGrade="";
    var indexSubject="";
    var firstTime=true;
    var subjects= [];
    var searchMode=false;

    <%if(level!=null){%>
    indexLevel="${level}";
    document.cookie ="indexLevel="+indexLevel;
    <% }else{%>
    if(getCookie("indexLevel")) {
        indexLevel = getCookie("indexLevel");
    }
    <%}%>

    <% if(syllabus!=null){%>
    indexBoard = '${syllabus}';
    document.cookie ="indexBoard="+indexBoard;
    <% }else{%>
    if(getCookie("indexBoard")) {
        indexBoard = getCookie("indexBoard");
    }
    <%}%>

    <% if(grade!=null){%>
    indexGrade = '${grade}';
    document.cookie ="indexGrade="+indexGrade;
    <%}else{%>
    if(getCookie("indexGrade")) {
        indexGrade = getCookie("indexGrade");
    }
    <%}%>

<%  if(subject!=null) { %>
    indexSubject = '${subject}';
    document.cookie ="indexSubject="+indexSubject;
<%  } else { %>
    if(getCookie("indexSubject")) {
        indexSubject = getCookie("indexSubject");
    }
<%
    }

    if("true" == params.search) {
%>
    searchMode=true;
<%  } %>
    function booksListReceived(data){
        booksData=data;
        displayBooks(booksData);

    }

    function displayBooks(data) {
        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];

        var books;
        var booksTags;
        if (data.status != "Nothing present"){
            $('#tab-head').css('color','black');
        if (data.search != null && data.status != "Nothing present") {
            books = data.books;
            booksTags = data.booksTag;
        } else if (data.status != "Nothing present") {
            books = JSON.parse(data.books);
            booksTags = JSON.parse(data.booksTag);
        }
        var htmlStr = "";
        var offerPrice = "";
        var listPrice = "";
        var imgSrc = "";
        var ratingCount = data.books.rating;
        var buyOrAdd;
        var publisher = "";
        var bookType;
        var previewLabel = "Preview";
        // subjects = [];
        htmlStr += "";
        for (var i = 0; i < books.length; ++i) {

            if ("eutkarsh" == defaultSiteName) bookType = "eBook+"; else bookType = "WS eBook+";
            previewLabel = "Preview";
            var urlTag = "";

            for (var s = 0; s < booksTags.length; ++s) {
                if (booksTags[s].bookId == books[i].id) {
                    if ("School" == indexLevel) urlTag = replaceAll(booksTags[s].syllabus,' ', '-').toLowerCase();
                    else urlTag = replaceAll(booksTags[s].grade,' ', '-').toLowerCase();

                }

            }

            if (data.search == null) {
                if (indexBoard) {
                    var bookExists = false;
                    document.getElementById("board" + indexBoard.replace(/\W+/g, '')).classList.add('active');
                    if (indexBoard != "New releases") {
                        for (var s = 0; s < booksTags.length; ++s) {
                            if (indexGrade) {
                                if (indexSubject) {
                                    if (booksTags[s].bookId == books[i].id && booksTags[s].syllabus == indexBoard && booksTags[s].grade == indexGrade && booksTags[s].subject == indexSubject) {
                                        bookExists = true;
                                        break;
                                    }
                                } else {
                                    if (booksTags[s].bookId == books[i].id && booksTags[s].syllabus == indexBoard && booksTags[s].grade == indexGrade) {
                                        bookExists = true;
                                        break;
                                    }
                                }
                            } else if (indexSubject) {
                                if (booksTags[s].bookId == books[i].id && booksTags[s].syllabus == indexBoard && booksTags[s].subject == indexSubject) {
                                    bookExists = true;
                                    break;
                                }
                            } else {
                                if (booksTags[s].bookId == books[i].id && booksTags[s].syllabus == indexBoard) {
                                    bookExists = true;
                                    break;
                                }
                            }

                        }
                        if (!bookExists) continue;
                        else {
                            if (subjects.indexOf(booksTags[s].subject) < 0) //Compatible IE 9 or later
                                subjects.push(booksTags[s].subject);
                        }
                    }
                }
            } else {
                // search results coming in
                document.getElementById("subtabs").innerHTML = "";
                document.getElementById("tab-head").innerHTML = "Search Results";
                $('#materialtabs li >a.nav-link').removeClass('active');
                $("#filterDiv,.mob-sort").hide();
                $(".class-selection-btns").addClass('disabled');
                $(".class-selection-btn-modal").addClass('disabled');
                $(".subject-selection-btn").addClass('disabled');
                $(".subject-selection-btn-modal").addClass('disabled');
            }
            if ($('#materialtabs  li > #tabschool').hasClass('active')) {
                document.getElementById("tab-head").innerHTML = "School eBooks";
            } else if ($('#materialtabs  li > #tabcollege').hasClass('active')) {
                document.getElementById("tab-head").innerHTML = "College eBooks";
            } else if ($('#materialtabs  li > #tabcollege').hasClass('active')) {
                document.getElementById("tab-head").innerHTML = "Test";
            }
            // else if ($('#materialtabs  li > #tabhealthcare').hasClass('active')) {
            //     document.getElementById("tab-head").innerHTML = "Healthcare eBooks";
            // }
            //get the tag required for the book.

            if (books[i].offerPrice == 0 || books[i].offerPrice == 0.0 || books[i].offerPrice == null) {
                offerPrice = "FREE";
                buyOrAdd = "<button onclick='javascript:addToMyLibrary(" + books[i].id + "," + books[i].offerPrice + ",\"" + books[i].title + "\");' class='btn btn-block btn-book-buy waves-effect hidden' style='padding: 6px 0; font-size:12px;'>" + "Add to library" + "</button>";
            } else {
                buyOrAdd = "<a class='btn btn-block btn-book-buy waves-effect hidden' href='/wonderpublish/bookdtl?bookId=" + books[i].id + "&mode=buy' class='preview-links' style='padding: 6px 0; font-size:12px;'>Buy Now</a>";
                offerPrice = "<i class='fa fa-inr'></i> " + books[i].offerPrice;
            }

            if (books[i].listPrice == 0 || books[i].listPrice == 0.0 || books[i].listPrice == "null" || books[i].listPrice == null || books[i].listPrice == books[i].offerPrice) {
                listPrice = "";
            } else {
                listPrice = books[i].listPrice;
            }

            if (books[i].coverImage == null || books[i].coverImage == "null" || books[i].coverImage == "") {
                imgSrc = "/assets/booksmojo/img_cover_placeholder.png";
            } else {
                if(books[i].coverImage.startsWith("https")){
                     imgSrc = books[i].coverImage;
                    imgSrc = imgSrc.replace("~",":");
                }else {
                    imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=passport";
                }
            }

            if (books[i].rating == 0 || books[i].rating == 0.0 || books[i].rating == null) {
                ratingCount = "";
            } else {
                ratingCount = "(" + books[i].rating + ")";
            }

            if (books[i].publisher != null) {
                publisher = books[i].publisher;
            } else {
                publisher = "";
            }
            previewLabel = "<div class='book-btns-home hidden hidden-xs hidden-sm'>" +
                "<a href='/library/" + books[i].title.replace(/\s+/g, '-').toLowerCase() + "?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true' class='btn-book-preview btn btn-block waves-effect' style='margin-bottom: 8px; padding: 6px 0; font-size:12px;'>" + "Preview" + "</a>" +
                buyOrAdd +
                "</div>";
            if ("print" == books[i].bookType) {
                bookType = "Print Book";
                previewLabel = "<div class='book-btns-home hidden hidden-xs hidden-sm'></div>";
            } else if ("ebook" == books[i].bookType) bookType = "eBook";
            if ("test" == books[i].bookType) {

                books[i].testStartDate = replaceAll(books[i].testStartDate, "~", ":");
                books[i].testEndDate = replaceAll(books[i].testEndDate, "~", ":");
                var testEndTime = moment.utc(books[i].testEndDate).local();
                var serverTime = "";
                if(data.serverTime == undefined) serverTime = testEndTime;
                else moment(data.serverTime.replace('T',' ').replace('Z',''));
                var testStartTime = moment.utc(books[i].testStartDate).local();
                if (serverTime > testEndTime) continue;

                htmlStr += "<div class='col-6 col-lg-3 d-flex d-sm-block justify-content-center mb-4'>" +
                    "<a  href='/" + urlTag +"/"+ replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(),'\'','') + "/book-details" + "?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true' class='test-wrapper'>" +
                    "<div>" +
                    "<div class='card'> " +

                    "<h4 class='mt-2'>" + books[i].title + "</h4>" +
                    "<p>Starts On</p>" +
                    "<h2>" + moment.utc(books[i].testStartDate).local().format("MMM Do YYYY h:mm a") + "</h2>" +

                    "<p>Ends On</p>" +
                    "<h2>" + moment.utc(books[i].testEndDate).local().format("MMM Do YYYY h:mm a") + "</h2>" +


                    "<div class='d-flex justify-content-between test-content'>" +
                    "<div>" +
                    "<p>" + books[i].noOfQuestions + "</p>" +
                    "<span>Question</span>" +
                    "</div>" +
                    "<div>" +
                    "<p>" + books[i].totalMarks + "</p>" +
                    "<span>Marks</span>" +
                    "</div>" +
                    "</div>" +

                    "</div>" +
                    "<div class='content-wrapper'> " +
                    "<h3>Mock Test</h3>" +
                    "<p class='sub-name'>" + "<span>by</span>" + publisher + "</p>" +
                    "<p class='price'>" + " &#x20b9 " + "" + offerPrice + "<span>" + listPrice + "</span>" + "</p>" +
                    "</div>" +
                    "</div>" +
                    "</a>" +

                    "</div>" +
                    "</div>" +
                    "</div>";
            } else {
                htmlStr += "<div class='col-6 col-lg-3 d-flex d-sm-block justify-content-center mb-4'>" +
                    "<div class='topSchoolBooks'>" +
                    "<div class='image-wrapper'>";

                htmlStr += "<a href='/" + urlTag + "/" + replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(),'\'','') + "/book-details?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true'>";
                if (books[i].coverImage == null || books[i].coverImage == "null" || books[i].coverImage == "") {
                    htmlStr += "<div class='uncover'>" +
                            "<p>"+books[i].title+"</p>"+
                        "</div>";
                }
                else{
                    htmlStr += "<img src='" + imgSrc + "' alt=''/>" ;
                }
                htmlStr += "<h3>" + bookType + "</h3>" +
                    "</a>" +
                    "</div>";

                htmlStr += "<a href='/" + urlTag + "/" + replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(),'\'','') + "/book-details?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true'>"

                htmlStr += "<div class='content-wrapper'>" +
                    "<h3>" + books[i].title + "</h3>";
                if(!(publisher==null || publisher=="null"))
                htmlStr +=     "<p class='sub-name'>" + "<span>by</span>" + publisher + "</p>";
                if ("print" != books[i].bookType) {

                    if (books[i].chapterSellPrice != null && books[i].chapterSellPrice != "null") {
                        var chapterType = "";
                        if (books[i].chapterType != null) chapterType = books[i].chapterType.toUpperCase();
                        htmlStr += "<div class='d-flex price-tag'>" +
                            "<div>" +
                            "<p class='complete'>SINGLE " + chapterType + "</p>" +
                            "<p class='price'>" + " &#x20b9 " + "" + offerPrice + "<span>" + listPrice + "</span>" + "</p>" +
                            "</div>";
                    }
                    htmlStr +=
                        "<div>" +
                        "<p class='complete'>COMPLETE BOOK</p>" +
                        "<p class='price'>" + " &#x20b9 " + "" + offerPrice + "<span>" + listPrice + "</span>" + "</p>" +
                        "</div>";
                }
                htmlStr += "</div>" +
                    "</a>" +
                    "</div>" +
                    "</div>" +
                    "</div>";
            }


        }

        $('.loading-icon').addClass('hidden');
        if (books.length > 0) {
            $('#content-data-books').hide();
            document.getElementById("content-data-books-" + indexLevel.replace(/\W+/g, '')).innerHTML = htmlStr;
            $("#content-data-books-" + indexLevel.replace(/\W+/g, '')).show();


        } else {

            displayNoBooks();
        }
        if (books.length <= 8) {
            $('#show-more').hide();
        }
        if (indexBoard != "New releases" && data.search == null) {
            if (indexLevel == "Competitive Exams") {
                document.querySelector(".dropdown-toggle.class-selection-btns").textContent = "Select Exam";
                document.querySelector(".dropdown-toggle.class-selection-btn-modal").textContent = "Select Exam";
            }
            else if (indexLevel == "School"){
                document.querySelector(".dropdown-toggle.class-selection-btns").textContent = "Select Class";
                document.querySelector(".dropdown-toggle.class-selection-btn-modal").textContent = "Select Class";
            }
            else {
                document.querySelector(".dropdown-toggle.class-selection-btns").textContent = "Select";
                document.querySelector(".dropdown-toggle.class-selection-btn-modal").textContent = "Select";
            }
            $(".class-selection-btns").removeClass('disabled');
            $(".class-selection-btn-modal").removeClass('disabled');
            $(".subject-selection-btn").removeClass('disabled');
            $(".subject-selection-btn-modal").removeClass('disabled');
            $("#filterDiv,.mob-sort").show();
            populateGrades(indexLevel);
            document.querySelector(".subject-selection-btn").textContent = "Select Subject";
            document.querySelector(".subject-selection-btn-modal").textContent = "Select Subject";
            populateSubjects();
        } else {
            $(".class-selection-btns").addClass('disabled');
            $(".class-selection-btn-modal").addClass('disabled');
            $(".subject-selection-btn").addClass('disabled');
            $(".subject-selection-btn-modal").addClass('disabled');
            $("#filterDiv,.mob-sort").hide();

        }
    }else{
            $('.loading-icon').addClass('hidden');
            $('#tab-head').html('Sorry no results found').css('color','red');
        }
        var fronts = document.querySelectorAll(".uncover");
        for(var i=0 ; i < fronts.length; i++) {
            fronts[i].style.background = colors[i%11];
        }

    }



    function displayNoBooks(){
        $('#content-data-books').show();
        document.getElementById("content-data-books-"+indexLevel.replace(/\W+/g, '')).innerHTML="<div class='no-books-available'>" +
            "<div class='no-book-wrapper'>" + "<img class='book-image img-responsive' src='${assetPath(src: 'wonderslate/img_desert.png')}'>" + "</div>" + "<p>It's like a desert in here! Currently no eBooks available.</p>"+
            "</div>";
        $('.loading-icon').addClass('hidden');
        $('#show-more').hide();
        $('body').css({
            'position' : 'relative'
        });
        $('footer').css({
            'width' : '100%',
            'position' : 'static',
            'bottom' : '0'
        });
        $('.search-filters').hide();
        $('.books-wrapper').removeClass('books-wrapper col-md-9 col-sm-9').addClass('col-md-12 col-sm-12');
        $('.main-content-container').css({
            'position' : 'relative',
            'top' : '50%',
            'transform' : 'translateY(-50%)'
        });
    }

    function getBookCategories(){
        <g:remoteFunction controller="wonderpublish" action="getBookCategories"  onSuccess='initializeDataIndex(data);' params="'apiMode=optimized'"/>
    }



    function getBooksForSelectedCategories(fieldSelected,fieldValue) {
        alert("calling")
        $(".class-selection-btns").addClass('disabled');
        $(".class-selection-btn-modal").addClass('disabled');
        $(".subject-selection-btn").addClass('disabled');
        $(".subject-selection-btn-modal").addClass('disabled');
        $("#filterDiv,.mob-sort").hide();
        $('.loading-icon').removeClass('hidden');
        var level = indexLevel;
        var syllabus = fieldValue;
        var grade = null;
        var subject = null;
        jQuery('#booksloading').show(500);
        if(syllabus=="New releases"){
            <g:remoteFunction controller="wonderpublish" action="getLatestBooksList"  onSuccess='booksListReceived(data);'
                  params="'level='+level+'&apiMode=optimized'" />
        }else {
            <g:remoteFunction controller="wonderpublish" action="getBooksList"  onSuccess='booksListReceived(data);'
                  params="'categories=true&apiMode=optimized&level='+level+'&syllabus='+syllabus+'&grade='+grade+'&subject='+subject" />
        }
    }

    function initializeDataIndex(data){
        bookCategories=false;

        if(data.bookCategories!=null&&"true"==data.bookCategories) bookCategories=true;

        if(data.status=="Nothing present"){
            $('#contentsPresent').hide();
            $('#contentsNotPresent').show();
            displayNoBooks();

        }
        else {
            formattedTopicMapIndex = formatDataIndex(JSON.parse(data.results));
            if(searchMode){
                submitSearch();
            }
            else{
                populateSubTabs();
            }

        }

    }

    function populateSubTabs(){
        //populate tabs
        var tabClass="";
        var tabsPanelStr="";
        var levelHref = "";
        var firstBoard="";
        console.log(formattedTopicMapIndex.levels.length)
        for(i=0;i<formattedTopicMapIndex.levels.length;i++){

            tabClass="";

            if(indexLevel==formattedTopicMapIndex.levels[i]) {
                levelHref = formattedTopicMapIndex.levels[i].replace(/\W+/g, '');
                tabClass = "active";

                var indexBoards = formattedTopicMapIndex.boards[levelHref];
                var subTabStr="<li class='nav-item'>\n" +
                    "        <a href='javascript:subTabClicked(\"New releases\");' class='nav-link' id='boardNewreleases'>"+"Latest Releases</a>\n" +
                    "      </li>";
                firstBoard = "New releases";

                for(j=0;j<indexBoards.length;j++){
                    subTabStr+="<li class='nav-item'>\n" +
                        "        <a href='javascript:subTabClicked(\""+indexBoards[j]+"\");' class='nav-link' id='board"+indexBoards[j].replace(/\W+/g, '')+"'>"+indexBoards[j]+"</a>\n" +
                        "      </li>";
                }
                subTabStr += "<div class='scroll-bar'></div>";
                document.getElementById("subtabs").innerHTML=subTabStr;
                //    $('#materialtabs a[href="#'+indexLevel+'"]').tab('show');
                break;
            }else {}

        }
        $('.loading-icon').addClass('hidden');
         $('#materialtabs a[href="#'+indexLevel.replace(/\W+/g, '')+'"]').tab('show');
        if(indexBoard) subTabClicked(indexBoard);
        else if(firstBoard.length>0)subTabClicked(firstBoard);
        else {
            displayNoBooks();
        }
    }

    function populateGrades(level){

        var levelKey=level.replace(/\W+/g, '');
        var boardKey=indexBoard.replace(/\W+/g, '');
        var grades = formattedTopicMapIndex.grades[levelKey+boardKey];

        var gradesStr="";
        var gradeType="";

        for(j=0;j<grades.length;j++){
            if(isNaN(grades[j])) gradeType="";
            else gradeType="Class ";
            gradesStr+="<li>\n" +
                "    <a href='javascript:loadBooksForGrade(\""+grades[j]+"\");'>"+gradeType+grades[j]+"</a>\n" +
                "    </li>";
        }
        document.getElementById("gradedropdown").innerHTML=gradesStr;
        document.getElementById("gradedropdown-modal").innerHTML=gradesStr;
        if(indexGrade)
        {
            if(indexLevel=="Competitive Exams"){
                document.querySelector(".dropdown-toggle.class-selection-btns").textContent=indexGrade;
            document.querySelector(".dropdown-toggle.class-selection-btn-modal").textContent=indexGrade;}
            else if(indexLevel=="School"){
                if(indexGrade=='Pre Primary'){
                    document.querySelector(".dropdown-toggle.class-selection-btns").textContent=indexGrade;
                    document.querySelector(".dropdown-toggle.class-selection-btn-modal").textContent=indexGrade;
                }else{
                    document.querySelector(".dropdown-toggle.class-selection-btns").textContent="Class "+indexGrade;
                    document.querySelector(".dropdown-toggle.class-selection-btn-modal").textContent="Class "+indexGrade;
                }
            }

            else{
                document.querySelector(".dropdown-toggle.class-selection-btns").textContent = indexGrade;
                document.querySelector(".dropdown-toggle.class-selection-btn-modal").textContent = indexGrade;
            }
        }

    }

    function populateSubjects(){
        subjects.sort();
        var subjectStr="";
        for(j=0;j<subjects.length;j++){

            subjectStr+="<li>\n" +
                "    <a href='javascript:loadBooksForSubject(\""+subjects[j]+"\");'>"+subjects[j]+"</a>\n" +
                "    </li>";
        }
        document.getElementById("subjectdropdown").innerHTML=subjectStr;
        document.getElementById("subjectdropdown-modal").innerHTML=subjectStr;
        if(indexSubject) {
            document.querySelector(".subject-selection-btn").textContent=indexSubject;
            document.querySelector(".subject-selection-btn-modal").textContent=indexSubject;
        }

    }
    function formatDataIndex(data){
        var formattedTopicMap = {};
        var levels = formattedTopicMap.levels = [],
            boards = formattedTopicMap.boards = {},
            grades = formattedTopicMap.grades = {},
            subjects = formattedTopicMap.subjects = {},
            topics = formattedTopicMap.topics = {};
        var length = data.length;
        var level,board, grade, subject, topic, topicId,subjectkey,boardkey,gradekey,levelkey;

        for(var i = 0; i < length; ++i){
            level = data[i].level;
            levelkey = level.replace(/\W+/g, '');//removing the space in between
            board = data[i].syllabus;
            boardkey = board.replace(/\W+/g, '');//removing the space in between
            grade = ""+data[i].grade;
            gradekey = grade.replace(/\W+/g, '');//removing the space in between
            subject = data[i].subject;
            subjectkey = subject.replace(/\W+/g, ''); //removing the space in between
            if(!bookCategories) {
                topic = data[i].topic;
                topicId = data[i].id;
            }


            if(levels.indexOf(level) < 0) //Compatible IE 9 or later
                levels.push(level);
            if(!boards[levelkey])
                boards[levelkey] = [];
            if(boards[levelkey].indexOf(board) < 0)
                boards[levelkey].push(board);

            if(!grades[levelkey+boardkey])
                grades[levelkey+boardkey] = [];
            if(grades[levelkey+boardkey].indexOf(grade) < 0)
                grades[levelkey+boardkey].push(grade);

            if(!subjects[levelkey+boardkey+gradekey])
                subjects[levelkey+boardkey+gradekey] = [];
            if(subjects[levelkey+boardkey+gradekey].indexOf(subject) < 0) {
                subjects[levelkey+boardkey + gradekey].push(subject);

            }
            if(!bookCategories) {
                if (!topics[levelkey + boardkey + gradekey + subjectkey]) topics[levelkey + boardkey + gradekey + subjectkey] = {};
                topics[levelkey + boardkey + gradekey + subjectkey][topic] = "" + topicId;
            }

        }
        return formattedTopicMap;
    }
    $('a[data-toggle="pill"]').on('shown.bs.tab', function (e) {
         var differentTabClicked=false;
        var target = $(e.target).attr("id") // activated tab

        if(target=="tabschool"){
            differentTabClicked=true;
            indexLevel="School";


        }else  if(target=="tabcompetitiveexams"){
            if(indexLevel!="Competitive Exams") differentTabClicked=true;
            indexLevel="Competitive Exams";
        }
        else  if(target=="tabcollege"){
            if(indexLevel!="College") differentTabClicked=true;
            indexLevel="College";
        }
        // else  if(target=="tabhealthcare"){
        //     if(indexLevel!="Healthcare") differentTabClicked=true;
        //     indexLevel="Healthcare";
        // }
        document.cookie ="indexLevel="+indexLevel;
        indexBoard="";
        indexGrade="";
        if(differentTabClicked) {
            document.getElementById("subtabs").innerHTML="";
            populateSubTabs();

        }

    });

    function subTabClicked(field){
        $('.quick-nav > .nav-item > .nav-link').removeClass('active');
        indexBoard = field;
        if(firstTime){
            firstTime=false;
        }else{
            indexGrade="";
            indexSubject="";
        }
        document.cookie ="indexBoard="+field;
        getBooksForSelectedCategories("syllabus",field);


    }

    function loadBooksForGrade(grade){
        indexGrade=grade;
        document.cookie ="indexGrade="+grade;
        displayBooks(booksData);
    }

    function loadBooksForSubject(subject){
        indexSubject=subject;
        document.cookie ="indexSubject="+subject;
        displayBooks(booksData);
    }

    $('.tab-content').on('mouseenter', '.book-item', function() {
        $(this).find('.book-btns-home').removeClass('hidden');
    }).on('mouseleave', '.book-item', function() {
        $(this).find('.book-btns-home').addClass('hidden');
    });

    getBookCategories();

    $('#search-book').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch();
        }
    });

    function submitSearch(){
        $('.loading-icon').removeClass('hidden');
        var searchString =document.getElementById("search-book").value;

        <g:remoteFunction controller="discover" action="search"  onSuccess='displayBooks(data);'
         params="'searchString='+searchString" />
    }



    $(document).on("keypress", "#search-book", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                // to prevent submitting of the page
                e.preventDefault();
                submitSearch();
            }
        }
    });

    function temp(data){
    }

    $('#book-search-type li a').click(function(e) {
        e.preventDefault();
        var a = $(this).html();
        $('#book-search-type-btn').html(a + " <span class='caret'></span>");
        $('#search-book').attr('placeholder', "Search using " + a +"...");
        setCssOfInputOnCategorySelection();
    });
    function setCssOfInputOnCategorySelection() {
        $('#search-book').css({
            "padding-left" : $('#book-search-type-btn').width() + 30
        }).focus();
    }

</script>

<!--hide show quick-sort-->
<script>
    $(document).ready(function() {
        $('.show-more').on('click', function(){
            $('.quick-nav li:gt(9)').show();
            $('.show-less').removeClass('d-none');
            $('.show-more').addClass('d-none');
        });

        $('.show-less').on('click', function(){
            $('.quick-nav li:gt(9)').hide();
            $('.show-more').removeClass('d-none');
            $('.show-less').addClass('d-none');
        });

        if ( $('.quick-nav li').length > 9 ) {
            $('.show-less').click();
        }

        if($(window).width()<769){
            $('.show-more').on('click', function(){
                 $('.quick-nav li:gt(6)').show();
                $('.show-less').removeClass('d-none');
                $('.show-more').addClass('d-none');
            });

            $('.show-less').on('click', function(){
                $('.quick-nav li:gt(6)').hide();
                $('.show-more').removeClass('d-none');
                $('.show-less').addClass('d-none');
            });

            if ( $('.quick-nav li').length > 6 ) {
                $('.show-less').click();
            }
        }
        

    });
    function replaceAll(str, find, replace) {
        if(str==undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }
    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }
</script>
