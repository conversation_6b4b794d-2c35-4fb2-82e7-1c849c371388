<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<asset:stylesheet href="material-input.css"/>
<asset:stylesheet href="wonderslate/adminCommonStyle.css" async="true"/>

<style>
.image-upload > input {
    display: none;
}

.cke_textarea_inline {
    height: 80px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

.inlineEditor{
    min-height: 70px;
    border-bottom: 1px solid #828180;
}

.form-group .cke_textarea_inline{
    height: 35px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}
</style>
<div class="videoExplanation">
    <div class="container-fluid " >
      <div class="row mx-0 mobile_column_swap">
%{--        <div id="staticContainer" class="rounded">--}%
        <div id="static-content " width="90%" class="col-md-9 staticContainer rounded"  >

            <div class="row" id="containerRow">
                <div class="col-md-12">

                    <form  class="form-horizontal" enctype="multipart/form-data" role="form" name="addquiz" id="addquiz"  action="/wonderpublish/addVideoHTML" method="post">
                        <input type="hidden" name="resourceType">
                        <input type="hidden" name="bookId">
                        <input type="hidden" name="chapterId">
                        <input type="hidden" name="mode">
                        <input type="hidden" name="resourceDtlId">
                        <input type="hidden" name="objectiveMstId">
                        <input type="hidden" name="finished">
                        <div class="row align-items-end">
                        <div class="form-group col-md-6 col-lg-6 col-12" id="resourcetitle">

                            <label for="resourceName"><b>TITLE</b></label>
                            <input type="text" class="form-control" id="resourceName"  name="resourceName">
                        </div>
                        <div class="form-group col-md-6 col-lg-6 col-12" id ="slidetitle">
                            <label for="slideName"><b>SLIDE NAME</b></label>
                            <input type="text" class="form-control" id="slideName" name="slideName" autocomplete="off">
                        </div>
                        </div>

                        <div class="row align-items-end">
                        <div class="col-12"><label><b>IMAGE</b></label>
                            <div class="form-group notes float-label-control">
                                <div class="cktext">
                                    <textarea  rows="30" class="form-control" id="notes" name="notes" placeholder="Enter your notes here"></textarea>
                                </div>
                            </div>
                        </div>
                        </div>

                        <div class="row align-items-end points">
                            <div class="col-md-6 col-12">
                                <b>POINT 1</b>
                                <input type="hidden" name="notes1">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="notes1" class="inlineEditor"></div>
                                </div>
                                <br>
                                <label for="description"><b>EXPLANATION 1</b></label>
                                <textarea class="form-control" rows="10" id="description" placeholder="Detailed Description" name="description"></textarea>
                            </div>
                            <div class="col-md-6 col-12">
                                <b>POINT 2</b>
                                <input type="hidden" name="notes2">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="notes2" class="inlineEditor"></div>
                                </div>
                                <br>
                                <label for="description2"><b>EXPLANATION 2</b></label>
                                <textarea class="form-control" rows="10" id="description2" placeholder="Detailed Description" name="description2"></textarea>
                            </div>
                        </div>

                        <div class="row align-items-end points">
                            <div class="col-md-6 col-12">
                                <b>POINT 3</b>
                                <input type="hidden" name="notes3">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="notes3" class="inlineEditor"></div>
                                </div>
                                <br>
                                <label for="description3"><b>EXPLANATION 3</b></label>
                                <textarea class="form-control" rows="10" id="description3" placeholder="Detailed Description" name="description3"></textarea>
                            </div>
                            <div class="col-md-6 col-12">
                                <b>POINT 4</b>
                                <input type="hidden" name="notes4">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="notes4" class="inlineEditor"></div>
                                </div>
                                <br>
                                <label for="description4"><b>EXPLANATION 4</b></label>
                                <textarea class="form-control" rows="10" id="description4" placeholder="Detailed Description" name="description4"></textarea>
                            </div>
                        </div>

                        <div class="row align-items-end points">
                            <div class="col-md-6 col-12">
                                <b>POINT 5</b>
                                <input type="hidden" name="notes5">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="notes5" class="inlineEditor"></div>
                                </div>
                                <br>
                                <label for="description5"><b>EXPLANATION 5</b></label>
                                <textarea class="form-control" rows="10" id="description5" placeholder="Detailed Description" name="description5"></textarea>
                            </div>
                        </div>


                        <div class="row">
                            <div  class="col-sm-9 col-sm-offset-1 smallText" id="previousDirection"></div>
                        </div>


                        <div class="row buttonsVideos mx-0 px-2 px-lg-5 mb-3">
                            <div class="form-group col-sm-12 text-center ">
                                <button type="button" onclick="javascript:deleteQuestion()" class="btn  btn-primary btn-lg" style="display: none;" id="deletebutton">Delete</button>
                                <button type="button" onclick="javascript:formSubmit('Save')" class="btn btn-primary btn-lg">Save</button>
                                <button type="button" onclick="javascript:formSubmit('SaveNext')" class="btn btn-primary btn-lg">Save and Goto Next</button>
                                <button type="button" onclick="javascript:formSubmit('Next')" class="btn btn-primary btn-lg">Save and Add Next</button>
                                <button type="button" onclick="javascript:formSubmit('Done')" class="btn btn-primary btn-lg finishBtn" >Save and Finish</button>
                            </div>
                        </div>


                        <div class="row" id="savedNotification" style="display: none">
                            <div class="col-md-8 col-md-offset-4 smallText green" id="savedNotificationText"><b>Video saved</b></div>
                        </div>
                    </form>
                </div>
            </div>
%{--            <div id="sidebar" class="col-md-2 hidden-xs hidden-sm text-left">--}%

%{--            </div>--}%
        </div>
%{--    </div>--}%
          <div id="sidebar" class="col-md-3 hidden-xs hidden-sm text-left rounded-right">

      </div>
    </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
</div>

<asset:javascript src="jquery-1.11.2.min.js"/>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<asset:javascript src="bootstrap.min.js"/>
<asset:javascript src="material-input.js"/>
<asset:javascript src="videocreator.js"/>
<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>


<script>
    // var resourceDtlId1="";
    var serverPath = "${request.contextPath}";
    var mode = "${params.mode}";
    var resourceType = "${params.resourceType}";
    var chapterId = "${params.chapterId}";
    var bookId = "${params.bookId}";


    //important to call data after all the data is loaded
    var noOFCKEditors=6;
    ''

    // CKEDITOR.disableAutoInline = true;
    // CKEDITOR.inline( 'notes1');
    // CKEDITOR.inline( 'notes2');
    // CKEDITOR.inline( 'notes3');
    // CKEDITOR.inline( 'notes4');
    // CKEDITOR.inline( 'notes5');

    CKEDITOR.replace( 'notes', {
        height:100,
        customConfig: '/assets/ckeditor/customConfig.js',
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
        // Upload images to a CKFinder connector (note that the response type is set to JSON).
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        // Configure your file manager integration. This example uses CKFinder 3 for PHP.
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        // The following options are not necessary and are used here for presentation purposes only.
        // They configure the Styles drop-down list and widgets to use classes.
        stylesSet: [
            { name: 'Narrow image', type: 'widget', widget: 'image', attributes: { 'class': 'image-narrow' } },
            { name: 'Wide image', type: 'widget', widget: 'image', attributes: { 'class': 'image-wide' } }
        ],
        // Load the default contents.css file plus customizations for this sample.
        contentsCss: [ CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css','http://fonts.googleapis.com/css?family=Merriweather'],
        //contentsCss: [ CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css','http://fonts.googleapis.com/css?family=Merriweather','http://fonts.googleapis.com/css?family=MerriweatherSans'],
        // Configure the Enhanced Image plugin to use classes instead of styles and to disable the
        // resizer (because image size is controlled by widget styles or the image takes maximum
        // 100% of the editor width).
        image2_alignClasses: [ 'image-align-left', 'image-align-center', 'image-align-right' ],
    });


    CKEDITOR.inline( 'notes1',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,

    });
    CKEDITOR.inline( 'notes2',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,

    });

    CKEDITOR.inline( 'notes3',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,

    });
    CKEDITOR.inline( 'notes4',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,

    });
    CKEDITOR.inline( 'notes5',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,

    });
    //
    //
    // CKEDITOR.replace( 'notes1', {
    //     height:100,
    //     customConfig: '/assets/ckeditor/customConfig.js',
    //     extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
    //     mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
    //     // Upload images to a CKFinder connector (note that the response type is set to JSON).
    //     uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
    //     // Configure your file manager integration. This example uses CKFinder 3 for PHP.
    //     filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
    //     filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
    //     // The following options are not necessary and are used here for presentation purposes only.
    //     // They configure the Styles drop-down list and widgets to use classes.
    //     stylesSet: [
    //         { name: 'Narrow image', type: 'widget', widget: 'image', attributes: { 'class': 'image-narrow' } },
    //         { name: 'Wide image', type: 'widget', widget: 'image', attributes: { 'class': 'image-wide' } }
    //     ],
    //     // Load the default contents.css file plus customizations for this sample.
    //     contentsCss: [ CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css','http://fonts.googleapis.com/css?family=Merriweather'],
    //     //contentsCss: [ CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css','http://fonts.googleapis.com/css?family=Merriweather','http://fonts.googleapis.com/css?family=MerriweatherSans'],
    //     // Configure the Enhanced Image plugin to use classes instead of styles and to disable the
    //     // resizer (because image size is controlled by widget styles or the image takes maximum
    //     // 100% of the editor width).
    //     image2_alignClasses: [ 'image-align-left', 'image-align-center', 'image-align-right' ],
    // });
    //
    // CKEDITOR.replace( 'notes2', {
    //     height:100,
    //     customConfig: '/assets/ckeditor/customConfig.js',
    //     extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
    //     mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
    //     // Upload images to a CKFinder connector (note that the response type is set to JSON).
    //     uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
    //     // Configure your file manager integration. This example uses CKFinder 3 for PHP.
    //     filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
    //     filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
    //     // The following options are not necessary and are used here for presentation purposes only.
    //     // They configure the Styles drop-down list and widgets to use classes.
    //     stylesSet: [
    //         { name: 'Narrow image', type: 'widget', widget: 'image', attributes: { 'class': 'image-narrow' } },
    //         { name: 'Wide image', type: 'widget', widget: 'image', attributes: { 'class': 'image-wide' } }
    //     ],
    //     // Load the default contents.css file plus customizations for this sample.
    //     contentsCss: [ CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css','http://fonts.googleapis.com/css?family=Merriweather'],
    //     //contentsCss: [ CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css','http://fonts.googleapis.com/css?family=Merriweather','http://fonts.googleapis.com/css?family=MerriweatherSans'],
    //     // Configure the Enhanced Image plugin to use classes instead of styles and to disable the
    //     // resizer (because image size is controlled by widget styles or the image takes maximum
    //     // 100% of the editor width).
    //     image2_alignClasses: [ 'image-align-left', 'image-align-center', 'image-align-right' ],
    // });
    //
    // CKEDITOR.replace( 'notes5', {
    //     height:100,
    //     customConfig: '/assets/ckeditor/customConfig.js',
    //     extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
    //     mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
    //     // Upload images to a CKFinder connector (note that the response type is set to JSON).
    //     uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
    //     // Configure your file manager integration. This example uses CKFinder 3 for PHP.
    //     filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
    //     filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
    //     // The following options are not necessary and are used here for presentation purposes only.
    //     // They configure the Styles drop-down list and widgets to use classes.
    //     stylesSet: [
    //         { name: 'Narrow image', type: 'widget', widget: 'image', attributes: { 'class': 'image-narrow' } },
    //         { name: 'Wide image', type: 'widget', widget: 'image', attributes: { 'class': 'image-wide' } }
    //     ],
    //     // Load the default contents.css file plus customizations for this sample.
    //     contentsCss: [ CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css','http://fonts.googleapis.com/css?family=Merriweather'],
    //     //contentsCss: [ CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css','http://fonts.googleapis.com/css?family=Merriweather','http://fonts.googleapis.com/css?family=MerriweatherSans'],
    //     // Configure the Enhanced Image plugin to use classes instead of styles and to disable the
    //     // resizer (because image size is controlled by widget styles or the image takes maximum
    //     // 100% of the editor width).
    //     image2_alignClasses: [ 'image-align-left', 'image-align-center', 'image-align-right' ],
    // });



    // CKEDITOR.inline( 'notes1',{
    //     customConfig: '/assets/ckeditor/customConfigMin.js',
    //     uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
    //     filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
    //     filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
    //
    // });


    if ( CKEDITOR.env.ie && CKEDITOR.env.version == 8 ) {
        document.getElementById( 'ie8-warning' ).className = 'tip alert';
    }
    CKEDITOR.on("instanceReady",function() {
        console.log("noOFCKEditors===="+noOFCKEditors);
        // insert code to run after editor is ready
        noOFCKEditors--;
        console.log("noOFCKEditors===after dec="+noOFCKEditors);
        if(noOFCKEditors==0){
            <% if("edit".equals(params.mode)){%>
            gotoQuestion(0);

            <%}%>
        }
    });

    <% if("edit".equals(params.mode)){
    objectives.each{ objective ->%>
    var quizItem={};
    quizItem.resourceName=htmlDecode("${resourceDtl.resourceName}");
    quizItem.slideName = htmlDecode("${objective.slideName}");
    quizItem.description = htmlDecode("${objective.explanation1}");
    quizItem.description2= htmlDecode("${objective.explanation2}");
    quizItem.description3 = htmlDecode("${objective.explanation3}");
    quizItem.description4 = htmlDecode("${objective.explanation4}");
    quizItem.description5 = htmlDecode("${objective.explanation5}");
    quizItem.objectiveMstId = "${objective.id}";
    quizItem.notes = htmlDecode("${objective.image!=null?objective.image.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.notes1 = htmlDecode("${objective.point1!=null?objective.point1.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.notes2 = htmlDecode("${objective.point2!=null?objective.point2.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.notes3 = htmlDecode("${objective.point3!=null?objective.point3.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.notes4 = htmlDecode("${objective.point4!=null?objective.point4.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.notes5 = htmlDecode("${objective.point5!=null?objective.point5.replaceAll("(\\r|\\n)", ""):""}");

    quizItems.push(quizItem);
    <% }
  %>
    resourceDtlId = "${resourceDtl.id}";
    quizId = "${resourceDtl.resLink}";
    quizName = quizItems[0].resourceName;
    passage = quizItems[0].passage;
    document.getElementById("sidebar").innerHTML = "<br><b> Saved Questions </b> <br>";
    for(i=0;i<quizItems.length;i++){
        document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + i + ")' class='pagenumber-green' style='margin-bottom: 20px;margin-right: 10px;' >" + (i + 1)  + "</a>";
    }

    <%}%>

    // var counter = 1;
    // var limit = 3;
    // function addInput(addbutton){
    //
    //         var newdiv = document.createElement('div');
    //         newdiv.innerHTML = "                        <div class=\"col-sm-6\">\n";
    //          document.getElementById(addbutton).appendChild(newdiv);
    //         counter++;
    //
    // }


    function formSubmit(submitType) {
        $('#slidetitle').removeClass('col-md-6 col-lg-6');
        $('#savedNotification').hide();
        if (validate(submitType)) {
            saveAndGoNext = false;
            var resourceName = document.getElementById("resourceName").value;
            var slideName=document.getElementById("slideName").value;
            var description2 = document.getElementById("description2").value;
            description2 =description2.replace(/(\r\n|\n|\r)/gm,"");
            var description = document.getElementById("description").value;
            description =description.replace(/(\r\n|\n|\r)/gm,"");
            var description3 = document.getElementById("description3").value;
            description3 =description3.replace(/(\r\n|\n|\r)/gm,"");
            var description4 = document.getElementById("description4").value;
            description4 =description4.replace(/(\r\n|\n|\r)/gm,"");
            var description5 = document.getElementById("description5").value;
            description5 =description5.replace(/(\r\n|\n|\r)/gm,"");
            var resourceName = document.getElementById("resourceName").value;
            document.addquiz.mode.value = mode;
            document.addquiz.resourceType.value = resourceType;
            document.addquiz.chapterId.value = chapterId;
            document.addquiz.resourceDtlId.value=resourceDtlId;
            document.addquiz.objectiveMstId.value=objectiveMstId;
            document.addquiz.bookId.value = bookId;
            document.addquiz.notes.value = CKEDITOR.instances.notes.getData();
            // document.addquiz.description.value = description;
            document.addquiz.resourceName.value = resourceName;
            document.addquiz.notes1.value = CKEDITOR.instances.notes1.getData();
            document.addquiz.description.value = description;
            document.addquiz.slideName.value = slideName;
            document.addquiz.notes2.value = CKEDITOR.instances.notes2.getData();
            document.addquiz.description2.value = description2;
            document.addquiz.notes3.value = CKEDITOR.instances.notes3.getData();
            document.addquiz.description3.value = description3;
            document.addquiz.notes4.value = CKEDITOR.instances.notes4.getData();
            document.addquiz.description4.value = description4;
            document.addquiz.notes5.value = CKEDITOR.instances.notes5.getData();
            document.addquiz.description5.value = description5;

            if ('Next' == submitType) {
                $("#resourcetitle").hide();
                var oData = new FormData(document.forms.namedItem("addquiz"));
                var url = "${createLink(controller:'wonderpublish',action:'addVideoHTML')}";
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: oData,
                    processData: false,  // tell jQuery not to process the data
                    contentType: false,
                    success: function (req) {
                        questionAdded(req);
                    }
                });
                document.getElementById("addquiz").reset();
                document.getElementById("description").value = "";
                document.getElementById("slideName").value = "";
                document.getElementById("description2").value = "";
                document.getElementById("description3").value = "";
                document.getElementById("description4").value = "";
                document.getElementById("description5").value = "";
                document.getElementById("resourceName").value = "";
                //     if(tinyMCE.get('question')!=null) tinyMCE.get('question').setContent('');

                CKEDITOR.instances.notes.setData('');
                CKEDITOR.instances.notes1.setData('');
                CKEDITOR.instances.notes2.setData('');
                CKEDITOR.instances.notes3.setData('');
                CKEDITOR.instances.notes4.setData('');
                CKEDITOR.instances.notes5.setData('');


                $('#deletebutton').hide();
                $("html, body").animate({scrollTop: 0}, "slow");

            }
            else if ('Save' == submitType || 'SaveNext' == submitType) {
                $("#resourcetitle").hide();
                if ('SaveNext' == submitType) saveAndGoNext = true;
                var oData = new FormData(document.forms.namedItem("addquiz"));
                var url = "${createLink(controller:'wonderpublish',action:'addVideoHTML')}";

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: oData,
                    processData: false,  // tell jQuery not to process the data
                    contentType: false,
                    success: function (req) {
                        console.log(req);
                        questionSaved(req);
                    }
                });

            }
            else
            {
                document.addquiz.finished.value='true';
                document.addquiz.submit();
            }
        }
    }
    function htmlDecode( html ) {
        console.log(html);
        var a = document.createElement( 'a' ); a.innerHTML = html;
        return a.textContent;
    };

    function deleteQuestion(){
        if(confirm("Are you sure to delete this video?")){
            showLoader();
            <g:remoteFunction controller="wonderpublish" action="deleteQuestion"  onSuccess='reloadPage();'
                params="'objectiveId='+objectiveMstId" />
        }
    }

    function reloadPage(){
        location.reload();
    }



    var flds =  new Array (
        'resourceName',
        'notes'
    );


    var resSubFileNm="";

    function getHtmlsData(resId){
        <g:remoteFunction controller="funlearn" action="getHtmlsFileNm"  onSuccess='setResSubFileNm(data);' params="'resId='+resId" />
        <g:remoteFunction controller="funlearn" action="getHtmlsFile"  onSuccess="displayHtmls(data);" params="'resId='+resId+'&mode=edit'" />
    }

    function setResSubFileNm(data){
        resSubFileNm = data.fileNm;
    }

    function displayHtmls(data){
        var resLink="${resLink}";
        var filename="${filename}";
        var extraPath = "/OEBPS";
        var isZipBook = false;
        var replaceStr = "/funlearn/downloadEpubImage"+
            "?source="+resLink.substring(0,resLink.lastIndexOf('/')+ 1)+"extract"+
            (isZipBook?"":extraPath)+"/"+resSubFileNm.substring(0,resSubFileNm.lastIndexOf("/")+1);

        var htmls = data;
        htmls =  replaceAll(htmls,"src=\"", "src=\"" +replaceStr);
        htmls =  replaceAll(htmls,"../Styles/", replaceStr+"../Styles/");
        //htmls = htmls.replace("</head>"," <link rel=\"stylesheet\" href=\"/assets/wonderslate-material-design.css\" /></head>");
        if(isZipBook) htmls = replaceAll(htmls,"data=\"", "data=\"" +replaceStr );
        htmls = replaceAll(htmls,"src: url(\"", "src: url(\"" +replaceStr);
        htmls = replaceAll(htmls,"background-image: url('", "background-image: url('" +replaceStr);
        htmls = htmls.replace(/\\\\/g , '\\');
        htmls = htmls.replace(/\\mathring\ *\{\ *([A-Za-z])\ *\}\ */g , '\\overset\{\\ \\ \\circ\}\{$1\}');
        document.addhtml.resourceName.value=htmlDecode("${resourceName}");
        CKEDITOR.instances.notes.setData(htmls);
        CKEDITOR.instances.notes1.setData(htmls);
        CKEDITOR.instances.notes2.setData(htmls);
        CKEDITOR.instances.notes3.setData(htmls);
        CKEDITOR.instances.notes4.setData(htmls);
        CKEDITOR.instances.notes5.setData(htmls);


    }
</script>
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1){ %>
<asset:javascript src="analytics.js"/>
<% } %>

<script>
    $('.navbar-wonderslate').css({
        'padding' : '0 0 0 10px'
    });
    $('.navbar-right').css({
        'padding-right' : '10px'
    });
    $('.user-profile-dropdown .login-signup-dropdown').css({
        'right' : '-40px'
    });
    $('.search-book').detach();

    function replaceAll(str, find, replace) {
        return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }


</script>
</body>
</html>
