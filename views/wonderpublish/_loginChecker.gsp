<!DOCTYPE html>
<%@ page import="com.wonderslate.data.UtilService; com.wonderslate.data.KeyValueMst" %>
<%
    def utilService = grailsApplication.classLoader.loadClass("com.wonderslate.data.UtilService");
    def logsService =  grailsApplication.mainContext.getBean("logsService");


%>
<script>
    <%
    String entryController=session['entryController'];
    if(session['entryController']==null){
        entryController=request.getParameter("siteName")
        if(entryController==null ||"".equals(entryController) ||"null".equals(entryController)) {
            entryController = g.cookie(name: 'siteName');
        }
        utilService.navigationHelper(session,request,entryController)
        //call the util function to set the rest of the stuff.


    }
     //login log check
        if(session["userdetails"]!=null&&session["userLoginLogged"]==null){
                logsService.addUserLoginLog(new Integer(1),"web",session["userdetails"].username)
                session["userLoginLogged"]="true"
       }

%>

    var userAccess = false;

    <%if(session['userdetails']==null){%>
    //Do nothing
    userAccess = false;
    <%}%>

    <%if( session['userdetails']!=null && session["userdetails"].username.indexOf(""+session['siteId']+"_")<0){%>
    userAccess = true;
    <%}%>

    if (userAccess){
        window.location.href='/logoff'
    }

</script>
<!-- Add the code here to check if the user is logged in and if so the session userdetails are set.-->
