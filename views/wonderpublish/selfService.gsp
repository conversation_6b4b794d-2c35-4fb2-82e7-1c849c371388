<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>


<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid publish-management" >
    <div class='row'>
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">

               <div class="row">
                   <div class="col-md-3 font-weight-bold">Title of the course</div>
                   <div class="col-md-9">${title}</div>
               </div>
                <div class="row">
                    <div class="col-md-3 font-weight-bold">User</div>
                    <div class="col-md-9">${name}</div>
                </div>
                <div class="row">
                    <div class="col-md-3 font-weight-bold">Mobile</div>
                    <div class="col-md-9">${mobile}</div>
                </div>
                <div class="row">
                    <div class="col-md-3 font-weight-bold">Status</div>
                    <div class="col-md-9">${status}</div>
                </div>
                <% if(grailsApplication.config.grails.appServer.default=="eutkarsh") { %>
                <div class="row">
                    <div class="col-md-12"><hr></div>
                </div>
                <div class="row">
                    <div class="col-md-12"><a href="https://utkarsh.com/student-support/">Click here</a> to go to support page.</div>
                </div>
                <%}%>
            </div>
        </div>
    </div>

</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->


</body>
</html>
