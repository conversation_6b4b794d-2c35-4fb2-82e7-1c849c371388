<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<style>
.dataTables_wrapper {
    width: 100%;
}
.modal{z-index:100000;}
.page-item.active .page-link {
    background-color: #007bff !important;
}
table td a {
    color: #007bff;
}

table.dataTable.table-sm .sorting:before, table.dataTable.table-sm .sorting_asc:before, table.dataTable.table-sm .sorting_desc:before {
    right: 1.20em;
    font-size: 14px;
}
table.dataTable.table-sm .sorting:after, table.dataTable.table-sm .sorting_asc:after, table.dataTable.table-sm .sorting_desc:after {
    font-size: 14px;
}
table.dataTable {
    width:100% !important;
}
table.dataTable th {
    white-space: nowrap !important;
}
table.dataTable thead th:first-child {
    width:10% !important;
}
table.dataTable thead th:nth-child(2) {
    width:50% !important;
}
table.dataTable td {
    white-space: normal !important;
}
#adminPublished {
    /*margin-bottom: 15px !important;*/
    width: 100% !important;
}
/*#adminPublished tr th:first-child {
    width:10% !important;
}
#adminPublished tr th:nth-child(2) {
    width:50% !important;
}*/
#adminPublished tr td:first-child , table#adminPublished td:last-child , div#adminUnPublished_wrapper td:last-child {
    text-align: center;
}
#adminUnPublished {
    /*margin-bottom: 15px !important;*/
    width: 100% !important;
}
/*#adminUnPublished tr th:first-child {
    width:10% !important;
}
#adminUnPublished tr th:nth-child(2) {
    width:50% !important;
}*/
#adminUnPublished tr td:first-child {
    text-align: center;
}
table.dataTable {
    margin-bottom: 15px !important;
}
div#downloadspopup {
    z-index:10000;
}
div#downloadspopup .modal-body {
    display:flex;
    align-items: center;
    justify-content:center;
    border:none !important;
}
div#downloadspopup .modal-body input
{
    width:50%;
    margin-right:2%;

}
div#downloadspopup .modal-header {
    display:flex;
    align-items: center;
    justify-content:center;
}
div#downloadspopup .modal-content
{
    width:70% !important;
    margin:auto;
}

</style>

<div>
    <div class="container-fluid pub-desks publishing_desk my-5 px-5">
        <ul class="nav nav-tabs nav-pills nav-justified container" role="tablist">
            <%if(printBooks){%>
            <li role="presentation" class="nav-item" >
                <a  class="nav-link" href="#main-books" role="tab" data-toggle="tab"  id="tabPubDesks">My Books</a>
            </li>
            <%}else{%>
            <li role="presentation" class="nav-item" >
                <a  class="nav-link " href="#main-books" role="tab" data-toggle="tab"  id="tabPubDesks">My eBooks (Unpublished)</a>
            </li>
            <li role="presentation" class="nav-item" >
                <a class="nav-link" href="#published-books" role="tab" data-toggle="tab" id="tabMyPublished">eBooks (published)</a>
            </li>
            <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                <li role="presentation" class="nav-item">
                    <a class="nav-link" href="#admin-published-books" role="tab" data-toggle="tab" data-search="search-book" id="tabAdminPublished">All eBooks (published)</a>
                </li>
                <%if(!institutePublisher){%>
                <li role="presentation" class="nav-item" >
                    <a class="nav-link" href="#admin-unpublished-books" role="tab" data-toggle="tab" data-search="search-book1" id="tabAdminUnPublished">All eBooks (Unpublished)</a>
                </li>
                <%}%>
            </sec:ifAllGranted>
            <%if(session["siteId"]!=null&&session["siteId"].intValue()==9){%>
            <li role="presentation"  id="tabInstructorResources">
                <a href="#instructor-resources" role="tab" data-toggle="tab">Instructor</a>
            </li>
            <li role="presentation"  id="tabDiscipline">
                <a href="#disciplines" role="tab" data-toggle="tab">Discipline</a>
            </li>
            <%}
            }%>
        </ul>

        <div class="tab-content instructor-tab-content">
            <div role="tabpanel" class="tab-pane fade in active" id="main-books">
                <div  class='' id="bookdtl" style="min-height: calc(100vh - 156px);">
                    <div class="container">
                        <div class='col-md-12 main my-4 mx-0'>
                            <div id="content-books" class="py-4 pb-5">

                                <div class="row">
                                    <div class="col-md-12 text-right px-5">
                                        <% if(!institutePublisher){%>
                                        <a href="/wonderpublish/manageTabs" class="btn btn-primary light11 justify-content-end mr-2" role="button">Manage Tabs</a>
                                        <%}%>
                                        <button type="button" class="btn btn-primary light11 justify-content-end" onclick="addNewBook();"><i class='fa fa-file-text-o fa-x'></i> Create new eBook </button>
                                        <% if(institutePublisher){%>
                                        <button type="button" class="btn btn-primary light11 justify-content-end" onclick="addPDFBook();"><i class='fa fa-file-text-o fa-x'></i> Create pdf eBook </button>
                                        <%}%>
                                        <%if(session["siteId"].intValue()==12 ||session["siteId"].intValue()==23||session["siteId"].intValue()==24){%>
                                        <a href="/pubdesk/downloadBooksUnPublished" class="btn btn-primary light11 justify-content-end ml-2" role="button">Download eBook Details</a>
                                        <%}%>
                                    </div>
                                </div>
                                <%if(siteMst!=null&&"true".equals(siteMst.exercises)){%>
                                <div class="row">
                                </div>
                                <%}%>

                                <div class="row">
                                    <h4 class="text-center w-100 mt-3 px-5"></h4>
                                    <div class="d-flex justify-content-center col-md-12 px-5">
                                        <table id="pubTable" class="table table-sm table-bordered table-hover">
                                            <thead class="bg-primary text-white text-center">
                                            <tr>
                                                <th>Book Id</th>
                                                <th>Title</th>
                                                <th>Manage Price</th>
                                                <th>ISBN</th>
                                                <% if(session["siteId"].intValue()!=9){%>
                                                <th>Publisher</th>
                                                <th></th>
                                                <%}%>

                                                <%if(session["siteId"]!=null&&session["siteId"].intValue()==9){%>
                                                <th></th>
                                                <%}%>

                                                <%if(siteMst!=null&&"true".equals(siteMst.exercises)){%>
                                                <th>Book Scratch Code</th>
                                                <%}%>
                                            </tr></thead>
                                        </table></div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div role="tabpanel" class="tab-pane fade" id="published-books">

            </div>
            <div role="tabpanel" class="tab-pane fade" id="admin-published-books">

                <div class="container">
                    <div class='col-md-12 main my-4 mx-0'>
                        <div class="py-4 pb-5">
                            <div class="row">
                                <div class="col-md-12 text-right px-5 mb-4">

                                    <a href="/wonderpublish/manageTabs" class="btn btn-primary light11 justify-content-end mr-2" role="button">Manage Tabs</a>

                                    <button type="button" class="btn btn-primary light11 justify-content-end" onclick="addNewBook();"><i class='fa fa-file-text-o fa-x'></i> Create new eBook </button>
                                    <% if(institutePublisher){%>
                                    <button type="button" class="btn btn-primary light11 justify-content-end" onclick="addPDFBook();"><i class='fa fa-file-text-o fa-x'></i> Create pdf eBook </button>
                                    <%}%>
                                </div>
                            </div>
                            <%if(siteMst!=null&&"true".equals(siteMst.exercises)){%>
                            <div class="row">
                            </div>
                            <%}%>
                            <div class="row">
                                <div class="d-flex justify-content-center col-md-12 px-5">

                                    <table id="adminPublished" class="table table-sm table-bordered table-hover">
                                        <thead class="bg-primary text-white text-center">
                                        <tr>
                                            <th>Book Id</th>
                                            <th>Title</th>
                                            <!--<th>Created By</th>--->
                                            <th>Manage Price</th>
                                            <th>ISBN</th>
                                            <% if(session["siteId"].intValue()!=9){%>
                                            <th>Publisher</th>
                                            <% } %>
                                            <th></th>
                                            <%if(siteMst!=null&&"true".equals(siteMst.exercises)){%>
                                            <th>Book Scratch Code</th>
                                            <%}%>
                                        </tr>
                                        </thead>
                                    </table>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div role="tabpanel" class="tab-pane fade" id="admin-unpublished-books">
                <div class="container">
                    <div class='col-md-12 main my-4 mx-0'>
                        <div class="py-4 pb-5">
                            <div class="row">
                                <div class="col-md-12 text-right px-5 mb-4">
                                    <a href="/wonderpublish/manageTabs" class="btn btn-primary light11 justify-content-end mr-2" role="button">Manage Tabs</a>
                                    <button type="button" class="btn btn-primary light11 justify-content-end" onclick="addNewBook();"><i class='fa fa-file-text-o fa-x'></i> Create new eBook </button>
                                </div>
                            </div>
                            <%if(siteMst!=null&&"true".equals(siteMst.exercises)){%>
                            <div class="row">
                            </div>
                            <%}%>
                            <div class="row">
                                <div class="d-flex justify-content-center col-md-12 px-5">

                                    <table id="adminUnPublished" class="table table-sm table-bordered table-hover">
                                        <thead class="bg-primary text-white text-center">
                                        <tr>
                                            <th>Book Id</th>
                                            <th>Title</th>
                                            <th>Manage Price</th>
                                            %{--<th>Created By</th>--}%
                                            <th>ISBN</th>
                                            <% if(session["siteId"].intValue()!=9){%>
                                            <th>Publisher</th>
                                            <% } %>
                                            <th></th>
                                            <%if(siteMst!=null&&"true".equals(siteMst.exercises)){%>
                                            <th>Book Scratch Code</th>
                                            <%}%>
                                        </tr>
                                        </thead>
                                    </table>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div role="tabpanel" class="tab-pane fade" id="instructor-resources">
                <div class="col-md-12">
                    <form class="form-horizontal"  role="form" name="updateInstructorDescription" id="updateInstructorDescription" action="/wonderpublish/updateInstructorDescription" method="post">

                        <div class="form-group">
                            <label for="teachingNotes">Teaching Notes</label>
                            <textarea class="form-control" rows="3" name='teachingNotes' id="teachingNotes"  placeholder="Teaching Notes"  maxlength="500"><%= siteMst !=null?siteMst.teachingNotes:"" %></textarea>
                            &nbsp;
                        </div>
                        <div class="form-group">
                            <label for="teachingSlides">Teaching Slides</label>
                            <textarea class="form-control" rows="3" name='teachingSlides' id="teachingSlides"  placeholder="Teaching Slides"  maxlength="500"><%= siteMst !=null?siteMst.teachingSlides:"" %></textarea>
                            &nbsp;
                        </div>
                        <div class="form-group">
                            <label for="sampleChapters">Sample Chapters</label>
                            <textarea class="form-control" rows="3" name='sampleChapters' id="sampleChapters"  placeholder="Sample Chapters"  maxlength="500"><%= siteMst !=null?siteMst.sampleChapters:"" %></textarea>
                            &nbsp;
                        </div>
                        <div class="form-group">
                            <label for="mediaLinks">Media Links</label>
                            <textarea class="form-control" rows="3" name='mediaLinks' id="mediaLinks"  placeholder="Media Links"  maxlength="500"><%= siteMst !=null?siteMst.mediaLinks:"" %></textarea>
                            &nbsp;
                        </div>
                        <div class="form-group">
                            <label for="exercises">Exercises</label>
                            <textarea class="form-control" rows="3" name='exercises' id="exercises"  placeholder="Exercises"  maxlength="500"><%= siteMst !=null?siteMst.exercises:"" %></textarea>
                            &nbsp;
                        </div>

                        <div class="form-group">
                            <button type="button" class="btn btn-primary light11" onclick="addInstructorDescription();" style="border-radius: 0px;border-width: 0px;"><i class='fa fa-file-text-o fa-x'></i>&nbsp;&nbsp;  Save </button>&nbsp;&nbsp;&nbsp;
                        </div>
                    </form>
                </div>
            </div>
            <div role="tabpanel" class="tab-pane fade" id="disciplines">

                <div class="row">
                    <div class="col-md-2">
                        <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploaddisciplinecover" id="uploaddisciplinecover" action="/wonderpublish/uploadDisciplineCover" method="post">
                            <div class="row text-left"><div class="col-md-12 text-left image-wrapper overlay-fade-in" id="bookcover">
                                <%if(siteMst!=null&&siteMst.displineCoverImage!=null){%>
                                <img src="/funlearn/showProfileImage?id=${siteMst.id}&fileName=${siteMst.displineCoverImage}&type=site&imgType=passport" width="100">
                                <%}else{%> <a href="#" ><i class="fa fa-book fa-5x"></i><br>Discipline Cover Image</a><%}%>
                                <div class="image-overlay-content image-upload">
                                    <p><br></p>
                                    <div id="filelabel1"><label for="fileoption1"><span class="smallText" style="cursor: pointer;color:white">Upload Discipline Cover Image</span></label></div>
                                    <input id="fileoption1" name="file" type="file"  accept="image/png, image/jpeg, image/gif" onchange="updateDisciplineCover();"/></div>
                            </div></div>

                        </form>
                    </div>

                </div>

            </div>

        </div>
        <!-- Modal -->
        <div class="modal fade" id="downloadspopup" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <p class="modal-title" id="">Generate scratch codes</p>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <input type="text" class="form-control"  placeholder="Campaign name"   id="campaignName" name="campaignName"><br>
                        <input type="number" class="form-control"  placeholder="Max 10000" aria-label="Max 10000" aria-describedby="basic-addon1" id="noOfTokens" name="noOfTokens"><br>
                        <a class="btn btn-primary" id="submitBtn" role="button" onclick="generateTokenwithId(bookIdhead)">Submit</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="push"></div>
</div>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="loading-popup" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <p class="modal-title">Please wait while we generate scratch codes.</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="contact-popup" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <p class="modal-title">Unable to generate scratch codes. Please contact Admin.</p>
            </div>
        </div>
    </div>
</div>


<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<asset:javascript src="jquery.tablesorter.min.js"/>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<script>

    var siteName = "";

    <%if("true".equals(session["commonWhiteLabel"])){%>
    siteName = "${session['siteName']}";
    <%}else{%>
    siteName = "${session['entryController']}";
    <%}%>

    var bookIdhead;
    // for model box
    $('#myModal').on('shown.bs.modal', function () {
        $('#myInput').trigger('focus')
    })
    // generate token validation
    $('#downloadspopup input').keyup(function(){
        if ($(this).val() > 10000){
            $(this).val("10000");
        }
        else if($(this).val() < 1)
        {
            $(this).val("");
        }
    });


    var modelinput = document.querySelector('#downloadspopup input');
    var sub = document.querySelector('#submitBtn')
    modelinput.addEventListener("click", clearinput );
    function clearinput(){
        modelinput.value="";

    };
    function deleteBook(id) {
        if(confirm("Do you want to go ahead and delete this book?")) {
            <g:remoteFunction controller="admin" action="deleteBook" params="'bookId='+id" onSuccess="bookDeleted(data)"></g:remoteFunction>
        }
    }
    function bookDeleted(data) {
        if(data.status=="OK"){
           alert("Book deleted successfully.")
            location.reload();
        }
    }

    function addNewBook() {
        <% if("sage".equals(session["entryController"])){%>
        window.location = "/wonderpublish/bookCreate?printBooks=${printBooks}";
        <%}else{ %>
        window.location = "/wonderpublish/bookCreateNew?printBooks=${printBooks}";
        <% } %>
    }



    function updateDisciplineCover(){

        document.uploaddisciplinecover.submit();

    }

    function addInstructorDescription(){
        document.updateInstructorDescription.submit();
    }

    var publishedBooksShown=false;
    var tabToBeUpdated="";

    // Showing loader on click different tabs
    $(document).ready(function(){
        initializeMainBooksTable();
        setTimeout(function(){
            $("#tabPubDesks").trigger("click");
        },1000)
    });

    function initializeMainBooksTable() {
        $('#pubTable').DataTable({
            "processing": true,
            "serverSide": true,
            "ordering": false,
            "pageLength": 25,
            "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
            "language": {
                "processing": "Loading books...",
                "emptyTable": "No books found",
                "zeroRecords": "No matching books found"
            },
            "ajax": {
                "url": "/admin/getMyUnpublishedBooks",
                "type": "GET",
                "data": function (outData) {
                    return outData;
                },
                dataFilter: function (inData) {
                    return inData;
                },
                error: function (err, status) {
                    console.log("Error loading books:", err);
                    showErrorMessage("Failed to load books. Please refresh the page.");
                }
            },
            "columns": [{
                "data": "id"
            }, {
                "data": "title",
                "searchable": false,
                "orderable": false,
                "render": function (data, type, row) {
                    if (row.title != null) {
                        <% if("sage".equals(session["entryController"])){%>
                        return "<a href=\"/book-create?bookId=" + row.id + "&printBooks=${printBooks}\">" + row.title + "</a>";
                        <%}else{%>
                        return "<a href=\"/book-create-new?bookId=" + row.id + "&printBooks=${printBooks}\">" + row.title + "</a>";
                        <%}%>
                    }
                }
            }, {
                "data": "title",
                "searchable": false,
                "orderable": false,
                "render": function (data, type, row) {
                    return "<a href=\"javascript:manageBookPrices(" + row.id + ");\">Edit price</a>"
                }
            }, {
                "data": "isbn"
            }
            <% if(session["siteId"].intValue()!=9){%>
            , {
                "data": "publisher",
                "searchable": false,
                "orderable": false,
                "render": function (data, type, row) {
                    if (row.publisher != null) {
                        <% if("sage".equals(session["entryController"])){%>
                        return "<a href=\"/book-create?bookId=" + row.id + "&printBooks=${printBooks}\">" + row.publisher + "</a>";
                        <%}else{%>
                        return "<a href=\"/book-create-new?bookId=" + row.id + "&printBooks=${printBooks}\">" + row.publisher + "</a>";
                        <%}%>
                    }
                    return "";
                }
            }, {
                "data": "title",
                "searchable": false,
                "orderable": false,
                "render": function (data, type, row) {
                    <%if(printBooks){%>
                    return "<a href=\"wonderpublish/bookdtl?checkurl=true&siteName=${session['entryController']}&bookId=" + row.id + "\">View Book</a>";
                    <%}else{%>
                    <%if("books".equals(session["entryController"])){%>
                    return "<a href=\"resources/ebook?checkurl=true&siteName=${session['entryController']}&bookId=" + row.id + "\">View Book</a>" +
                        "&nbsp;<a href=\"wsshop/validityExtensionAdmin?bookId=" + row.id + "\">Validity Price</a>";
                    <%}else{%>
                    return "<a href=\"library/" + encodeURIComponent(row.title).replace(/"/g, "&#39;") + "?checkurl=true&siteName=" + siteName + "&bookId=" + row.id + "\">View Book</a>";
                    <%}}%>
                }
            }
            <%}%>
            <%if(session["siteId"]!=null&&session["siteId"].intValue()==9){%>
            , {
                "data": "title",
                "searchable": false,
                "orderable": false,
                "render": function (data, type, row) {
                    return "<a href=\"/sage/instructorResourcesAdmin?siteName=${session['entryController']}&bookId=" + row.id + "\">Instructor Resources</a>";
                }
            }
            <%}%>
            <%if(siteMst!=null&&"true".equals(siteMst.exercises)){%>
            , {
                "data": "title",
                "searchable": false,
                "orderable": false,
                "render": function (data, type, row) {
                    return "<button type=\"button\" class=\"btn btn-primary\" onclick=\"generateToken(" + row.id + ")\" data-toggle=\"modal\" data-target=\"#downloadspopup\">Generate Scratch Code</button>";
                }
            }
            <%}%>
            ]
        });
    }

    // Utility functions for better performance and error handling
    function showErrorMessage(message) {
        if (typeof toastr !== 'undefined') {
            toastr.error(message);
        } else {
            alert(message);
        }
    }

    function showSuccessMessage(message) {
        if (typeof toastr !== 'undefined') {
            toastr.success(message);
        } else {
            console.log(message);
        }
    }

    // Debounce function to optimize search performance
    function debounce(func, wait) {
        var timeout;
        return function executedFunction() {
            var args = arguments;
            var later = function() {
                clearTimeout(timeout);
                func.apply(this, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Performance monitoring
    function logPerformance(operation, startTime) {
        if (typeof performance !== 'undefined' && performance.now && startTime) {
            const endTime = performance.now();
            const duration = endTime - startTime;
            console.log(operation + " took " + duration.toFixed(2) + " milliseconds");

            // Log slow operations (> 2 seconds)
            if (duration > 2000) {
                console.warn("Slow operation detected: " + operation + " took " + duration.toFixed(2) + "ms");
            }
        }
    }

    var adminPublishedInitialized = false;
    $("#tabAdminPublished").on('click', function () {
        if (!adminPublishedInitialized) {
            adminPublishedInitialized = true;
            var test=$('#adminPublished').DataTable( {
                "processing": true,
                "serverSide": true,
                "bRetrieve": true,
                "ordering":  false,
                "ajax": {
                    "url": "/admin/getAdminAllPublishedBooks",
                    "type": "GET",
                    "data": function ( outData ) {
                        return outData;
                    },
                    dataFilter:function(inData){
                        // what is being sent back from the server (if no error)
                        return inData;
                    },
                    error:function(err, status){
                        // what error is seen(it could be either server side or client side.
                        console.log(err);
                    },
                },
            "columns": [{
                "data": "id",
            }, {
                "data": "title",
                "searchable": false,
                "orderable":false,
                "render": function (data, type, row) {
                    if ( row.title !=null) {
                        <% if("sage".equals(session["entryController"])){%>
                            return "<a href=\"/book-create?bookId=" + row.id + "&printBooks=${printBooks}\">" + row.title + "</a>";
                        <%}else{%>
                            return "<a href=\"/book-create-new?bookId=" + row.id + "&printBooks=${printBooks}\">" + row.title + "</a>";
                        <%}%>
                    }
                }
            },
                     {
                         "data": "title",
                         "searchable": false,
                         "orderable":false,
                         "render": function (data, type, row) {
                             return "<a href=\"javascript:manageBookPrices("+row.id+");\">Edit price</a>"
                         }
                 },
                {
                    "data": "isbn",

                },
                <% if(institutePublisher){%>
                {
                    "data": "authors",
                    "searchable": false,
                    "orderable":false,
                    "render": function (data, type, row) {


                            return  row.authors;

                        }


                },

                <%}else if(session["siteId"].intValue()!=9&&!institutePublisher){%>
                {
                    "data": "publisher",
                    "searchable": false,
                    "orderable":false,
                    "render": function (data, type, row) {

                        if ( row.publisher !=null) {
                            <% if("sage".equals(session["entryController"])){%>
                                return "<a href=\"/book-create?bookId=" + row.id + "&printBooks=${printBooks}\">" + row.publisher + "</a>";
                            <%}else{%>
                                return "<a href=\"/book-create-new?bookId=" + row.id + "&printBooks=${printBooks}\">" + row.publisher + "</a>";
                            <%}%>
                        }

                    }
                },

                <%}%>


                {
                    "data": "title",
                    "searchable": false,
                    "orderable":false,
                    "render": function (data, type, row) {
                        <% if(session["siteId"].intValue()!=9){%>
                        //return "<a href=\"library/"+row.title+"?siteName=books&bookId="+row.id+"\">View Books</a>";

                        <%if(printBooks){%>
                        return "<a href=\"wonderpublish/bookdtl?checkurl=true&siteName=${session['entryController']}&bookId="+row.id+"\">View Book</a>";
                        <%}else{%>
                        <%if("books".equals(siteMst.siteName)){%>
                        return "<a href=\"resources/ebook?checkurl=true&siteName=${session['entryController']}&bookId="+row.id+"\">View Book</a>" +
                        "&nbsp;<a href=\"wsshop/validityExtensionAdmin?bookId="+row.id+"\">Validity Price</a>";

                        <%}else{%>
                        return "<a href=\"library/"+encodeURIComponent(row.title).replace(/"/g,"&#39;")+"?checkurl=true&siteName="+siteName+"&bookId="+row.id+"\">View Book</a>";
                        <%}}%>

                        <%}%>
                        <%if(session["siteId"]!=null&&session["siteId"].intValue()==9){%>
                        return "<a href=\"/sage/instructorResourcesAdmin?siteName=${session['entryController']}&bookId="+row.id+"\">Instructor Resources</a>";
                        <%}%>

                    }


                },
                <%if(siteMst!=null&&"true".equals(siteMst.exercises)){%>
                {
                    "data": "title",
                    "searchable": false,
                    "orderable":false,
                    "render": function (data, type, row) {
                        return "<button type=\"button\" class=\"btn btn-primary\" onclick=\"generateToken("+row.id+")\" data-toggle=\"modal\" data-target=\"#downloadspopup\">Generate Scratch Code</button>";
                    }

                },
                <%}%>
            ],

            // "initComplete": function()
            // {
            //     $(".dataTables_filter input")
            //         .unbind() // Unbind previous default bindings
            //         .bind("input", function(e) { // Bind our desired behavior
            //             // If the length is 3 or more characters, or the user pressed ENTER, search
            //             if(this.value.length >= 3 || e.keyCode == 13) {
            //                 // Call the API search function
            //                 test.search(this.value).draw();
            //             }
            //             // Ensure we clear the search if they backspace far enough
            //             if(this.value == "") {
            //                 test.search("").draw();
            //             }
            //             return;
            //         });
            //
            // }
        });
        }
    });


    var adminUnpublishedInitialized = false;
    $("#tabAdminUnPublished").on('click', function () {
        if (!adminUnpublishedInitialized) {
            adminUnpublishedInitialized = true;
            var test=$('#adminUnPublished').DataTable( {
                "processing": true,
                "serverSide": true,
                "bRetrieve": true,
                "ordering":  false,
                "ajax": {
                    "url": "/admin/getAdminAllUnpublishedBooks",
                    "type": "GET",
                    "data": function ( outData ) {
                        return outData;
                    },
                    dataFilter:function(inData){
                        // what is being sent back from the server (if no error)
                        return inData;
                    },
                    error:function(err, status){
                        // what error is seen(it could be either server side or client side.
                        console.log(err);
                    },
                },
            "columns": [{
                "data": "id",
            }, {
                "data": "title",
                "searchable": false,
                "orderable":false,
                "render": function (data, type, row) {
                    if ( row.title !=null) {
                        <% if("sage".equals(session["entryController"])){%>
                            return "<a href=\"/book-create?bookId=" + row.id + "&printBooks=${printBooks}\">" + row.title + "</a>";
                        <%}else{%>
                            return "<a href=\"/book-create-new?bookId=" + row.id + "&printBooks=${printBooks}\">" + row.title + "</a>";
                        <%}%>
                    }
                }
            },
                {
                    "data": "title",
                    "searchable": false,
                    "orderable":false,
                    "render": function (data, type, row) {
                        return "<a href=\"javascript:manageBookPrices("+row.id+");\">Edit price</a>"
                    }
                },

                {
                "data": "isbn"

                },
                <% if(session["siteId"].intValue()!=9){%>
                {
                    "data": "publisher",
                    "searchable": false,
                    "orderable":false,
                    "render": function (data, type, row) {

                        if ( row.publisher !=null) {
                            <% if("sage".equals(session["entryController"])){%>
                                return "<a href=\"/book-create?bookId=" + row.id + "&printBooks=${printBooks}\">" + row.publisher + "</a>";
                            <%}else{%>
                                return "<a href=\"/book-create-new?bookId=" + row.id + "&printBooks=${printBooks}\">" + row.publisher + "</a>";
                            <%}%>
                        }

                    }
                },

                <%}%>


                {
                    "data": "title",
                    "searchable": false,
                    "orderable":false,
                    "render": function (data, type, row) {
                        <% if(session["siteId"].intValue()!=9){%>
                        //return "<a href=\"library/"+row.title+"?siteName=books&bookId="+row.id+"\">View Books</a>";

                        <%if(printBooks){%>
                        return "<a href=\"wonderpublish/bookdtl?checkurl=true&siteName=${session['entryController']}&bookId="+row.id+"\">View Book</a>";
                        <%}else{%>

                        <% if("welcome".equals(session["entryController"]) || "ebouquet".equals(session["entryController"])){%>
                        return "<a href=\"library/"+encodeURIComponent(row.title).replace(/"/g,"&#39;")+"?checkurl=true&siteName=${session['entryController']}&bookId="+row.id+"\">View Book</a> &nbsp;&nbsp;   <a href=\"javascript:deleteBook("+row.id+")\">Delete Book</a>";
                        <%}else{%>
                        <%if("books".equals(siteMst.siteName)){%>
                        return "<a href=\"resources/ebook?checkurl=true&siteName=${session['entryController']}&bookId="+row.id+"\">View Book</a>"+
                            "&nbsp;<a href=\"wsshop/validityExtensionAdmin?bookId="+row.id+"\">Validity Price</a>";

                        <%}else{%>
                        return "<a href=\"library/"+encodeURIComponent(row.title).replace(/"/g,"&#39;")+"?checkurl=true&siteName="+siteName+"&bookId="+row.id+"\">View Book</a>";
                        <%}}%>

                        <%}%>

                        <%}%>
                        <%if(session["siteId"]!=null&&session["siteId"].intValue()==9){%>
                        return "<a href=\"/sage/instructorResourcesAdmin?siteName=${session['entryController']}&bookId="+row.id+"\">Instructor Resources</a>";
                        <%}%>
                    }

                },

                <%if(siteMst!=null&&"true".equals(siteMst.exercises)){%>
                {
                    "data": "title",
                    "searchable": false,
                    "orderable":false,
                    "render": function (data, type, row) {
                        return "<button type=\"button\" class=\"btn btn-primary\" data-toggle=\"modal\" onclick=\"generateToken("+row.id+")\" data-target=\"#downloadspopup\">Generate Scratch Code</button>";
                    }
                },
                <%}%>

            ],

            // "initComplete": function()
            // {
            //     $(".dataTables_filter input")
            //         .unbind() // Unbind previous default bindings
            //         .bind("input", function(e) { // Bind our desired behavior
            //             // If the length is 3 or more characters, or the user pressed ENTER, search
            //             if(this.value.length >= 3 || e.keyCode == 13) {
            //                 // Call the API search function
            //                 test.search(this.value).draw();
            //             }
            //             // Ensure we clear the search if they backspace far enough
            //             if(this.value == "") {
            //                 test.search("").draw();
            //             }
            //             return;
            //         });
            //
            // }
        });
        }
    });


    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        var differentTabClicked=false;
        var target = $(e.target).attr("id") // activated tab

        if(target=="tabMyPublished"&&!publishedBooksShown) {

            publishedBooksShown = true;
            tabToBeUpdated="published-books";
            <g:remoteFunction controller="admin" action="getWSPublishedMyBooks" onSuccess='populateTabWithBooksList(data)'></g:remoteFunction>
        }
    });

    function populateTabWithBooksList(data){

        var booksList = data.booksList;
        if(booksList!=null){
            var htmlStr=" <div  class='' style=\"min-height: calc(100vh - 156px);\">\n" +
                "<div class=\"container\">\n" +
                "                    <div class='col-md-12 main my-4 mx-0'>\n" +
                "                        <div class='py-4 pb-5 px-0'>\n" +
                "<div class='row'>\n" +
                "<div class='col-md-12 text-right px-5'>\n"
            <% if(!institutePublisher){%>
            htmlStr += "<a href='/wonderpublish/manageTabs' class='btn btn-primary light11 mr-2' role='button'>Manage Tabs</a>\n"
            <%}%>
            htmlStr +=    "<button type='button' class='btn btn-primary light11' onclick='addNewBook()'><i class='fa fa-file-text-o fa-x'></i> Create new book</button>\n"
                <% if(institutePublisher){%>
                htmlStr +="    <button type=\"button\" class=\"btn btn-primary light11 justify-content-end\" onclick=\"addPDFBook();\"><i class='fa fa-file-text-o fa-x'></i> Create pdf eBook </button>"

                <%}%>
            <%if(session["siteId"].intValue()==12 ||session["siteId"].intValue()==23||session["siteId"].intValue()==24){%>
                htmlStr+="<a href=\"/pubdesk/downloadBooksPublished\" class=\"btn btn-primary light11 justify-content-end ml-2\" role=\"button\">Download eBook Details</a>\n"
            <%}%>
            htmlStr+=
                "</div>" +
                "</div>\n";
            <%if(siteMst!=null&&"true".equals(siteMst.exercises)){%>
            htmlStr+=" <div class='row'>"+
            "</div>";
            <%}%>
            htmlStr+= "<div class=\"row\">\n" +
                "<h4 class='text-center w-100 mt-3 px-5'></h4>"+

                "                                <div class=\"d-flex justify-content-center col-md-12 px-5\"><table  class='evidya-Title table table-sm table-bordered table-hover' border=\"1\" width=\"100%\">\n" +
                // "<div class=\"row\"><br>\n"+
                // "<h4 style='float: right;margin-bottom:10px;'>Total Books: <strong>" + booksList.length + "</strong></h4>"+
                // "</div>\n"+
                "                                    <thead class='bg-primary text-white text-center'> <tr><th>Book Id</th> <th>Title</th><th>Manage Price</th>\n" ;
            // if(tabToBeUpdated=="admin-published-books"||tabToBeUpdated=="admin-unpublished-books"){
            //     htmlStr+= "   <th>Created By</th>\n";
            // }else{
            //     htmlStr+=      " <th>Status</th>\n";
            // }

            htmlStr += "<th>ISBN</th>\n";
            <% if(session["siteId"].intValue()!=9){%>
            htmlStr+= "                                          <th>Publisher</th>\n" +
                "                                        <th></th>\n" ;
            <%}%>
            <%if(session["siteId"]!=null&&session["siteId"].intValue()==9){%>
            htmlStr+= "                                        <th></th>\n" ;
            <%}%>
            <%if("true".equals(siteMst.exercises)){%>
            htmlStr+= "                                        <th>Book Scratch codes</th>\n" ;
            <%}%>
            htmlStr+= "                                    </tr></thead>\n" +
                "                                    <tbody>";
            for(i=0;i<booksList.length;i++){
                htmlStr+=" <tr>\n" +
                    " <td class='text-center'>"+booksList[i].id+"</td>\n";

                <% if("sage".equals(session["entryController"])){%>
                htmlStr+=" <td class=\"pub-buk\">&nbsp;<a href=\"/book-create?bookId="+booksList[i].id+"&printBooks=${printBooks}\">"+(booksList[i].title)+"</a></td>\n" ;
                <%}else{ %>
                htmlStr+=" <td class=\"pub-buk\">&nbsp;<a href=\"/book-create-new?bookId="+booksList[i].id+"&printBooks=${printBooks}\">"+(booksList[i].title)+"</a></td>\n" ;
                <%}%>
                htmlStr +="<td ><a href='javascript:manageBookPrices("+booksList[i].id+");'>Edit price</a></td>";
                htmlStr += "<td class='text-left'>"+booksList[i].isbn+"</td>";

                <% if(session["siteId"].intValue()!=9){%>


                <% if("sage".equals(session["entryController"])){%>
                htmlStr+=" <td class='text-left'>&nbsp;<a href=\"/book-create?bookId="+booksList[i].id+"&printBooks=${printBooks}\">"+booksList[i].publisher+"</a></td>\n";
                <%}else{ %>
                htmlStr+=" <td class='text-left'>&nbsp;<a href=\"/book-create-new?bookId="+booksList[i].id+"&printBooks=${printBooks}\">"+booksList[i].publisher+"</a></td>\n";
                <%}%>
                <%if(printBooks){%>
                htmlStr+= " <td class='text-center'><a href='/wonderpublish/bookdtl?checkurl=true&siteName=${session['entryController']}&bookId="+booksList[i].id+'>View book</a></td>\n";
                <%}else{%>
                <%if("books".equals(siteMst.siteName)){%>
                htmlStr+=       " <td class='text-center'><a href='/resources/ebook?checkurl=true&siteName=${session['entryController']}&bookId="+booksList[i].id+"'>View book</a>&nbsp;<a href=\"wsshop/validityExtensionAdmin?bookId="+booksList[i].id+"\">Validity Price</a>\n";

                <%}else{%>
                htmlStr+=       " <td class=\"text-center\"><a href=\"/library/"+encodeURIComponent(booksList[i].title).replace(/"/g,"&#39;")+"?checkurl=true&siteName="+siteName+"&bookId="+booksList[i].id+"\">View book</a></td>\n";
                <%}}}%>
                
                <%if(session["siteId"]!=null&&session["siteId"].intValue()==9){%>
                htmlStr+= "<td class='text-center'><a href='/sage/instructorResourcesAdmin?siteName=${session['entryController']}&bookId="+booksList[i].id+"'>Instructor Resources</a></td>\n";
                <%}%>
                <%if("true".equals(siteMst.exercises)){%>
                htmlStr+= " <td class='text-center'><button type='button' class='btn btn-primary' data-toggle='modal' onclick='generateToken("+booksList[i].id+")'  data-target='#downloadspopup'>Generate Scratch Code</button></td>";
                <%}%>
                htmlStr+= "</tr>";
            }
            htmlStr += "        </tbody>\n" +
                "                                </table></div>\n" +
                "\n" +
                "</div>\n" +
                "                            </div>\n" +
                "                        </div>\n" +
                "                    </div>\n" +
                "                </div>"+"" +
                ""
            document.getElementById("published-books").innerHTML=htmlStr;
            $('#published-books table').DataTable( {
                "ordering": false
            } );
        }
    }

    function addPDFBook() {
        window.location.href="/resources/createPdfBook";
    }

     function generateToken(bookIdj) {
         var numberOfCodes = document.querySelector('#downloadspopup input').value;
         bookIdhead = bookIdj;

     }
     function generateTokenwithId(bookIdj){
         bookIdhead = bookIdj;
         var numberOfCodes = document.getElementById('noOfTokens').value;
         var campaignName = document.getElementById('campaignName').value;
          if (numberOfCodes != '') {
             <g:remoteFunction controller="wsshop" action="checkAccessCodesExists" params="'numberOfCodes='+numberOfCodes+'&campaignName='+campaignName" onSuccess="downloadCodes(data)"></g:remoteFunction>
         }
     }

     function downloadCodes(data){
         $('#downloadspopup').modal('hide');
        if(data.status=="Ok") {
            var numberOfCodes = document.getElementById('noOfTokens').value;
            var campaignName = document.getElementById('campaignName').value;
            $('#loading-popup').modal('show');
            window.open(
                '/wsshop/downloadBookAccessCode?bookId=' + bookIdhead + '&numberOfCodes=' + numberOfCodes+'&campaignName='+campaignName,
                '_self'
            );
        }
        else{
            $('#contact-popup').modal('show');
        }
         clearinput();
     }


</script>
<g:render template="/bookPrice/priceManager"></g:render>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
</body>
</html>

