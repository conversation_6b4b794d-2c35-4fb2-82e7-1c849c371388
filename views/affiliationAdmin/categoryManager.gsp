<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<script>
    var loggedIn=false;
    $('link[data-role="baseline"]').attr('href', '');
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="page-main-wrapper mdl-js information-admin p-5">
    <div class="container-fluid">
        <div class="d-flex justify-content-start align-items-center">
            <h3><strong>Manage Wonderslate - Affiliation categories</strong></h3>
        </div>
        <div class="card card-modifier card-shadow border-0 mt-4 p-5">


            <div id="addInformation" class="mt-4 row justify-content-center align-items-start" >
                <div class="col-12">
                    <div class="form-row mb-4">

                        <div class="col-12 col-md-8" id="tagEditBox">
                            <label>Tag</label>
                            <div class="row">
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier">
                                        <g:select id="createLevel" class="form-control form-control-modifier border-bottom" name="createLevel" from="${levelsMstList}" optionKey="name" optionValue="name"
                                                  noSelection="['':'Select Level']" onchange="getCreateSyllabus(this.value)"/>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier">
                                        <select id="createSyllabus" class="form-control form-control-modifier border-bottom" name="createSyllabus"  onchange="getCreateGrade(this.value)" style="display: none"><option value="">Select Syllabus</option></select>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier">
                                        <select id="createGrade" class="form-control form-control-modifier border-bottom" name="createGrade"  onchange="getCreateSubject(this.value)" style="display: none"><option value="">Select Grade</option></select>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier">
                                        <select id="createSubject" class="form-control form-control-modifier border-bottom" name="createSubject"  onchange="" style="display: none"><option value="">Select Subject</option></select>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="form-row mb-4">

                        <div class="col-12 col-md-8" >
                            <label>Affiliation Categorories</label>
                            <div class="row">
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier">
                                        <g:select id="categoryType" class="form-control form-control-modifier border-bottom" name="categoryType" from="${topLevelCategories}" optionKey="category_type" optionValue="category_type"
                                                  noSelection="['':'Select Category']" onchange="getSecondLevelCategories(this.value)"/>
                                    </div>
                                </div>

                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier" >
                                        <select id="secondLevel" class="form-control form-control-modifier border-bottom" name="secondLevel"  onchange="getThirdLevel()" style="display: none"><option value="">Select Category</option></select>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier" >
                                        <select id="thirdLevel" class="form-control form-control-modifier border-bottom" name="thirdLevel"  onchange="getFourthLevel()" style="display: none"><option value="">Select Category</option></select>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier" >
                                        <select id="fourthLevel" class="form-control form-control-modifier border-bottom" name="fourthLevel"  onchange="getFifthLevel()" style="display: none"><option value="">Select Category</option></select>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier" >
                                        <select id="fifthLevel" class="form-control form-control-modifier border-bottom" name="fifthLevel"   style="display: none"><option value="">Select Category</option></select>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier" >
                                        <select id="filterOption" class="form-control form-control-modifier border-bottom" name="filterOption" onchange="getSellers()"  style="display: none">
                                            <option value="All">All</option>
                                            <option value="Updated">Updated</option>
                                            <option value="Notupdated">Not Updated</option></select>
                                    </div>
                                </div>
                                <div class=" col-md-2 pr-0">
                                <button class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect px-5" onclick='updateCategory()'>Update</button>
                                </div>




                            </div>
                        </div>
                        <div class="col-12 col-md-8" id="categoriesList">

                        </div>
                    </div>




                </div>
            </div>

        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>


    var el;
    function getCreateSyllabus(level){
        $('#createSyllabus option').remove();

        $('#createSyllabus').val();
        $('#createGrade').val();
        $('#createSyllabus').hide();
        $('#createGrade').hide();
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wonderpublish" action="getSyllabus"  onSuccess='initializeCreateSyllabus(data);'
        params="'level='+level"/>

    }
    function initializeCreateSyllabus(data){
        syllabusList = data.results;
        var select = document.getElementById("createSyllabus");
        select.options.length = 1;
        for(var i=0;i< syllabusList.length; i++) {
            el= document.createElement("option");
            el.textContent = syllabusList[i].syllabus;
            el.value = syllabusList[i].syllabus;
            select.appendChild(el);
        }
        //select.focus();
        $('#createSyllabus option:first').text('Select Syllabus').attr('value','');
        $('#createSyllabus').show().focus();
        $('.loading-icon').addClass('hidden');
    }
    function getCreateGrade(syllabus){
        $('#createGrade').hide();
        var length=13;
        var level = document.getElementById("createLevel");
        if("School"==level[level.selectedIndex].value&&syllabus!='NIOS'){
            var select = document.getElementById("createGrade");
            select.options.length = 1;
            for(var i=1;i< length; i++) {
                el = document.createElement("option");
                el.textContent = i;
                el.value = i;
                select.appendChild(el);
            }

            //select.focus();
            $('#createGrade').show().focus();
        }else{
            var seperate=true;
            for(var i=0;i< syllabusList.length; i++) {
                if(syllabus==syllabusList[i].syllabus){
                    if(syllabusList[i].gradeType=="Semester"){
                        seperate=false;
                        var select = document.getElementById("createGrade");
                        select.options.length = 1;
                        var startSemester=1;
                        var endSemester=8;
                        if(syllabusList[i].startSemester!=null) startSemester = syllabusList[i].startSemester;
                        if(syllabusList[i].endSemester!=null) endSemester = syllabusList[i].endSemester;
                        el = document.createElement("option");
                        el.textContent = 'All Semesters';
                        el.value = 'All Semesters';
                        select.appendChild(el);
                        for(var j=startSemester;j< (1+endSemester); j++) {

                            el = document.createElement("option");
                            el.textContent = 'Semester '+j;
                            el.value = 'Semester '+j;
                            select.appendChild(el);
                        }

                        //select.focus();
                        $('#createGrade').show().focus();
                    }
                    break;
                }
            }
            if(seperate){
                if(syllabus==='NIOS'){
                    $('#createGrade').html(" <option>Select</option>" +
                        "<option value=\"Open Basic Education\">Open Basic Education</option>\n" +
                        "    <option value=\"Secondary courses\">Secondary courses</option>\n" +
                        "    <option value=\"Open Basic Education\">Senior Secondary courses</option>\n" +
                        "    <option value=\"Vocational Courses\">Vocational Courses</option>\n" +
                        "    <option value=\"Diploma in Elementary Education (D.El.Ed)\">Diploma in Elementary Education (D.El.Ed)</option>");
                    $('#createGrade').show().focus();
                    //select.focus();
                }
                else {
                    $('.loading-icon').removeClass('hidden');
                    <g:remoteFunction controller="wonderpublish" action="getGrades"  onSuccess='initializeCreateGrades(data);'
        params="'syllabus='+syllabus"/>

                }
            }


        }


    }
    function initializeCreateGrades(data){
        var  grades = data.results;
        var select = document.getElementById("createGrade");
        select.options.length = 1;
        for(var i=0;i< grades.length; i++) {

            el = document.createElement("option");

            el.textContent = grades[i].grade+(grades[i].state!=null?" ( "+grades[i].state+" )":"");
            el.value = grades[i].grade;
            select.appendChild(el);
        }

        //select.focus();
        $('#createGrade').show().focus();
        $('.loading-icon').addClass('hidden');
    }


    function getCreateSubject(value){
        $('#createSubject').hide();
        var level = document.getElementById("createLevel");
        var syllabusList = document.getElementById("createSyllabus");
        var syllabus=syllabusList[syllabusList.selectedIndex].value;
        if("School"==level[level.selectedIndex].value) syllabus="School";
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wonderpublish" action="getSubjects"  onSuccess='initializeCreateSubjects(data);'
        params="'syllabus='+syllabus"/>
    }
    function initializeCreateSubjects(data){
        var subjects = data.results;
        var select = document.getElementById("createSubject");
        select.options.length = 1;
        for(var i=0;i<  subjects.length; i++) {
            el = document.createElement("option");
            el.textContent =  subjects[i].subject;
            el.value =  subjects[i].subject;
            select.appendChild(el);
        }
        //select.focus();
        $('#createSubject').show().focus();
        $('.loading-icon').addClass('hidden');
    }

    function getSecondLevelCategories(categoryType){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="affiliationAdmin" action="getSecondLevelCategories"  onSuccess='updateSecondLevelCategories(data);'
        params="'categoryType='+categoryType"/>
        $('#secondLevel').hide();
        $('#thirdLevel').hide();
        $('#fourthLevel').hide();
        $('#fifthLevel').hide();
    }

    function updateSecondLevelCategories(data){
        var  categoryList = data.secondLevelCategories;
        var select = document.getElementById("secondLevel");
        select.options.length = 1;
        for(var i=0;i< categoryList.length; i++) {
            el= document.createElement("option");
            el.textContent = categoryList[i].nodeName;
            el.value = categoryList[i].browseNodeId;
            select.appendChild(el);
        }
        //select.focus();
        $('#secondLevel option:first').text('Select Category').attr('value','');
        $('#secondLevel').show().focus();
        $('#filterOption').show();
        $('.loading-icon').addClass('hidden');
        document.getElementById("categoriesList").innerHTML="";

    }

    function getThirdLevel(){
        var browseNodeId = document.getElementById("secondLevel").value;
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="affiliationAdmin" action="getLevel2Categories"  onSuccess='displayThirdLevelCategories(data);'
        params="'browseNodeId='+browseNodeId"/>
        $('#thirdLevel').hide();
        $('#fourthLevel').hide();
        $('#fifthLevel').hide();
    }

    function displayThirdLevelCategories(data){
        var  categoryList = data.level2Categories;
        var select = document.getElementById("thirdLevel");
        select.options.length = 1;
        for(var i=0;i< categoryList.length; i++) {
            el= document.createElement("option");
            el.textContent = categoryList[i].nodeName;
            el.value = categoryList[i].browseNodeId;
            select.appendChild(el);
        }
        //select.focus();
        $('#thirdLevel option:first').text('Select Category').attr('value','');
        $('#thirdLevel').show().focus();
        $('.loading-icon').addClass('hidden');
        categoryMappings(categoryList);
    }

    function getFourthLevel(){
        var browseNodeId = document.getElementById("thirdLevel").value;
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="affiliationAdmin" action="getLevel3Categories"  onSuccess='displayFourthLevelCategories(data);'
        params="'browseNodeId='+browseNodeId"/>
        $('#fourthLevel').hide();
        $('#fifthLevel').hide();
    }

    function displayFourthLevelCategories(data){
        var  categoryList = data.level3Categories;
        var select = document.getElementById("fourthLevel");
        select.options.length = 1;
        for(var i=0;i< categoryList.length; i++) {
            el= document.createElement("option");
            el.textContent = categoryList[i].nodeName;
            el.value = categoryList[i].browseNodeId;
            select.appendChild(el);
        }
        //select.focus();
        $('#fourthLevel option:first').text('Select Category').attr('value','');
        $('#fourthLevel').show().focus();
        $('.loading-icon').addClass('hidden');
        categoryMappings(categoryList);
    }
    function getFifthLevel(){
        var browseNodeId = document.getElementById("fourthLevel").value;
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="affiliationAdmin" action="getLevel4Categories"  onSuccess='displayFifthLevelCategories(data);'
        params="'browseNodeId='+browseNodeId"/>
        $('#fifthLevel').hide();
    }

    function displayFifthLevelCategories(data){
        var  categoryList = data.level4Categories;
        var select = document.getElementById("fifthLevel");
        select.options.length = 1;
        for(var i=0;i< categoryList.length; i++) {
            el= document.createElement("option");
            el.textContent = categoryList[i].nodeName;
            el.value = categoryList[i].browseNodeId;
            select.appendChild(el);
        }
        //select.focus();
        $('#fifthLevel option:first').text('Select Category').attr('value','');
        $('#fifthLevel').show().focus();
        $('.loading-icon').addClass('hidden');
        categoryMappings(categoryList);
    }

    function updateCategory(){
        $('.loading-icon').removeClass('hidden');
        var categoryLevel;
        var browseNodeId;
        //let's get the browseNodeId first
        if(document.getElementById("fifthLevel").selectedIndex>0){
            categoryLevel = "4";
            browseNodeId = document.getElementById("fifthLevel").value;
        }
        else if(document.getElementById("fourthLevel").selectedIndex>0){
            categoryLevel = "3";
            browseNodeId = document.getElementById("fourthLevel").value;
        }
        else if(document.getElementById("thirdLevel").selectedIndex>0){
            categoryLevel = "2";
            browseNodeId = document.getElementById("thirdLevel").value;
        }
        else if(document.getElementById("secondLevel").selectedIndex>0){
            categoryLevel = "1";
            browseNodeId = document.getElementById("secondLevel").value;
        }
        var level=document.getElementById("createLevel").value;
        var syllabus=document.getElementById("createSyllabus").value;
        var grade=document.getElementById("createGrade").value;
        var subject=document.getElementById("createSubject").value;

        <g:remoteFunction controller="affiliationAdmin" action="updateAffiliationCategory"
                    params="
                    'level='+level+
                    '&syllabus='+syllabus+
                    '&grade='+grade+
                    '&subject='+subject+
                    '&categoryLevel='+categoryLevel+
                    '&browseNodeId='+browseNodeId
                     "
                   onSuccess="updateSuccess(data)"/>

    }

    function updateSuccess(data){
        $('.loading-icon').addClass('hidden');
        alert(data.status);
    }

    function categoryMappings(categories){
        var htmlSrc = "<table cellspacing='1' cellpadding='1' border='1'><tr><th>Affliation Category</th><th>Wonderslate mapping</th></tr>";
        for(var i=0;i<categories.length;i++){
            htmlSrc+="<tr><td>"+categories[i].nodeName+"</td><td> "+categories[i].mappedCategory+"</td></tr>";
        }
        htmlSrc+="</table>"
        document.getElementById("categoriesList").innerHTML = htmlSrc;
    }

</script>



</body>
</html>
