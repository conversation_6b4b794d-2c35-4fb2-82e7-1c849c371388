<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<script>
    var loggedIn=false;
    $('link[data-role="baseline"]').attr('href', '');
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="page-main-wrapper mdl-js information-admin p-5">
    <div class="container-fluid">
        <div class="d-flex justify-content-start align-items-center">
            <h3><strong>Seller Information Admin</strong></h3>
        </div>
        <div class="card card-modifier card-shadow border-0 mt-4 p-5">


            <div id="addInformation" class="mt-4 row justify-content-center align-items-start" >
                <div class="col-12">
                    <div class="form-row mb-4">

                        <div class="col-12 col-md-8" id="tagEditBox">
                            <label>Category</label>
                            <div class="row">
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier">
                                        <g:select id="categoryType" class="form-control form-control-modifier border-bottom" name="categoryType" from="${topLevelCategories}" optionKey="category_type" optionValue="category_type"
                                                  noSelection="['':'Select Category']" onchange="getSecondLevelCategories(this.value)"/>
                                    </div>
                                </div>

                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier" >
                                        <select id="browseNodeId" class="form-control form-control-modifier border-bottom" name="createSyllabus"  onchange="getSellers()" style="display: none"><option value="">Select Category</option></select>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier" >
                                        <select id="filterOption" class="form-control form-control-modifier border-bottom" name="filterOption" onchange="getSellers()"  style="display: none">
                                            <option value="All">All</option>
                                            <option value="Updated">Updated</option>
                                            <option value="Notupdated">Not Updated</option></select>
                                    </div>
                                </div>



                            </div>
                        </div>
                        <div class="col-12 col-md-8" id="sellers">

                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>
    function getSecondLevelCategories(categoryType){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="affiliationAdmin" action="getSecondLevelCategories"  onSuccess='updateSecondLevelCategories(data);'
        params="'categoryType='+categoryType"/>
    }

    function updateSecondLevelCategories(data){
         var  categoryList = data.secondLevelCategories;
        var select = document.getElementById("browseNodeId");
        select.options.length = 1;
        for(var i=0;i< categoryList.length; i++) {
            el= document.createElement("option");
            el.textContent = categoryList[i].nodeName;
            el.value = categoryList[i].browseNodeId;
            select.appendChild(el);
        }
        //select.focus();
        $('#browseNodeId option:first').text('Select Category').attr('value','');
        $('#browseNodeId').show().focus();
        $('#filterOption').show().focus();
        $('.loading-icon').addClass('hidden');
    }

    function getSellers(browseNodeId){
        var browseNodeId = document.getElementById("browseNodeId").value;
        $('.loading-icon').removeClass('hidden');
        var filterOption=document.getElementById("filterOption").value;
        <g:remoteFunction controller="affiliationAdmin" action="getSellersForCategory"  onSuccess='updateSellersInformation(data);'
        params="'browseNodeId='+browseNodeId+'&filterOption='+filterOption"/>
    }

    function updateSellersInformation(data){

        var sellers = data.sellers;
        var htmlStr ="";
        if(sellers.length==0){
            htmlStr="<b>No Sellers found for this category</b>";
        }else {
            for (var i = 0; i < sellers.length; i++) {
                htmlStr += "<br> <div class='row'> <div class=\" col-md-2 pr-0\">" + sellers[i].name + "</div>" +
                    "  <div class=\" col-md-8 pr-0\"><textarea class=\"form-control\" id=\"sellerDesc_" + sellers[i].id + "\"  rows=\"4\">" + (sellers[i].description!=null?sellers[i].description:"") + "</textarea></div>" +
                    " <div class=\" col-md-2 pr-0\"><br><br><button class=\"btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect px-5\" onclick=\"updateSeller(" + sellers[i].id + ")\">SAVE</button>" +
                    "<br><a href='/printbooks/index?pubId="+sellers[i].id+"' target='_blank'>View</a> </div></div>";

            }
        }
        document.getElementById("sellers").innerHTML=htmlStr;
        $('.loading-icon').addClass('hidden');

    }

    function updateSeller(sellerId){
        $('.loading-icon').removeClass('hidden');
           var description = document.getElementById("sellerDesc_"+sellerId).value;
        <g:remoteFunction controller="affiliationAdmin" action="updateSellerDescription"  onSuccess='sellerUpdateConfirmation(data);'
        params="'sellerId='+sellerId+'&description='+description"/>
    }

    function sellerUpdateConfirmation(data){
        $('.loading-icon').addClass('hidden');
        alert(data.status);
    }
</script>
