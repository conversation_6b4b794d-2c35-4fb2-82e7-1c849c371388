<!DOCTYPE html>
<html>

<head>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <asset:javascript src="jquery-1.11.2.min.js"/>
    <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.9.2/jquery-ui.min.js"></script>

<asset:stylesheet href="flipbook.style.css"/>
<asset:stylesheet href="font-awesome.css"/>

<asset:javascript src="pageflip/flipbook.min.js"/>

    <script type="text/javascript">

        $(document).ready(function () {
            $("#container").flipBook({
                pdfUrl:"/funlearn/download?id=304",
            });

        })
    </script>
</head>

<body>
<div id="container">
    <p>Real 3D Flipbook has lightbox feature - book can be displayed in the same page with lightbox effect.</p>
    <p>Click on a book cover to start reading.</p>
    <img src="/assets/book2/thumb1.jpg" />
</div>
</body>

</html>
