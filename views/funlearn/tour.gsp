
<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>

<g:render template="navheader"></g:render>





<!--End Of Navbar-->




  <div class="container main-shadow">
    <div class="demo-headline">
      <!--<h1 id="hopscotch-title" class="demo-logo">Wonderslate</h1>-->
    </div>

    <div id="content" class="tour-page">
      <!--<h1 id="what-is">What is Wonderslate?</h1>-->
      <!--<p>Wonderslate – Your Study Buddy, is a platform that thrives to bring a new edge to education. It nurtures a social-collaborative approach to address the needs of educational content in the overall picture of academia.</p>-->

      <p class="text-center"><button id="startTourBtn" class="btn btn-large btn-primary">Take a tour</button></p>

      <br>
      <h3 id="general-usage">General Usage</h3>
      <p id="general-use-desc">
      To get started, Sign in with your existing account or create your new account by clicking Register.
      </p>
      <br>  
      <!-- <pre><code> -->  
        <div class="row imageSize">
          <div class="col-md-8 col-md-offset-2 tour-shadow">
            <span id="register-popover">
            <img src="${assetPath(src: 'tour/register.png')}"/>
            </span>
          </div>
        </div>
        <br>
        <div class="row imageSize">  
          <div class="col-md-8 col-md-offset-2 tour-shadow">
            <span id="signIn-popover">
            <img src="${assetPath(src: 'tour/signIn.png')}">
            </span>
          </div>
        </div>
     
      <!-- </code></pre> -->

      <br><br>

      <p><b>Start your tour from <code id="profile-popover">Home</code> page</b></p>
      <br>
        <div class="row imageSize">
          <div class="col-md-8 col-md-offset-2 tour-shadow">
          <span id="editProfile-popover">
            <img src="${assetPath(src: 'tour/profile.png')}">
          </span>    
          </div>
        </div>
        <br>
        <div class="row imageSize">  
          <div class="col-md-8 col-md-offset-2 tour-shadow">  
            <img src="${assetPath(src: 'tour/editProfile.png')}"> 
          </div>  
        </div>

      <br><br>

      <h3 id="topics-body">Topics</h3>

      <p>Choose from <code>Video</code> <code>Mind Map</code> <code>Notes</code> <code>Quiz</code></p>
      <br>
      <div class="row imageSize">
          <div class="col-md-8 col-md-offset-2 tour-shadow">
          <span id="start-tour-popover">
            <img src="${assetPath(src: 'tour/topics.png')}">
          </span>    
          </div>
      </div>

      <br><br>    

      <h3 id="basic-step-options">Quiz</h3>

      <!-- <p>The step options below are the most basic options.</p> -->

      <p>You can create, solve, learn, challenge and have fun with Quiz.</p>
      <br>
        <div class="row imageSize">
          <div class="col-md-8 col-md-offset-2 tour-shadow">
          <span id="video-popover">
            <img src="${assetPath(src: 'tour/videos.png')}">
          </span>  
          </div>
        </div>
        <br>
        <div class="row imageSize">
          <div class="col-md-8 col-md-offset-2 tour-shadow">  
            <img src="${assetPath(src: 'tour/profile.png')}">  
          </div>  
        </div>

        <br><br>
      

      <h3 id="add-body">Add</h3>
      <p>Wonderslate will give you options to add your quiz types like <code>Fill in the Blanks</code>, <code>Mind Map</code>, <code>Multiple Choice Questions</code> & many more things!</p>
      <br>
      <!-- <pre><code> -->
        <div class="row imageSize">
            <div class="col-md-8 col-md-offset-2 tour-shadow">
            <span id="add-popover">
              <img src="${assetPath(src: 'tour/addingNotes.png')}">
            </span>
            </div>
        </div>
        <br>
        <div class="row imageSize">
            <div class="col-md-8 col-md-offset-2 tour-shadow">  
              <img src="${assetPath(src: 'tour/notes.png')}"> 
            </div>  
        </div>
      <!-- </code></pre> -->
        
      <br><br>

      <h3 id="add-chapter-topic-body">Add Chapter/Topic</h3>
      <p>
         Here you can add a specific topic of a specific subject, belonging to a particular board of education, to your profile. To add, a simple click on Add Chapter/Topic and following the instructions are all you need.
      </p>
      <br>
        <div class="row imageSize">
            <div class="col-md-8 col-md-offset-2 tour-shadow">
              <img src="${assetPath(src: 'tour/add-chapter-topic-body1.png')}">
            </div>
        </div>
        <br>
        <div class="row imageSize">
            <div class="col-md-8 col-md-offset-2 tour-shadow">
            <span id="add-chapter-topic-popover">  
              <img src="${assetPath(src: 'tour/add-chapter-topic-body2.png')}">
            </span>  
            </div>  
        </div>

      <br><br>

      <h3 id="my-library-body">My Library</h3>
      <p id="my-library-popover">
        In <code>My Library</code> you'll find all your favourite study materials and the ones you have added.
      </p>

      <br><br>

      <h3 id="find-friends-body">Find Friends</h3>
      <p>
        In Wonderslate you'll be able to connect with your friends. Find them & send requests. 
      </p>
      <br>
        <div class="row imageSize">
            <div class="col-md-8 col-md-offset-2 tour-shadow">
            <span id="find-friends1-popover">
              <img src="${assetPath(src: 'tour/find-friends1.png')}">
            </span>
            </div>
        </div>
        <br>
        <div class="row imageSize">
            <div class="col-md-8 col-md-offset-2 tour-shadow">
            <span id="find-friends2-popover">  
              <img src="${assetPath(src: 'tour/find-friends2.png')}">
            </span>  
            </div>  
        </div>

      <br><br>
      
      <h3 id="notifications-body">Notifications</h3>
      <p>In your Wonderslate profile you'll also get an option of notification where you'll get all the notifications like Friend Requests, Likes etc.</p>
        <br>
        <div class="row imageSize">
          <div class="col-md-8 col-md-offset-2 tour-shadow">
          <span id="notifications-popover">
            <img src="${assetPath(src: 'tour/notifications.png')}">
          </span>
          </div> 
        </div>

        <br><br>

      <h3 id="groups-body">Groups</h3>
        <p>Create your own group with your friends so that you can communicate and share study materials with them</p>
        <br>
        <div class="row imageSize">
          <div class="col-md-8 col-md-offset-2 tour-shadow">
          <span id="groups-popover">
            <img src="${assetPath(src: 'tour/groups1.png')}">
          </span>
          </div>
        </div>
        <br>
        <div class="row imageSize">
          <div class="col-md-8 col-md-offset-2 tour-shadow">
          <span id="create-groups-popover"> 
            <img src="${assetPath(src: 'tour/groups2.png')}">
          </span> 
          </div>  
        </div>

      <br><br><br><br>  
      <hr>
      <div class="row row-centered">

            <div class="col-md-12 homefeatureList">
                <span id="hopscotch-title">
                <a class='btn btn-lg btn-primary' type='button' href="javascript:showregister('signup');">Sign up ( It's FREE <i class="fa fa-smile-o fa-x"> </i> )</a>
                </span>
            </div>
            
        </div>
      <hr>  
      <br><br><br><br>
      
    </div>
    
 </div>   



    <g:render template="footer"></g:render>
<!--</div>-->

<!--<asset:javascript src="searchContents.js"/>-->
<!--<asset:javascript src="quiz.js"/>-->
<!--<asset:javascript src="topic.js"/>-->
<asset:javascript src="jquery-1.10.2.js"/>
<asset:javascript src="jquery-1.11.2.min.js"/>
<asset:javascript src="hopscotch-0.1.1.js"/>
<asset:javascript src="demo.js"/>
<asset:javascript src="bootstrap.min.js"/>
<asset:javascript src="addcontents.js"/>
<asset:javascript src="clock.js"/>



<!--<%
    if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ ////%>
////
////<asset:javascript src="analytics.js"/>
<% }%>-->
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1){ %>
<asset:javascript src="analytics.js"/>
<% } %>   
</body>
</html>