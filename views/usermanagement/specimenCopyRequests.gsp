<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">

<style>
.dataTables_paginate ul.pagination .page-link {
    font-size: 15px;
}
.dataTables_paginate ul.pagination .page-link:not(:disabled):not(.disabled) {
    color: #212529;
}
.dataTables_paginate ul.pagination .page-link:focus {
    box-shadow: 0 0 0 0.1rem rgba(0,123,255,.25);
}
.dataTables_paginate ul.pagination .page-item.active .page-link {
    background-color: #007bff !important;
}
#jwVideoModalData p {
    font-size: 15px;
}
#jwVideoModalData p strong {
    font-weight: 500;
}
p{
    margin-bottom: 0!important;

}
.btn-viewDtl{
    background: #F79420;
}
</style>

<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid my-5 px-2 px-lg-5" style="min-height: calc(100vh - 160px);">
    <div class='row m-0 px-2 px-lg-5'>
        <div class='col-md-12 main p-2 p-lg-4' style=" margin: 40px auto; float: none;">
            <h2 class="text-center">REQUESTS FOR SPECIMEN COPY</h2>
            <div id="content-books">
                <table id="nomineeData" class='table table-hover table-bordered w-100' style="display: none;">
                    <thead>
                    <tr class="bg-primary text-white">
                        <th>Photo ID </th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Mobile</th>
                        <th>Address</th>
                        <th>Requested Book details</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>

<g:render template="/books/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script>

    function getData(data){
        data.reverse();
        $('.loading-icon').removeClass('hidden');
        if ($.fn.dataTable.isDataTable('#nomineeData')) {
            $('#nomineeData').DataTable().destroy();
        }
        $('#nomineeData').show();
        $('#nomineeData').DataTable({
            "bRetrieve": true,
            "searching": true,
            "ordering":  false,
            'data':data,
            'columns': [
                {
                    'data': 'id',
                    'render': function (data, type, row) {
                        if(row.image!=null) {
                            return "<img style='width:130px;height: 130px;object-fit: contain' src=\"/usermanagement/showTeachersID?fileName=" + row.image + "&id=" + row.id + "\">";
                        }else{
                            return "<p>No image found</p>";
                        }
                    }
                },
                {
                    'data': 'userName',
                    'render':function (data, type, row) {
                        return  "<p><strong>"+row.name+"</strong></p>\n";
                    }
                },
                {
                    'data': 'email',
                    'render':function (data, type, row) {
                        return  "<p><strong>"+row.email+"</strong></p>\n";
                    }
                },
                {
                    'data': 'mobile',
                    'render':function (data, type, row) {
                        return  "<p><strong>"+row.phone+"</strong></p>\n";
                    }
                },
                {
                    'data': 'address',
                    'render':function (data, type, row) {
                        return  "<p><strong>"+row.address+"</strong></p>\n";
                    }
                },
                {
                    'data': 'bookDetails',
                    'render':function (data, type, row) {
                        return  "<p>Publisher: <strong>"+row.publisher+"</strong></p>\n"+
                            "<p>Level: <strong>"+row.level+"</strong></p>\n"+
                            "<p>Syllabus : <strong>"+row.syllabus+"</strong></p>\n"+
                            "<p>Grade : <strong>"+row.grade+"</strong></p>\n"+
                            "<p>Subject : <strong>"+row.subject+"</strong></p>\n";
                    }
                },
                {
                    'data': 'status',
                    'render':function (data, type, row) {
                        var createdDate = Number(row.dateCreated)
                        var options = {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            hour12: false
                        };

                        createdDate = new Date(createdDate)
                        var currentDateTimeString = createdDate.toLocaleString('en-US', options);

                        if (row.dateCreated!=""){
                            var tempDate = currentDateTimeString.split(',')[0].split('/')
                            tempDate = tempDate[1]+"-"+tempDate[0]+"-"+tempDate[2]  + ","+currentDateTimeString.split(',')[1];
                            return  "<p><strong>"+tempDate+"</strong></p>\n";
                        }else{
                            return  "<p><strong></strong></p>\n";
                        }

                    }
                },
                {
                    'data': 'status',
                    'render':function (data, type, row) {
                        return  "<p><strong>"+row.status+"</strong></p>\n";
                    }
                },
                {
                    'data': 'status',
                    'render':function (data, type, row) {
                        if(row.status == 'Copy Not Sent'){
                            return  "<button  class='btn btn-primary' onclick='changeStatus("+row.id+")'>Copy Sent</button>\n";
                        }else{
                            return  "<button class='btn btn-primary' onclick='changeStatus("+row.id+")' disabled>Copy Sent</button>\n";
                        }

                    }
                },
            ],
        })

        $('.loading-icon').addClass('hidden');
    }



    // var table = $('#nomineeData').DataTable();


    function getList(){
        <g:remoteFunction controller="usermanagement" action="getSpecimenCopyReq" onSuccess="getData(data)" />
    }
    getList();

    function changeStatus(id){
        <g:remoteFunction controller="usermanagement" action="changeReqStatus"  params="'id='+id" onSuccess="statusChanged(data)" />
    }
    function statusChanged(data){
        alert("STATUS UPDATED")
        window.location.reload();
    }
</script>


</body>
</html>
