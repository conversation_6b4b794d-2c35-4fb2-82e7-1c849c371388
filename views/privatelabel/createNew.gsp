<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<style>
input.form-control[readonly] {
    cursor: not-allowed;
}
input.form-control:focus {
    outline: 0;
    box-shadow: none;
    border-color: #212121;
}
input[type=text] {
    border-color: #EEE;
}
input[type=color] {
    border-color: #EEE;
    cursor: pointer;
}
input[type=file],
input[type=file]::-webkit-file-upload-button {
    border-style: dashed;
    cursor: pointer;
    font-size: 12px;
}
input[type=file]::file-selector-button {
    margin-right: 20px;
    border: none;
    background: #333333;
    padding: 3px 15px;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    transition: background .2s ease-in-out;
    font-size: 10px;
}
input[type=file]::file-selector-button:hover {
    background: #212121;
}
.form-group img {
    border: 1px solid #EEE;
    border-radius: 5px;
    padding: 3px;
    margin-right: 5px;
}
</style>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js mt-5 whitelabel-creation">
    <div class="container">
        <div class="d-flex justify-content-start align-items-center">
            <h4>Create New Privatelabel</h4>
            <a href="/privatelabel/allWebsites" class="btn btn-primary btn-primary-modifier shadow-sm px-3 ml-4">Show All Websites</a>
        </div>
        <div class="card card-modifier card-shadow border-0 mt-3 p-4 p-lg-5">

            <form enctype="multipart/form-data" role="form" name="createWebsite" id="createWebsite" action="/privatelabel/createPrivatelabel" onsubmit="return creationSubmit();" method="post">

                <div class="mb-4 alert alert-info border-0">
                    <h5 class="mb-0 font-weight-normal">Basic Details <small class="text-danger"><i>(Mandatory)</i></small></h5>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="websiteId">Website Id</label>
                        <input type="text" class="form-control" name="websiteId" id="websiteId" value="${siteDtl != null ? siteDtl.siteId : ""}" autocomplete="off" ${siteDtl != null ? "readonly" : ""}>
                    </div>
                    <div class="form-group col mb-3">
                        <label for="themeColor">Theme Colour</label>
                        <div class="input-group">
                            <input type="text" class="form-control text-uppercase" id="hexCode" value="${siteDtl != null ? siteDtl.themeColor : "#000000"}" autocomplete="off">
                            <div class="input-group-append col-2 px-0">
                                <input type="color" class="form-control p-1" name="themeColor" id="themeColor" value="${siteDtl != null ? siteDtl.themeColor : "#000000"}" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="form-group col mb-3">
                        <label for="faviconImg">Favicon <small class="text-black-50"><i>(Recommended size 32x32px)</i></small></label>
                        <input type="file" class="form-control ${siteDtl != null ? "d-none" : ""}" name="favicon" id="faviconImg" value="${siteDtl != null ? siteDtl.favicon : ""}" accept="image/*">
                        <% if (siteDtl != null) { %>
                            <div id="selectedFavicon">
                                <img src="/privatelabel/showPrivatelabelImage?siteId=${siteDtl.siteId}&fileName=${siteDtl.favicon}" width="50" height="50">
                                <a href="javascript:changeFaviconSelector();">Change</a>
                            </div>
                        <% } %>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="logoImg">Logo</label>
                        <input type="file" class="form-control ${siteDtl != null ? "d-none" : ""}" name="logo" id="logoImg" value="${siteDtl != null ? siteDtl.logo : ""}" accept="image/*">
                        <% if (siteDtl != null) { %>
                        <div id="selectedLogo">
                            <img src="/privatelabel/showPrivatelabelImage?siteId=${siteDtl.siteId}&fileName=${siteDtl.logo}" width="100" height="50">
                            <a href="javascript:changeLogoSelector();">Change</a>
                        </div>
                        <% } %>
                    </div>
                    <div class="form-group col mb-3">
                        <label for="logoIcon">Logo Icon</label>
                        <input type="file" class="form-control ${siteDtl != null ? "d-none" : ""}" name="logoIcon" id="logoIcon" value="${siteDtl != null ? siteDtl.logoIcon : ""}" accept="image/*">
                        <% if (siteDtl != null) { %>
                        <div id="selectedLogoIcon">
                            <img src="/privatelabel/showPrivatelabelImage?siteId=${siteDtl.siteId}&fileName=${siteDtl.logoIcon}" width="100" height="50">
                            <a href="javascript:changeLogoIconSelector();">Change</a>
                        </div>
                        <% } %>
                    </div>
                    <div class="form-group col mb-3">
                        <label for="bannerImg">Default Banner <small class="text-black-50"><i>(Recommended size 1920x535px)</i></small></label>
                        <input type="file" class="form-control ${siteDtl != null ? "d-none" : ""}" name="banner" id="bannerImg" value="${siteDtl != null ? siteDtl.bannerImage : ""}" accept="image/*">
                        <% if (siteDtl != null) { %>
                        <div id="selectedBanner">
                            <img src="/privatelabel/showPrivatelabelImage?siteId=${siteDtl.siteId}&fileName=${siteDtl.bannerImage}" width="200" height="50">
                            <a href="javascript:changeBannerSelector();">Change</a>
                        </div>
                        <% } %>
                    </div>
                </div>

                <div class="text-danger text-danger-modifier my-2 mb-0 py-1">
                    <i><b>Note:</b> All the image files name must not contain any space or special characters.</i>
                </div>

                <div class="mb-4 mt-5 alert alert-info border-0">
                    <h5 class="mb-0 font-weight-normal">Contact Information</h5>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="companyName">Company Name</label>
                        <input type="text" class="form-control" name="companyName" id="companyName" value="${siteDtl != null ? siteDtl.companyName : ""}" autocomplete="off">
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="addressLine1">Address Line 1</label>
                        <input type="text" class="form-control" name="addressLine1" id="addressLine1" value="${siteDtl != null ? siteDtl.addressLine1 : ""}" autocomplete="off">
                    </div>
                    <div class="form-group col mb-3">
                        <label for="addressLine2">Address Line 2</label>
                        <input type="text" class="form-control" name="addressLine2" id="addressLine2" value="${siteDtl != null ? siteDtl.addressLine2 : ""}" autocomplete="off">
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="gstNumber">GST Number</label>
                        <input type="text" class="form-control" name="gstNumber" id="gstNumber" value="${siteDtl != null ? siteDtl.gstNumber : ""}" autocomplete="off">
                    </div>
                    <div class="form-group col mb-3">
                        <label for="websiteLink">Website Link</label>
                        <input type="text" class="form-control" name="websiteLink" id="websiteLink" value="${siteDtl != null ? siteDtl.websiteLink : ""}" autocomplete="off">
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="mobileNumber">Mobile Number</label>
                        <input type="text" class="form-control" name="mobileNumber" id="mobileNumber" value="${siteDtl != null ? siteDtl.mobileNumber : ""}" autocomplete="off">
                    </div>
                    <div class="form-group col mb-3">
                        <label for="emailAddress">Email Address</label>
                        <input type="text" class="form-control" name="emailAddress" id="emailAddress" value="${siteDtl != null ? siteDtl.emailAddress : ""}" autocomplete="off">
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3 d-flex align-items-center">
                        <label for="mobileNumber" class="mb-0 mr-4">Enable Contact Us Page</label>
                        <input type="checkbox" class="form-control" style="width: 20px" name="enableContactusCheck" id="enableContactusCheck" >
                        <input type="hidden" name="enableContactus" id="enableContactus">
                    </div>
                    <div class="form-group col mb-3 d-flex align-items-center">
                        <label for="loginMethod" class="mb-0 mr-4">Enable OTP Only Login</label>
                        <input type="checkbox" class="form-control" style="width: 20px" name="loginMethod" id="loginMethod" >
                        <input type="hidden" name="enableOTPLogin" id="enableOTPLogin">
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3 d-flex align-items-center">
                        <label for="landingPage" class="mb-0 mr-4">Enable Landing Page</label>
                        <input type="checkbox" class="form-control" style="width: 20px" name="landingPage" id="landingPage" >
                        <input type="hidden" name="showLandingPage" id="showLandingPage">
                    </div>
                </div>

                <div class="row">
                    <div class="form-group col mb-3 d-flex align-items-center">
                        <label class="mb-0 mr-4">Custom GPT Loader</label>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="enableGptLoader" id="customGptLoaderYes" value="Yes">
                            <label class="form-check-label" for="customGptLoaderYes">Yes</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="enableGptLoader" id="customGptLoaderNo" value="No" checked>
                            <label class="form-check-label" for="customGptLoaderNo">No</label>
                        </div>
                        <input type="hidden" name="customGptLoader" id="customGptLoader">
                    </div>
                </div>
                <div class="row d-none" id="displayNameContainer">
                    <div class="form-group col-6 mb-3">
                        <label for="gptLoaderDisplayName">Display Name</label>
                        <input type="text" class="form-control" name="gptLoaderDisplayName" id="gptLoaderDisplayName" placeholder="Ex. iBookGPT" value="${siteDtl != null ? siteDtl.gptLoaderDisplayName : ""}" autocomplete="off">
                    </div>
                    <div class="form-group col-6 mb-3">
                        <label for="gptLoaderPath">GPT Loader</label>
                        <input type="file" class="form-control" name="gptLoaderPath" id="gptLoaderPath" value="${siteDtl != null ? siteDtl.gptLoaderPath : ""}" accept="image/*">
                        <% if (siteDtl!=null&&siteDtl.gptLoaderPath != null) { %>
                        <div id="selectedGPTLoader">
                            <img src="/privatelabel/showPrivatelabelImage?siteId=${siteDtl.siteId}&fileName=${siteDtl.gptLoaderPath}" width="50" height="50">
                            <a href="javascript:changeGPTLoaderSelector();">Change</a>
                        </div>
                        <% } %>
                    </div>
                </div>

                <div class="mb-4 mt-5 alert alert-info border-0">
                    <h5 class="mb-0 font-weight-normal">Social Media Links</h5>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="facebookLink">Facebook</label>
                        <input type="text" class="form-control" name="facebookLink" id="facebookLink" value="${siteDtl != null ? siteDtl.facebookLink : ""}" autocomplete="off">
                    </div>
                    <div class="form-group col mb-3">
                        <label for="twitterLink">Twitter</label>
                        <input type="text" class="form-control" name="twitterLink" id="twitterLink" value="${siteDtl != null ? siteDtl.twitterLink : ""}" autocomplete="off">
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="instagramLink">Instagram</label>
                        <input type="text" class="form-control" name="instagramLink" id="instagramLink" value="${siteDtl != null ? siteDtl.instagramLink : ""}" autocomplete="off">
                    </div>
                    <div class="form-group col mb-3">
                        <label for="linkedinLink">Linkedin</label>
                        <input type="text" class="form-control" name="linkedinLink" id="linkedinLink" value="${siteDtl != null ? siteDtl.linkedinLink : ""}" autocomplete="off">
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="youtubeLink">Youtube</label>
                        <input type="text" class="form-control" name="youtubeLink" id="youtubeLink" value="${siteDtl != null ? siteDtl.youtubeLink : ""}" autocomplete="off">
                    </div>
                    <div class="form-group col mb-3">
                        <label for="whatsappLink">WhatsApp</label>
                        <input type="text" class="form-control" name="whatsappLink" id="whatsappLink" value="${siteDtl != null ? siteDtl.whatsappLink : ""}" autocomplete="off">
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="telegramLink">Telegram</label>
                        <input type="text" class="form-control" name="telegramLink" id="telegramLink" value="${siteDtl != null ? siteDtl.telegramLink : ""}" autocomplete="off">
                    </div>
                    <div class="form-group col mb-3"></div>
                </div>

                <div class="mb-4 mt-5 alert alert-info border-0">
                    <h5 class="mb-0 font-weight-normal">Footer Links</h5>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="privacyPolicy">Privacy Policy</label>
                        <input type="text" class="form-control" name="privacyPolicy" id="privacyPolicy" value="${siteDtl != null ? siteDtl.privacyPolicy : ""}" autocomplete="off">
                    </div>
                    <div class="form-group col mb-3">
                        <label for="termsCondition">Terms & Conditions</label>
                        <input type="text" class="form-control" name="termsCondition" id="termsCondition" value="${siteDtl != null ? siteDtl.termsCondition : ""}" autocomplete="off">
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="playStore">Play Store</label>
                        <input type="text" class="form-control" name="playStore" id="playStore" value="${siteDtl != null ? siteDtl.playStore : ""}" autocomplete="off">
                    </div>
                    <div class="form-group col mb-3">
                        <label for="appStore">App Store</label>
                        <input type="text" class="form-control" name="appStore" id="appStore" value="${siteDtl != null ? siteDtl.appStore : ""}" autocomplete="off">
                    </div>
                </div>

                <div class="mb-4 mt-5 alert alert-info border-0">
                    <h5 class="mb-0 font-weight-normal">Invoice Email Template</h5>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="jurisdictionPlace">Jurisdiction Place</label>
                        <input type="text" class="form-control" name="jurisdictionPlace" id="jurisdictionPlace" value="${siteDtl != null ? siteDtl.jurisdictionPlace : ""}" autocomplete="off">
                    </div>
                    <div class="form-group col mb-3">
                        <label for="jurisdictionState">Jurisdiction State</label>
                        <input type="text" class="form-control" name="jurisdictionState" id="jurisdictionState" value="${siteDtl != null ? siteDtl.jurisdictionState : ""}" autocomplete="off">
                    </div>
                </div>

                <div class="mb-4 mt-5 alert alert-info border-0">
                    <h5 class="mb-0 font-weight-normal">Custom Styles</h5>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="customStyles">Add custom styles below</label>
                        <textarea class="form-control" rows="10" name="customStyles" id="customStyles" autocomplete="off">
                            ${siteDtl != null ? siteDtl.customStyles : ""}
                        </textarea>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="siteTitle">Site Title</label>
                        <textarea class="form-control" rows="2" name="siteTitle" id="siteTitle" autocomplete="off">${siteDtl != null ? siteDtl.siteTitle : ""}</textarea>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="siteDescription">Site Description</label>
                        <textarea class="form-control" rows="4" name="siteDescription" id="siteDescription" autocomplete="off">${siteDtl != null ? siteDtl.siteDescription : ""}</textarea>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col mb-3">
                        <label for="keywords">Site Keywords</label>
                        <textarea class="form-control" rows="4" name="keywords" id="keywords" autocomplete="off">${siteDtl != null ? siteDtl.keywords : ""}</textarea>
                    </div>
                </div>
                <div class="mb-4 mt-5 alert alert-info border-0">
                    <h5 class="mb-0 font-weight-normal">Library and Landing page related</h5>
                </div>

                <div class="row">
                    <div class="form-group  col mb-3">
                        <label for="disableScratchCode">Disable Scratch Code</label>
                        <select class="form-control" name="disableScratchCode" id="disableScratchCode">
                            <option value="Yes" ${siteDtl != null && siteDtl.disableScratchCode == "Yes" ? "selected" : ""}>Yes</option>
                            <option value="No" ${siteDtl != null && siteDtl.disableScratchCode == "No" ? "selected" : ""}>No</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group  col mb-3">
                        <label for="disableStore">Disable Store</label>
                        <select class="form-control" name="disableStore" id="disableStore">
                            <option value="Yes" ${siteDtl != null && siteDtl.disableStore == "Yes" ? "selected" : ""}>Yes</option>
                            <option value="No" ${siteDtl != null && siteDtl.disableStore == "No" ? "selected" : ""}>No</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group  col mb-3">
                        <label for="canAddBooksFromAllSites">Add Books From All Sites</label>
                        <select class="form-control" name="canAddBooksFromAllSites" id="canAddBooksFromAllSites">
                            <option value="false" ${siteDtl != null && siteDtl.canAddBooksFromAllSites == "false" ? "selected" : ""}>No</option>
                            <option value="true" ${siteDtl != null && siteDtl.canAddBooksFromAllSites == "true" ? "selected" : ""}>Yes</option>

                        </select>
                </div>
                </div>
                <div class="row">
                    <div class="form-group  col mb-3">
                        <label for="showAnalytics">Show Analytics</label>
                        <select class="form-control" name="showAnalytics" id="showAnalytics">
                            <option value="false" ${siteDtl != null && siteDtl.showAnalytics == "false" ? "selected" : ""}>No</option>
                            <option value="true" ${siteDtl != null && siteDtl.showAnalytics == "true" ? "selected" : ""}>Yes</option>

                        </select>
                    </div>
                </div>
                <div class="d-flex">
                    <button type="submit" class="btn btn-success btn-${siteDtl != null ? "success" : "primary"}-modifier shadow-sm mb-2 px-5">${siteDtl != null ? "Update" : "Create"} Website</button>
                </div>

            </form>

        </div>
    </div>
</section>

<script>
    var themeColor = "";
    var websiteId = "";
    var mode = "${params.mode}";
    var enCusVal=null;
    var otpLogin=null;
    var landingPage=null;
    var custGptLoaderEnabled=null;
    if (mode=='edit' && '${siteDtl}' !=null){
        enCusVal = '${siteDtl != null ? siteDtl.enableContactus:null}';
        otpLogin = '${siteDtl != null ? siteDtl.enableOTPLogin:null}';
        landingPage = '${siteDtl != null ? siteDtl.showLandingPage:null}';
        custGptLoaderEnabled = '${siteDtl != null ? siteDtl.customGptLoader:null}';
    }

    const siteIdSelector = document.getElementById("websiteId");
    const faviconFile = document.getElementById("faviconImg");
    const logoFile = document.getElementById("logoImg");
    const logoIconFile = document.getElementById("logoIcon");
    const enableContactusCheck = document.getElementById("enableContactusCheck");
    const loginMethodCheck = document.getElementById("loginMethod");
    const showLandingPageCheck = document.getElementById("landingPage");
    const enableContactus = document.getElementById("enableContactus");
    const enableOTPLogin = document.getElementById("enableOTPLogin");
    const showLandingPage = document.getElementById("showLandingPage");
    const customGptLoaderYes = document.getElementById('customGptLoaderYes');
    const displayNameContainer = document.getElementById('displayNameContainer');

    // Theme Color
    const colorSelector = document.getElementById('themeColor');
    const hexCodeSelector = document.getElementById('hexCode');
    colorSelector.addEventListener('input', () => {
        hexCodeSelector.value = colorSelector.value;
    });
    hexCodeSelector.addEventListener('change', () => {
        colorSelector.value = hexCodeSelector.value;
    });

    if (enCusVal == true || enCusVal == 'true' && enCusVal!="" && enCusVal!=null && enCusVal!='null'){
        enableContactusCheck.checked = true;
    }
    if (otpLogin == true || otpLogin == 'true' && otpLogin!="" && otpLogin!=null && enCusVal!='null'){
        loginMethodCheck.checked = true;
    }
    if (landingPage == true || landingPage == 'true' && landingPage!="" && landingPage!=null && enCusVal!='null'){
        showLandingPageCheck.checked = true;
    }
    if (custGptLoaderEnabled == "Yes"){
        customGptLoaderYes.checked = true;
        displayNameContainer.classList.remove('d-none');
    }else {
        displayNameContainer.classList.add('d-none');
    }
    function toggleDisplayName() {
        const customGptLoaderYes = document.getElementById('customGptLoaderYes');
        const displayNameContainer = document.getElementById('displayNameContainer');

        if (customGptLoaderYes.checked) {
            displayNameContainer.classList.remove('d-none');
        } else {
            displayNameContainer.classList.add('d-none');
        }
    }
    document.getElementById('customGptLoaderYes').addEventListener('change', toggleDisplayName);
    document.getElementById('customGptLoaderNo').addEventListener('change', toggleDisplayName);

    // Submitting All Data
    creationSubmit = () => {
        websiteId = siteIdSelector.value;
        themeColor = hexCodeSelector.value;
        enableContactus.value = enableContactusCheck.checked;
        enableOTPLogin.value = loginMethodCheck.checked;
        showLandingPage.value = showLandingPageCheck.checked;
        if(websiteId == "") {
            alert("Please enter website id.");
            return false;
        } else if(themeColor == "") {
            alert("Please select theme color.");
            return false;
        } else if(mode != "edit" && (faviconFile.value == "" || logoFile.value == "" || logoIconFile.value == "")) {
            alert("Please upload all the below images.\nLogo, Logo icon, Favicon, Banner");
            return false;
        } else if((faviconFile.files[0] != undefined && faviconFile.files[0].name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g) >- 1)
            || (logoFile.files[0] != undefined && logoFile.files[0].name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g) >- 1)
            || (logoIconFile.files[0] != undefined && logoIconFile.files[0].name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g) >-1 )
            || (bannerFile.files[0] != undefined && bannerFile.files[0].name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g) >-1 )) {
            alert("Image files name must not contain any space or special characters.");
            return false;
        } else if(bannerFile !=undefined && bannerFile.size >= 256000) {
            alert("Banner image file size should be below 150kb.");
            return false;
        } else {
            $(".loading-icon").removeClass('hidden');
            <% if(siteDtl != null) { %>
                alert("Updated successfully!");
            <% } else { %>
                alert("Created successfully!");
            <% } %>
            // return false;
        }
    }

    // Switch to file selectors
    changeFaviconSelector = () => {
        document.getElementById('selectedFavicon').style.display = "none";
        document.getElementById('faviconImg').classList.remove('d-none');
        document.getElementById('faviconImg').value = "";
    }
    changeLogoSelector = () => {
        document.getElementById('selectedLogo').style.display = "none";
        document.getElementById('logoImg').classList.remove('d-none');
        document.getElementById('logoImg').value = "";
    }
    changeLogoIconSelector = () => {
        document.getElementById('selectedLogoIcon').style.display = "none";
        document.getElementById('logoIcon').classList.remove('d-none');
        document.getElementById('logoIcon').value = "";
    }
    changeBannerSelector = () => {
        document.getElementById('selectedBanner').style.display = "none";
        document.getElementById('bannerImg').classList.remove('d-none');
        document.getElementById('bannerImg').value = "";
    }
    changeGPTLoaderSelector = () => {
        document.getElementById('selectedGPTLoader').style.display = "none";
        document.getElementById('gptLoaderPath').classList.remove('d-none');
        document.getElementById('gptLoaderPath').value = "";
    }

</script>

<g:render template="/${session['entryController']}/footer_new"></g:render>
