<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <div class="container mt-5">
                        <div class="row">
                            <div class="col-md-8 offset-md-2">
                                <%if(params.id){%>
                                <h2>Edit Site (Name: ${siteInstance.clientName})</h2>
                                <%}else{%>
                                <h2>Add new site</h2>
                                <%}%>
                                <hr/>
                                <g:if test="${flash.message}">
                                    <div class="alert alert-info">${flash.message}</div>
                                </g:if>

                                <g:form controller="privatelabel" action="saveSite" class="form">
                                    <!-- We need to store recordId separately because instance.id might not be bound before saving -->

                                    <div class="form-group">
                                        <label for="idField">ID</label>
                                        <!-- If editing, show current ID, if adding, show new calculated ID -->
                                        <!-- Usually ID is not edited by user, so we can make it read-only -->
                                        <input type="number" class="form-control" id="idField" name="recordId" value="${siteInstance?.id}" required <%=params.id==null?"":"readonly"%>/>
                                    </div>

                                    <div class="form-group">
                                        <label for="siteNameField">Site Name</label>
                                        <input type="text" class="form-control" id="siteNameField" name="siteName" value="${siteInstance?.siteName ?: ''}" placeholder="Enter site name" required/>
                                    </div>

                                    <div class="form-group">
                                        <label for="clientNameField">Client Name</label>
                                        <input type="text" class="form-control" id="clientNameField" name="clientName" value="${siteInstance?.clientName ?: ''}" placeholder="Enter client name" required/>
                                    </div>
                                    <div class="form-group">
                                        <label for="sellPrintOnMainSiteField">Sell Print On Main Site</label>
                                        <select class="form-control" id="sellPrintOnMainSiteField" name="sellPrintOnMainSite" required>
                                            <option value="No" ${siteInstance?.sellPrintOnMainSite == 'No' ? 'selected' : ''}>No</option>
                                            <option value="Yes" ${siteInstance?.sellPrintOnMainSite == 'Yes' ? 'selected' : ''}>Yes</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="sellPrintOnWhiteLabelField">Sell Print On White Label</label>
                                        <select class="form-control" id="sellPrintOnWhiteLabelField" name="sellPrintOnWhiteLabel" required>
                                            <option value="No" ${siteInstance?.sellPrintOnWhiteLabel == 'No' ? 'selected' : ''}>No</option>
                                            <option value="Yes" ${siteInstance?.sellPrintOnWhiteLabel == 'Yes' ? 'selected' : ''}>Yes</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="siteDomainNameField">Site Domain Name</label>
                                        <input type="text" class="form-control" id="siteDomainNameField" name="siteDomainName" value="${siteInstance?.siteDomainName ?: ''}" placeholder="Enter site domain name" />
                                    </div>
                                     <div class="form-group">
                                        <label for="fromEmailField">From Email</label>
                                        <input type="text" class="form-control" id="fromEmailField" name="fromEmail" value="${siteInstance?.fromEmail ?: ''}" placeholder="Enter from email" />
                                    </div>
                                     <div class="form-group">
                                        <label for="googleUniversalAnalyticsKeyField">Google Analytics Code</label>
                                        <input type="text" class="form-control" id="googleUniversalAnalyticsKeyField" name="googleUniversalAnalyticsKey" value="${siteInstance?.googleUniversalAnalyticsKey ?: ''}" placeholder="Enter google universal analytics key" />
                                    </div>

                                    <div class="form-group">
                                        <label for="smsSenderNameField">SMS Sender Name</label>
                                        <input type="text" class="form-control" id="smsSenderNameField" name="smsSenderName" value="${siteInstance?.smsSenderName ?: ''}" placeholder="Enter sms sender name" />
                                    </div>


                                    <!-- Hidden fields for all other variables -->
                                    <g:hiddenField name="displayInMainSite" value="${siteInstance?.displayInMainSite}"/>
                                    <g:hiddenField name="categories" value="${siteInstance?.categories}"/>
                                    <g:hiddenField name="publisherId" value="${siteInstance?.publisherId}"/>
                                    <g:hiddenField name="displineCoverImage" value="${siteInstance?.displineCoverImage}"/>
                                    <g:hiddenField name="teachingNotes" value="${siteInstance?.teachingNotes}"/>
                                    <g:hiddenField name="teachingSlides" value="${siteInstance?.teachingSlides}"/>
                                    <g:hiddenField name="sampleChapters" value="${siteInstance?.sampleChapters}"/>
                                    <g:hiddenField name="mediaLinks" value="${siteInstance?.mediaLinks}"/>
                                    <g:hiddenField name="exercises" value="${siteInstance?.exercises}"/>
                                    <g:hiddenField name="otpReg" value="${siteInstance?.otpReg}"/>
                                    <g:hiddenField name="googleClientId" value="${siteInstance?.googleClientId}"/>
                                    <g:hiddenField name="fbMessageDatabaseUrl" value="${siteInstance?.fbMessageDatabaseUrl}"/>
                                    <g:hiddenField name="fbMessageKeyFile" value="${siteInstance?.fbMessageKeyFile}"/>
                                    <g:hiddenField name="fbFirebaseWebAPI" value="${siteInstance?.fbFirebaseWebAPI}"/>
                                    <g:hiddenField name="sendNotification" value="${siteInstance?.sendNotification}"/>
                                    <g:hiddenField name="googleApiKey" value="${siteInstance?.googleApiKey}"/>
                                    <g:hiddenField name="domainUriPrefix" value="${siteInstance?.domainUriPrefix}"/>
                                    <g:hiddenField name="iosBundleId" value="${siteInstance?.iosBundleId}"/>
                                    <g:hiddenField name="androidPackageName" value="${siteInstance?.androidPackageName}"/>
                                    <g:hiddenField name="razorPayKeyId" value="${siteInstance?.razorPayKeyId}"/>
                                    <g:hiddenField name="razorPaySecretKey" value="${siteInstance?.razorPaySecretKey}"/>
                                    <g:hiddenField name="channelId" value="${siteInstance?.channelId}"/>
                                    <g:hiddenField name="siteBaseUrl" value="${siteInstance?.siteBaseUrl}"/>
                                    <g:hiddenField name="smsUsername" value="${siteInstance?.smsUsername}"/>
                                    <g:hiddenField name="smsPassword" value="${siteInstance?.smsPassword}"/>
                                    <g:hiddenField name="smsSenderId" value="${siteInstance?.smsSenderId}"/>
                                   <g:hiddenField name="appOnly" value="${siteInstance?.appOnly}"/>
                                    <g:hiddenField name="smsUrl" value="${siteInstance?.smsUrl}"/>
                                    <g:hiddenField name="smsUrl1" value="${siteInstance?.smsUrl1}"/>
                                    <g:hiddenField name="securityKey" value="${siteInstance?.securityKey}"/>
                                    <g:hiddenField name="smsResendUrl" value="${siteInstance?.smsResendUrl}"/>
                                    <g:hiddenField name="smsResendUrl1" value="${siteInstance?.smsResendUrl1}"/>
                                    <g:hiddenField name="jwChannelId" value="${siteInstance?.jwChannelId}"/>
                                    <g:hiddenField name="jwAuthId" value="${siteInstance?.jwAuthId}"/>
                                    <g:hiddenField name="sageOnly" value="${siteInstance?.sageOnly}"/>
                                    <g:hiddenField name="appInApp" value="${siteInstance?.appInApp}"/>
                                    <g:hiddenField name="awsAccessKeyId" value="${siteInstance?.awsAccessKeyId}"/>
                                    <g:hiddenField name="awsSecretAccessKey" value="${siteInstance?.awsSecretAccessKey}"/>
                                    <g:hiddenField name="currentAffairsType" value="${siteInstance?.currentAffairsType}"/>
                                    <g:hiddenField name="prepjoySite" value="${siteInstance?.prepjoySite}"/>
                                    <g:hiddenField name="playStoreInstallUrl" value="${siteInstance?.playStoreInstallUrl}"/>
                                    <g:hiddenField name="allBooksLibrary" value="${siteInstance?.allBooksLibrary}"/>
                                    <g:hiddenField name="downloadBookChapters" value="${siteInstance?.downloadBookChapters}"/>
                                    <g:hiddenField name="instituteLibrary" value="${siteInstance?.instituteLibrary}"/>
                                    <g:hiddenField name="mainResourcesPage" value="${siteInstance?.mainResourcesPage}"/>
                                    <g:hiddenField name="fixPayments" value="${siteInstance?.fixPayments}"/>
                                    <g:hiddenField name="commonWhiteLabel" value="true"/>
                                    <g:hiddenField name="productYoutubeLink" value="${siteInstance?.productYoutubeLink}"/>
                                    <g:hiddenField name="youtubeLinkTitle" value="${siteInstance?.youtubeLinkTitle}"/>
                                    <g:hiddenField name="associatedSites" value="${siteInstance?.associatedSites}"/>

                                    <div class="form-group mt-4">
                                         <a class="btn btn-secondary" href="${createLink(controller:'privatelabel', action:'addSite')}">Cancel</a>
                                        <button type="submit" class="btn btn-primary">Save</button>

                                    </div>
                                </g:form>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
