<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js"></script>

<script>
    var base_url = location.protocol + '//' + location.host;
    window.history.replaceState("", "", base_url);
    var siteId = "${session["siteId"]}";
    var privateLabelBanner = "${session["bannerImage"]}";
    var checkDescBanners = false;
    var checkMobBanners = false;
    var templateDesktop = "";
    var templateMobile = "";
    var defaultDeskImageUrl = "/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["bannerImage"]}&imgType=webp";
    var defaultMobImageUrl = "/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["bannerImage"]}&imgType=webp";


    function showBanners(data) {
        setTimeout(function () {
            $(".loading-icon").addClass("hidden");
        },2000);
        if(data.status=="OK") {
            var banners = data.banners.reverse();
            $('#slider-desktop-views,#slider-mobile-views').empty();

            // Banner slider
            $.each(banners, function (i, v) {
                var item = v;
                var htmlStr = '';
                var htmlStrMobile = '';
                var indicatorStr = '';
                var indicatorStrMobile = '';
                var imageDescUrl = "/wonderpublish/showImage?id=" + v.id + "&fileName=" + v.imagePath + "&imgType=webp";
                var imageMobUrl = "/wonderpublish/showImage?id="+v.id+"&fileName="+v.imagePathMobile + "&imgType=webp";
                if(v.bookTitle) {
                    var bookTitle = "/" + replaceAll(replaceAll(v.bookTitle, ' ', '-').toLowerCase(), '\'', '');
                }
                var bookHyperLink = bookTitle + "/ebook-details?siteName=${session['entryController']}&bookId=" + v.bookId + "&preview=true";

                var actionLink = v.action //action field link
                if(actionLink.indexOf('http')!=0) {
                    var serverURL = window.location.origin //getting base url
                    actionLink = serverURL + '/' + actionLink;
                }

                indicatorStr += "<li data-target='#slider-desktop' class='border-light' data-slide-to='" + i + "'></li>";
                indicatorStrMobile += "<li data-target='#slider-mobile' class='border-light' data-slide-to='" + i + "'></li>";
                if(v.bookId) {
                    htmlStr += '<div class="carousel-item">' +
                        '<a href="' + bookHyperLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageDescUrl+'">' +
                        '</a>' +
                        '</div>';
                    htmlStrMobile += '<div class="carousel-item">' +
                        '<a href="' + bookHyperLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageMobUrl+'">' +
                        '</a>' +
                        '</div>';
                } else if (v.action) {
                    htmlStr += '<div class="carousel-item">' +
                        '<a href="' + actionLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageDescUrl+'">' +
                        '</a>' +
                        '</div>';
                    htmlStrMobile += '<div class="carousel-item">' +
                        '<a href="' + actionLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageMobUrl+'">' +
                        '</a>' +
                        '</div>';
                }else{
                    htmlStr += '<div class="carousel-item">' +
                        '<img src="'+imageDescUrl+'">' +
                        '</div>';
                    htmlStrMobile += '<div class="carousel-item">' +
                        '<img src="'+imageMobUrl+'">' +
                        '</div>';
                }

                // If desktop banners are available
                if(v.imagePath) {
                    checkDescBanners = true;
                    $('#slider-desktop-views').append(htmlStr).find('.carousel-item:first-child').addClass('active');
                    $('#slider-desktop-indicators').append(indicatorStr).find('li:first-child').addClass('active');
                }

                // If mobile banners are available
                if(v.imagePathMobile) {
                    checkMobBanners = true;
                    $('#slider-mobile-views').append(htmlStrMobile).find('.carousel-item:first-child').addClass('active');
                    $('#slider-mobile-indicators').append(indicatorStrMobile).find('li:first-child').addClass('active');
                }

            });

        } else if(data.status=="Nothing Present") {
            checkDescBanners = false; checkMobBanners = false;
        }

        // Showing empty banners based on condition
        if(!checkDescBanners && !checkMobBanners) {
            templateDesktop += emptyDesktopBannerUI(defaultDeskImageUrl);
            $('#slider-desktop-views').append(templateDesktop);
            templateMobile += emptyMobileBannerUI(defaultMobImageUrl);
            $('#slider-mobile-views').append(templateMobile);
        } else if(!checkMobBanners) {
            templateMobile += emptyMobileBannerUI(defaultMobImageUrl);
            $('#slider-mobile-views').append(templateMobile);
        }

    }

    // If desktop banner images are empty calling this function
    function emptyDesktopBannerUI(defaultImage) {
        var emptyBannerDesk = '<div class="carousel-item active">' +
            '<img src="'+defaultImage+'">' +
            '</div>';
        return emptyBannerDesk;
    }

    // If mobile banner images are empty calling this function
    function emptyMobileBannerUI(defaultImage) {
        var emptyBannerMob = '<div class="carousel-item active">' +
            '<img src="'+defaultImage+'">' +
            '</div>';
        return emptyBannerMob;
    }

    function getBannerDetails(siteId) {
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="wonderpublish" action="getBannerdetails" params="'siteId='+siteId" onSuccess="showBanners(data);"/>
    }

    getBannerDetails(siteId);

    function replaceAll(str, find, replace) {
        if (str == undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }

    if($.browser.platform == "win") {
        $("html").addClass("windows");
    }

    if($.browser.name == "chrome") {
        $("html").addClass("chrome");
    } else if($.browser.name == "mozilla") {
        $("html").addClass("mozilla");
    } else if($.browser.name == "safari") {
        $("html").addClass("safari");
        if($(window).width() < 767){
            $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
                $(this).css('background','#eee');
                $('.btn-primary-modifier').css('background-color','#A81175 !important');
                $('.btn-success-modifier').css('background-color','#27AE60 !important');
                $('.btn-secondary-modifier').css('background-color','#8E8E8E !important');
                $('.btn-danger-modifier').css('background-color','#FF4B33 !important');
                $('.btn-warning-modifier').css('background-color','#FFD602 !important');
            });
            document.addEventListener('touchmove', function (event) {
                if (event.scale !== 1) { event.preventDefault(); }
            }, { passive: false });
        }
    } else {
        $("html").addClass("others");
        if($(window).width() < 767){
            $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
                $(this).css('background','#eee');
            });
            document.addEventListener('touchmove', function (event) {
                if (event.scale !== 1) { event.preventDefault(); }
            }, { passive: false });
        }
    }
</script>
