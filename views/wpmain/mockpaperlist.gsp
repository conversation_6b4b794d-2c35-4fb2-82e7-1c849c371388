<%@ page contentType="text/html;charset=UTF-8" %>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<html>
<head>
    <title>Mock Papers - ${bookTitle}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .mock-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: calc(100vh - 160px);
        }
        
        .mock-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .mock-header h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .mock-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .mock-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none !important;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: #667eea !important;
            color: white !important;
            border: 1px solid #667eea;
        }

        .btn-primary:hover {
            background: #5a6fd8 !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .btn-success {
            background: #28a745 !important;
            color: white !important;
            border: 1px solid #28a745;
        }

        .btn-success:hover {
            background: #1e7e34 !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .btn-secondary {
            background: #6c757d !important;
            color: white !important;
            border: 1px solid #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62 !important;
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .btn-sm {
            padding: 8px 16px;
            font-size: 0.9rem;
        }
        
        .mock-list {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        
        .mock-list-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .mock-list-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .mock-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .mock-table th {
            background: #f8f9fa;
            padding: 15px 20px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #e9ecef;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .mock-table td {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f3f4;
            color: #555;
            vertical-align: middle;
        }
        
        .mock-table tr:hover {
            background: #f8f9fa;
        }
        
        .mock-name {
            font-weight: 600;
            color: #333;
            font-size: 1rem;
        }
        
        .mock-date {
            color: #666;
            font-size: 0.9rem;
        }
        
        .mock-questions {
            font-weight: 600;
            color: #667eea;
            font-size: 1rem;
        }
        
        .mock-actions-cell {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #333;
        }
        
        .empty-state p {
            font-size: 1.1rem;
            margin-bottom: 30px;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .feature-highlight h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.1rem;
        }
        
        .feature-highlight p {
            margin: 0;
            color: #666;
            font-size: 0.95rem;
        }
        
        @media (max-width: 768px) {
            .mock-container {
                padding: 15px;
            }
            
            .mock-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .mock-table {
                font-size: 0.9rem;
            }
            
            .mock-table th,
            .mock-table td {
                padding: 10px 15px;
            }
            
            .mock-actions-cell {
                flex-direction: column;
                gap: 5px;
            }
            
            .btn-sm {
                width: 100%;
                justify-content: center;
            }
        }
        
        @media (max-width: 600px) {
            .mock-table,
            .mock-table thead,
            .mock-table tbody,
            .mock-table th,
            .mock-table td,
            .mock-table tr {
                display: block;
            }
            
            .mock-table thead tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
            }
            
            .mock-table tr {
                border: 1px solid #ccc;
                margin-bottom: 10px;
                padding: 15px;
                border-radius: 8px;
                background: white;
            }
            
            .mock-table td {
                border: none;
                position: relative;
                padding: 8px 0 8px 30%;
                text-align: left;
            }
            
            .mock-table td:before {
                content: attr(data-label) ": ";
                position: absolute;
                left: 0;
                width: 25%;
                padding-right: 10px;
                white-space: nowrap;
                font-weight: 600;
                color: #333;
            }
        }
    </style>
</head>
<body>
    <div class="mock-container">
        <!-- Header -->
        <div class="mock-header">
            <h1><i class="fas fa-magic"></i> Mock Papers</h1>
            <p>Practice with AI-generated mock papers for ${bookTitle}</p>
        </div>

        <!-- Feature Highlight -->
        <div class="feature-highlight">
            <h4><i class="fas fa-lightbulb"></i> Quick Mock Paper Creation</h4>
            <p>Create comprehensive mock papers in seconds! Just select question types and difficulty levels - we'll handle the rest.</p>
        </div>

        <!-- Actions -->
        <div class="mock-actions">
            <a href="/wpmain/mockpapercreator?bookId=${bookId}" class="btn btn-success">
                <i class="fas fa-plus"></i> Create New Mock Paper
            </a>

        </div>

        <!-- Mock Papers List -->
        <div class="mock-list">
            <div class="mock-list-header">
                <h2 class="mock-list-title">
                    <i class="fas fa-file-alt"></i>
                    Your Mock Papers
                    <g:if test="${mockPapers}">
                        <span style="font-size: 0.8em; color: #666; font-weight: normal;">(${mockPapers.size()} total)</span>
                    </g:if>
                </h2>
            </div>

            <g:if test="${mockPapers && mockPapers.size() > 0}">
                <table class="mock-table">
                    <thead>
                        <tr>
                            <th>Mock Paper Name</th>
                            <th>Date Created</th>
                            <th>Total Marks</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <g:each in="${mockPapers}" var="mp">
                            <tr>
                                <td data-label="Name">
                                    <div class="mock-name">${mp.patternName}</div>
                                </td>
                                <td data-label="Date Created">
                                    <div class="mock-date">
                                        <g:formatDate date="${mp.dateCreated}" format="dd-MMMM-yyyy" locale="en_IN"/>
                                    </div>
                                </td>
                                <td data-label="Questions">
                                    <div class="mock-questions">${mp.totalMarks ?: 'N/A'} marks</div>
                                </td>
                                <td data-label="Actions">
                                    <div class="mock-actions-cell">
                                        <a href="/wpmain/qpview?id=${mp.id}&bookId=${bookId}&fromMock=true"
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-play"></i> Take Test
                                        </a>
                                        <a href="/wpmain/qpprint?id=${mp.id}" 
                                           target="_blank" 
                                           class="btn btn-secondary btn-sm">
                                            <i class="fas fa-print"></i> Print
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </g:each>
                    </tbody>
                </table>
            </g:if>
            <g:else>
                <div class="empty-state">
                    <i class="fas fa-magic"></i>
                    <h3>No Mock Papers Yet</h3>
                    <p>Create your first mock paper to start practicing!</p>
                    <a href="/wpmain/mockpapercreator?bookId=${bookId}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Create Your First Mock Paper
                    </a>
                </div>
            </g:else>
        </div>
    </div>
</body>
</html>

<g:render template="/${session['entryController']}/footer_new"></g:render>
