<div class="ai-section start-here-section">
    <div class="ai-section-header section-toggle">
        <div class="header-left">
            <div class="icon-container" style="background-color: rgba(66, 133, 244, 0.8);">
                <i data-lucide="rocket"></i>
            </div>
            <h3>Start here</h3>
        </div>
        <i data-lucide="chevron-up" class="section-chevron"></i>
    </div>
    <div class="ai-prompt-buttons">
        <!-- Lesson Plan from Plan section -->
        <button class="ai-prompt-btn lesson-plan" onclick="talkToAI('Lesson Plan', 'teacher_lessonplan')">
            <div class="icon-container" style="background-color: rgba(66, 133, 244, 0.8);">
                <i data-lucide="clipboard-list"></i>
            </div>
            <span>Lesson Plan</span>
        </button>
        
        <!-- Chapter Summary from Plan section -->
        <button class="ai-prompt-btn chapter-summary" onclick="talkToAI('Chapter Summary', 'summary')">
            <div class="icon-container" style="background-color: rgba(52, 168, 83, 0.8);">
                <i data-lucide="file-text"></i>
            </div>
            <span>Chapter Summary</span>
        </button>
        
        <!-- Interactive Activities from Teach section -->
        <button class="ai-prompt-btn" onclick="talkToAI('Interactive Activities', 'teacher_activities')">
            <div class="icon-container" style="background-color: rgba(161, 66, 244, 0.8);">
                <i data-lucide="puzzle"></i>
            </div>
            <span>Interactive Activities</span>
        </button>
        
        <!-- Real-World Examples from Teach section -->
        <button class="ai-prompt-btn" onclick="talkToAI('Real-World Examples', 'teacher_realworld')">
            <div class="icon-container" style="background-color: rgba(234, 67, 53, 0.8);">
                <i data-lucide="globe"></i>
            </div>
            <span>Real-World Examples</span>
        </button>
        
        <!-- Create Test from Assess section -->
        <button class="ai-prompt-btn create-test" onclick="talkToAI('Create Test', 'mcq')">
            <div class="icon-container" style="background-color: rgba(251, 140, 0, 0.8);">
                <i data-lucide="clipboard-check"></i>
            </div>
            <span>Create Test</span>
        </button>
        
        <!-- Analytics from Assess section -->
        <button class="ai-prompt-btn analytics" onclick="talkToAI('Analytics', 'chapter_overview')">
            <div class="icon-container" style="background-color: rgba(0, 150, 136, 0.8);">
                <i data-lucide="bar-chart"></i>
            </div>
            <span>Analytics</span>
        </button>
    </div>
</div>
