<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<script>
    var loggedIn=false;
</script>
<style>

.page-item.active .page-link {
    background-color: #007bff !important;
    color: #ffffff;
}
table td a {
    color: #007bff;
}
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>th:first-child:before {
    top: 16px !important;
}
table.dataTable>tbody>tr.child ul.dtr-details>li {
    border-bottom: none !important;
    padding: 0.2em 0 !important;
}
table.dataTable>tbody>tr.child span.dtr-title {
    min-width: 180px !important;
}
@media screen and (max-width: 767px){
    div.dataTables_wrapper div.dataTables_paginate ul.pagination {
        margin: 10px 0 30px !important;
        justify-content: center !important;
    }
    table.dataTable.nowrap th, table.dataTable.nowrap td {
        white-space: normal !important;
    }
    table.dataTable>tbody>tr.child span.dtr-title {
        min-width: 100% !important;
    }
    .form-group.col-md-6.mt-2 {
        display:flex;
    }
    .form-group.col-md-6.mt-2 button{
        max-width:100%;
    }
}

table.dataTable {
    width: 100% !important;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<div>
    <div class="container-fluid my-5 px-5 publishing_sales">
        <div  class='px-0' id="bookdtl" style="min-height: calc(100vh - 156px);">
            <div class='col-md-12 main mx-auto p-4'>
                <h3 class="text-center mb-4"> Download Print Books</h3><br>
                <div id="content-books" class="sale">
                    <div class="form-inline align-items-end">
                        <div class="form-group col-md-3">
                            <label for="poStartDate"><strong>From date <span class="text-danger">*</span></strong></label>
                            <input type="text" class="form-control" id="poStartDate" placeholder="Any" value="" autocomplete="off">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="poEndDate"><strong>To date <span class="text-danger">*</span></strong></label>
                            <input type="text" class="form-control" id="poEndDate" placeholder="Any" value="" autocomplete="off">
                        </div>
                    </div>

                </div>

                <div class="ml-4">
                    <p class="text-danger d-none" id="errorText" style="transition: all 0.3s ease-in">Please enter all the mandatory fields</p>
                </div>
                <div class="form-group col-md-6 mt-2">
                    <button type="button" id="download-btn" onclick="downloadPrintBooks()" class="btn btn-lg btn-primary col-3">Download</button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<div class="push"></div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<sec:ifLoggedIn>
    <script>

        $('#saleSelect').change(function() {
            localStorage.removeItem('SalesReport');
            var salesVal = $(this).val();
            localStorage.setItem("SalesReport", salesVal);
        });

        window.onload = function() {
            var SalesReport = localStorage.getItem("SalesReport");
            if(SalesReport==null){
                $('#saleSelect').val('paymentId');
            }
            else {
                $('#saleSelect').val(SalesReport);
            }
        }


    </script>

</sec:ifLoggedIn>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script>
    $('#poStartDate, #poEndDate').datepicker({
        format: 'dd-mm-yyyy',
        startView: 1,
        todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });


    function downloadPrintBooks(){
        var poStartDate = $('#poStartDate').val();
        var poEndDate = $('#poEndDate').val();
        var isValid = inputValidation();
        var url ="/printbooks/downloadPrintBooks"
        if (isValid){
            poStartDate = poStartDate.split('-').reverse().join('-')
            poEndDate = poEndDate.split('-').reverse().join('-')
            window.location.href = url+"?download=true&startDate="+poStartDate+"&endDate="+poEndDate;
        }else{
            document.getElementById('errorText').classList.remove('d-none');
            document.getElementById('errorText').classList.add('d-flex');
            setTimeout(function (){
                document.getElementById('errorText').classList.add('d-none');
                document.getElementById('errorText').classList.remove('d-flex');
            },1600)
        }
    }

    function inputValidation(){
        var startDateVal = document.getElementById('poStartDate');
        var endDateVal = document.getElementById('poEndDate');

        if (startDateVal.value!="" && endDateVal.value!=""){
            return true
        }else{
            return false;
        }
    }
</script>
</body>
</html>
