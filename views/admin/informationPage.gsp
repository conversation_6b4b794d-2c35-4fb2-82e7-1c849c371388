<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="katex.min.css"/>

<script>
    var loggedIn=false;
    $('link[data-role="baseline"]').attr('href', '');
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="page-main-wrapper mdl-js information ebooks pb-5">
    <div class="container pt-4 pt-md-5 mt-3 mt-md-0">
        <div class="d-flex justify-content-start align-items-center page_title">
            <button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button>
            <div class="mdl-tooltip" data-mdl-for="goBack">Back</div>
            <h3><strong>${params.resType}</strong></h3>
            <div class="ml-2 bg-success bg-success-modifier rounded-circle d-flex justify-content-center align-items-center btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect share-ebooks-page" data-toggle="modal" onclick="javascript:openShareContentModalGeneric('Interesting ${params.resType} found. Please click on the link to see the ${params.resType}!','${request.getRequestURL()}')">
                <i class="material-icons text-white">share</i>
            </div>
        </div>
        <div class="mt-4" id="contents">
        </div>
        <div id="showMore" class="text-center" style="display: none;">
            <a href="javascript:showMore();" class="d-inline-flex align-items-center justify-content-center btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect">
                <span class="material-icons">
                    expand_more
                </span>
                Show More
            </a>
        </div>
    </div>
</div>
<g:render template="/books/moreOptions"></g:render>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<g:render template="/resources/shareContent"></g:render>
<script src="/assets/katex.min.js"></script>
<script src="/assets/auto-render.min.js"></script>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>
<asset:javascript src="wonderslate/trunk8.js"/>
<script>
    var currentPageNo=0;
    function getInformationList(){
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="admin" action="getInformationList"  onSuccess='showInformationList(data);'
        params="'resType=${params.resType}&pageNo='+currentPageNo"/>
    }

    function showInformationList(data){
        $(".loading-icon").addClass("hidden");
        var resourceType = "${params.resType}";
        var className = "";
        if(resourceType === "HUMOUR") {
            className = "humour-desc";
        }
        var list = JSON.parse(data.results);
        var htmlStr=document.getElementById("contents").innerHTML;
        if(list.length==0&&currentPageNo>0){
            $("#showMore").hide();
        }
        else if(list.length==0&&currentPageNo==0){
            document.getElementById("contents").innerHTML = "<p>We are currently adding  <span class='text-lowercase'>${params.resType}</span>. Kindly try after sometime.</p>";
            $("#showMore").hide();
        }
        else{
            var description ;
            for(var i=0;i<list.length;i++){
                description = list[i].description;

                htmlStr = htmlStr + "<div class='info-list mb-4 pb-4'><div class='row'><p class='col-12 created-date'>"+list[i].dateCreated+"</p></div>\n";

                <%if(showTitle){%>
                htmlStr +="<div class='row'><div class='col-12 d-flex align-items-center'><a href='/${params.pageName}/"+encodeURIComponent(list[i].title.replaceAll('\'','').replaceAll(' ','-'))+"?id="+list[i].id+"'><h5 class='info-title mb-2'> "+list[i].title+"</h5></a>";
                htmlStr +=    " </div></div>";
                <%}%>

                <%if(showDetails){%>
                htmlStr +=     "<div class='row'><div class='col-12'><div class='info-description "+className+" <%if(showAnswerOption){%>with-answer-desc<%}%>'> "+description+"</div><a href='/${params.pageName}/"+encodeURIComponent(list[i].title.replaceAll('\'','').replaceAll(' ','-'))+"?id="+list[i].id+"' class='show-info-details'>... Show Details</a></div></div>";
                <%}else{%>
                htmlStr +=     "<div class='row'><div class='col-12'><div class='info-description "+className+" <%if(showAnswerOption){%>with-answer-desc<%}%>'> "+description+"</div></div></div>";
                <%}%>
                <%if(showAnswerOption){%>
                htmlStr +=     "<div class='row'><div class='col-12 mt-2' id='showAnswer_"+list[i].id+"'> <a href='javascript:showAnswer("+list[i].id+")'>Show Answer</a></div></div>";
                htmlStr +=     "<div class='row'><div class='col-12 mt-2' id='answer_"+list[i].id+"' style='display: none'><p><b>Answer</b>: "+list[i].answer+"</p></div></div>";
                <%}%>

                htmlStr +="<span class='divider'></span></div>";

            }
            document.getElementById("contents").innerHTML = htmlStr;
            if(list.length>=20){
                $("#showMore").show();
            }
            var anchors = document.querySelectorAll('.info-description a');
            for (var i=0; i<anchors.length; i++){
                anchors[i].setAttribute('target', '_blank');
            }
            var removeAttr = document.querySelectorAll('.show-info-details');
            for (var i=0; i<removeAttr.length; i++){
                removeAttr[i].removeAttribute('target');
            }

            <%if(!showAnswerOption){%>
                if(resourceType != "HUMOUR") {
                    $('.info-description').trunk8({
                        fill: '...',
                        lines: 5,
                        side: 'right'
                    });
                }
            <%}%>
        }

    }
    getInformationList();

    function showAnswer(id){
        $("#showAnswer_"+id).hide();
        $("#answer_"+id).show();
    }
    function showMore(){
        currentPageNo++;
        getInformationList();
    }
</script>
</body>
</html>
