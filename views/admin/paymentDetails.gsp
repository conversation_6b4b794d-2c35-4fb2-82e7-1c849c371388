<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);">
    <div class='row'>
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-inline p-4">
                    <div class="flex_st1 col-12 p-0">
                        <h5>PAYMENT DETAILS</h5>
                        <div class="form-group">
                            <input type="texts" class="form-control col-5" name="paymentId" id="paymentId"  placeholder="Enter Payment Id" />
                            <button class="btn btn-lg btn-primary col-2 ml-3"  onclick="getPaymentDetails();" style="margin:10px auto;">Get Details</button>
                        </div>
                    </div>
                    <div id="batchUsers" class=""></div>

                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>
    <%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())
%>
    var siteId = "${session["siteId"]}";
    var razorPayId=""
    function getPaymentDetails(){
         razorPayId = document.getElementById("paymentId").value;
        <g:remoteFunction controller="admin" action="paymentDetails"  params="'razorPayId='+razorPayId+'&filter=true'"    onSuccess = "deleteUser(data)"/>

    }

    function deleteUser(data){
        var discountId = data.discountId;
        var bookId = data.bookId;
        var userName = data.username;
        var amount = data.amount;
        var status=data.status;
        var htmlStr = "";
        if(data.payment=="OK"){
            htmlStr += "<div class='p-3'>";
          htmlStr += "<h5 class='mb-2'>Username: "+(userName?userName.split(siteId+'_')[1]:"")+"<h5/>"+
              "<h5  class='mb-2'>Book id : "+bookId+"</h5>";
              if(discountId !=0){
               htmlStr += "<h5 >Discount id: "+discountId+"</h5>";
              }
            htmlStr += "<h5 >Amount: "+amount+"</h5>";
             htmlStr +="<h5 >Status: "+status+"</h5>";
            htmlStr +=" <a href='${serverURL}/wonderpublish/selfService?razorPayId="+razorPayId+"' target='_blank' style='color: #007BFF;font-weight: 300;padding:0;text-decoration:underline;'>Click here to add the book to user library</a>";
             htmlStr +=  "</div>";
            document.getElementById("batchUsers").innerHTML = htmlStr;
        }else {
           var htmlStr1 = "<p class='text-danger'>Incorrect Payment Id</p>";
            document.getElementById("batchUsers").innerHTML = htmlStr1;
        }
    }

</script>


</body>
</html>