<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<script>
    var loggedIn = false;
</script>

<sec:ifLoggedIn>
    <script>
        loggedIn = true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid mt-4 row justify-content-around">
    <button class="btn col-4" onclick="showDatabase('sql')">MySql</button>
    <button class="btn col-4" onclick="showDatabase('mongo')">MongoDB</button>
</div>
<div class="container-fluid p-4" style="min-height: calc(100vh - 160px);" id="view">

</div>

<script>
    var dataSource = ""
    const sqlStringHtml = "<div class='row justify-content-center align-items-center'>\n" +
        "        <div class=\"col m-1 p-0\">\n" +
        "            <label for=\"select\">select</label>\n" +
        "            <textarea class=\"form-control mb-4\" id=\"select\" onchange=\"updateQuery()\"></textarea>\n" +
        "        </div>\n" +
        "\n" +
        "        <div class=\"col m-1 p-0\">\n" +
        "            <label for=\"from\">from</label>\n" +
        "            <textarea class=\"form-control mb-4\" id=\"from\" onchange=\"updateQuery()\"></textarea>\n" +
        "        </div>\n" +
        "\n" +
        "    </div>\n" +
        "\n" +
        "    <div class=\"row justify-content-center align-items-center\">\n" +
        "        <div class=\"col m-1 p-0\">\n" +
        "            <label for=\"where\">where</label>\n" +
        "            <textarea class=\"form-control mb-4\" id=\"where\" onchange=\"updateQuery()\"></textarea>\n" +
        "        </div>\n" +
        "\n" +
        "        <div class=\"col m-1 p-0\">\n" +
        "            <label for=\"addf\">additional fields</label>\n" +
        "            <textarea class=\"form-control mb-4\" id=\"addf\" placeholder=\"\" onchange=\"updateQuery()\"></textarea>\n" +
        "        </div>\n" +
        "\n" +
        "    </div>\n" +
        "\n" +
        "    <div class=\"row justify-content-center align-items-center\">\n" +
        "        <div class=\"col m-1 p-0\">\n" +
        "            <label for=\"completeQuery\">complete query</label>\n" +
        "            <textarea class=\"form-control mb-4\" id=\"completeQuery\" placeholder=\"\" onblur=\"resetOthers()\"></textarea>\n" +
        "        </div>\n" +
        "    </div>\n" +
        "\n" +
        "    <div class=\"row justify-content-center align-items-center\">\n" +
        "        <div class=\"col m-1 p-0\">\n" +
        "            <label for=\"datasrc\">database</label>\n" +
        "            <select id=\"datasrc\" class=\"form-control mb-4\" onchange=\"setDataSource()\">\n" +
        "                <option value=\"\" disabled=\"disabled\" selected>Select Datasource</option>\n" +
        "                <option value=\"wscontent\">wscontent</option>\n" +
        "                <option value=\"wscomm\">wscomm</option>\n" +
        "                <option value=\"wsuser\">wsuser</option>\n" +
        "                <option value=\"wslog\">wslog</option>\n" +
        "                <option value=\"wsshop\">wsshop</option>\n" +
        "            </select>\n" +
        "        </div>\n" +
        "\n" +
        "        <div class=\"col m-1 p-0\">\n" +
        "            <div class=\"col-2\">\n" +
        "                <button class=\"col m-1 p-0 btn btn-primary\" onclick=\"checkQuery()\">Get Results</button>\n" +
        "            </div>\n" +
        "        </div>\n" +
        "    </div>\n" +
        "\n" +
        "    <div class=\"row justify-content-center align-items-center\">\n" +
        "        <div class=\"col m-0 p-4 \">\n" +
        "            <p class=\"border p-2 rounded\" id=\"query\">Query</p>\n" +
        "            <h6>Results:</h6>\n" +
        "\n" +
        "            <div id=\"result\" style=\"overflow: scroll\"></div>\n" +
        "        </div>\n" +
        "    </div>\n"
    function updateQuery() {
        var str = "select " + document.getElementById("select").value + " from " + document.getElementById("from").value
        if (document.getElementById("where").value.length > 0) {
            str += " where " + document.getElementById("where").value
        }
        if (document.getElementById("addf").value.length > 0) {
            str += " " + document.getElementById("addf").value
        }
        document.getElementById('completeQuery').value=''
        document.getElementById("query").innerText = str
    }

    function setDataSource() {
        dataSource = document.getElementById("datasrc")[document.getElementById("datasrc").selectedIndex].value;
    }

    function checkQuery() {

        if (!dataSource) {
            alert("Select schema")
            return
        }
        if(document.getElementById('completeQuery').value.trim().length>0){
            var completeQuery = document.getElementById('completeQuery').value.replace(/%/g,'o~o').replace(/&/g,'o*o')
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="admin" action="getSqlData"
            params="'datasource='+dataSource+'&completeQuery='+completeQuery"
            onSuccess = "showResults(data)"/>
            return
        }
        var select = document.getElementById("select").value.replace(/%/g,'o~o').replace(/&/g,'o*o')
        if (!select.trim()) {
            alert("Enter fields to select")
            return
        }
        var from = document.getElementById("from").value.replace(/%/g,'o~o').replace(/&/g,'o*o')
        if (!from.trim()) {
            alert("Enter table")
            return
        }
        var where = document.getElementById("where").value.replace(/%/g,'o~o').replace(/&/g,'o*o')
        var adf = document.getElementById("addf").value.replace(/%/g,'o~o').replace(/&/g,'o*o')


        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="admin" action="getSqlData"
            params="'datasource='+dataSource+'&fields='+select+'&fromdb='+from+'&conditions='+where+'&adf='+adf"
            onSuccess = "showResults(data)"/>

    }



    const resetOthers=()=>{
        if(document.getElementById('completeQuery').value.trim().length>0){
            document.getElementById("select").value = ""
            document.getElementById("from").value = ""
            document.getElementById("where").value=""
            document.getElementById("addf").value=""
            document.getElementById("query").innerText=""
        }
    }

</script>

<script>
    const mongoStringHtml = "<div class=\"row justify-content-center align-items-center\">\n" +
        "        <div class=\"col m-1 p-0\">\n" +
        "            <label for=\"schema\">schema</label>\n" +
        "            <input placeholder=\"schema\" class=\"form-control mb-4\" id=\"schema\" >\n" +
        "        </div>\n" +
        "        <div class=\"col m-1 p-0\">\n" +
        "            <label for=\"filters\">filters</label>\n" +
        "            <textarea placeholder=\"eg: {'column': 'value','column': 'value'}\" class=\"form-control mb-4\" id=\"filters\"></textarea>\n" +
        "        </div>\n" +
        "    </div>\n" +
        "    <div class=\"row justify-content-center align-items-center\">\n" +
        "        <div class=\"col m-1 p-0\">\n" +
        "            <label for=\"sort\">sort</label>\n" +
        "            <textarea placeholder=\"eg: {'column': 1,'column': -1} ** 1 for ascending ** -1 for descending **\" class=\"form-control mb-4\" id=\"sort\"></textarea>\n" +
        "        </div>\n" +
        "        <div class=\"col m-1 p-0\">\n" +
        "            <label for=\"projection\">projection</label>\n" +
        "            <textarea placeholder=\"eg: {'column': 1,'column': 0} ** 1 to show ** 0 to hide **\" class=\"form-control mb-4\" id=\"projection\"></textarea>\n" +
        "        </div>\n" +
        "    </div>\n" +
        "    <div class=\"row justify-content-center align-items-center\">\n" +
        "        <button class='btn-primary btn' onclick=\"checkMongoQuery()\" >Get Data</button>\n" +
        "    </div>"+
        "\n" +
        "    <div class=\"row justify-content-center align-items-center\">\n" +
        "        <div class=\"col m-0 p-4 \">\n" +
        "            <h6>Results:</h6>\n" +
        "\n" +
        "            <div id=\"result\" style=\"overflow: scroll\"></div>\n" +
        "        </div>\n" +
        "    </div>\n"


    const checkMongoQuery=()=>{
        var schema = document.getElementById('schema').value
        var filters = document.getElementById('filters').value
        var sort = document.getElementById('sort').value
        var projection = document.getElementById('projection').value
        if(schema.trim().length==0){
            alert('Please enter valid schema.')
            return
        }

        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="admin" action="getMongoData"
            params="'schema='+schema+'&filter='+filters+'&sort='+sort+'&projection='+projection"
            onSuccess = "showResults(data)"/>

    }
</script>

<script>
    document.getElementById('view').innerHTML=sqlStringHtml
    function showDatabase(db){
        if(db=='sql'){
            document.getElementById('view').innerHTML=sqlStringHtml
        }
        if(db=='mongo'){
            document.getElementById('view').innerHTML=mongoStringHtml
        }
    }
    function showResults(data) {
        $('.loading-icon').addClass('hidden');

        if (data.success === "OK") {
            if(data.result.length===0){
                alert("No Result Found")
                document.getElementById("result").innerHTML = "<div>No Records Found...</div>"
                return
            }
            var htmlStr = "<table class='border'> <tr class='border'>"
            var keys = Object.keys(data.result[0])


            keys.forEach((da) => {
                htmlStr += "<th class='p-2 border'><center>" + da + "</center></th>"
            })
            htmlStr += "</tr>"
            data.result.forEach((da) => {
                htmlStr += "<tr>"
                keys.forEach((k) => {
                    htmlStr += "<td class='p-2 border'><center>" + da[k] + "</center></td>"
                })
                htmlStr += "</tr>"
            })
            htmlStr += "</table>"
            document.getElementById("result").innerHTML = htmlStr

        } else {
            alert(data.result)
            document.getElementById("result").innerText = data.result
        }
    }

</script>

<g:render template="/${session['entryController']}/footer_new"></g:render>
