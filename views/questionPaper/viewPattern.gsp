<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h2 class="text-center">Question Paper Details</h2>
                    <p><strong>Name:</strong> ${pattern.name}</p>
                    <p><strong>Description:</strong> ${pattern.description ?: 'N/A'}</p>
                    <p><strong>Created By:</strong> ${creatorName}</p>

                    <hr>

                    <h3>Sections</h3>
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th>#</th>
                            <th>Name</th>
                            <th>Instructions</th>
                            <th>Total Marks</th>
                            <th>Actions</th>
                        </tr>
                        </thead>
                        <tbody>
                        <g:each in="${sections}" var="section" status="i">
                            <tr>
                                <td>${i + 1}</td>
                                <td>${section.name}</td>
                                <td>${section.instructions ?: 'N/A'}</td>
                                <td>${section.totalMarks}</td>
                                <td>
                                    <a href="${createLink(controller: 'QuestionPaper', action: 'addQuestionType', params: [sectionId: section.id])}" class="btn btn-success btn-sm">Add Question Type</a>
                                    <a href="javascript:deleteSection(${section.id});" class="btn btn-danger btn-sm">Delete</a>
                                </td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td colspan="5">
                                    <h5>Details</h5>
                                    <table class="table table-bordered">
                                        <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Type</th>
                                            <th>Number of Questions</th>
                                            <th>Marks per Question</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <%
                                        def questionTypes = section.questionTypes.sort { a, b -> a.id <=> b.id }
                                        %>

                                        <g:each in="${questionTypes}" var="questionType" status="j">
                                            <tr>
                                                <td>${j + 1}</td>
                                                <td>${questionType.type}</td>
                                                <td>${questionType.numberOfQuestions}</td>
                                                <td>${questionType.marksPerQuestion}</td>
                                            </tr>
                                        </g:each>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </g:each>
                        </tbody>
                    </table>

                    <a href="${createLink(controller: 'QuestionPaper', action: 'addSection', params: [patternId: pattern.id])}" class="btn btn-primary">Add Section</a>



                </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script>
    function deleteSection(sectionId) {
        if (confirm('Are you sure you want to delete this section?')) {
            //start loader
            $('.loading-icon').removeClass('hidden');
            //call json action to delete section
            $.ajax({
                url: "${createLink(controller: 'QuestionPaper', action: 'deleteSection')}",
                type: 'POST',
                data: {sectionId: sectionId},
                success: function (data) {
                    if (data.status==='success') {
                        location.reload();
                    } else {
                        alert('Failed to delete section');
                    }
                },
                error: function () {
                    alert('Failed to delete section');
                }
            });
        }
    }
</script>

</body>
</html>
