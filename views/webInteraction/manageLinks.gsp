<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<style>
.sortable:hover {
    cursor: pointer;
    text-decoration: underline;
}
.table td, .table th {
    vertical-align: middle;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Approved Websites</h3>
                    <div class="container">

                        <!-- Form to Add a New Link -->
                        <%if(isLibrarian){%>
                        <form id="addLinkForm" class="form-inline mb-4">
                            <div class="form-group mx-sm-3 mb-2">
                                <label for="newLink" class="sr-only">New Link</label>
                                <input type="text" class="form-control" id="newLink" placeholder="Enter new link" style="width: 400px;">
                            </div>
                            <button type="submit" class="btn btn-primary mb-2">Add Link</button>
                        </form>
                        <%}%>

                        <!-- Links Table -->
                        <div class="table-responsive">
                            <table class="table table-hover" id="linksTable">
                                <thead>
                                <tr>
                                    <th class="sortable" data-field="link">Link</th>
                                    <th class="sortable" data-field="dateCreated">Created Date</th>
                                    <%if(isLibrarian){%>
                                    <th>Actions</th>
                                    <%}%>
                                </tr>
                                </thead>
                                <tbody>
                                <!-- Links will be loaded here via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script type="text/javascript">
    $(document).ready(function() {
        var sortField = 'link';
        var sortOrder = 'asc';

        // Load initial links
        loadLinks();

        // Function to load links via AJAX
        function loadLinks() {
            //loader
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: '${createLink(controller: 'webInteraction', action: 'loadLinks')}',
                data: {
                    batchId: '${batchId}',
                    sort: sortField,
                    order: sortOrder
                },
                success: function(response) {
                    // Clear existing links
                    $('#linksTable tbody').empty();
                    //loader
                    $('.loading-icon').addClass('hidden');
                    // Append links to the table
                    appendLinks(response.links);
                },
                error: function() {
                    alert('An error occurred while loading links.');
                }
            });
        }

        // Function to append links to the table
        function appendLinks(links) {
            links.forEach(function(link) {
                var row = $('<tr>');

                row.append($('<td>').text(link.link));
                row.append($('<td>').text(link.dateCreated));
                <%if(isLibrarian){%>
                var deleteButton = $('<button>', {
                    class: 'btn btn-danger btn-sm',
                    text: 'Delete',
                    click: function() {
                        if (confirm('Are you sure you want to delete this link?')) {
                            deleteLink(link.id);
                        }
                    }
                });

                row.append($('<td>').append(deleteButton));
                <%}%>

                $('#linksTable tbody').append(row);
            });
        }

        // Function to delete a link
        function deleteLink(id) {
            //loader
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: '${createLink(controller: 'webInteraction', action: 'deleteLink')}',
                type: 'POST',
                data: {
                    id: id
                },
                success: function() {
                    loadLinks();
                },
                error: function() {
                    alert('An error occurred while deleting the link.');
                }
            });
        }

        // Event handler for adding a new link
        $('#addLinkForm').on('submit', function(e) {
            e.preventDefault();
            //loader
            $('.loading-icon').removeClass('hidden');
            var newLink = $('#newLink').val().trim();
            if (newLink === '') {
                alert('Please enter a link.');
                return;
            }
            addLink(newLink);
        });

        // Function to add a new link
        function addLink(newLink) {
            //loader
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: '${createLink(controller: 'webInteraction', action: 'addLink')}',
                type: 'POST',
                data: {
                    newLink: newLink,
                    batchId: '${batchId}'
                },
                success: function() {
                    $('#newLink').val('');
                    loadLinks();
                },
                error: function(xhr) {
                    alert('Error: ' + xhr.responseText);
                }
            });
        }

        // Event handler for sorting columns
        $('.sortable').on('click', function() {
            var field = $(this).data('field');
            if (sortField === field) {
                sortOrder = (sortOrder === 'asc') ? 'desc' : 'asc';
            } else {
                sortField = field;
                sortOrder = 'asc';
            }
            loadLinks();
        });
    });
</script>

</body>
</html>
