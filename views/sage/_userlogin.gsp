<style>
.field-icon {
    float: right;
    margin-left: -10px;
    margin-top: -32px;
    position: relative;
    z-index: 2;
    font-size: 20px;
    color:rgba(0, 0, 0, 0.5);
    left:-1rem;
}
</style>
<div class="loading-icon hidden">
  <div class="loader-wrapper">
    <div class="loader">Loading</div>
  </div>
</div>

<div class="user-login" id="user-login">
  <a href="javascript:hideUserLogin();" class="close-sidebar">
    <i class="material-icons">close</i>
  </a>

  <div class="sage-login text-center">
    <p class="sage-login-signup-title">Login to SAGE Texts</p>
    <p class="sage-login-signup-welcome">Welcome back! Please log in below</p>
    <form id="forgotpassword" method="post" autocomplete="off" style="display:none;">
      <div class="form-group">
        <input type="email" name="username" id="fPemail" class="form-control sage-input" placeholder="Your Email Address">
      </div>
      <div class="form-group">
        <input type="button" id="fPbtn" onclick="javascript:formFPSubmit();" class="form-control full-width-input sage-input sage-login-btn waves-effect waves-ripple" value="Send reset link">
      </div>
      <div class="form-group">
        <div id="forgot-password-error" style="color: red; display: none;">Please enter email address</div>
      </div>
    </form>

    <form class="sage-login-form" id="sage-user-login-form" name="signin" method="post" autocomplete="off" action="/login/authenticate">
        <input type="hidden" name="username">
      <div class="form-group">
        <input type="email" name="username_temp" id="email" class="form-control sage-input email-input" placeholder="Email">
      </div>
      <div class="form-group password-form-group">
        <input type="password" name="password" id="password" class="form-control sage-input" placeholder="Password">
        %{--<a href="javascript:showHideLoginPassword();" class="show-password" id="show-password">Show password</a>--}%
          %{--<input id="password-field" type="password" class="form-control" name="password" value="secret">--}%
          <span toggle="#password" class="fa fa-fw fa-eye field-icon toggle-password"></span>
      </div>
      <div class="form-group">
        <input type="button" id="sign-in" onclick="submitSignIn();" class="form-control full-width-input sage-input sage-login-btn waves-effect waves-ripple" value="Sign in">
      </div>
        <div class="form-group">
            <div id="login-failed-error" style="color: red;display: none;">Incorrect username or password. Please try again.</div>
        </div>
    </form>
    <div class="forgot-password-signup">
      <a href="javascript:showForgotPassword();" class="forgot-or-signup">Forgot your username or password ›</a>
      <a href="javascript:showRegistration();" class="forgot-or-signup">Sign up now ›</a>
    </div>
  </div>
</div>

<div class="user-login" id="user-registration">
  <a href="javascript:hideUserLogin();" class="close-sidebar">
    <i class="material-icons">close</i>
  </a>

  <div class="sage-login text-center">
    <p class="sage-login-signup-title">Signup for SAGE Texts</p>
    <p class="sage-login-signup-welcome">Getting started takes only 60 seconds</p>
    
    <ul class="user-type-selection">
      <li>
        I'm a
        <a href="javascript:showRegisterationOptions();" class="user-type-instructor">
          <i class="material-icons">person</i>
        </a>
        Professor
      </li>
      <li>
        I'm a
        <a href="javascript:showRegisterationOptions();" class="user-type-student">
          <i class="material-icons">school</i>
        </a>
        Student
      </li>
    </ul>
    <hr class="bottom-hr" />

    <form class="sage-login-form sage-login-form-hide" style="display: none;" autocomplete="off" name="adduser" action="/creation/addUser">
      <input type="hidden" name="userType">
      <div class="form-group">
        <input type="text" class="form-control sage-input" id="name" placeholder="Name" name="name">
      </div>
      <div class="form-group">
        <input type="email" class="form-control sage-input email-input" id="signup-email" placeholder="Email" name="username">
      </div>
      <div class="form-group">
        <input type="password" class="form-control sage-input" id="signup-password" placeholder="Password" name="password">
          <span toggle="#signup-password" class="fa fa-fw fa-eye field-icon toggle-password"></span>
      </div>
      <div class="form-group">
        <input type="text" class="form-control sage-input mobile-input" id="signup-mobile" placeholder="Mobile" name="mobile">
        <div class="input-error-tooltip" style="display: none;">
          <div class="input-error-tooltip-inner">Please enter 10 digit mobile number.</div>
        </div>
      </div>
      <div class="form-group" id="is-tute">
        <input type="text" class="form-control sage-input" id="institute" placeholder="Institute" name="institution">
      </div>
      <div class="form-group" id="inst-dept">
        <input type="text" class="form-control sage-input" id="department" placeholder="Department" name="department">
      </div>
      <div class="form-group hidden" id="inst-course-sub">
        <input type="text" class="form-control sage-input" id="course-subject" placeholder="Course Subject" name="courseSubjects">
      </div>
      <div class="form-group" id="intrst-area">
        <select class="form-control sage-input-select" id="inst-int-ares" multiple="multiple" name="interests">
          <option>Aging and Gerontology</option>
          <option>Anthropology &amp; Archaeology</option>
          <option>Area and Ethnic Studies</option>
          <option>Arts & Humanitiess</option>
          <option>Business & Management</option>
          <option>Communication and Media Studies</option>
          <option>Counseling and Psychotherapy</option>
          <option>Criminology & Criminal Justice</option>
          <option>Cultural Studies</option>
          <option>Economics & Development Studies</option>
          <option>Education</option>
          <option>Engineering</option>
          <option>Environmental Studies</option>
          <option>Family Studies</option>
          <option>Gender & Sexuality Studies</option>
          <option>Health and Nursing</option>
          <option>Urban Studies and Geography</option>
          <option>History</option>
          <option>Information Science</option>
          <option>Interpersonal/Domestic Violence</option>
          <option>Politics & International Relations</option>
          <option>Psychology</option>
          <option>Research Methods</option>
          <option>Social Work & Social Policy</option>
          <option>Sociology</option>
          <option>Study Skills</option>
          <option>Theology and Biblical Studies</option>
          <option>Vocational and Professional Studies</option>
          <option>Medicine</option>
          <option>Science and Technology</option>
          <option>Serious Non-Fiction</option>
          <option>Other</option>
        </select>
      </div>
      <div class="form-group" id="education-level" include="form-input-select()">
        <select class="form-control sage-input-select" id="course-level" name="qualification" required>
          <option value="">Course Level</option>
          <option value="Undergraduate">Undergraduate</option>
          <option value="Post Graduate">Post Graduate</option>
          <option value="Certificate/Diploma">Certificate/Diploma</option>
        </select>
      </div>
      <div class="form-group password-form-group" id="student-discipline">
        <input type="text" class="form-control sage-input" id="discipline-student" placeholder="Discipline/Subject like history, etc." name="studentDiscipline">
      </div>
      <div class="form-group password-form-group">
        <select class="form-control sage-input-select selectpicker countrypicker" id="country-drp" name="country" data-default="India"></select>
      </div>
      <div class="form-group">
        <button type="button" class="form-control sage-input sage-login-btn waves-effect waves-ripple full-width-input" onclick="javascript:submitSignUp();">Signup</button>
      </div>
      <div class="form-group">
        <div id="form-error" style="display: none; color: red;">Highlighted fields are mandatory*</div>
      </div>
    </form>
    <div class="forgot-password-signup">
      <a href="javascript:showUserLogin();" class="forgot-or-signup">Already have an account, login now ›</a>
    </div>
  </div>
</div>
<div class="login-overlay"></div>
<div class="modal fade sage-modal" id="otpModal" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="otpModal">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header sage-modal-header">
        <a href="javascript:hideUserLogin();" class="close-sidebar pull-right" data-dismiss="modal">
          <i class="material-icons">close</i>
        </a>
      </div>
      <div class="modal-body">
        <form class="sage-login-form" style="margin-top: 0;">
          <div class="form-group" id="use-email-wrapper">
            <input type="text" class="form-control sage-input" id="otp-email" placeholder="Email" disabled="disabled">
          </div>
          <div class="form-group" id="email-otp-wrapper">
            <input type="text" maxlength="10" class="form-control sage-input" id="user-email-otp" placeholder="Enter Email OTP">
              <a href="javascript:requestEmailOtp();" id="resend-email-otp" class="resend-otp">Resend OTP</a>
          </div>
          <div class="form-group">
            <input type="button" class="form-control sage-input sage-login-btn waves-effect waves-ripple full-width-input" onclick="javascript:submitUserOtp();" value="Submit">
          </div>
          <div class="form-group">
            <div id="user-otp-error" style="display: none; color: red;">Please enter OTP*</div>
            <div id="user-email-otp-error" style="display: none; color: red;">Incorrect Email OTP. Please try again or request a resend.</div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<script>
    $("#forgotpassword").on("keydown", function (e) {
        if (e.keyCode === 13) {  //checks whether the pressed key is "Enter"
            e.preventDefault();
            formFPSubmit();
        }
    });

    function submitSignUp() {
        var userEnterMobile = document.getElementById('signup-mobile').value;
        var userEnteredEmail = document.getElementById('signup-email').value;
        var passwordRegex = /^(?=[a-zA-Z0-9#@$?~!%^&*()-_+=|\]{8,}$)(?=.*?[a-z])(?=.*?[A-Z])(?=.*?[0-9]).*/;
        var allEntered=true;

        if($('#name').val()=="") {
            allEntered=false;
            $('#form-error').show();

            $('#name').css({
                'border' : '1px solid #F6A145'
            });
        } else {
          $('#name').css({
            'border' : '1px solid #cccccc'
          });
        }

        if($('#signup-email').val()=="") {
            allEntered=false;
            $('#form-error').show();

            $('#signup-email').css({
                'border' : '1px solid #F6A145'
            });
        } else {
          $('#signup-email').css({
            'border' : '1px solid #cccccc'
          });
        }

        if($('#signup-password').val()=="") {
            allEntered=false;
            $('#form-error').show();

            $('#signup-password').css({
                'border' : '1px solid #F6A145'
            });
        } else if (!$('#signup-password').val().match(passwordRegex)) {
            var allEntered=false;
            $('#form-error').html("Please enter atleast one capital letter and one number in password");
            $('#form-error').show();

            $('#signup-password').css({
                'border' : '1px solid #F6A145'
            });
        } else {
          $('#signup-password').css({
            'border' : '1px solid #cccccc'
          });
        }

        if($('#institute').val()=="") {
            allEntered=false;
            $('#form-error').show();

            $('#institute').css({
                'border' : '1px solid #F6A145'
            });
        } else {
          $('#institute').css({
            'border' : '1px solid #cccccc'
          });
        }

        if($('#department').val()=="") {
            allEntered=false;
            $('#form-error').show();

            $('#department').css({
                'border' : '1px solid #F6A145'
            });
        } else {
          $('#department').css({
            'border' : '1px solid #cccccc'
          });
        }

        if($('#inst-int-ares').val()=="") {
            allEntered=false;
            $('#form-error').show();

            $('#inst-int-ares').css({
                'border' : '1px solid #F6A145'
            });
        } else {
          $('#inst-int-ares').css({
            'border' : '1px solid #cccccc'
          });
        }

        if("student"==document.adduser.userType.value&&$('#course-level').val()=="") {
            allEntered=false;
            $('#form-error').show();

            $('#course-level').css({
                'border' : '1px solid #F6A145'
            });
        } else {
          $('#course-level').css({
            'border' : '1px solid #cccccc'
          });
        }

        if("student"==document.adduser.userType.value&&$('#discipline-student').val()=="") {
            allEntered=false;
            $('#form-error').show();

            $('#discipline-student').css({
                'border' : '1px solid #F6A145'
            });
        } else {
          $('#discipline-student').css({
            'border' : '1px solid #cccccc'
          });
        }

        if($('#country-drp').val()=="") {
            allEntered=false;
            $('#form-error').show();

            $('#country-drp').css({
                'border' : '1px solid #F6A145'
            });
        }

        if($('#signup-mobile').val()=="") {
            allEntered=false;
            $('#form-error').show();

            $('#signup-mobile').css({
                'border' : '1px solid #F6A145'
            });
        } else {
          $('#signup-mobile').css({
            'border' : '1px solid #cccccc'
          });
        }

        if(allEntered){
            document.getElementById('otp-email').value = userEnteredEmail;
            $('.loading-icon').removeClass('hidden');
            checkUsernameExists();
        } else {
            $('#form-error').show().html("Highlighted fields are mandatory*");
            if($('#signup-password').val() == "") {
                var allEntered=false;
                $('#form-error').show().html("Highlighted fields are mandatory*");
                $('#signup-password').css({
                    'border' : '1px solid #F6A145'
                });
            } else if (!$('#signup-password').val().match(passwordRegex)) {
                $('#form-error').show().html("Please enter atleast one capital letter and one number in password");
            }
        }
    }

    function requestEmailOtp() {
        $('#user-email-otp-error').hide();
        $('#resend-email-otp').hide();
        <g:remoteFunction controller="creation" action="generateOTP" onSuccess=''
                params="'email='+document.getElementById('otp-email').value" />
    }

    function requestMobileOtp() {
        $('#user-mobile-otp-error').hide();
        $('#resend-mobile-otp').hide();
        <g:remoteFunction controller="creation" action="generateOTP" onSuccess=''
                params="'mobile='+document.getElementById('otp-mobile').value" />
    }

    function checkUsernameExists() {
        var username = $("#signup-email").val();
        <g:remoteFunction controller="creation" action="checkUserNameExists"  onSuccess='userNameExistsResult(data);' params="'username='+username" />
    }

    function checkMobileExists() {
        var mobile = $("#signup-mobile").val();
        <g:remoteFunction controller="creation" action="checkMobileExists"  onSuccess='userNameExistsResult1(data);' params="'mobile='+mobile" />
    }

    function userNameExistsResult(data) {
        if(data=="0") {
            checkMobileExists();
        } else {
            $('.loading-icon').addClass('hidden');
            $('#form-error').html('Email already exists.!!');
            $('#form-error').show();
            $('#signup-email').css({
                'border' : '1px solid #F6A145'
            });
        }
    }

    function userNameExistsResult1(data) {
        if(data==="0") {
            <g:remoteFunction controller="creation" action="generateOTP" onSuccess=''
                    params="'email='+document.getElementById('otp-email').value+'&name='+document.getElementById('name').value" />

            $('.loading-icon').addClass('hidden');
            $('#user-otp-error').hide();
            $('#user-email-otp-error').hide();
            $('#resend-email-otp').hide();
            $('#otpModal').modal('show');
            $('.login-overlay').hide();
            $('#user-registration').removeClass('user-login-slide');
        } else {
            $('.loading-icon').addClass('hidden');
            $('#form-error').html('Mobile already exists.!!');
            $('#form-error').show();
            $('#signup-mobile').css({
                'border' : '1px solid #F6A145'
            });
        }
    }

    function submitUserOtp() {
        var allEntered=true;
        $('#user-otp-error').hide();
        $('#user-email-otp-error').hide();

        if($('#use-email-wrapper').is(":visible") && $('#user-email-otp').val() === "") {
            $('#user-email-otp').css({
                'border' : '1px solid #F6A145'
            });

            $('#user-otp-error').show();
            allEntered=false;
        }

        if(allEntered) {
            $('#user-email-otp-error').hide();
            $('#resend-email-otp').hide();

            <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='submitUser(data);'
                        params="'email='+document.getElementById('otp-email').value+'&email_otp='+document.getElementById('user-email-otp').value" />
        }
    }

    function submitUser(data) {
        if(data.status === "OK") {
            $('#user-email-otp-error').hide();
            document.adduser.submit();
            $('#loading-icon').addClass('hidden');
        } else {
            if(data.status.endsWith("E")) {
                $('#resend-email-otp').show();
                $('#user-email-otp-error').show();
            }
        }
    }

    function formFPSubmit() {
        if($("#fPemail").val() === '') {
            $('#forgot-password-error').show()
        } else {
            var email = $("#fPemail").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                return false;
            } else {
                $(".loading-icon").removeClass('hidden');
                <g:remoteFunction controller="creation" action="forgottenPassword"  onSuccess='displayFPResults(data);'
                        params="'email='+email" />
            }
        }
    }

    function displayFPResults(data) {
        var userEmail = $('#fPemail').val();

        if(data.status === "Fail") {
            $(".loading-icon").addClass('hidden');
            $('#forgot-password-error').show().html("Email not registered. Please signup");
        } else {
            $(".loading-icon").addClass('hidden');
            $('#forgot-password-error').show().html("Password reset link sent to "+"“"+userEmail+"”");
            $('#fp-user-email').html("“"+userEmail+"”");
        }
    }

    function showHideLoginPassword() {
        var eye = document.getElementById("show-password");
        eye.addEventListener("click", function(e){
            e.preventDefault();

            var pwd = document.getElementById("password");

            if(pwd.getAttribute("type")==="password"){
                pwd.setAttribute("type","text");
                eye.style.background = 'url(../assets/eyeCross.png)';
                eye.style.backgroundSize = '100% 100%';
                eye.style.bottom = '13px';
                eye.style.height = '18px';
                eye.style.backgroundRepeat = 'no-repeat';
                $('#show-password').addClass('passwordShow');
            } else {
                pwd.setAttribute("type","password");
                eye.style.background = 'url(../assets/eye.png)';
                eye.style.height = '13px';
                eye.style.bottom = '15px';
                eye.style.backgroundRepeat = 'no-repeat';
                    $('#show-password').removeClass('passwordShow');
            }
        });
    }

    function showHideSignupPassword() {
        var eye = document.getElementById("show-password-signup");

        $(eye).on("click", function(e){
            e.preventDefault();
            var pwd = document.getElementById("signup-password");

            if(pwd.getAttribute("type")==="password"){
                pwd.setAttribute("type","text");
                eye.style.background = 'url(../assets/eyeCross.png)';
                eye.style.backgroundSize = '100% 100%';
                eye.style.bottom = '13px';
                eye.style.height = '18px';
                eye.style.backgroundRepeat = 'no-repeat';
                $('#show-password-signup.show-password').addClass('passwordShow');
            } else {
                pwd.setAttribute("type","password");
                eye.style.background = 'url(../assets/eye.png)';
                eye.style.height = '13px';
                eye.style.bottom = '15px';
                eye.style.backgroundRepeat = 'no-repeat';

                $('#show-password-signup.show-password').removeClass('passwordShow');
            }
        });
    }

    var addClass = function(el, className) {
        if (el.classList) {
            el.classList.add(className);
        } else {
            el.className += ' ' + className;
        }
    },
    hasClass = function(el, className) {
        return el.classList ?
            el.classList.contains(className) :
            new RegExp('(^| )' + className + '( |$)', 'gi').test(el.className);
    },
    removeClass = function(el, className) {
        if (el.classList) {
            el.classList.remove(className);
        } else {
            el.className = el.className.replace(new RegExp('(^|\\b)' + className.split(' ').join('|') + '(\\b|$)', 'gi'), ' ');
        }
    },
    updateSelectPlaceholderClass = function(el) {
        var opt = el.options[el.selectedIndex];
        if(hasClass(opt, "placeholder")) {
            addClass(el, "placeholder");
        } else {
            removeClass(el, "placeholder");
        }
    },
    selectList = document.querySelectorAll("select");

    //Simulate placeholder text for Select box
    for(var i = 0; i < selectList.length; i++) {
        var el = selectList[i];
        updateSelectPlaceholderClass(el);
        el.addEventListener("change", function() {
            updateSelectPlaceholderClass(this);
        });
    }

    $('.toggle-password').click(function() {
        $(this).toggleClass('fa-eye fa-eye-slash');
        var input = $($(this).attr('toggle'));

        if (input.attr('type') === 'password') {
            input.attr('type', 'text');
        } else {
            input.attr('type', 'password');
        }
    });

    function submitSignIn(){

        document.signin.username.value= "${session["siteId"]}_"+document.signin.username_temp.value;

        document.signin.submit();
    }
</script>
