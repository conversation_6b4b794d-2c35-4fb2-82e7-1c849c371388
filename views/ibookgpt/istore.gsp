<%@ page import="javax.servlet.http.Cookie" %>
<%String requestURL = request.getRequestURL().toString();
String servletPath = request.getServletPath();
String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));

session.setAttribute("servername", appURL);
def newCookie = new javax.servlet.http.Cookie( "siteName", "ibookgpt");
newCookie.path = "/"
response.addCookie newCookie;
%>

<!-- Critical CSS to prevent FOUC - loaded immediately -->
<style>
/* Critical styles to prevent flash of unstyled content */
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.page-main-wrapper {
    background: transparent !important;
}

/* Hide content initially to prevent FOUC */
.istore-container {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.istore-container.loaded {
    opacity: 1;
}

/* Show content immediately after critical styles load */
.istore-header {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Critical banner styles - match the final design */
.istore-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    background-image: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
                      radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.15) 0%, transparent 50%) !important;
    border-radius: 20px !important;
    padding: 40px 0 !important;
    margin: 0 -15px 2rem -15px !important;
    color: white !important;
    text-align: center !important;
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4) !important;
    position: relative !important;
    overflow: hidden !important;
}

/* Override any conflicting styles that might cause FOUC */
.istore-header * {
    color: white !important;
}

/* Critical header content layout - prevent vertical stacking FOUC */
.istore-header-content {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    text-align: center !important;
}

.istore-title {
    font-size: 2.5rem !important;
    font-weight: 800 !important;
    margin: 0 !important;
    line-height: 1.2 !important;
    white-space: normal !important;
}

.istore-subtitle {
    font-size: 1.2rem !important;
    font-weight: 400 !important;
    color: rgba(255, 255, 255, 0.9) !important;
    margin: 0 !important;
    white-space: normal !important;
}

/* Critical sparkle positioning - prevent FOUC */
.istore-header .sparkle {
    position: absolute !important;
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 1.5rem !important;
    z-index: 1 !important;
    opacity: 0.3 !important;
}
</style>

<!-- Immediate script to prevent FOUC -->
<script>
(function() {
    // Show content immediately after critical CSS loads
    document.documentElement.style.setProperty('--content-ready', '1');
})();
</script>

<style>

/* Ensure no old background colors show through */
.page-main-wrapper .istore-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}



.istore-filters-row {
    background: #ffffff;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    display: flex !important;
    gap: 1rem;
    align-items: flex-end;
    flex-wrap: wrap;
    justify-content: space-between;
}

.istore-books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
}
</style>

<g:render template="/privatelabel/navheader_new"></g:render>

<!-- Immediate style application script -->
<script>
// Apply styles immediately without waiting for DOMContentLoaded
(function() {
    var style = document.createElement('style');
    style.textContent = '.istore-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; color: white !important; padding: 40px 0 !important; margin: 0 -15px 2rem -15px !important; border-radius: 20px !important; box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4) !important; }';
    document.head.appendChild(style);
})();
</script>

<g:render template="/ibookgpt/istoreDisplay"></g:render>

<g:render template="/privatelabel/footer_new"></g:render>
<g:render template="/resources/shareContent"></g:render>

<g:render template="/ibookgpt/istoreLogic"></g:render>

<style>
/* Fix background color issues */
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}



/* Modern iStore Styles */
.istore-container {
    background: transparent !important;
    min-height: 100vh;
    padding: 2rem 0;
}





.istore-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);
    opacity: 0.3;
    pointer-events: none;
}

.istore-header::after {
    content: '🤖';
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 3rem;
    opacity: 0.3;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.istore-header .sparkle {
    position: absolute;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.5rem;
    animation: sparkle 2s ease-in-out infinite;
    z-index: 1;
}

.istore-header .sparkle:nth-child(1) { top: 15%; left: 15%; animation-delay: 0s; }
.istore-header .sparkle:nth-child(2) { top: 25%; right: 20%; animation-delay: 0.5s; }
.istore-header .sparkle:nth-child(3) { bottom: 20%; left: 25%; animation-delay: 1s; }
.istore-header .sparkle:nth-child(4) { bottom: 30%; right: 15%; animation-delay: 1.5s; }

@keyframes sparkle {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(180deg);
    }
}





.istore-search-container {
    position: relative;
    flex: 0 0 280px;
    transition: flex 0.3s ease;
    align-self: flex-end;
}

.istore-search-container.expanded {
    flex: 0 0 400px;
}

.istore-search-input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 0.9rem;
    background: #ffffff;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.istore-search-input:focus {
    outline: none;
    border-color: #e8d5c4;
    box-shadow: 0 0 0 3px rgba(232, 213, 196, 0.3);
}

.istore-search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #e8d5c4;
    font-size: 1.1rem;
    cursor: pointer;
}

.istore-filters-horizontal {
    display: flex !important;
    gap: 1rem;
    flex: 1;
    align-items: flex-end;
    flex-wrap: wrap;
    flex-direction: row !important;
}

.istore-filter-item {
    display: flex;
    flex-direction: column;
    min-width: 140px;
}

.istore-filter-label {
    font-size: 0.75rem;
    color: #718096;
    margin-bottom: 0.25rem;
    font-weight: 500;
    letter-spacing: 0.02em;
}

.istore-filter-select {
    padding: 0.6rem 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.85rem;
    background: #ffffff;
    transition: all 0.2s ease;
    cursor: pointer;
    min-width: 140px;
}

.istore-filter-select:focus {
    outline: none;
    border-color: #e8d5c4;
    box-shadow: 0 0 0 2px rgba(232, 213, 196, 0.2);
}



.istore-clear-filters {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 0.6rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
    align-self: flex-end;
    margin-left: 0.5rem;
}

.istore-clear-filters:hover {
    background: #ff5252;
    transform: translateY(-1px);
}

.istore-filter-label {
    font-size: 0.75rem;
    color: #718096;
    margin-bottom: 0.25rem;
    font-weight: 500;
    letter-spacing: 0.02em;
}

.istore-filter-select {
    padding: 0.6rem 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.85rem;
    background: #ffffff;
    transition: all 0.2s ease;
    cursor: pointer;
    min-width: 140px;
}

.istore-filter-select:focus {
    outline: none;
    border-color: #e8d5c4;
    box-shadow: 0 0 0 2px rgba(232, 213, 196, 0.2);
}





.istore-class-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

/* Different gradient backgrounds for each color */
.istore-class-icon.color-1 {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.istore-class-icon.color-2 {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.istore-class-icon.color-3 {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.istore-class-icon.color-4 {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.istore-class-icon.color-5 {
    background: linear-gradient(135deg, #fa709a, #fee140);
}

.istore-class-icon.color-6 {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.istore-class-icon i {
    color: white;
    font-size: 1.8rem;
}

.istore-class-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #4a5568;
    font-size: 1.3rem;
    line-height: 1.4;
    letter-spacing: -0.01em;
    transition: color 0.3s ease;
}

.istore-class-card-container:hover .istore-class-title {
    color: #2d3748;
}



.istore-class-card-container {
    background: #faf7f2;
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.istore-class-card-container:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
}

.istore-class-card-content {
    position: relative;
    z-index: 2;
}

.istore-clear-filters {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
    border: none !important;
    color: white !important;
    padding: 0.6rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    height: fit-content;
    align-self: flex-end;
}

.istore-clear-filters:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.istore-no-results {
    text-align: center;
    padding: 4rem 2rem;
    background: #ffffff;
    border-radius: 20px;
    margin: 2rem 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.istore-no-results-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.istore-no-results-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.istore-no-results-text {
    color: #718096;
    font-size: 1rem;
    line-height: 1.5;
}

/* Loading Animation */
.istore-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.istore-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(232, 213, 196, 0.3);
    border-left: 4px solid #e8d5c4;
    border-radius: 50%;
    animation: istore-spin 1s linear infinite;
}

@keyframes istore-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .istore-filters-row {
        flex-direction: column;
        gap: 1.5rem;
    }

    .istore-search-container {
        flex: 1;
        max-width: 100%;
    }

    .istore-filters-horizontal {
        justify-content: center;
    }

    .istore-books-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 1.5rem;
        row-gap: 2rem;
    }
}

@media (max-width: 768px) {
    .istore-filters-row {
        padding: 1rem;
        align-items: flex-start;
        flex-direction: column;
        gap: 1rem;
    }

    .istore-search-container {
        align-self: flex-start;
        flex: 1;
        max-width: 100%;
    }

    .istore-filters-horizontal {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        width: 100%;
    }

    .istore-filter-item {
        min-width: auto;
    }

    .istore-filter-select {
        min-width: auto;
    }

    .istore-clear-filters {
        align-self: flex-start;
    }

    .istore-header {
        padding: 30px 0;
        margin: 0 -15px 2rem -15px;
    }

    .istore-header-content {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .istore-title {
        font-size: 2.2rem;
        font-weight: 800;
        white-space: normal;
    }

    .istore-subtitle {
        font-size: 1.1rem;
        white-space: normal;
    }

    .istore-books-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        row-gap: 1.5rem;
        padding: 1rem;
    }

    .istore-class-title {
        font-size: 1.2rem;
    }
}

/* Additional background fixes */
.page-main-wrapper,
.page-main-wrapper *,
section,
.container,
.row,
.col-12,
.col-lg-3,
.col-lg-9 {
    background: transparent !important;
}

html,
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
}

/* Filter selection highlighting */
.background-bg {
    background-color: #e8f4fd !important;
    border-color: #667eea !important;
}

/* Ensure filters display properly */
#filters {
    display: flex !important;
    visibility: visible !important;
    flex-direction: row !important;
    gap: 1rem !important;
    align-items: flex-end !important;
    flex-wrap: wrap !important;
}

#filters select {
    background: #ffffff !important;
    border: 2px solid #e9ecef !important;
    color: #495057 !important;
}

#filters select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
}
</style>

<script>
// Prevent FOUC and enhanced hover effects for class cards
// Show content immediately when script runs (before DOMContentLoaded)
(function() {
    function showContent() {
        var storeContainer = document.querySelector(".istore-container");
        if (storeContainer) {
            storeContainer.classList.add("loaded");
        }
    }

    // Try to show content immediately
    if (document.readyState === 'loading') {
        // If still loading, wait for DOM
        document.addEventListener("DOMContentLoaded", showContent);
    } else {
        // DOM already loaded, show immediately
        showContent();
    }
})();

document.addEventListener("DOMContentLoaded", function() {

    // Enhanced hover effects for class cards
    var classCards = document.querySelectorAll(".istore-class-card");

    for(var i = 0; i < classCards.length; i++) {
        classCards[i].addEventListener("mouseenter", function() {
            this.style.transform = "translateY(-10px) scale(1.02)";
        });

        classCards[i].addEventListener("mouseleave", function() {
            this.style.transform = "translateY(0) scale(1)";
        });
    }
});

// Fallback to show content if DOMContentLoaded doesn't fire
window.addEventListener("load", function() {
    var storeContainer = document.querySelector(".istore-container");
    if (storeContainer && !storeContainer.classList.contains("loaded")) {
        storeContainer.classList.add("loaded");
    }
});
</script>
