<%
    String levelLabel = "Select Level"
    String syllabusLabel = "Select Board"
    String gradeLabel = "Select Grade"
    if(session["levelLabel"]!=null) levelLabel = session["levelLabel"]
    if(session["syllabusLabel"]!=null) syllabusLabel = session["syllabusLabel"]
    if(session["gradeLabel"]!=null) gradeLabel = session["gradeLabel"]
%>

<!-- Full Width Hero Section -->
<div class="istore-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); background-image: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.15) 0%, transparent 50%); color: white; padding: 40px 0; margin: 0 -15px 2rem -15px; border-radius: 20px; box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);">
    <div class="container">
        <div class="sparkle">✨</div>
        <div class="sparkle">⭐</div>
        <div class="sparkle">💫</div>
        <div class="sparkle">✨</div>
        <div class="istore-header-content">
            <h1 class="istore-title">Choose your AI Tutor</h1>
            <p class="istore-subtitle">Personalized Learning with AI-Powered Books</p>
        </div>
    </div>
</div>

<div class="istore-container">
    <div class="container">

        <!-- Compact Search and Filters Row -->
        <div class="istore-filters-row">
            <!-- Search Container -->
            <div class="istore-search-container">
                <input type="text" 
                       id="search-book-store" 
                       class="istore-search-input" 
                       placeholder="Search for courses"
                       onfocus="expandSearch(this)"
                       onblur="contractSearch(this)"
                       onkeypress="if(event.keyCode==13) submitSearchTop();">
                <i class="material-icons istore-search-icon" onclick="submitSearchTop();">search</i>
            </div>

            <!-- Filters -->
            <div class="istore-filters-horizontal" id="filters">
                <div class="istore-filter-item">
                    <label class="istore-filter-label">Level</label>
                    <select id="level" onchange="levelChanged(this)" class="istore-filter-select">
                        <option value="" selected="selected">${levelLabel}</option>
                    </select>
                </div>

                <div class="istore-filter-item">
                    <label class="istore-filter-label">Board</label>
                    <select id="syllabus" onchange="syllabusChanged(this)" class="istore-filter-select">
                        <option value="" selected="selected">${syllabusLabel}</option>
                    </select>
                </div>

                <div class="istore-filter-item">
                    <label class="istore-filter-label">Grade</label>
                    <select id="grade" onchange="gradeChanged(this)" class="istore-filter-select">
                        <option value="" selected="selected">${gradeLabel}</option>
                    </select>
                </div>

                <!-- Note: Subject filter is removed as per requirements -->

                <button onclick="resetFilters()" id="resetFilter" class="istore-clear-filters">
                    Clear All
                </button>
            </div>
        </div>

        <sec:ifAllGranted roles="ROLE_WS_SALES_TEAM">
            <div class="text-center mb-4">
                <button class="btn btn-primary" onclick="downloadCatalogue();">
                    <i class="material-icons" style="font-size: 16px;">download</i>
                    Download Catalogue
                </button>
            </div>
        </sec:ifAllGranted>

        <!-- Main Content Row -->
        <div class="row">
            <!-- Books Grid Section -->
            <div class="col-12">
                <!-- No Results Message -->
                <div class="istore-no-results searchShowHide" style="display: none;" id="noResultsFound">
                    <div class="istore-no-results-icon">
                        <i class="material-icons">sentiment_dissatisfied</i>
                    </div>
                    <div class="istore-no-results-title">No Results Found</div>
                    <div class="istore-no-results-text">
                        We couldn't find any AI tutors matching your search criteria. Please try adjusting your filters.
                    </div>
                </div>

                <!-- Books Grid Container -->
                <div class="istore-books-grid" id="content-data-books-ebooks">
                    <!-- Books will be loaded here dynamically -->
                </div>

                <!-- Load More Section -->
                <div class="text-center mt-4" id="loadMoreSection" style="display: none;">
                    <button class="btn btn-primary btn-lg" onclick="loadMoreBooks();" id="loadMoreBtn">
                        Load More AI Tutors
                    </button>
                </div>
            </div>
        </div>


    </div>
</div>

<!-- Loading Spinner -->
<div class="loading-icon hidden">
    <div class="istore-loading">
        <div class="istore-loading-spinner"></div>
    </div>
</div>



<style>
/* Additional styles for class cards in grid layout */
.istore-class-card-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #f0ebe0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    overflow: hidden;
    border-radius: 20px;
    background: #faf7f2;
    position: relative;
    z-index: 1;
    cursor: pointer;
    transition: all 0.3s ease;
}

.istore-class-card-container:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
}

/* Different background colors for variety */
.istore-class-card-container.color-1 { background: #faf7f2; border-color: #f0ebe0; }
.istore-class-card-container.color-2 { background: #faf2f7; border-color: #f0d8eb; }
.istore-class-card-container.color-3 { background: #f2f7fa; border-color: #d8e8f0; }
.istore-class-card-container.color-4 { background: #f7faf2; border-color: #e8f0d8; }
.istore-class-card-container.color-5 { background: #faf2f2; border-color: #f0d8d8; }
.istore-class-card-container.color-6 { background: #f2f2fa; border-color: #d8d8f0; }

/* Icon color variations */
.istore-class-icon.color-1 { background: linear-gradient(135deg, #667eea, #764ba2); }
.istore-class-icon.color-2 { background: linear-gradient(135deg, #4285f4, #667eea); }
.istore-class-icon.color-3 { background: linear-gradient(135deg, #ff9500, #ff6b35); }
.istore-class-icon.color-4 { background: linear-gradient(135deg, #34a853, #4caf50); }
.istore-class-icon.color-5 { background: linear-gradient(135deg, #e91e63, #f06292); }
.istore-class-icon.color-6 { background: linear-gradient(135deg, #8e44ad, #9b59b6); }

/* Ensure proper spacing and alignment */
.istore-class-card-content {
    padding: 10px 10px;
    text-align: center;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .istore-class-card-content {
        padding: 30px 20px;
        height: 180px;
    }
}

@media (max-width: 576px) {
    .istore-class-card-content {
        padding: 25px 15px;
        height: 160px;
    }
}
</style>
