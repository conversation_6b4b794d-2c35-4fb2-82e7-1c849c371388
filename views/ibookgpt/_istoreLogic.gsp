<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "url": "${request.getRequestURL()}",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://${request.getServerName()}/ibookgpt/istore?searchString={search_term_string}",
    "query-input": "required name=search_term_string"
  }
}
</script>

<script>
    var searchStringValues = [];
    var searchDataValues = [];
    var searchString = "${params.searchString}";

    var tokenId="${params.tokenId}";
    var allBooksData;
    var useSearchSelect = false;
    var pageNo = 0;
    var isAjaxCalled;
    var allBooksLoaded = false;
    var showPublishers = false;
    var searchMode = false;
    var searchStatus = "";
    var pageInitialized = false;
    var siteName="ibookgpt";
    var prepjoySite = false;
    var subscriptionBooks=false;
    var amazonSearch = false;
    var wiley=false;
    var wileyCollectionBookId=null;

    var ebookOfTheDay,newlyReleasedEbook,trendingNowEbook,urltags = "";
    var disablePrev = 1;

    // Enhanced search functionality for iStore
    function expandSearch(element) {
        jQuery(element).closest('.istore-search-container').addClass('expanded');
    }

    function contractSearch(element) {
        setTimeout(function() {
            jQuery(element).closest('.istore-search-container').removeClass('expanded');
        }, 200);
    }

    function submitSearchTop(){
        isAjaxCalled = true;
        var searchString = document.getElementById("search-book-store").value;
        var callRemoteSearch = true;

        document.querySelector('.loading-icon').classList.remove('hidden');
        document.getElementById("content-data-books-ebooks").innerHTML="";
        
        if(searchStringValues!=null&&searchStringValues!=""){
            for(var i=0;i<searchStringValues.length;i++){
                if(searchStringValues[i]==searchString){
                    callRemoteSearch = populateFromFilters(searchDataValues[i]);
                    break
                }
            }
        }
        if(callRemoteSearch) {
            var level=null,syllabus=null,grade=null,publisherId=null,pageNo;
           <% if(params.publisherId!=null||params.level!=null||params.syllabus!=null||params.grade!=null) {%>
            pageInitialize();
            <%}else {%>
                <g:remoteFunction controller="discover" action="search"  onSuccess='booksReceived(data);'
         params="'searchString='+searchString" />
            <%}%>
        }
    }

    function populateFromFilters(data){
        var level = document.getElementById("level").value;
        var syllabus = document.getElementById("syllabus").value;
        var grade = document.getElementById("grade").value;
        // Note: subject filter removed for iStore

        var filteredData = data;
        if(level!=""){
            filteredData = filteredData.filter(function(book) {
                return book.level == level;
            });
        }
        if(syllabus!=""){
            filteredData = filteredData.filter(function(book) {
                return book.syllabus == syllabus;
            });
        }
        if(grade!=""){
            filteredData = filteredData.filter(function(book) {
                return book.grade == grade;
            });
        }

        booksReceived(filteredData);
        return false;
    }

    function resetFilters(){
        document.getElementById("level").selectedIndex = 0;
        document.getElementById("syllabus").selectedIndex = 0;
        document.getElementById("grade").selectedIndex = 0;
        document.getElementById("search-book-store").value = "";

        // Remove background highlighting from all filters
        document.getElementById("level").classList.remove("background-bg");
        document.getElementById("syllabus").classList.remove("background-bg");
        document.getElementById("grade").classList.remove("background-bg");

        document.getElementById("content-data-books-ebooks").innerHTML="";
        pageNo = -1;
        allBooksLoaded = false;
        getBooks();

        document.getElementById("resetFilter").removeAttribute('disabled');
        document.getElementById("resetFilter").style.color = '#007bff';
        document.getElementById("loadMoreSection").style.display = 'none';
    }

    function levelChanged(field){
        allBooksLoaded=false;
        if(document.getElementById("level").selectedIndex>0) document.getElementById("level").classList.add("background-bg");
        else document.getElementById("level").classList.remove("background-bg");
        document.getElementById("syllabus").style.display = 'block';

        // Reset dependent filters when level changes
        var syllabusSelect = document.getElementById("syllabus");
        var gradeSelect = document.getElementById("grade");

        if(syllabusSelect) {
            syllabusSelect.selectedIndex = 0;
            syllabusSelect.classList.remove("background-bg");
        }
        if(gradeSelect) {
            gradeSelect.selectedIndex = 0;
            gradeSelect.classList.remove("background-bg");
        }

        document.getElementById("content-data-books-ebooks").innerHTML="";
        pageNo = -1;
        getBooks();
        document.getElementById("loadMoreSection").style.display = 'none';
    }

    function syllabusChanged(field) {
        allBooksLoaded=false;
        if(document.getElementById("syllabus").selectedIndex>0) document.getElementById("syllabus").classList.add("background-bg");
        else document.getElementById("syllabus").classList.remove("background-bg");

        // Reset dependent filters when syllabus changes
        var gradeSelect = document.getElementById("grade");

        if(gradeSelect) {
            gradeSelect.selectedIndex = 0;
            gradeSelect.classList.remove("background-bg");
        }

        document.getElementById("content-data-books-ebooks").innerHTML="";
        pageNo = -1;
        getBooks();
        document.getElementById("loadMoreSection").style.display = 'none';
    }

    function gradeChanged(field){
        allBooksLoaded=false;
        isAjaxCalled = true;

        if(document.getElementById("grade").selectedIndex>0) document.getElementById("grade").classList.add("background-bg");
        else document.getElementById("grade").classList.remove("background-bg");

        document.getElementById("content-data-books-ebooks").innerHTML="";
        pageNo = -1;
        getBooks();
        document.getElementById("loadMoreSection").style.display = 'none';
    }

    function setBlankState(){
        document.getElementById("level").selectedIndex = 0;
        document.getElementById("syllabus").selectedIndex = 0;
        document.getElementById("grade").selectedIndex = 0;
    }

    function getBooksWithParameters(level,syllabus,grade,subject,publisherId,pageNo){
        if(level) level = replaceAll(level,'-',' ');
        if(syllabus) syllabus = replaceAll(syllabus,'-',' ');
        if(grade) grade = replaceAll(grade,'-',' ');

        document.querySelector('.loading-icon').classList.remove('hidden');

        var title = "AI Tutors";
        var pageUrl = "/ibookgpt/istore";

        if(level!=null && level!=""){
            title = level + " " + title;
            pageUrl += "?level=" + level.replace(/\s/g, '-');
        }
        if(syllabus!=null && syllabus!=""){
            title = syllabus + " " + title;
            if(pageUrl.indexOf("?") > -1) pageUrl += "&syllabus=" + syllabus.replace(/\s/g, '-');
            else pageUrl += "?syllabus=" + syllabus.replace(/\s/g, '-');
        }
        if(grade!=null && grade!=""){
            title = "Grade " + grade + " " + title;
            if(pageUrl.indexOf("?") > -1) pageUrl += "&grade=" + grade.replace(/\s/g, '-');
            else pageUrl += "?grade=" + grade.replace(/\s/g, '-');
        }
        if(subject!=null && subject!=""){
            title = subject + " " + title;
            if(pageUrl.indexOf("?") > -1) pageUrl += "&subject=" + subject.replace(/\s/g, '-');
            else pageUrl += "?subject=" + subject.replace(/\s/g, '-');
        }
        if(publisherId!=null && publisherId!=""){
            if(pageUrl.indexOf("?") > -1) pageUrl += "&publisherId=" + publisherId;
            else pageUrl += "?publisherId=" + publisherId;
        }

        if(pageInitialized) window.history.replaceState(title + " AI Tutors - iStore", "title", pageUrl);
        else pageInitialized = true;

        <g:remoteFunction controller="wonderpublish" action="getNewBooksList"  onSuccess='latestBooksReceived(data);'
                  params="'fromApp=false&categories=true&level='+level+'&syllabus='+syllabus+'&grade='+grade+'&subject='+subject+'&pageNo='+pageNo+'&publisherId='+publisherId+'&getSubscription='+subscriptionBooks" />
    }

    function getBooks(){
        pageNo = pageNo+1;
        isAjaxCalled = true;
        var level = encodeURIComponent(document.getElementById("level")[document.getElementById("level").selectedIndex].value);
        var syllabus = encodeURIComponent(document.getElementById("syllabus")[document.getElementById("syllabus").selectedIndex].value);
        var grade = encodeURIComponent(document.getElementById("grade")[document.getElementById("grade").selectedIndex].value);

        // Ensure we pass null for publisherId, not -1 or any other value
        getBooksWithParameters(level,syllabus,grade,null,null,pageNo);
    }

    function replaceAll(str, find, replace) {
        if(str==undefined) return str;
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }

    function compareNumbers(a, b) {
        return a - b;
    }

    function pageInitialize(){
        var level=null,syllabus=null,grade=null,publisherId=null,subject=null,pageNo;
        <%if(params.level!=null){%>
          level =encodeURIComponent("${params.level.replace('~','&')}".replace('&amp;','&'));
        <%}else if(session.getAttribute('instituteLevel')!=null){%>
        level =encodeURIComponent("${session.getAttribute('instituteLevel').replace('~','&')}".replace('&amp;','&'));
        <%}%>
        <%if(params.syllabus!=null){%>
        syllabus = encodeURIComponent("${params.syllabus.replace('~','&')}".replace('&amp;','&'));
        <%}else if(params.level==null&&session.getAttribute('instituteSyllabus')!=null){%>
        syllabus =encodeURIComponent("${session.getAttribute('instituteSyllabus').replace('~','&')}".replace('&amp;','&'));
        <%}%>
        <%if(params.grade!=null){%>
        grade=encodeURIComponent("${params.grade.replace('~','&')}".replace('&amp;','&'));
        <%}%>
        <%if(params.subject!=null){%>
        subject=encodeURIComponent("${params.subject.replace('~','&')}".replace('&amp;','&'));
        <%}%>
        <%if(params.publisherId!=null){%>
        publisherId=encodeURIComponent("${params.publisherId}");
        <%}%>
        <%if(params.searchString!=null){%>
        var searchString = "${params.searchString}";
        document.getElementById("search-book-store").value = searchString;
        <%}%>

        getBooksWithParameters(level,syllabus,grade,subject,publisherId,0);
    }

    function booksReceived(data){
        document.querySelector('.loading-icon').classList.add('hidden');

        if(data.length == 0){
            document.getElementById("noResultsFound").style.display = "block";
            document.getElementById("loadMoreSection").style.display = "none";
        } else {
            document.getElementById("noResultsFound").style.display = "none";
            populateBooks(data);
        }
        isAjaxCalled = false;
    }

    function populateBooks(data){
        var booksHtml = "";
        var colors = ['color-1', 'color-2', 'color-3', 'color-4', 'color-5', 'color-6'];
        var icons = ['fas fa-trophy', 'fas fa-graduation-cap', 'fas fa-book-open', 'fas fa-brain', 'fas fa-star', 'fas fa-atom'];

        for(var i = 0; i < data.length; i++){
            var book = data[i];
            var colorClass = colors[i % colors.length];
            var iconClass = icons[i % icons.length];

            booksHtml += '<div class="istore-class-card-container ' + colorClass + '" onclick="openBook(' + book.id + ')">';
            booksHtml += '  <div class="istore-class-card-content">';
            booksHtml += '    <div class="istore-class-icon ' + colorClass + '">';
            booksHtml += '      <i class="' + iconClass + '"></i>';
            booksHtml += '    </div>';
            booksHtml += '    <h3 class="istore-class-title">' + book.title + '</h3>';
            booksHtml += '  </div>';
            booksHtml += '</div>';
        }

        if(pageNo == 0){
            document.getElementById("content-data-books-ebooks").innerHTML = booksHtml;
        } else {
            document.getElementById("content-data-books-ebooks").innerHTML += booksHtml;
        }

        // Show load more button if there are more books
        if(data.length >= 20){
            document.getElementById("loadMoreSection").style.display = "block";
        } else {
            document.getElementById("loadMoreSection").style.display = "none";
            allBooksLoaded = true;
        }
    }

    function openBook(bookId){
        document.querySelector('.loading-icon').classList.remove('hidden');
        window.location.href = "/ibookgpt/ibookDtl?id=" + bookId;
    }

    function loadMoreBooks(){
        getBooks();
    }

    // Initialize page



    function submitSearchTop(){
        var searchString = document.getElementById("search-book-store").value;
        if(searchString.trim() != ""){
            document.getElementById("content-data-books-ebooks").innerHTML="";
            pageNo = -1;
            allBooksLoaded = false;
            searchMode = true;

            <g:remoteFunction controller="discover" action="search" onSuccess='booksReceived(data);'
                params="'searchString='+searchString" />
        }
    }

    function expandSearch(element) {
        element.parentElement.classList.add('expanded');
    }

    function contractSearch(element) {
        if(element.value.trim() === '') {
            element.parentElement.classList.remove('expanded');
        }
    }

    function latestBooksReceived(data) {
        var books = JSON.parse(data.books);
        var booksPresent = true;

        if(books == '' || books == null || books == 'null' || data.books == '[]') booksPresent = false;

        document.querySelector('.loading-icon').classList.add('hidden');

        if(booksPresent) {
            var filtersElement = document.getElementById('filters');
            if(filtersElement) {
                filtersElement.classList.remove('d-none');
                filtersElement.style.display = 'flex';
                filtersElement.style.flexDirection = 'column';
            }

            // Populate filter dropdowns using session data and bookTags (same as aiStore)
            var levelSelect = document.getElementById("level");
            var syllabusSelect = document.getElementById("syllabus");
            var gradeSelect = document.getElementById("grade");

            var checkLevel = false, checkSyllabus = false;

            var levelTags = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
            var syllabusTags = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
            var gradeTags = [];

            var tagsList = JSON.parse(data.bookTags);
            if (data.publisherId != null && "undefined" == data.publisherId) {
            }
            var levels = [];
            if (!(data.publisherId == ""||data.publisherId=="null")) {
                levelTags = JSON.parse(data.bookTags);
                syllabusTags = JSON.parse(data.bookTags);
            }
            gradeTags = JSON.parse(data.bookTags);

            for (var i = 0; i < levelTags.length; i++) {
                if (levels.indexOf(levelTags[i].level) == -1) levels.push(levelTags[i].level);
            }
            levels.sort();

            // Reset and populate level dropdown
            var select = document.getElementById("level");
            select.options.length = 1;
            for (var i = 0; i < levels.length; i++) {
                var el = document.createElement("option");
                el.textContent = levels[i];
                el.value = levels[i];
                if (data.level!=null&&levels[i] == data.level.replace("&amp;","&")) {
                    el.selected = true;
                    document.getElementById("level").classList.add("background-bg");
                    document.getElementById("syllabus").style.display = 'block';
                    pageNo = 0;
                    allBooksLoaded = false;
                }
                select.appendChild(el);
            }

            var selectedLevel = document.getElementById("level")[document.getElementById("level").selectedIndex].value;
            if (document.getElementById("level").selectedIndex > 0) checkLevel = true;

            // Populate syllabus dropdown
            var syllabus = [];
            for (var i = 0; i < syllabusTags.length; i++) {
                if (checkLevel) {
                    if (selectedLevel == syllabusTags[i].level && syllabus.indexOf(syllabusTags[i].syllabus) == -1) syllabus.push(syllabusTags[i].syllabus);
                } else if (syllabus.indexOf(syllabusTags[i].syllabus) == -1) {
                    syllabus.push(syllabusTags[i].syllabus);
                }
            }
            syllabus.sort();

            select = document.getElementById("syllabus");
            select.options.length = 1;
            for (var i = 0; i < syllabus.length; i++) {
                var el = document.createElement("option");
                el.textContent = syllabus[i];
                el.value = syllabus[i];
                if (data.syllabus!=null&&syllabus[i] == data.syllabus.replace("&amp;","&")) {
                    el.selected = true;
                    document.getElementById("syllabus").classList.add("background-bg");
                    document.getElementById("grade").style.display = 'block';
                    pageNo = 0;
                    allBooksLoaded = false;
                }
                select.appendChild(el);
            }

            var selectedSyllabus = document.getElementById("syllabus")[document.getElementById("syllabus").selectedIndex].value;
            if (document.getElementById("syllabus").selectedIndex > 0) checkSyllabus = true;

            // Populate grade dropdown
            if(gradeTags.length>0) {
                var grade = [];
                for (var i = 0; i < gradeTags.length; i++) {
                    if (grade.indexOf(gradeTags[i].grade) == -1) {
                        grade.push(gradeTags[i].grade);
                    }
                }

                if (data.level == 'School') {
                    grade.sort(compareNumbers);
                } else {
                    grade.sort();
                }

                select = document.getElementById("grade");
                select.options.length = 1;
                for (var i = 0; i < grade.length; i++) {
                    var el = document.createElement("option");
                    el.textContent = grade[i];
                    el.value = grade[i];
                    if (grade[i] == data.grade) {
                        el.selected = true;
                        document.getElementById("grade").classList.add("background-bg");
                    }
                    select.appendChild(el);
                }
                document.getElementById("grade").style.display = 'block';
            }else{
                document.getElementById("grade").style.display = 'none';
            }

            // Populate books as cards
            populateBooks(books);
        } else {
            document.getElementById("noResultsFound").style.display = "block";
            document.getElementById("loadMoreSection").style.display = "none";
        }
        isAjaxCalled = false;
    }

    // Initialize page with URL parameters
    window.addEventListener("DOMContentLoaded", function() {
        // Show filters and search immediately
        var filtersElement = document.getElementById("filters");
        var searchElement = document.querySelector(".istore-search-container");

        if(filtersElement) {
            filtersElement.classList.remove("d-none");
            filtersElement.style.display = "flex";
            filtersElement.style.flexDirection = "row";
        }
        if(searchElement) {
            searchElement.classList.remove("d-none");
            searchElement.style.display = "flex";
        }

        // Check if URL parameters are present and call pageInitialize
        <% if(params.publisherId!=null||params.level!=null||params.syllabus!=null||params.grade!=null||params.subject!=null||params.searchString!=null) {%>
        pageInitialize();
        <%}else {%>
        getBooksWithParameters(null,null,null,null,null,0);
        <%}%>
    });

</script>
