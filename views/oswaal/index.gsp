<!DOCTYPE html>
<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/oswaal/navheader_new"></g:render>
<div class="loading-icon hidden">
  <div class="loader-wrapper">
    <div class="loader">Loading</div>
  </div>
</div>

<div id="wrapper" class="index-page">

    <div class="header-wrapper">
      <div class="bg-wrapper-sec">

        <div id="slider-desktop" class="carousel slide this-is-a-web-view-slider" data-ride="carousel">
          <ol class="carousel-indicators" id="slider-desktop-indicators"></ol>
          <div class="carousel-inner" id="slider-desktop-views"></div>
        </div>

        <div id="bindAllItem">
          <div id="slider-mobile" class="carousel slide this-is-a-responsive-view-slider" data-ride="carousel">
            <ol class="carousel-indicators" id="slider-mobile-indicators"></ol>
            <div class="carousel-inner" id="slider-mobile-views"></div>
          </div>
          <div class="header-menu-wrapper">
            <div class="logo-wrapper">
              <a href="/oswaal/index">
                <img src="${assetPath(src: 'oswaal/oswaal-logo-icon.png')}" alt="Oswaal Logo"/>
              </a>
            </div>
            <div class="menu-bar-wrp">
              <ul>
                <li class="facebook"><a href="https://www.facebook.com/Oswaalbooks/" target="_blank" title="facebook">facebook</a></li>
                <li class="twitter"><a href="https://twitter.com/oswaal_books" target="_blank" title="twitter">twitter</a></li>
                <li class="linkedin"><a href="https://www.linkedin.com/company/oswaal-books/" target="_blank" title="linkedin">linkedin</a></li>
                <li class="youtube"><a href="https://www.youtube.com/c/oswaalbooks-learningmadesimple" target="_blank" title="youtube">youtube</a></li>
              </ul>
            </div>
          </div>
        </div>

      </div>
    </div>

  <div class="overlay-wrp-s"></div>

  <div class="categories-section">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-10 col-lg-7">
          <div class="text-center border-title-categories">
            <h2 class="title-categories-section">eBook Categories</h2>
          </div>
        </div>
      </div>

      <div id="ebookCategories">
        <div class="category_list">

        </div>
      </div>
    </div>
  </div>

  <div class="connect-section">
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-2 display-none-responsive">
        </div>
        <div class="col-lg-8">
          <div class="row justify-content-center">
            <div class="col-lg-9 col-sm-10">
              <div class="text-center border-title-coonect">
                <h2 class="title-connect-section">Connect</h2>
              </div>
            </div>
            <div class="col-lg-9 col-sm-9">
              <p class="connect-sec-wrp-disc-pera">'Customer Experience always comes first', we genuinely believe in this and would  always love to hear from you, be it related to books, orders, content or business. <br/> Get in touch and follow us to stay updated.</p>
            </div>
          </div>


          <div class="row mrgn-top-connect-sec">
            <div class="col-lg-6 col-sm-12 center-responsive-div">
              <ul class="social-icon-wrp-connect">
                <li><a href="https://www.facebook.com/Oswaalbooks/" target="_blank" title="fb"><i class="fa fa-facebook"></i></a></li>
                <li><a href="https://twitter.com/oswaal_books" target="_blank" title="twitter"><i class="fa fa-twitter"></i></a></li>
                <li><a href="https://www.linkedin.com/company/oswaal-books/" target="_blank" title="linkedin"><i class="fa fa-linkedin"></i></a></li>
                <li><a href="https://www.youtube.com/c/oswaalbooks-learningmadesimple" target="_blank" title="youtube"><i class="fa fa-youtube-play"></i></a></li>
              </ul>
            </div>

            <div class="col-lg-6 col-sm-12 responsive-center-text">
              <h3 class="call-here-number"><a href="tel:+919015666688">+91 9015666688</a></h3>
            </div>

          </div>
        </div>

      </div>
    </div>
  </div>

</div>

<script>

  var activeCategories = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
  var activeCategoriesSyllabus = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
  const groupedData = activeCategoriesSyllabus.reduce((acc, obj) => {
    const key = obj.level;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(obj);
    return acc;
  }, {});

  let html = '';

  for (const key in groupedData) {
    const group = groupedData[key];
    html += "<div class='category_level'>"+
            "<h4><a href='/${session["entryController"]}/store?level="+encodeURIComponent(key)+"' style='font-size:inherit;color: inherit;font-weight: inherit;'> "+key+"</a></h4>";
    html += "<div class='category_cards'>";
    for (const obj of group) {
      html +="<a href='/${session["entryController"]}/store?level="+encodeURIComponent(key)+"&syllabus="+encodeURIComponent(obj.syllabus)+"' class='category_card'>"+
              "<div class='category_card-title'>"+
              "<p>"+obj.syllabus+"</p>"+
              "</div>"+
              "</a>";
    }
    html += '</div>'+
            "</div>";
  }

  document.querySelector(".category_list").innerHTML=html;

  function logout(){
    setCookie("level","");
    setCookie("syllabus","");
    setCookie("grade","");
    setCookie("subject","");
    window.location.href = '/logoff';
  }

  function findPosY(obj) {
    var curtop = 0;
    if (typeof (obj.offsetParent) != 'undefined' && obj.offsetParent) {
      while (obj.offsetParent) {
        curtop += obj.offsetTop;
        obj = obj.offsetParent;
      }
      curtop += obj.offsetTop;
    } else if (obj.y)
      curtop += obj.y;
    return curtop;
  }
</script>

<g:render template="/oswaal/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js"></script>

<script>
  var siteId = "${session["siteId"]}";
  var checkDescBanners = false;
  var checkMobBanners = false;
  var templateDesktop = "";
  var templateMobile = "";
  var defaultDeskImageUrl = "/assets/oswaal/oswaal-banner.jpg";
  var defaultMobImageUrl = "/assets/oswaal/oswaal-banner.jpg";

  function showBanners(data) {
    setTimeout(function () {
      $(".loading-icon").addClass("hidden");
    },2000);
    if(data.status=="OK") {
      var banners = data.banners.reverse();
      $('#slider-desktop-views,#slider-mobile-views').empty();

      // Banner slider
      $.each(banners, function (i, v) {
        var item = v;
        var htmlStr = '';
        var htmlStrMobile = '';
        var indicatorStr = '';
        var indicatorStrMobile = '';
        var imageDescUrl = "/wonderpublish/showImage?id=" + v.id + "&fileName=" + v.imagePath + "&imgType=webp";
        var imageMobUrl = "/wonderpublish/showImage?id="+v.id+"&fileName="+v.imagePathMobile + "&imgType=webp";
        if(v.bookTitle) {
          var bookTitle = "/" + replaceAll(replaceAll(v.bookTitle, ' ', '-').toLowerCase(), '\'', '');
        }
        var bookHyperLink = bookTitle + "/ebook-details?siteName=${session['entryController']}&bookId=" + v.bookId + "&preview=true";

        var actionLink = v.action //action field link
        var serverURL =  window.location.origin //getting base url
        actionLink = serverURL+'/'+actionLink;

        indicatorStr += "<li data-target='#slider-desktop' class='border-light' data-slide-to='" + i + "'></li>";
        indicatorStrMobile += "<li data-target='#slider-mobile' class='border-light' data-slide-to='" + i + "'></li>";
        if(v.bookId) {
          htmlStr += '<div class="carousel-item">' +
                  '<a href="' + bookHyperLink + '" target="_blank" class="d-block">' +
                  '<img src="'+imageDescUrl+'" alt="banner">' +
                  '</a>' +
                  '</div>';
          htmlStrMobile += '<div class="carousel-item">' +
                  '<a href="' + bookHyperLink + '" target="_blank" class="d-block">' +
                  '<img src="'+imageMobUrl+'" alt="banner">' +
                  '</a>' +
                  '</div>';
        } else if (v.action) {
          htmlStr += '<div class="carousel-item">' +
                  '<a href="' + actionLink + '" target="_blank" class="d-block">' +
                  '<img src="'+imageDescUrl+'" alt="banner">' +
                  '</a>' +
                  '</div>';
          htmlStrMobile += '<div class="carousel-item">' +
                  '<a href="' + actionLink + '" target="_blank" class="d-block">' +
                  '<img src="'+imageMobUrl+'" alt="banner">' +
                  '</a>' +
                  '</div>';
        }else{
          htmlStr += '<div class="carousel-item">' +
                  '<img src="'+imageDescUrl+'" alt="banner">' +
                  '</div>';
          htmlStrMobile += '<div class="carousel-item">' +
                  '<img src="'+imageMobUrl+'" alt="banner">' +
                  '</div>';
        }

        // If desktop banners are available
        if(v.imagePath) {
          checkDescBanners = true;
          $('#slider-desktop-views').append(htmlStr).find('.carousel-item:first-child').addClass('active');
          $('#slider-desktop-indicators').append(indicatorStr).find('li:first-child').addClass('active');
        }

        // If mobile banners are available
        if(v.imagePathMobile) {
          checkMobBanners = true;
          $('#slider-mobile-views').append(htmlStrMobile).find('.carousel-item:first-child').addClass('active');
          $('#slider-mobile-indicators').append(indicatorStrMobile).find('li:first-child').addClass('active');
        }

      });

    } else if(data.status=="Nothing Present") {
      checkDescBanners = false; checkMobBanners = false;
    }

    // Showing empty banners based on condition
    if(!checkDescBanners && !checkMobBanners) {
      templateDesktop += emptyDesktopBannerUI(defaultDeskImageUrl);
      $('#slider-desktop-views').append(templateDesktop);
      templateMobile += emptyMobileBannerUI(defaultMobImageUrl);
      $('#slider-mobile-views').append(templateMobile);
    } else if(!checkMobBanners) {
      templateMobile += emptyMobileBannerUI(defaultMobImageUrl);
      $('#slider-mobile-views').append(templateMobile);
    }

  }

  // If desktop banner images are empty calling this function
  function emptyDesktopBannerUI(defaultImage) {
    var emptyBannerDesk = '<div class="carousel-item active">' +
            '<img src="'+defaultImage+'" alt="banner default">' +
            '</div>';
    return emptyBannerDesk;
  }

  // If mobile banner images are empty calling this function
  function emptyMobileBannerUI(defaultImage) {
    var emptyBannerMob = '<div class="carousel-item active">' +
            '<img src="'+defaultImage+'" alt="banner default">' +
            '</div>';
    return emptyBannerMob;
  }

  function getBannerDetails(siteId) {
    $(".loading-icon").removeClass("hidden");
    <g:remoteFunction controller="wonderpublish" action="getBannerdetails" params="'siteId='+siteId" onSuccess="showBanners(data);"/>
  }

  getBannerDetails(siteId);

  function replaceAll(str, find, replace) {
    if (str == undefined) return str
    else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
  }

  function escapeRegExp(str) {
    return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
  }

  if($.browser.platform == "win") {
    $("html").addClass("windows");
  }

  if($.browser.name == "chrome") {
    $("html").addClass("chrome");
  } else if($.browser.name == "mozilla") {
    $("html").addClass("mozilla");
  } else if($.browser.name == "safari") {
    $("html").addClass("safari");
    if($(window).width() < 767){
      $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
        $(this).css('background','#eee');
        $('.btn-primary-modifier').css('background-color','#A81175 !important');
        $('.btn-success-modifier').css('background-color','#27AE60 !important');
        $('.btn-secondary-modifier').css('background-color','#8E8E8E !important');
        $('.btn-danger-modifier').css('background-color','#FF4B33 !important');
        $('.btn-warning-modifier').css('background-color','#FFD602 !important');
      });
      document.addEventListener('touchmove', function (event) {
        if (event.scale !== 1) { event.preventDefault(); }
      }, { passive: false });
    }
  } else {
    $("html").addClass("others");
    if($(window).width() < 767){
      $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
        $(this).css('background','#eee');
      });
      document.addEventListener('touchmove', function (event) {
        if (event.scale !== 1) { event.preventDefault(); }
      }, { passive: false });
    }
  }

</script>

</body>
</html>
