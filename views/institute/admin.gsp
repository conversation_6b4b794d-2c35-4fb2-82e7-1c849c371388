<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<asset:javascript src="moment.min.js"/>
<script>
var loggedIn=false;
</script>
<sec:ifLoggedIn>
<script>
loggedIn=true;
</script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
  <div class="loader-wrapper">
    <div class="loader">Loading</div>
  </div>
</div>
  <div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);">
    <div class='row'>
      <div class='col-md-9 main p-4' style=" margin: 40px auto; float: none;">
        <div id="content-books">
          <div class="form-group mb-4">
            <label for="institutes" style="display: block;">Institute</label>
              <div class="flex_st ad">
%{--                  <div class="col-md-4">--}%
                <select name="institutes" id="institutes" class="form-control col-3" onchange="instituteChanged();">
                  <option value="" disabled="disabled" selected>Select Institute</option>
                  <g:each in="${institutes}" var="institute" status="i">
                    <option value="${institute.id}">${institute.name}</option>
                  </g:each>
                </select>
%{--              </div>--}%
%{--              <div class="col-md-4">--}%
                  <input type="text" class="form-control libad border col-3 ml-3" name="batchName" id="batchName"  placeholder="Add batch name"></input><br>
%{--              </div>--}%
%{--              <div class="col-md-4">--}%
                  <input type="text" id="endDate" class="form-control border col-3 ml-3" name="endDate" placeholder="Add completion Date">
%{--              </div>--}%
                  <button class="btn btn-primary col-2 btn-lg ml-3"  onclick="addBatch();">Add Batch</button>
              </div>

              </div>
          </div>
          %{--<div class="form-group">--}%

          %{--</div>--}%
          <div class="form-group ">
            <label for="batches" style="display: block;">Batch</label>
            <select name="batches" id="batches" class="form-control" style="">
                <option value="" disabled="disabled" selected>Select batch</option>
                <g:each in="${batchesList}" var="batch" status="i">
                <option value="${batch.id}">${batch.name + (batch.endDate!=null?" - Completion date: "+(new SimpleDateFormat("dd-MM-yyyy")).format(batch.endDate):"")}</option>
              </g:each>
            </select>
            <div class="d-flex justify-content-between flex_st">

            <a href="javascript:showAllUsers('instructor')" style="display: inline-block;">Show Instructors for this batch</a><br>

            <a href="javascript:showAllUsers('student')" style="display: inline-block;">Show students for this batch</a><br>

                    <a href="javascript:getBooksForBatch()" style="display: inline-block;">Show eBooks for this batch</a><br>

                <a href="javascript:getBooksReport()" style="display: inline-block;">Show eBooks report</a><br>

              <a href="javascript:batchCompleted()" style="display: inline-block;">Mark the batch as completed</a>

            </div>
          </div>
            <h5>Details</h5>
            <div class="d-flex">
            <div class="form-group col-md-4 pl-0">
                <input type="text" size="4" class="form-control admin w-100 border mb-3" name="bookId" id="bookId"  placeholder="Book Id">
                <button class="btn btn-primary col-5" onclick="addBook();">Add Book</button>
            </div>
        <div class="form-group col-md-4 ">
          <textarea class="form-control admin w-100 border mb-3" name="useremail" id="useremail"  placeholder="Add students email/mobile number(separated by comma for multiple users)" style="min-height: 100px;"></textarea>
          <button class="btn btn-primary col-5" onclick="addUser('student');">Add Student</button>
        </div>
          <div class="form-group col-md-4">
            <textarea class="form-control admin w-100 border mb-3" name="instructoremail" id="instructoremail"  placeholder="Add instructor email/mobile number(separated by comma for multiple users)" style="min-height: 100px;"></textarea>
            <button class="btn btn-primary col-5"  onclick="addUser('instructor');">Add Instructor</button>
          </div>
            </div>
        <div id="errormsg" class="alert alert-danger has-error mt-4" role="alert" style="display: none; background: none;"></div>
        <div id="successmsg" class="alert alert-success border-success mt-4" style="display: none; background: none;"></div>
        <div id="batchUsers" class="my-4" style="display: none"></div>
            <div style="display: none;" id="download">
                <div class="form-group">
                    <button type="button" id="download-btn" class="btn btn-primary btn-lg col-2" >Download</button>
                </div>
            </div>
      </div>
    </div>
  </div>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>

<script>
    $(function(){
        $('*[name=endDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "dateOnly":true
        });

    });
  var instituteBatches={};
  <%batchesList.each{batch->%>
  if(!instituteBatches[${batch.conductedFor}])
      instituteBatches[${batch.conductedFor}] = [];
      instituteBatches[${batch.conductedFor}].push(''+${batch.id});
  <%}%>
  function addBatch(){
      var institute = document.getElementById("institutes");
      if(institute.selectedIndex==0){
          document.getElementById("errormsg").innerHTML="Please select the institute to add the batch."
          $("#errormsg").show();
      }else if(document.getElementById("batchName").value==="" ){
          document.getElementById("errormsg").innerHTML="Please enter the batch name."
          $("#errormsg").show();
      }
      else{
          $('.loading-icon').removeClass('hidden');
          $("#errormsg").hide();
          var instituteId = institute.value;
          var batchName = document.getElementById("batchName").value;
          var endDate = document.getElementById("endDate").value;

          <g:remoteFunction controller="institute" action="addBatch"
            params="'instituteId='+instituteId+'&endDate='+endDate+'&batchName='+batchName" onSuccess = "batchAdded(data);"/>
      }
  }

  function batchAdded(data){
      window.location.href = "/institute/admin";
  }
  function instituteChanged(){
      var batchesArray = instituteBatches[document.getElementById("institutes").value];
      var batches = document.getElementById('batches');

      for(var i=1; i < batches.length; i++)
      {
          if(batchesArray.includes(''+batches.options[i].value)){
              $("#batches option[value='"+batches.options[i].value+"']").show();
          }else{
              $("#batches option[value='"+batches.options[i].value+"']").hide();
          }
      }
  }
function addUser(userType){
  $('.loading-icon').addClass('hidden');
  $("#errormsg").hide();
  $("#successmsg").hide();
  var batch = document.getElementById("batches");
  if(batch.selectedIndex==0){
    document.getElementById("errormsg").innerHTML="Please select a batch to add the student/instructor."
    $("#errormsg").show();

  }else if(document.getElementById("useremail").value==="" && document.getElementById("instructoremail").value==="" ){
    document.getElementById("errormsg").innerHTML="Please enter the email id/ Mobile number."
    $("#errormsg").show();
  }
  else{
    $('.loading-icon').removeClass('hidden');
    $("#errormsg").hide();
    var batchId = batch.value;
    var email = document.getElementById("useremail").value;
    if(userType=='instructor') email = document.getElementById("instructoremail").value;
    <g:remoteFunction controller="institute" action="addUserToBatch" params="'batchId='+batchId+'&useremail='+email+'&userType='+userType" onSuccess = "userAdded(data);"/>
  }
}

  function addBook(){
      $('.loading-icon').addClass('hidden');
      $("#errormsg").hide();
      $("#successmsg").hide();
      var batch = document.getElementById("batches");
      if(batch.selectedIndex==0){
          document.getElementById("errormsg").innerHTML="Please select a batch to add the book."
          $("#errormsg").show();

      }else if(document.getElementById("bookId").value==="" ){
          document.getElementById("errormsg").innerHTML="Please enter the book id."
          $("#errormsg").show();
      }
      else{
          $('.loading-icon').removeClass('hidden');
          $("#errormsg").hide();
          var batchId = batch.value;
          var bookId = document.getElementById("bookId").value;

          <g:remoteFunction controller="institute" action="addBookForBatch" params="'batchId='+batchId+'&bookId='+bookId" onSuccess = "bookAdded(data);"/>
      }
  }

function userAdded(data){
    $("#errormsg").hide();
    $("#successmsg").hide();
  $('.loading-icon').addClass('hidden');
  if(data.status=="error"){
    document.getElementById("errormsg").innerHTML="This user is not registered. Kindly ask the user to register first."
    $("#errormsg").show();
  }
  else{
    document.getElementById("successmsg").innerHTML=data.status;
    document.getElementById("useremail").value="";
    $("#successmsg").show();
  }
}

  function bookAdded(data){
      $("#errormsg").hide();
      $("#successmsg").hide();
      $('.loading-icon').addClass('hidden');
      if(data.status=="OK"){
          document.getElementById("successmsg").innerHTML="Book added";
          document.getElementById("bookId").value="";
          $("#successmsg").show();
      }
      else{
          document.getElementById("errormsg").innerHTML="This book was not added. Either the book id is incorrect or you do not have sufficient rights to add the book";
          $("#errormsg").show();

      }
  }

function getBooksForBatch(){
    $("#errormsg").hide();
    $("#successmsg").hide();
    $("#batchUsers").show();
    var batch = document.getElementById("batches");
    if(batch.selectedIndex==0){
        document.getElementById("errormsg").innerHTML="Please select a batch"
        $("#errormsg").show();
    }
    else{
        $('.loading-icon').removeClass('hidden');
        $("#errormsg").hide();
        var batchId = batch.value;
        <g:remoteFunction controller="institute" action="getBooksForBatch" params="'batchId='+batchId" onSuccess = "showBooksForBatch(data);"/>
    }
}


    function getBooksReport(){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $("#batchUsers").show();

        var batch = document.getElementById("batches");
        if(batch.selectedIndex==0){
            document.getElementById("errormsg").innerHTML="Please select a batch"
            $("#errormsg").show();
        }
        else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            var batchId = batch.value;
            <g:remoteFunction controller="institute" action="getBooksReport" params="'batchId='+batchId" onSuccess = "showBooksReportForBatch(data);"/>
        }
    }






function showAllUsers(userType){
  $("#errormsg").hide();
  $("#successmsg").hide();
  $("#batchUsers").show();
  var batch = document.getElementById("batches");
  if(batch.selectedIndex==0){
    document.getElementById("errormsg").innerHTML="Please select a batch"
    $("#errormsg").show();
  }
  else{
    $('.loading-icon').removeClass('hidden');
    $("#errormsg").hide();
    var batchId = batch.value;
    <g:remoteFunction controller="institute" action="showAllUsersForBatch" params="'batchId='+batchId+'&userType='+userType" onSuccess = "showUsersForBatch(data);"/>
  }
}

  function batchCompleted(){
      $("#errormsg").hide();
      $("#successmsg").hide();
      $("#batchUsers").show();
      var batch = document.getElementById("batches");
      if(batch.selectedIndex==0){
          document.getElementById("errormsg").innerHTML="Please select a batch"
          $("#errormsg").show();
      }
      else{
          $('.loading-icon').removeClass('hidden');
          $("#errormsg").hide();
          var batchId = batch.value;
          if(confirm("Do you want to go ahead and mark this batch as completed?")) {
              <g:remoteFunction controller="institute" action="batchCompleted" params="'batchId='+batchId" onSuccess = "batchCompletionUpdated(data);"/>
          }
      }
  }

function batchCompletionUpdated(data){
    window.location.href = "/institute/admin";
}

function showUsersForBatch(data){
  $('.loading-icon').addClass('hidden');
  var htmlStr="<hr><h4 class='text-capitalize pt-4'>"+data.userType+" of this batch</h4>\n" +
  "                    <table class='table table-bordered table-hover'>\n" +
  "                        <tr class='bg-primary text-white'>\n" +
  "                            <th>Name</th>\n" +
  "                            <th>Email</th>\n" +
      "                            <th>Username</th>\n" +
      "                            <th>Mobile</th>\n" +
      "                            <th class='text-center'>Delete</th>\n" +
  "                        </tr>\n" ;

  if(data.status=="OK"){
    var users = data.users;
     for(i=0;i<users.length;i++){
      htmlStr +="<tr><td style='text-transform:capitalize;'>"+users[i].name+"</td>"+
      "<td>"+users[i].email+"</td>" +
          "<td>"+users[i].username+"</td>" +
          "<td>"+users[i].mobile+"</td>" +
          "<td class='text-center'><a href='javascript:deleteUser("+users[i].userId+","+data.batchId+");'><img  src='${assetPath(src: 'baseline-delete-24px.svg')}' alt='wonderslate'></a></td>"+
          "</tr>";
    }
    htmlStr +="                        \n" +
    "                    </table>";
    document.getElementById("batchUsers").innerHTML= htmlStr;
  }else{
    document.getElementById("batchUsers").innerHTML= "<span class='text-danger'>No students added to this batch yet</span>";
  }
  $("#batchUsers").show();

}
    function showBooksReportForBatch(data){
        $('.loading-icon').addClass('hidden');

        var htmlStr="<hr><h4 class='text-capitalize pt-4'>eBooks Report</h4>\n" +
            "                    <table class='table table-bordered table-hover'>\n" +
            "                        <tr class='bg-primary text-white'>\n" +
            "                            <th>Title</th>\n" +
            "                            <th>UserName</th>\n" +
            "                            <th>End Date</th>\n" +
            "                        </tr>\n" ;

        if(data.status=="OK"){
            var books = data.booklist;
            for(i=0;i<books.length;i++){
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+books[i].title+"</td>"+
                    "<td>"+books[i].username+"</td>";
                if(books[i].date!=null&&books[i].date!=" ") {
                    htmlStr +=  "<td>" + moment(books[i].date).format("MMM Do YYYY")+"</td>";
                } else{
                    htmlStr +=  "<td>" + books[i].date+"</td>";
                }
                htmlStr +=  "</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#download').show();
        }else{
            $('#download').hide();
            document.getElementById("batchUsers").innerHTML= "<span class='text-danger'>No eBooks report</span>";
        }
        $("#batchUsers").show();

    }

  function showBooksForBatch(data){
      $('.loading-icon').addClass('hidden');
      var htmlStr="<hr><h4 class='text-capitalize pt-4'>eBooks of this batch</h4>\n" +
          "                    <table class='table table-bordered table-hover'>\n" +
          "                        <tr class='bg-primary text-white'>\n" +
          "                            <th>Book Id</th>\n" +
          "                            <th>Title</th>\n" +
          "                            <th class='text-center'>Delete</th>\n" +
          "                        </tr>\n" ;

      if(data.status=="OK"){
          var books = data.books;
           for(i=0;i<books.length;i++){
              htmlStr +="<tr><td style='text-transform:capitalize;'>"+books[i].bookId+"</td>"+
                  "<td>"+books[i].title+"</td>"+
                  "<td class='text-center'><a href='javascript:deleteBook("+books[i].bookId+","+books[i].batchId+");'><img  src='${assetPath(src: 'baseline-delete-24px.svg')}' alt='wonderslate'></a></td>"+
                  "</tr>";
          }
          htmlStr +="                        \n" +
              "                    </table>";
          document.getElementById("batchUsers").innerHTML= htmlStr;
      }else{
          document.getElementById("batchUsers").innerHTML= "<span class='text-danger'>No eBooks added to this batch yet</span>";
      }
      $("#batchUsers").show();

  }

  function deleteBook(bookId,batchId){
      if(confirm("Do you want to go ahead and delete this book from the batch?")) {
          <g:remoteFunction controller="institute" action="removeBookFromBatch" params="'batchId='+batchId+'&bookId='+bookId" onSuccess = "bookDeleted(data);"/>
      }
  }

  function deleteUser(userId,batchId){
      if(confirm("Do you want to go ahead and delete this user from the batch?")) {
          <g:remoteFunction controller="institute" action="removeUserFromBatch" params="'batchId='+batchId+'&userId='+userId" onSuccess = "bookDeleted(data);"/>
      }
  }


  function bookDeleted(data){
      window.location.href = "/institute/admin";
  }


    $('#download-btn').on('click', function() {
        var batch = document.getElementById("batches");
        var batchId = batch.value;
        window.location.href = "/institute/downloadbooksreportData?batchId="+batchId;
    });

</script>

</body>
</html>