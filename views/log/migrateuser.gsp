<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<script>
    var loggedIn=false;
</script>
<style>
.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
}
.modal.show .modal-dialog {
    -webkit-transform: none;
    transform: none;
}
.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out;
    -webkit-transform: translate(0,-50px);
    transform: translate(0,-50px);
}
.modal-content{
    width: 100%;
}
@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
    @media (min-width: 576px) {
        .modal-dialog {
            /*max-width: 500px;*/
            margin: 1.75rem auto;
        }
    }
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main p-4' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <label for="userValue" style="display: block;">Get User</label>
                <div class="form-group form-inline miguser">
                    <input type="text" class="form-control col-md-4 mr-4" name="userValue" id="userValue"  placeholder="Email">
                    %{--&nbsp;&nbsp;Mobile number&nbsp;<input type="radio" name="userMode" value="mobile" checked>&nbsp;&nbsp;Email&nbsp;<input type="radio" name="userMode" value="email"><br>--}%
                    <button class="btn btn-lg btn-primary col-2 m-0"  onclick="getUser();">Get User</button>
                </div>
                <div id="errormsg" class="alert alert-danger has-error mt-4" role="alert" style="display: none; background: none;"></div>
                <div id="successmsg" style="display: none"></div>
                <div id="batchUsers" class="mt-4" style="display: none"></div>
            </div>
        </div>

    </div>
</div>
<div class="modal" id="removePhone">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title">Migrate User</h4>
                <button type="button" class="close" data-dismiss="modal" style='margin-top: -26px;'>&times;</button>
            </div>

            <!-- Modal body -->
            <div class="modal-body text-center">
                <input type='number' name='number' id='phonenum' placeholder='Enter user Ph.No' style='padding: 1rem;' oninput="this.value = this.value.replace(/[^0-9.]/g, ''); this.value = this.value.replace(/(\..*)\./g, '$1');" onKeyDown="if(this.value.length==10 && event.keyCode!=8) return false;"> "
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="submit" class="btn btn-danger"  onclick="javascript:submitsearch()">Submit</button>
            </div>

        </div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>

<script>
    var oldUsername;
    function getUser(){

        if(document.getElementById("userValue").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the user email id"
            $("#errormsg").show();
        }
        else {
            $("#errormsg").hide();
            $("#successmsg").hide();
            $("#batchUsers").show();
            var userValue = document.getElementById("userValue").value;
            <g:remoteFunction controller="log" action="getUsers" params="'userValue='+userValue" onSuccess = "showUsers(data);"/>
        }

    }

    function openModal(username){
        oldUsername = username;
        $('#removePhone').modal('show');

    }

    function showUsers(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr= "                    <table class='table table-hover table-bordered'>\n" +
            "                        <tr class='bg-primary text-white'>\n" +
            "                            <th>Name</th>\n" +
            "                            <th>Username</th>\n" +
            "                            <th>Email</th>\n" +
            "                            <th class='text-center'>Migrate</th>\n" +
            "                        </tr>\n" ;
        if(data.status=="OK"){
            var users = data.userList;
            for(var i=0;i<users.length;i++){
                var username =users[i].username;
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+users[i].name+"</td>"+
                    "<td>"+users[i].username+"</td>" +
                    "<td>"+users[i].email+"</td>" +
                    "<td class='text-center'><button type=\"button\" class=\"btn btn-primary\" onclick=\"javascript:openModal('"+username+"')\">EDIT\n" +

                    "  </button>\n" +
                    "  </td>"+
                    "</tr>";
            }
            htmlStr += "</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
        }else{
            document.getElementById("batchUsers").innerHTML= "No user found with this information.";
        }
        $("#batchUsers").show();

    }

    function submitsearch(){
        $('#removePhone').modal('hide');
        var usermobile = document.getElementById("phonenum").value;
        <g:remoteFunction controller="creation" action="switchUser" params="'mobile='+usermobile+'&username='+oldUsername" onSuccess='showUsersdata(data);'/>

    }

    function showUsersdata(data){
        if(data.status=="OK"){
            alert("User Migrated Successfully");
        }else{

            alert(data.condition);
        }
    }






</script>


</body>
</html>