<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container"><br>
    <div class="d-flex justify-content-between align-items-center">
        <button onclick="window.history.back();" class="btn btn-secondary">Back</button>
        <h2 class="text-center flex-grow-1">Edit User</h2>
    </div>
    <hr/>
    <g:form action="updateUser" method="post" class="form-horizontal">
        <input type="hidden" name="id" value="${user.id}"/>
        <input type="hidden" name="instituteId" value="${instituteId}"/>
        <div class="form-group">
            <label class="col-sm-2 control-label">Username:</label>
            <div class="col-sm-10">
                <p class="form-control-static">${user.username}</p>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">Name:</label>
            <div class="col-sm-10">
                <g:textField name="name" value="${user.name}" required="true" class="form-control"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">Email:</label>
            <div class="col-sm-10">
                <g:textField name="email" value="${user.email}" required="true" class="form-control"/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">Mobile Number:</label>
            <div class="col-sm-10">
                <g:textField name="mobileNumber" value="${user.mobileNumber}" required="true" class="form-control"/>
            </div>
        </div>
        <div class="form-group text-center">
            <g:submitButton name="update" value="Update User" class="btn btn-success"/>
            <g:link action="listUsersInBatch" params="[instituteId: instituteId]" class="btn btn-default">Cancel</g:link>
        </div>
    </g:form>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
