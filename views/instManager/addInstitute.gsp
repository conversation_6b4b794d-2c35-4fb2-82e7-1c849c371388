<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container"><br>
    <h2 class="text-center"><g:if test="${isEdit}">Edit Institute</g:if><g:else>Create Institute</g:else></h2>
    <hr/>

    <g:form controller="institute" action="addInstitute" method="post" class="form-horizontal" name="instituteForm">
        <g:if test="${isEdit}">
            <input type="hidden" name="institutionId" value="${instituteInstance.id}"/>
            <input type="hidden" name="institutionEdit" value="true"/>
        </g:if>

        <!-- Name -->
        <div class="form-group ${hasErrors(bean: instituteInstance, field: 'name', 'has-error')}">
            <label class="col-sm-2 control-label">Name<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:textField name="name" value="${instituteInstance?.name}" required="true" class="form-control"/>
                <g:fieldError bean="${instituteInstance}" field="name" class="text-danger"/>
            </div>
        </div>

        <!-- Contact Name -->
        <div class="form-group ${hasErrors(bean: instituteInstance, field: 'contactName', 'has-error')}">
            <label class="col-sm-2 control-label">Contact Name<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:textField name="contactName" value="${instituteInstance?.contactName}" required="true" class="form-control"/>
                <g:fieldError bean="${instituteInstance}" field="contactName" class="text-danger"/>
            </div>
        </div>

        <!-- Contact Email -->
        <div class="form-group ${hasErrors(bean: instituteInstance, field: 'contactEmail', 'has-error')}">
            <label class="col-sm-2 control-label">Contact Email<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:textField name="contactEmail" value="${instituteInstance?.contactEmail}" required="true" class="form-control"/>
                <g:fieldError bean="${instituteInstance}" field="contactEmail" class="text-danger"/>
            </div>
        </div>

        <!-- Start Date -->
        <div class="form-group ${hasErrors(bean: defaultBatch, field: 'startDate', 'has-error')}">
            <label class="col-sm-2 control-label">Start Date<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:textField name="startDate" value="${defaultBatch?.startDate?.format('yyyy-MM-dd')}" required="true" class="form-control" readonly="readonly"/>
                <g:fieldError bean="${defaultBatch}" field="startDate" class="text-danger"/>
            </div>
        </div>

        <!-- End Date -->
        <div class="form-group ${hasErrors(bean: defaultBatch, field: 'endDate', 'has-error')}">
            <label class="col-sm-2 control-label">End Date:</label>
            <div class="col-sm-4">
                <input type="text" class="form-control" id="endDate" name="endDate" placeholder="Completion Date" size="10" autocomplete="off"  value="${defaultBatch?.endDate?.format('yyyy-MM-dd')}">
                <g:fieldError bean="${defaultBatch}" field="endDate" class="text-danger"/>
            </div>
        </div>

        <div class="form-group ${hasErrors(bean: instituteInstance, field: 'fullLibraryView', 'has-error')}">
            <label class="col-sm-3 control-label">Full Library View For All<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:select name="fullLibraryView" from="${['true', 'false']}" value="${instituteInstance?.fullLibraryView ?: 'true'}" required="true" class="form-control"/>
                <g:fieldError bean="${instituteInstance}" field="fullLibraryView" class="text-danger"/>
            </div>
        </div>
        <!-- Status -->
        <div class="form-group ${hasErrors(bean: defaultBatch, field: 'status', 'has-error')}">
            <label class="col-sm-2 control-label">Status<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:select name="status" from="${['active', 'completed']}" value="${defaultBatch?.status ?: 'active'}" required="true" class="form-control"/>
                <g:fieldError bean="${defaultBatch}" field="status" class="text-danger"/>
            </div>
        </div>

        <div class="form-group ${hasErrors(bean: instituteInstance, field: 'driveForInstructor', 'has-error')}">
            <label class="col-sm-3 control-label">Studio for Instructor<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:select name="driveForInstructor" from="${['true', 'false']}" value="${instituteInstance?.driveForInstructor ?: 'false'}" required="true" class="form-control"/>
                <g:fieldError bean="${instituteInstance}" field="driveForInstructor" class="text-danger"/>
            </div>
        </div>
        <div class="form-group ${hasErrors(bean: instituteInstance, field: 'driveForStudent', 'has-error')}">
            <label class="col-sm-3 control-label">Studio for Student<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:select name="driveForStudent" from="${['true', 'false']}" value="${instituteInstance?.driveForStudent ?: 'false'}" required="true" class="form-control"/>
                <g:fieldError bean="${instituteInstance}" field="driveForStudent" class="text-danger"/>
            </div>
        </div>
        <div class="form-group ${hasErrors(bean: instituteInstance, field: 'raForInstructor', 'has-error')}">
            <label class="col-sm-3 control-label">Research Assistant for Instructor<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:select name="raForInstructor" from="${['true', 'false']}" value="${instituteInstance?.raForInstructor ?: 'false'}" required="true" class="form-control"/>
                <g:fieldError bean="${instituteInstance}" field="raForInstructor" class="text-danger"/>
            </div>
        </div>
        <div class="form-group ${hasErrors(bean: instituteInstance, field: 'raForStudent', 'has-error')}">
            <label class="col-sm-3 control-label">Research Assistant for Student<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:select name="raForStudent" from="${['true', 'false']}" value="${instituteInstance?.raForStudent ?: 'false'}" required="true" class="form-control"/>
                <g:fieldError bean="${instituteInstance}" field="raForStudent" class="text-danger"/>
            </div>
        </div>
        <div class="form-group ${hasErrors(bean: instituteInstance, field: 'enableTest', 'has-error')}">
            <label class="col-sm-3 control-label">Enable Online Test<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:select name="enableTest" from="${['true', 'false']}" value="${instituteInstance?.enableTest ?: 'false'}" required="true" class="form-control"/>
                <g:fieldError bean="${instituteInstance}" field="enableTest" class="text-danger"/>
            </div>
        </div>
        <div class="form-group ${hasErrors(bean: instituteInstance, field: 'enableAnalytics', 'has-error')}">
            <label class="col-sm-3 control-label">Enable Analytics<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:select name="enableAnalytics" from="${['true', 'false']}" value="${instituteInstance?.enableAnalytics ?: 'false'}" required="true" class="form-control"/>
                <g:fieldError bean="${instituteInstance}" field="enableAnalytics" class="text-danger"/>
            </div>
        </div>
        <div class="form-group ${hasErrors(bean: instituteInstance, field: 'enableQuestionPaper', 'has-error')}">
            <label class="col-sm-3 control-label">Enable Question Paper Generation<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
                <g:select name="enableQuestionPaper" from="${['true', 'false']}" value="${instituteInstance?.enableQuestionPaper ?: 'false'}" required="true" class="form-control"/>
                <g:fieldError bean="${instituteInstance}" field="enableQuestionPaper" class="text-danger"/>
            </div>
        </div>
        <!-- Reference section drop down. Populate the select drop down from referenceSections which is a list with id and name  -->
        <div class="form-group ${hasErrors(bean: instituteInstance, field: 'showReferenceSection', 'has-error')}">
            <label class="col-sm-3 control-label">Reference Section<span class="text-danger">*</span>:</label>
            <div class="col-sm-4">
               <g:select name="showReferenceSection" from="${referenceSections}" optionKey="id" optionValue="name" value="${instituteInstance?.showReferenceSection}" required="true" class="form-control" noSelection="['':'Select Reference Section']"/>
                <g:fieldError bean="${instituteInstance}" field="showReferenceSection" class="text-danger"/>
            </div>

         <!-- Submit Button -->
        <div class="form-group text-center">
            <a href="javascript:submitForm()" class="btn btn-success">${isEdit ? 'Update Institute' : 'Create Institute'}</a>
            <g:link action="listInstitutes" class="btn btn-default">Cancel</g:link>
        </div>
    </g:form>
</div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>


</body>
</html>
<script type="text/javascript">


    $(function(){
        $('*[name=endDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "dateOnly":true
        });

        $('*[name=startDate]').appendDtpicker({
            "autodateOnStart": false,
            "dateOnly":true
        });

    });

    //instead of form submit we are using ajax to submit the form, create a method and call it on form submit and also make sure all validations stay intact
    function submitForm(){
        //get the form using its name
        var form = document.forms['instituteForm'];
        var isValid = false;
        //do the custom validation and show the error messages
        if(form.name.value.trim() == ''){
            alert('Name is required');
            form.name.focus();
        }else if(form.contactName.value.trim() == ''){
            alert('Contact Name is required');
            form.contactName.focus();
        }
        else if(form.contactEmail.value.trim() == ''){
            alert('Contact Email is required');
            form.contactEmail.focus();
        }else if(form.startDate.value.trim() == ''){
            alert('Start Date is required');
            form.startDate.focus();
        }else isValid = true;



        if(isValid){
            //show loader
            $('.loading-icon').removeClass('hidden');
            var formData = new FormData(form);
            $.ajax({
                url: form.action,
                type: form.method,
                data: formData,
                success: function(response){
                    $('.loading-icon').addClass('hidden');
                    if("success" == response.status){
                        //close the loader
                        alert("Institute created/edited successfully");
                        $('.loading-icon').removeClass('hidden');
                        window.location.href = "/instManager/adminDashboard?instituteId="+response.institutionId;
                    }else{
                        alert(response.message);
                    }
                },
                error: function(){
                    alert('An error occurred while processing your request');
                },
                cache: false,
                contentType: false,
                processData: false
            });
        }
    }


</script>
