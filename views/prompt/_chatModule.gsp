<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/styles/default.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
<link rel="stylesheet" href="/assets/bookgpt/chatModule.css">
<style>
.botMessage img {
    width: 100%;
    overflow: scroll;
    object-fit: contain;
}

</style>
<div class="col-12 col-lg-3 ib_chat" id="ib_chat" data-open="false">
    <div id="gpt_loader" class="gpt_loader">
        <div class="spinner"></div>
        <div class="introText">
            <h3 id="loaderSub" style="margin-top: 12px;">
                <%if("71".equals(""+session["siteId"])){%>
                    <img src="/assets/resource/kicx-logo.png" style="width: 90px !important;">
                <%}else if ("129".equals(""+session["siteId"])) { %>
                    <img src="/assets/resource/ai-guru.svg" style="width: 200px !important;">
                <%} else{%>
                    Preparing your <img src="/assets/resource/ibookgpt-logo-light.svg" style="width: 120px;margin-top: -8px;" />
                <%}%>
            </span>
            </h3>
        </div>
    </div>

    <div class="ib_head-wrapper" style="display:none;">
        <div class="ib_chat_header">
            <h3>
                <%if("71".equals(""+session["siteId"])){%>
                <img src="/assets/resource/kicx-logo.png" style="width: 60px !important;">
                <%}else if ("129".equals(""+session["siteId"])) { %>
                <img src="/assets/resource/ai-guru.svg" style="width: 100px !important;">
                <%} else{%>
                <img src="/assets/resource/ibookgpt-logo-light.svg">
                <%}%>
            </h3>
            <span class="ib_chat_header_close" onclick="closeChatWidget()"><i class="fa-solid fa-xmark"></i></span>
        </div>
        <p style="font-size: 12px;color: #999;text-align: center;">All interactions are specific to selected MCQ</p>
    </div>
    <div class="ib_chat_conversation" style="display:none;">
        <p id="prevChat" style="display:none;text-align: center">Loading previous chat...</p>
        <div id="messages" class="messages" style="display:flex;flex-direction: column"></div>
        <div class="is-typing" style="display:none;">
            <div class="jump1"></div>
            <div class="jump2"></div>
            <div class="jump3"></div>
        </div>
    </div>
    <div class="ib_chat_input" id="ib_chat_input">
        <div class="chatInputWrapper">
            <div id="capture-result-wrapper" style="width: 72px;">
                <span class="snipCancel" style="display: none">
                    <i class="fa-solid fa-xmark"></i>
                </span>
                <div id="capture-result"></div>
            </div>
            <textarea type="text" id="chatInput" class="chatInputField" placeholder="Ask any question about the chapter..." oninput="auto_grow(this)"></textarea>
        </div>
        <div class="chatInputOptions">
            <div>
                <button class="formulaBtn">
                    <i class="fa-solid fa-square-root-variable"></i>
                </button>
                <button id="start-capture-btn" class="inptOtpBtn" style="display:none;"><i class="fa-solid fa-crop-simple"></i></button>
            </div>

            <button class="sendIcon" id="sendBtn">
                <i class="fa-solid fa-paper-plane"></i>
            </button>
        </div>
    </div>
</div>
<div id="printableDiv"></div>
<div>
    <div id="top-overlay" class="overlay"></div>
    <div id="left-overlay" class="overlay"></div>
    <div id="right-overlay" class="overlay"></div>
    <div id="bottom-overlay" class="overlay"></div>
    <div>
        <div id="selection-box" style="position: fixed; display: none;"></div>
    </div>
</div>
<div id="feedbackModal" class="feedbackModal">
    <div class="modal-content">
        <div id="feedbackContent"></div>
    </div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/13.0.1/marked.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/highlight.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

<script>
    const ib_chat = document.getElementById('ib_chat')
    const gpt_loader = document.getElementById('gpt_loader')
    const chatInput = document.getElementById('chatInput')
    const sendBtn = document.getElementById('sendBtn')
    const messages = document.getElementById('messages')
    const isTyping = document.querySelector('.is-typing')
    const conversation = document.querySelector('.ib_chat_conversation');
    var feedbackContent = document.getElementById('feedbackContent');
    var modal = document.getElementById('feedbackModal');
    const prevChat = document.getElementById('prevChat')
    let isResponseComplete = true
    let isdoubtInputEntered = false;
    const docsSupported = ["pdf", "epub", "txt", "mcq"]
    let isValidDoc = false
    let answersCount = 0;
    let mcqs = [];
    let API_ENDPOINT = ""
    let controller = null;
    let history_for_llm = []
    let isSnipQuestion = false;
    let currImgLink = null;
    var typingWorker;
    var tempSiteId ="${session["siteId"]}"
    var curTempIndex = null

    const selectionBox = document.getElementById('selection-box')
    const topOverlay = document.getElementById('top-overlay')
    const leftOverlay = document.getElementById('left-overlay')
    const rightOverlay = document.getElementById('right-overlay')
    const bottomOverlay = document.getElementById('bottom-overlay')
    const mainContainerDiv = document.querySelector('.quizes')
    let startCaptureBtn = document.getElementById('start-capture-btn')
    let snipCancelBtn = document.querySelector('.snipCancel')
    const captureResult = document.getElementById('capture-result')
    let startX, startY, endX, endY, isSelecting = false
    let captureActive = false
    let pageNo = 0;
    let currentQue = null;

    let mobileView = false;
    function isMobile() {
        return window.innerWidth <= 768;
    }
    if (isMobile()) {
        mobileView = true;
    }
    function isIOS() {
        const ua = navigator.userAgent || navigator.vendor || window.opera;
        return /iPad|iPhone|iPod/.test(ua) && !window.MSStream;
    }
    function openChatWidget(){
        ib_chat.setAttribute('data-open','true')
        ib_chat.classList.add('chatOpen')
    }
    function closeChatWidget(){
        if (controller) {
            controller.abort();
            controller = null;
        }
        pauseShowingAnswer()
        ib_chat.setAttribute('data-open','false')
        ib_chat.classList.remove('chatOpen')
    }

    function hideLoader(){
        document.querySelector('.ib_head-wrapper').style.display = "block"
        document.querySelector('.ib_chat_conversation').style.display = "block"
        gpt_loader.style.display = "none"
    }

    function showLoader(){
        document.querySelector('.ib_head-wrapper').style.display = "none"
        document.querySelector('.ib_chat_conversation').style.display = "none"
        gpt_loader.style.display = "flex"
    }

    if(sendBtn){
        sendBtn.addEventListener('click',()=>{
            if((tempSiteId=="71" && gpt == "true" && checkTokens()) || (tempSiteId!="71")){
                sendBtnHandler()
            }else{
                $("#rechargeModal").modal("show")
            }
        })
    }
    if(chatInput){
        chatInput.addEventListener('keydown',(e)=>{
            if(e.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                if((tempSiteId=="71" && gpt == "true" && checkTokens()) || (tempSiteId!="71")){
                    sendBtnHandler()
                }else{
                    $("#rechargeModal").modal("show")
                }

            }
        })
    }

    function sendBtnHandler(){
        if(isResponseComplete){
            if(chatInput.value.trim()!=='' || isSnipQuestion){
                isdoubtInputEntered = true
                askDoubts(chatInput.value,'userInput')
                auto_shrink()
            }
        }else{
            pauseShowingAnswer()
        }
    }

    async function askDoubts(query,resType){
        if(isdoubtInputEntered){
            mcqChat(curTempIndex, "userInput", "", query)
        }
    }

    const askDoubtAPI = async (reqObj, resType, userQuery, query) => {
        if (controller) {
            controller.abort();
        }

        controller = new AbortController();
        const { signal } = controller;

        try {
            let response = await fetch(API_ENDPOINT, {
                method: "POST",
                body: JSON.stringify(reqObj),
                headers: {
                    "Content-Type": "application/json"
                },
                signal
            });

            const answer = await response.json();
            answer.resType = resType;
            history_for_llm.push({
                user: answer.query,
                ai: answer.answer,
            });
            showAnswer(answer, query, true, false);
            hideLoader();
            chatInput.value = '';
            isdoubtInputEntered = false;
            saveQuizChat(answer.answer, answer.query, userQuery, query)
            if(!answer.isExist && reqObj.type != "similarMCQ"){
                storeQuizInteraction({response:answer.answer,qId : reqObj.qId,type:reqObj.type  })
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log("Fetch aborted");
            } else {
                console.error("Fetch error:", error);
            }
        } finally {
            controller = null;
        }
    };

    function auto_shrink(){
        chatInput.style.height = "20px";
        chatInput.value = ''
    }
    function auto_grow() {
        chatInput.style.height = "5px";
        chatInput.style.height = (chatInput.scrollHeight) + "px";
    }

    function showUserMessage(query,isHistory,historyObj){
        isResponseComplete = false
        isTyping.style.display='flex'
        answersCount++
        var htmlString = '<div class="studentMessageWrapper">' +
            '<div class="message userMessage" id="userQue_'+answersCount+'">'+query+'</div>' +
            '<div class="studentIcon">' +
            '<img src="/assets/resource/student-icon.svg">' +
            '</div>' +
            '</div>';
        messages.innerHTML += htmlString
        if(query){
            const ansDiv = document.getElementById("userQue_"+answersCount+"")
            const txt = ansDiv.textContent
            if(ansDiv){
                renderMathInElement(ansDiv);
            }
        }
        conversation.scrollTop = conversation.scrollHeight + 400
        if(isHistory){
            showAnswer(historyObj,query,false)
        }
    }

    function showAnswer(answer,quesQuery,showTyping,preChat){
        isTyping.style.display='none'
        const parsedContent = answer.answer;
        const botAnswerWrapper = document.createElement('div')
        botAnswerWrapper.id = "ans_"+answersCount
        botAnswerWrapper.classList.add('botAnswerWrapper')

        var botAnswerHtml = '<div class="botTutorIcon">' +
                                    '<img src="/assets/resource/tutor-icon.svg">' +
                            '</div>' +
                                '<div style="width: 100%;">' +
                                    '<div class="message botMessage" id="ansDiv_'+answersCount+'">' +
                                        '<div id="mesPlaceholder_'+answersCount+'" style="word-break: break-word"></div>' +
                                    '</div>' +
                                    '<div class="feedbackWrap" id="feedbackWrap_'+answersCount+'" style="display:none;">' +
                                        '<div style="display: flex; align-items: center; gap: 10px;">' +
                                            '<span class="likeOtp" style="display:none;">' +
                                                '<i class="fa-regular fa-thumbs-up" id="feedback_like_'+answersCount+'" data-gptlogid="" data-id="'+answersCount+'" onclick="likeResHandler(event,\''+answersCount+'\')"></i>' +
                                            '</span>' +
                                            '<span class="dislikeOtp" style="display:none;">' +
                                                '<i class="fa-regular fa-thumbs-down" id="feedback_dislike_'+answersCount+'" data-gptlogid="" data-id="" onclick="dislikeResHandler(event,\''+answersCount+'\')"></i>' +
                                            '</span>';
                                            if(tempSiteId=="71"){
                                botAnswerHtml +='<span class="printOpt" style="display:none !important;" id="printToolTip_'+answersCount+'" onclick="printContent(\''+answersCount+'\')">' +
                                                '<i class="fa-solid fa-print"></i>' +
                                                '<span class="printToolTip">Print chat</span>' +
                                                '</span>';
                                            }else{
                                botAnswerHtml +='<span class="printOpt" id="printToolTip_'+answersCount+'" onclick="printContent(\''+answersCount+'\')">' +
                                                '<i class="fa-solid fa-print"></i>' +
                                                '<span class="printToolTip">Print chat</span>' +
                                                '</span>';
                                            }
                                botAnswerHtml +='</div>' +
                                    '</div>' +
                                '</div>';

        botAnswerWrapper.innerHTML = botAnswerHtml
        if(preChat){
            messages.prepend(botAnswerWrapper)
        }else{
            messages.append(botAnswerWrapper)
        }
        if(showTyping){
            conversation.scrollTop = conversation.scrollHeight + 200
            document.getElementById('sendBtn').innerHTML = '<i class="fa-solid fa-pause"></i>'
            typeWriter(parsedContent, 0);
        }else{
            const ansDiv = document.getElementById("mesPlaceholder_"+answersCount+"")
            ansDiv.innerHTML = parsedContent
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });
            renderMathInElement(ansDiv, {
                delimiters: [
                    { left: "\\(", right: "\\)", display: false },
                    { left: "\\[", right: "\\]", display: true }
                ]
            });
            ansDiv.innerHTML = marked.parse(ansDiv.innerHTML)
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });
            document.getElementById("feedbackWrap_"+answersCount+"").style.display='block'
        }

    }

    const findIndexById = (id) => mcqs.findIndex(item => item.id === id);

    async function mcqChat(index, type, id, userQuery){
        showLoader()
        const isOpen = ib_chat.getAttribute('data-open')
        let prompt = ""
        let query = ""
        if(isOpen=="false"){
            openChatWidget()
        }
        let q = findIndexById(Number(index));
        curTempIndex = index
        if(id!="" && index!=""){
            q = findIndexById(Number(id));
            curTempIndex = id
        }
        if(curTempIndex != currentQue){
            clearChat()
            history_for_llm  = []
            globalChatHistory = []
            await getChatHistory()
        }
        currentQue = curTempIndex


        if(type=="explain"){
            query = "Explain the given MCQ"
        }else if(type=="similarMCQ"){
            query = "Create similar MCQs"
        }else if(type=="hint"){
            query = "Give Hint"
        }else if(type == "userInput"){
            query = userQuery
        }

        showUserMessage(query)

        let {mcqQuestionStr, hasImage} = constructQuestion(mcqs[q])
        mcqQuestionStr += prompt
        askDoubtAPI({
            query:mcqQuestionStr,
            chatHistory:history_for_llm,
            mcq:true,
            type:type,
            qId:curTempIndex,
            hasImage:hasImage,
            language:language
        }, "userInput", userQuery, query)
    }

    function typeWriter(text, i, callback) {
        var currId = "mesPlaceholder_"+answersCount+""
        var answerDiv = document.getElementById(currId);
        typingWorker = new Worker('/assets/bookGPTScripts/typeWorker.js');

        typingWorker.postMessage({ text: text, index: i, speed: 5 });

        typingWorker.onmessage = function (e) {
            if (e.data.done) {
                typingWorker.terminate();
                typingWorker = null;

                const tempEl = document.createElement("div");
                tempEl.innerHTML = text;

                renderMathInElement(tempEl, {
                    delimiters: [
                        { left: "\\(", right: "\\)", display: false },
                        { left: "\\[", right: "\\]", display: true }
                    ]
                });

                const renderedHtml = marked.parse(tempEl.innerHTML);
                answerDiv.innerHTML = renderedHtml;
                conversation.scrollTop = conversation.scrollHeight+50;
                document.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightBlock(block);
                });
                if( document.getElementById("feedbackWrap_" + answersCount)){
                    document.getElementById("feedbackWrap_" + answersCount).style.display = 'block';
                }

                document.getElementById('sendBtn').innerHTML = '<i class="fa-solid fa-paper-plane"></i>';
                isResponseComplete = true;
                isdoubtInputEntered=false;
                if (callback) {
                    callback();
                }
            } else {
                const tempEl = document.createElement("div");
                tempEl.innerHTML = text.substring(0, e.data.index);

                renderMathInElement(tempEl, {
                    delimiters: [
                        { left: "\\(", right: "\\)", display: false },
                        { left: "\\[", right: "\\]", display: true }
                    ]
                });

                answerDiv.innerHTML = marked.parse(tempEl.innerHTML);
                document.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightBlock(block);
                });
            }
        };
    }

    function pauseShowingAnswer(){
        if (typingWorker) {
            typingWorker.terminate();
            typingWorker = null;  // Reset the worker reference
        }
        isResponseComplete = true;
        isSnipQuestion=false
        isdoubtInputEntered=false;
        document.getElementById('sendBtn').innerHTML = "<i class='fa-solid fa-paper-plane'></i>"
        conversation.scrollTop = conversation.scrollHeight
    }
    const ibookgptObj = {
        openChatWidget,
        showLoader,
        hideLoader,
        closeChatWidget,
        mcqChat
    }

    function initIbookgpt(config){
        isValidDoc = validateDocType(config.docType)
        if(config.questions){
            mcqs = config.questions
        }
        if(config.enableSnip){
            startCaptureBtn.style.display = "block"
        }
        API_ENDPOINT = config.chatEndPoint
        return ibookgptObj
    }

    function validateDocType(type){
        if(docsSupported.includes(type)){
            return true
        }else {
            throw new Error("Document is not supported for ibookGPT.")
        }
    }

    function constructQuestion(questionObjInstance) {
        const baseUrl = window.location.origin;
        let hasImage = false;
        function prependBaseUrlIfNeeded(text) {
            if (/<img.*?src=['"]([^'"]+)['"]/g.test(text)) {
                hasImage = true;
            }
            return text.replace(/src='([^']+)'/g, (match, p1) => {
                if (p1.startsWith('/')) {
                    return "src='" + baseUrl + p1 + "'";
                }
                return match;
            });
        }
        let mcqQuestionStr = "MCQ:\n" + prependBaseUrlIfNeeded(questionObjInstance.ps) + "\n";

        for (let i = 1; i <= 5; i++) {
            const optionKey = "op" + i;
            if (questionObjInstance[optionKey]) {
                let optionText = prependBaseUrlIfNeeded(questionObjInstance[optionKey]);
                mcqQuestionStr += "Option " + i + ":\n" + optionText + "\n";
            }
        }

        mcqQuestionStr += "Correct answer: \n";

        for (let i = 1; i <= 5; i++) {
            const answerKey = "ans" + i;
            const optionKey = "op" + i;
            if (questionObjInstance[answerKey] === "Yes" && questionObjInstance[optionKey]) {
                let correctAnswerText = prependBaseUrlIfNeeded(questionObjInstance[optionKey]);
                mcqQuestionStr += correctAnswerText + "\n";
                break;
            }
        }

        return { mcqQuestionStr, hasImage };
    }

    function printContent(id) {
        const linkULR = new URL(window.location.href)
        // Get the elements for the question and answer based on the ID
        const answerPrint = document.getElementById('mesPlaceholder_' + id)
        const questionPrint = document.getElementById('userQue_' + id);
        const printableDiv = document.getElementById('printableDiv');

        // Clone the elements to prevent moving them from their original place
        const clonedAnswer = answerPrint.cloneNode(true);
        const clonedQuestion = questionPrint.cloneNode(true);

        printableDiv.innerHTML = "";

        // Create branding elements
        const logoDiv = document.createElement('div');
        logoDiv.classList.add('branding');

        const logoImg = document.createElement('img');
        logoImg.src = linkULR.origin+'/assets/resource/ibookgpt-logo-light.svg';
        logoImg.style.maxWidth = '150px';
        logoImg.style.marginBottom = '20px';
        logoImg.style.display = 'block';  // Ensures it's treated as a block element
        logoDiv.appendChild(logoImg);

        const siteLink = document.createElement('a');

        siteLink.href = linkULR.origin
        siteLink.textContent = "Visit us at: " +linkULR.host
        siteLink.style.marginRight = '30px'
        logoDiv.appendChild(siteLink)

        // Align logoDiv to the top-left corner
        logoDiv.style.position = 'absolute';
        logoDiv.style.top = '20px';
        logoDiv.style.left = '20px';


        // Create content div for question and answer
        const contentDiv = document.createElement('div');
        contentDiv.classList.add('content');
        contentDiv.style.border = '1px solid #ddd';
        contentDiv.style.padding = '20px';
        contentDiv.style.margin = '10px 0 20px 0';  // Adjust margin to avoid logo overlap
        contentDiv.style.borderRadius = '5px';
        contentDiv.style.backgroundColor = '#f9f9f9';

        const questionDiv = document.createElement('div');
        questionDiv.classList.add('question');
        questionDiv.innerHTML = clonedQuestion.innerHTML;
        questionDiv.style.fontWeight = 'bold';
        questionDiv.style.fontSize = '18px';
        questionDiv.style.marginBottom = '10px';
        const answerDiv = document.createElement('div');
        answerDiv.classList.add('response');
        answerDiv.innerHTML = clonedAnswer.innerHTML;
        answerDiv.style.fontSize = '16px';
        answerDiv.style.lineHeight = '1.6';

        contentDiv.appendChild(questionDiv);
        contentDiv.appendChild(answerDiv);

        // Append all elements to the printableDiv
        printableDiv.appendChild(logoDiv);
        printableDiv.appendChild(contentDiv);

        const watermark = document.createElement('div');
        watermark.innerHTML = 'iBookGPT';
        watermark.style.position = 'fixed';
        watermark.style.top = '50%';
        watermark.style.left = '50%';
        watermark.style.transform = 'translate(-50%, -50%) rotate(-45deg)';
        watermark.style.fontSize = '80px';
        watermark.style.color = 'rgba(0, 0, 0, 0.1)';
        watermark.style.pointerEvents = 'none';
        watermark.style.whiteSpace = 'nowrap';
        printableDiv.appendChild(watermark);
        var divContent = printableDiv.innerHTML;

        var printWindow = window.open('', '', 'height=600,width=800');
        printWindow.document.write('<html><head><title></title>');
        printWindow.document.write('<style>');
        printWindow.document.write('@media print {');
        printWindow.document.write('@page { size: A4; margin: 20; }'); // Set margins to 0 to minimize headers and footers
        printWindow.document.write('body { margin: 0; padding: 10mm; -webkit-print-color-adjust: exact; }');
        printWindow.document.write('.branding { position: absolute; top: 20px; left: 20px;width:100%; display:flex;justify-content:space-between;align-items:center;}');
        printWindow.document.write('.branding img { display: block; margin-bottom: 20px; max-width: 150px; }');
        printWindow.document.write('.content { padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9; }');
        printWindow.document.write('.content .question { font-weight: bold; font-size: 16px; margin-bottom: 10px; }');
        printWindow.document.write('.content .response { font-size: 14px; line-height: 1.6; }');
        printWindow.document.write('.watermark { font-size: 100px; opacity: 0.1; }'); // Lighter watermark for print
        printWindow.document.write('}');
        printWindow.document.write('</style>');
        printWindow.document.write('</head><body>');
        printWindow.document.write(divContent);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.print();

        printWindow.onafterprint = function() {
            printWindow.close();
        };

        printWindow.print();
        printableDiv.innerHTML = "";

        printableDiv.innerHTML = "";
    }

    selectionBox.style.display = 'none'
    startCaptureBtn.disabled = false
    startCaptureBtn.addEventListener('click', captureClicked)
    snipCancelBtn.addEventListener("click",cancelSnip)

    function cancelSnip(){
        captureResult.innerHTML = ""
        captureResult.style.display = "none"
        snipCancelBtn.style.display = "none"
        isSnipQuestion = false
        currImgLink = ""
    }
    function captureClicked() {
        if(mobileView){
            closeChatWidget()
        }
        captureActive = !captureActive // Toggle capture mode
        if (captureActive) {
            mainContainerDiv.style.cursor = 'crosshair'
            selectionBox.style.display = 'none'
            mainContainerDiv.classList.add('mainContainerBorder')
        } else {
            resetCapture()
        }
        startCaptureBtn.disabled = captureActive
    }

    document.addEventListener('mousedown', startSelection)
    document.addEventListener('mousemove', moveSelection)
    document.addEventListener('mouseup', endSelection)

    document.addEventListener('touchstart', startSelection)
    document.addEventListener('touchmove', moveSelection)
    document.addEventListener('touchend', endSelection)

    function startSelection(e) {
        if (!captureActive || e.target === startCaptureBtn) return
        isSelecting = true
        const { clientX, clientY } = e.type.includes('touch') ? e.touches[0] : e

        startX = clientX
        startY = clientY
        selectionBox.style.left = startX + "px"
        selectionBox.style.top = startY + "px"
        selectionBox.style.width = '0px'
        selectionBox.style.height = '0px'
        selectionBox.style.display = 'block'
        updateOverlay(startX, startY, startX, startY)
    }

    function moveSelection(e) {
        if (!isSelecting) return
        e.preventDefault()

        const { clientX, clientY } = e.type.includes('touch') ? e.touches[0] : e
        endX = clientX
        endY = clientY

        selectionBox.style.width = Math.abs(endX - startX) + 'px'
        selectionBox.style.height = Math.abs(endY - startY) + 'px'
        selectionBox.style.left = Math.min(startX, endX) + 'px'
        selectionBox.style.top = Math.min(startY, endY) + 'px'
        updateOverlay(startX, startY, endX, endY)
    }

    function endSelection(e) {
        if (!isSelecting) return
        isSelecting = false
        e.preventDefault()

        if (selectionBox.style.width !== '0px' && selectionBox.style.height !== '0px') {
            const rect = selectionBox.getBoundingClientRect()
            if (rect.width === 0 || rect.height === 0) {
                alert('Please select a portion of the page before capturing.')
                return
            }

            html2canvas(document.body, {
                x: rect.left + window.scrollX,
                y: rect.top + window.scrollY,
                width: rect.width,
                height: rect.height,
            }).then((canvas) => {
                const imgData = canvas.toDataURL('image/png')
                const blob = dataURLToBlob(imgData)
                displayCapturedImage(imgData)
                resetCapture()
                startCaptureBtn.disabled = false
                mainContainerDiv.style.cursor = 'default'
            }).catch((error) => {
                console.error('Error capturing image:', error)
            })
        }
    }

    function displayCapturedImage(imgData) {
        const imgElement = document.createElement('img')
        imgElement.src = imgData
        captureResult.innerHTML = ''
        captureResult.appendChild(imgElement)
        captureResult.style.display = "block"
        snipCancelBtn.style.display = "flex"
        if(mobileView){
            openChatWidget()
        }
    }

    function dataURLToBlob(dataURL) {
        const byteString = atob(dataURL.split(',')[1])
        const mimeString = dataURL.split(',')[0].split(':')[1].split(';')[0]
        const ab = new ArrayBuffer(byteString.length)
        const ia = new Uint8Array(ab)
        for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i)
        }
        return new Blob([ab], { type: mimeString })
    }

    function updateOverlay(startX, startY, endX, endY) {
        const rectX = Math.min(startX, endX)
        const rectY = Math.min(startY, endY)
        const rectWidth = Math.abs(endX - startX)
        const rectHeight = Math.abs(endY - startY)

        topOverlay.style.width = '100%'
        topOverlay.style.height = rectY + 'px'
        topOverlay.style.top = '0'
        topOverlay.style.left = '0'

        leftOverlay.style.width = rectX + 'px'
        leftOverlay.style.height = rectHeight + 'px'
        leftOverlay.style.top = rectY + 'px'
        leftOverlay.style.left = '0'

        rightOverlay.style.width = 'calc(100% - ' + (rectX + rectWidth) + 'px)'
        rightOverlay.style.height = rectHeight + 'px'
        rightOverlay.style.top = rectY + 'px'
        rightOverlay.style.left = (rectX + rectWidth) + 'px'

        bottomOverlay.style.width = '100%'
        bottomOverlay.style.height = 'calc(100% - ' + (rectY + rectHeight) + 'px)'
        bottomOverlay.style.top = (rectY + rectHeight) + 'px'
        bottomOverlay.style.left = '0'
    }

    function hideOverlay() {
        topOverlay.style.width = leftOverlay.style.width = rightOverlay.style.width = bottomOverlay.style.width = '0'
        topOverlay.style.height = leftOverlay.style.height = rightOverlay.style.height = bottomOverlay.style.height = '0'
    }

    function resetCapture() {
        captureActive = false
        hideOverlay()
        selectionBox.style.display = 'none'
        mainContainerDiv.classList.remove('mainContainerBorder')
        mainContainerDiv.style.cursor = 'default'
    }

    const formulaBtn = document.querySelector('.formulaBtn')
    formulaBtn.addEventListener('click',()=>{
        let inputHTML = "<div class='feedbackOptions'>" +
            "<div class='closeBtn'>" +
            "<span onclick='closeFormula()'><i class='fa-solid fa-xmark'></i></span>"+
            "</div>"+
            "<div class='formulaModalContent'>" +
            "<p>Write Formula LaTex</p>" +
            "<textarea id='formulaInputField'></textarea>" +
            "</div>" +
            "<div id='formulaPreviewDiv' class='formulaPreviewDiv'>" +
            "</div>"+
            "<div class='formulaActions'>" +
            "<div>" +
            "<button class='addFormula'>Add Formula</button>" +
            "</div>" +
            "<a href='https://en.wikibooks.org/wiki/LaTeX/Mathematics' target='_blank'>LaTex Documentation</a> "+
            "</div>"+
            "</div>";
        feedbackContent.innerHTML = inputHTML;
        openModal()

        const addFormula = document.querySelector('.addFormula')
        const formulaInputField = document.getElementById('formulaInputField')
        const formulaPreviewDiv = document.getElementById('formulaPreviewDiv')
        formulaInputField.value = "\\[ x^n + y^n = z^n \\]"
        renderFormula()
        addFormula.addEventListener('click',()=>{
            if(formulaInputField.value.trim()!=""){
                const chatInp = document.getElementById('chatInput')
                const cursorPosition = chatInp.selectionStart;
                const newValue = formulaInputField.value
                chatInp.value = newValue
                chatInp.setSelectionRange(cursorPosition + formulaInputField.value.length, cursorPosition + formulaInputField.value.length);
            }
            closeFormula()
        })
        function renderFormula() {
            try {
                formulaPreviewDiv.textContent = formulaInputField.value.trim()
                renderMathInElement(formulaPreviewDiv);
            } catch (error) {
                formulaPreviewDiv.innerHTML = "Invalid formula";
            }
        }
        formulaInputField.addEventListener('input', renderFormula);
    })
    function openModal() {
        modal.style.display = 'block';
    }
    function closeFormula(){
        setTimeout(() => {
            modal.style.display = 'none';
            modal.classList.remove('fade-out');
        }, 500);
    }

    async function storeQuizInteraction(reqObj) {
        try {

            const apiUrl = '/prompt/storeQuizGptContents';
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(reqObj)
            });

            if (!response.ok) {
                throw new Error("HTTP error! status: "+response.status);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error during API call:', error);
            throw error;
        }
    }

    async function saveQuizChat(answer, sysMessage, userQuery, query){
        const logUserQuery = {
            username:userId,
            response:answer,
            resId:readId,
            readingMaterialResId:readId,
            promptType:"userInput",
            quizObjId:curTempIndex,
            systemPrompt:sysMessage,
            userPrompt:query
        }
        try{
            const storeUserQuery = await fetch('/gptLog/save ',{
                method:"POST",
                body:JSON.stringify(logUserQuery),
                headers:{
                    "Content-Type":"application/json"
                }
            })
            const storedResponse = await storeUserQuery.json()
            const storedRes = storedResponse.GptLog
            freeTokenCount = storedResponse.freeTokenCount
            paidTokenCount = storedResponse.paidTokenCount
        }catch (err){
            console.log(err)
        }
    }

    function checkTokens(){
        if((freeTokenCount!==0 && freeTokenCount!==''&& freeTokenCount!=='0'  && freeTokenCount>0) ||
            (paidTokenCount !==0 && paidTokenCount!=='' && paidTokenCount!=='0' && paidTokenCount>0)){
            return true
        }else {
            return false
        }
    }

    async function paginateHistory(){
        pageNo = pageNo+10;
        let historyAPI = "/gptLog/find?username="+userId+"&resId="+readId+"&pageNo="+pageNo+"&quizObjId="+curTempIndex
        const chatHistory = await fetch(historyAPI)
        let chatHistoryJSON = await chatHistory.json()
        const historyObj = chatHistoryJSON.GptLogs
        if(historyObj.length==0){
            prevChat.innerHTML = "You've reached the end!"
            setTimeout(()=>{
                prevChat.style.display='none';
            },2500)
        }else{
            prevChat.style.display='none';
        }
        if(historyObj.length>0){
            for(let c=0;c<historyObj.length;c++){
                let question
                if(historyObj[c].systemPrompt!=null && historyObj[c].userPrompt==null){
                    question = historyObj[c].systemPrompt
                }else if(historyObj[c].systemPrompt==null && historyObj[c].userPrompt!=null){
                    question = historyObj[c].userPrompt
                }
                historyObj[c].answer = historyObj[c].response
                historyObj[c].resType = historyObj[c].promptType
                historyObj[c].gptLogId = historyObj[c].id
                updatePrevChat(historyObj[c],question)
            }
        }
    }
    async function getChatHistory(){
        try{
            showLoader();
            let historyAPI = "/gptLog/find?username="+userId+"&resId="+readId+"&pageNo="+pageNo+"&quizObjId="+curTempIndex
            const chatHistory = await fetch(historyAPI)
            let chatHistoryJSON = await chatHistory.json()
            chatHistoryJSON = chatHistoryJSON.GptLogs
            document.getElementById("messages").innerHTML = "";
            answersCount=0
            if(chatHistoryJSON.length>0){
                const historyObj = chatHistoryJSON.reverse();
                globalChatHistory = historyObj
                if(historyObj.length>0){
                    for(let c=0;c<historyObj.length;c++){
                        let question
                        if(historyObj[c].systemPrompt!=null && historyObj[c].userPrompt==null){
                            question = historyObj[c].systemPrompt
                        }else if(historyObj[c].systemPrompt==null && historyObj[c].userPrompt!=null){
                            question = historyObj[c].userPrompt
                        }else if(historyObj[c].systemPrompt!=null && historyObj[c].userPrompt!=null &&
                            (historyObj[c].userPrompt!="Explain the given MCQ" || historyObj[c].userPrompt!="Create similar MCQs" && historyObj[c].systemPrompt!="Give Hint")){
                            question = historyObj[c].userPrompt
                        }else if((historyObj[c].systemPrompt!=null && historyObj[c].userPrompt!=null) && (historyObj[c].systemPrompt=="Give Hint")){
                            question = historyObj[c].systemPrompt
                        }
                        historyObj[c].answer = historyObj[c].response
                        historyObj[c].resType = historyObj[c].promptType
                        historyObj[c].gptLogId = historyObj[c].id
                        updateHistoryUI(historyObj[c],question)
                    }
                }
            }
            hideLoader()
        }catch (err){
            console.log(err)
        }
    }

    function updateHistoryUI(resObj,question){
        showUserMessage(question,true,resObj)
        conversation.scrollTop = conversation.scrollHeight
    }
    function clearChat(){
        document.getElementById("messages").innerHTML = "";
    }
    function updatePrevChat(historyObj,question){
        answersCount++
        showAnswer(historyObj,question,false,preChat=true)

        const studentMessageWrapper = document.createElement('div')
        studentMessageWrapper.classList.add('studentMessageWrapper')
        let userMessageHTML = ""
        userMessageHTML +=
            "<div class='message userMessage' id='userQue_"+answersCount+"'>"+question+"</div>"+
                "<div class='botTutorIcon'>"+
                    "<img src='/assets/resource/student-icon.svg'/>"+
                "</div>";
        studentMessageWrapper.innerHTML  = userMessageHTML
        messages.prepend(studentMessageWrapper)
    }
</script>