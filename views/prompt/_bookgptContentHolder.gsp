
<link rel="stylesheet" href="/assets/viewer.css">

<div>
    <% if(enableCompiler) {%>
    <div class="pdfOpts" style="justify-content: space-between">
        <button class="compiler_open-modal-btn">Open Compiler</button>
        <% } else {%>
        <div class="pdfOpts" style="justify-content: flex-end">
            <%}%>
            <span class="fullScreenBtn" style="align-items: center;gap: 8px" data-chat="open">
                <i class="fa-solid fa-expand"></i> Full Screen
            </span>
        </div>
    <div id="contentViewerContainer" tabindex="0" >
    </div>
</div>

<style>


#contentViewerContainer {
    position: relative;
    overflow-y: auto;
}

</style>

<script>


    function createBookSnapsElement(rootElement) {
        var bookSnaps = document.createElement("div");
        bookSnaps.id = "book_snaps";
        bookSnaps.className = "book_snaps";
        bookSnaps.style.display = "none";

        function createElement(tag, attributes, textContent) {
            var element = document.createElement(tag);
            if (attributes) {
                for (var key in attributes) {
                    element.setAttribute(key, attributes[key]);
                }
            }
            if (textContent) {
                element.textContent = textContent;
            }
            return element;
        }

        var summarySnap = createElement("div", { id: "summary_snap", class: "summary_snap", style: "display: none" });
        var snapContent = createElement("div", { class: "snap_content", id: "snap_content" });
        var toggleSnapBtn = createElement("button", { id: "toggleSnapBtn", class: "toggleBtn" }, "Show more");

        summarySnap.appendChild(snapContent);
        summarySnap.appendChild(toggleSnapBtn);

        var qaSnap = createElement("div", { id: "qa_snap", class: "qa_snap", style: "display: none" });
        var snapTitleWrapQA = createElement("div", { class: "snapTitleWrapQA" });
        var qaContent = createElement("div", { class: "qa_content", id: "qa_content" });
        var toggleQABtn = createElement("button", { id: "toggleQABtn", class: "toggleBtn" }, "Show All QA");

        qaSnap.appendChild(snapTitleWrapQA);
        qaSnap.appendChild(qaContent);
        qaSnap.appendChild(toggleQABtn);

        var mcqSnap = createElement("div", { id: "mcq_snap", class: "mcq_snap", style: "display: none" });
        var snapTitleWrapMCQ = createElement("div", { class: "snapTitleWrapMCQ" });
        var mcqContent = createElement("div", { class: "mcq_content", id: "mcq_content" });
        var toggleBtn = createElement("button", { id: "toggleBtn", class: "toggleBtn" }, "Show All MCQs");

        mcqSnap.appendChild(snapTitleWrapMCQ);
        mcqSnap.appendChild(mcqContent);
        mcqSnap.appendChild(toggleBtn);

        var infoRibbon = createElement("div", { class: "info_ribbon" }, "Below given chapter is not part of the course you've purchased. This is given only for student's benefit as ready reference.");

        bookSnaps.appendChild(summarySnap);
        bookSnaps.appendChild(qaSnap);
        bookSnaps.appendChild(mcqSnap);
        bookSnaps.appendChild(infoRibbon);

        rootElement.insertBefore(bookSnaps, rootElement.firstChild);
    }

        document.addEventListener("DOMContentLoaded", async () => {

            const {showSnapshot, showMcq, showQa, showsnapPdf} = snapStateChecker()
            showsnapPdfVal = showsnapPdf
            showMcqVal = showMcq
            if (showSnapshot == "true" || showMcq == "true" || showQa == "true") {
                isSnapAvl = true
            }

            document.getElementById('contentViewerContainer').style.display = "block"

                        createBookSnapsElement(document.getElementById("contentViewerContainer"));

                        const book_snaps = document.getElementById('book_snaps')
                        const summary_snap = document.getElementById('summary_snap')
                        const snap_content = document.getElementById('snap_content')
                        const mcq_snap = document.getElementById('mcq_snap')
                        const qa_snap = document.getElementById('qa_snap')
                        const mcq_content = document.getElementById('mcq_content')
                        const qa_content = document.getElementById('qa_content')
                        const toggleBtn = document.getElementById('toggleBtn')
                        const toggleQABtn = document.getElementById('toggleQABtn')
                        const toggleSnapBtn = document.getElementById('toggleSnapBtn')
                        const snapTitleWrapMCQ = document.querySelector('.snapTitleWrapMCQ')
                        const snapTitleWrapQA = document.querySelector('.snapTitleWrapQA')
                        const res_tempId = "${params.resId ?: ''}"
                        let showAllMCQs = false;
                        let showAllQA = false;
                        const initialVisibleQuestions = 1;
                        let maxLength = 800;
                        let showFullText = false;
                        let fullScreen = null;
                        let panelLeftDiv = null;
                        let selectedLanguageGlobal = 0;
                        let languageVal = "English"
                        if(mobileView=="true"){
                        maxLength  = 400
                    }
                        const getGPTInfo = async (res_tempId)=>{
                        const response = await fetch("/prompt/createTest?readingMaterialResId="+res_tempId)
                        if(!response.ok){
                        return
                    }
                        return await response.json()
                    }

                        const getSnapshot = async (res_tempId)=>{
                        const response = await fetch("/prompt/getGPTsForResource?resId="+res_tempId+"&promptType=chapter_snapshot")
                        if(!response.ok){
                        return
                    }
                        return await response.json()
                    }
                        let snapdata;
                        let data;
                        if(showSnapshot=="true"){
                        snapdata = await getSnapshot(res_tempId)
                        if(snapdata.hasResource){
                       // renderSnapshot(snapdata)
                    }
                    }

                        if(showMcq=="true" || showQa=="true"){
                        data = await getGPTInfo(res_tempId)
                        mcqQuizId = data.mcqQuizId
                        mcqResId = data.mcqResId
                        if(showMcq=="true"){
                        if(data.mcqs.length>0){
                        if((showsnapPdfVal!="true" || showsnapPdfVal==null) && showMcqVal=="true"){
                        toggleBtn.style.display = 'none';
                        renderMCQsQuestions(data.mcqs, data.mcqLanguages);
                    }else{
                        renderMCQsQuestions(data.mcqs.slice(0, initialVisibleQuestions), data.mcqLanguages);
                    }
                    }

                    }

                        if(showQa=="true"){
                        if(data.qna.length>0){
                        renderQAQuestions(data.qna.slice(0, initialVisibleQuestions), data.qnaLanguages);
                    }
                    }
                    }

                        function renderSnapshot(data){
                        let parsedContent = data.answer;
                        if(parsedContent.length<=800 && mobileView!="true"){
                        toggleSnapBtn.style.display = 'none'
                    }
                        let snapHTML = ''
                        snapHTML+= "<div class='snap_content_div' id='snap_content_div'></div>"
                        snap_content.innerHTML = snapHTML;

                        const ansDiv = document.getElementById("snap_content_div")
                        if (!showFullText) {
                        parsedContent = parsedContent.substring(0, maxLength) + '...';
                        toggleSnapBtn.innerText = 'Show More';
                    }else{
                        toggleSnapBtn.innerText = 'Show Less';
                    }
                        ansDiv.innerHTML = parsedContent

                        renderMathInElement(ansDiv, {
                        delimiters: [
                    { left: "\\(", right: "\\)", display: false },
                    { left: "\\[", right: "\\]", display: true }
                        ]
                    });
                        ansDiv.innerHTML = marked.parse(ansDiv.innerHTML)
                        document.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightBlock(block);
                    });
                        summary_snap.style.display =  "block";
                    }

            function renderMCQsQuestions(questionsToShow, languages) {
                        let questions = questionsToShow
                        const siteName="${session['siteName']}";
                        let languagesList = languages ? [...new Set([languages.language1 !=null ? languages.language1 : null, languages.language2 !=null ? languages.language2 : null])] : []
                        languagesList = languagesList.filter(item => item != null);
                        snapTitleWrapMCQ.innerHTML = constructSnapTitle("Multiple Choice Questions", data.mcqs.length, languagesList, "mcqLanguages")
                        if((showsnapPdf!="true" || showsnapPdf==null) && showMcq=="true"){
                        var html_ts = '<div>' +
                        '<a href="/prepjoy/prepJoyGame?quizId='+mcqQuizId+'&resId='+mcqResId+'&quizType=&source=web&siteName='+siteName+'&learn=false&pubDesk=false&dailyTest=false&fromgpt=true&readId='+gptResId+'" target="_blank" class="ts_opt_link">Play</a>' +
                        '<span class="ts_opt_separator">|</span>' +
                        '<a href="/prepjoy/prepJoyGame?quizId='+mcqQuizId+'&resId='+mcqResId+'&quizType=practice&source=web&siteName='+siteName+'&learn=false&pubDesk=false&dailyTest=false&fromgpt=true&readId='+gptResId+'" target="_blank" class="ts_opt_link">Practice</a>' +
                        '<span class="ts_opt_separator">|</span>' +
                        '<a href="/prepjoy/prepJoyGame?quizId='+mcqQuizId+'&resId='+mcqResId+'&quizType=testSeries&source=web&siteName='+siteName+'&learn=false&pubDesk=false&dailyTest=false&fromgpt=true&readId='+gptResId+'" target="_blank" class="ts_opt_link">Test</a>' +
                        '</div>';
                        const tsOptMcqLanguages = document.getElementById("ts_opt_mcqLanguages");
                        if (tsOptMcqLanguages) {
                            tsOptMcqLanguages.innerHTML += html_ts;
                        }
                    }
                        mcq_content.innerHTML += "<div id='mcqContentRenderer'></div>";
                        let selectedLanguage = 0
                        if(languagesList.length>1){
                        const selectElement = document.getElementById("mcqLanguages");
                        if (selectElement) {
                            languagesList.forEach((option, index)=>{
                                const optionElement = document.createElement("option");
                                optionElement.value = index;
                                optionElement.textContent = option;
                                selectElement.appendChild(optionElement);
                            });
                            languageVal = selectElement.options[selectElement.selectedIndex].text;
                            selectElement.addEventListener("change", (event) => {
                                const selectedValue = event.target.value;
                                const selectedText = event.target.options[event.target.selectedIndex].text;
                                languageVal = selectedText;
                                selectedLanguage = selectedValue;
                                selectedLanguageGlobal = selectedLanguage;
                                showAllMCQs ? questions = data.mcqs : questions;
                                constructMCQHandler(questions, selectedLanguage);
                            });
                        }
                    }
                        constructMCQHandler(questions, selectedLanguage)
                    }

                        function renderQAQuestions(questionsToShow, languages) {
                        let questions = questionsToShow

                        let languagesList = languages ? [...new Set([languages.language1 !=null ? languages.language1 : null, languages.language2 !=null ? languages.language2 : null])] : []

                        languagesList = languagesList.filter(item => item != null);
                        snapTitleWrapQA.innerHTML = constructSnapTitle("Question and Answers ", data.qna.length, languagesList, "qnaLanguages")
                        qa_content.innerHTML += "<div id='qnaContentRenderer'></div>";
                        let selectedLanguage = 0
                        if(languagesList.length>1){
                        const selectElement = document.getElementById("qnaLanguages");
                        if (selectElement) {
                            languagesList.forEach((option, index)=>{
                                const optionElement = document.createElement("option");
                                optionElement.value = index;
                                optionElement.textContent = option;
                                selectElement.appendChild(optionElement);
                            });

                            selectElement.addEventListener("change", (event) => {
                                const selectedValue = event.target.value;
                                const selectedText = event.target.options[event.target.selectedIndex].text;
                                selectedLanguage = selectedValue;
                                showAllQA ? questions = data.qna : questions;
                                constructQAHandler(questions, selectedLanguage);
                            });
                        }
                    }

                        constructQAHandler(questions, selectedLanguage)
                    }

                        if(showSnapshot=="true" || showMcq=="true" || showQa=="true"){
                        book_snaps.style.display = 'block';
                    }

                        if((showsnapPdf!="true" || showsnapPdf==null) && showMcq=="true"){
                            const infoRibbon = document.querySelector('.info_ribbon');
                            if (infoRibbon) {
                                infoRibbon.style.display = "none";
                            }
                        }

                        function toggleMCQs() {
                        if(data){
                        showAllMCQs = !showAllMCQs;
                        if (showAllMCQs) {
                        const selectedLanguage = document.getElementById('mcqLanguages')?.value || 0
                        constructMCQHandler(data.mcqs, selectedLanguage)
                        toggleBtn.innerText = 'Show All MCQs';
                        toggleBtn.style.display = 'none';
                    }
                    }
                    }

                        function toggleQA() {
                        if(data){
                        showAllQA = !showAllQA;
                        if (showAllQA) {
                        const selectedLanguage = document.getElementById('qnaLanguages')?.value || 0
                        constructQAHandler(data.qna, selectedLanguage)
                        toggleQABtn.innerText = 'Show All QA';
                        toggleQABtn.style.display = 'none';
                    }
                    }
                    }

                        function toggleSnap() {
                        if(snapdata){
                        showFullText = !showFullText;
                        if(showFullText){
                        toggleSnapBtn.style.display = 'none'
                        renderSnapshot(snapdata)
                    }
                    }
                    }
                        function extractText(text, languageIndex) {
                            if(text==null){
                            return ""
                        }
                        const parts = text.split('~~');

                            var firstPart = parts[0].trim();
                            //remove any <p> tags from the first part
                            firstPart = firstPart.replace(/<p>/g, '');
                            firstPart = firstPart.replace(/<\/p>/g, '');
                            var secondPart = parts[1] ? parts[1].trim() : '';
                            //remove any <p> tags from the second part
                            secondPart = secondPart.replace(/<p>/g, '');
                            secondPart = secondPart.replace(/<\/p>/g, '');
                            return languageIndex == 0 ? firstPart : secondPart;
                    }
            function constructMCQHandler(questions, language){
                        let qCount = 0;
                        let questionHTML = "";
                        for(var q=0;q<questions.length;q++){
                        qCount++
                        questionHTML+= "<div class='queWrap'>"
                        questionHTML+= "<p class='ques'>Q"+qCount+". "+extractText(questions[q].question, parseInt(language))+"</p>"
                        questionHTML+= "<p class='optNo'>A. "+extractText(questions[q].option1, parseInt(language))+"</p>"
                        questionHTML+= "<p class='optNo'>B. "+extractText(questions[q].option2, parseInt(language))+"</p>"
                        questionHTML+= "<p class='optNo'>C. "+extractText(questions[q].option3, parseInt(language))+"</p>"
                        questionHTML+= "<p class='optNo'>D. "+extractText(questions[q].option4, parseInt(language))+"</p>"

                        if(questions[q].answer1 && questions[q].answer1 === "Yes"){
                        questionHTML+= "<p class='answerwrapItem'><span class='ansTxt'>Answer:</span> "+extractText(questions[q].option1, parseInt(language))+"</p>"
                    }else if(questions[q].answer2 && questions[q].answer2 === "Yes"){
                        questionHTML+= "<p class='answerwrapItem'>Answer: "+extractText(questions[q].option2, parseInt(language))+"</p>"
                    }else if(questions[q].answer3 && questions[q].answer3 === "Yes"){
                        questionHTML+= "<p class='answerwrapItem'>Answer: "+extractText(questions[q].option3, parseInt(language))+"</p>"
                    }else if(questions[q].answer4 && questions[q].answer4 === "Yes"){
                        questionHTML+= "<p class='answerwrapItem'>Answer: "+extractText(questions[q].option4, parseInt(language))+"</p>"
                    }

                        if(questions[q].difficultyLevel){
                        questionHTML+= "<p><span class='ansTxt'>Difficulty:</span> "+questions[q].difficultyLevel+"</p>";
                    }

                        if(questions[q].answerDescription){
                        questionHTML+= "<p class='expwrapItem'><span class='ansTxt'>Explanation:</span> "+extractText(questions[q].answerDescription, parseInt(language))+"</p>";
                    }

                        if((showsnapPdf!="true" || showsnapPdf==null) && showMcq=="true"){
                        questionHTML += constructActionButtons(q)
                    }
                        questionHTML+= "</div>"
                    }

                        const tempEl = document.createElement("div")
                        tempEl.innerHTML = questionHTML
                        renderMathInElement(tempEl, {
                        delimiters: [
                    { left: "\\(", right: "\\)", display: false },
                    { left: "\\[", right: "\\]", display: true }
                        ]
                    });

                        questionHTML = marked.parse(tempEl.innerHTML);
                        console.log("the html", questionHTML);
                        const mcqContentRenderer = document.getElementById("mcqContentRenderer");
                        if (mcqContentRenderer) {
                            mcqContentRenderer.innerHTML = questionHTML;
                        }
                        if((showsnapPdf!="true" || showsnapPdf==null) && showMcq=="true"){
                        attachEventListeners()
                        book_snaps.style.fontFamily = "font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;"
                    }
                        mcq_snap.style.display = 'block'
                    }

                        function constructQAHandler(questions, language){
                        let questionHTML = ""
                        let qCount = 0
                        for(var q=0;q<questions.length;q++){
                        qCount++
                        questionHTML+= "<div class='queWrap'>"
                        questionHTML+= "<p class='ques'>Q"+qCount+". "+extractText(questions[q].question, parseInt(language))+"</p>"
                        questionHTML+= "<p><span class='ansTxt'>Answer:</span> "+extractText(questions[q].answer, parseInt(language))+"</p>"
                        questionHTML+= "<p><span class='ansTxt'>Difficulty:</span> "+questions[q].difficultyLevel+"</p>"
                        questionHTML+= "</div>"
                    }
                        const tempEl = document.createElement("div")
                        tempEl.innerHTML = questionHTML
                        renderMathInElement(tempEl, {
                        delimiters: [
                    { left: "\\(", right: "\\)", display: false },
                    { left: "\\[", right: "\\]", display: true }
                        ]
                    });
                        questionHTML = marked.parse( tempEl.innerHTML);
                        const qnaContentRenderer = document.getElementById("qnaContentRenderer");
                        if (qnaContentRenderer) {
                            qnaContentRenderer.innerHTML = questionHTML;
                        }
                        qa_snap.style.display = 'block'
                    }
                        function constructSnapTitle(title, totalLength, languagesList, id){
                        let headerMCQHTML = '';
                        headerMCQHTML+= "<h4 class='snapTitle'>" + title+ "("+totalLength+")</h4>"+
                        "<div class='ts_opt_cont' id='ts_opt_"+id+"'>";
                        if(languagesList.length>1){
                        headerMCQHTML+="<select class='languagedropdown' id='"+id+"'></select>";
                    }
                        headerMCQHTML+= "</div>";
                        return headerMCQHTML
                    }

                        function constructActionButtons(ques_index){
                        var buttonGroup = '<div class="button-group">' +
                        '<button class="btn btn-primary giveHint" id="hint_'+ques_index+'" data-index="hint_'+ques_index+'">' +
                        '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">' +
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>' +
                        '</svg>' +
                        'Give Hint' +
                        '</button>' +

                        '<button class="btn btn-secondary explainMCQ" id="explain_'+ques_index+'" data-index="explain_'+ques_index+'">' +
                        '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">' +
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>' +
                        '</svg>' +
                        'Explain MCQ' +
                        '</button>' +

                        '<button class="btn btn-tertiary similarMCQ" id="similar_'+ques_index+'" data-index="similar_'+ques_index+'">' +
                        '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">' +
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>' +
                        '</svg>' +
                        'Create similar MCQs' +
                        '</button>' +
                        '<button class="btn btn-tertiary showHistory" id="history_'+ques_index+'" data-index="history_'+ques_index+'">'+
                        '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">'+
                        '<path strokelinecap="round" strokelinejoin="round" strokewidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>'+
                        '</svg>'+
                        'Show History'+
                        '</button>'+
                        '</div>';

                        return buttonGroup;
                    }

                        function attachEventListeners() {
                        document.querySelectorAll('.giveHint').forEach(function (button) {
                        button.addEventListener('click', function () {
                        var index = this.dataset.index;
                        const actionType = index.split("_")[0]
                        const actionId = index.split("_")[1]
                        const actionObj = data.mcqs[parseInt(actionId)]
                        showChatWindowCB(actionObj, actionType, actionId,selectedLanguageGlobal, languageVal)
                    });
                    });

                        document.querySelectorAll('.explainMCQ').forEach(function (button) {
                        button.addEventListener('click', function () {
                        var index = this.dataset.index;
                        const actionType = index.split("_")[0]
                        const actionId = index.split("_")[1]
                        const actionObj = data.mcqs[parseInt(actionId)]
                        showChatWindowCB(actionObj, actionType, actionId, selectedLanguageGlobal, languageVal)
                    });
                    });

                        document.querySelectorAll('.similarMCQ').forEach(function (button) {
                        button.addEventListener('click', function () {
                        var index = this.dataset.index;
                        const actionType = index.split("_")[0]
                        const actionId = index.split("_")[1]
                        const actionObj = data.mcqs[parseInt(actionId)]
                        showChatWindowCB(actionObj, actionType, actionId, selectedLanguageGlobal, languageVal)
                    });
                    });

                        document.querySelectorAll('.showHistory').forEach(function (button) {
                        button.addEventListener('click', function () {
                        var index = this.dataset.index;
                        const actionType = index.split("_")[0]
                        const actionId = index.split("_")[1]
                        const actionObj = data.mcqs[parseInt(actionId)]
                        showChatWindowCB(actionObj, actionType, actionId, selectedLanguageGlobal, languageVal)
                    });
                    });
                    }

                        if (toggleBtn) toggleBtn.addEventListener('click', toggleMCQs);
                        if (toggleQABtn) toggleQABtn.addEventListener('click', toggleQA);
                        if (toggleSnapBtn) toggleSnapBtn.addEventListener('click', toggleSnap);

            hideAppLoader();
            <%if(hideChat){%>

            console.log("hide chat called again");
            hideChatWindowCB();
            <%}%>

    });



    const groupByChapterId = (arr) => {
        return arr.reduce((acc, curr) => {
            if (!acc[curr.chapterId]) {
                acc[curr.chapterId] = {
                    chapterId: curr.chapterId,
                    chapterName: curr.chapterName,
                    resources: [],
                    previewChapter:curr.previewChapter,
                    sortOrder:curr.sortOrder
                };
            }
            acc[curr.chapterId].resources.push({
                bookId: curr.bookId,
                link: curr.link,
                name: curr.name,
                resId: curr.resId,
                resType: curr.resType,
                vectorStored: curr.vectorStored
            });
            return acc;
        }, {});
    };
    const groupedData = groupByChapterId(chaptersList);
    updateChapterDropDown(groupedData)
</script>
