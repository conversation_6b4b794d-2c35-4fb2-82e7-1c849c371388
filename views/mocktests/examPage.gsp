<!DOCTYPE html>
<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session["entryController"]}/navheader_new"></g:render>
<asset:stylesheet href="mocktests/mocktests.css" async="true" media="all"/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@5/dark.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" referrerpolicy="no-referrer" async/>
<%if("true".equals(session["prepjoySite"])){%>
<style>
body{
    background: #04001D !important;
}
</style>
<%}%>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>
<script>
    var level="${dailyTestsMst.level}",syllabus="${dailyTestsMst.syllabus}",grade="${dailyTestsMst.grade}",subject="${dailyTestsMst.subject}";
</script>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<main class="examPage">
    <div class="ws_container">
        <section style="margin-bottom: 2rem;">
            <div class="mockBanner">
                <div class="mockBannerWrap">
                    <p class="testBannerTitle">Best Mock Tests for ${title}</p>
                    <p class="testBannerSub">Get Access to Free ${title} Mock Tests</p>
                </div>
            </div>
        </section>
        <section class="mockTestsPageTitle">
            <h1 class="mockTest__title">Best Mock Tests for ${title}</h1>
        </section>
        <%if(selectedDate!=null){%>
        <section class="examActionItem">
            <div class="currentMonthItem" id="card-1" data-id="1">
                <div class="card-title">
                    <p style="font-size: 17px;">${title}</p>
                </div>
                <div class="card-quizInfo">
                    <div class="quizInfo">
                        <p><i class="fa-solid fa-circle-question"></i> Questions : </p>
                        <p id="qNos">00</p>
                    </div>
                    <div class="quizInfo">
                        <p><i class="fa-solid fa-stopwatch"></i> Time : </p>
                        <p id="qTime">00 mins</p>
                    </div>
                </div>
                <div class="card-options">
                    <button onclick="javascript:openQuiz('${selectedDate}','${params.dailyTestId}','','false',false);">
                        <img src='${assetPath(src: 'prepJoy/prepjoy-website/play_ws.svg')}' style='width:20px !important' alt="play quiz">Play
                    </button>
                    <button onclick="javascript:openQuiz('${selectedDate}','${params.dailyTestId}','practice','false',false);">
                        <img src='${assetPath(src: 'prepJoy/prepjoy-website/practice_ws.svg')}' style='width:18px !important' alt="practice quiz">Practice
                    </button>
                    <button onclick="javascript:openQuiz('${selectedDate}','${params.dailyTestId}','testSeries','false',false);">
                        <img src='${assetPath(src: 'prepJoy/prepjoy-website/test_ws-4.svg')}' style='width:18px !important' alt="test quiz">
                        Test
                    </button>
                    <button onclick="javascript:openQuiz('${selectedDate}','${params.dailyTestId}','flashCard','false',false);" id="flscard">
                        <img src='${assetPath(src: 'prepJoy/prepjoy-website/learn_ws.svg')}' style='width:20px !important' alt="add flashcard">
                        Flashcard
                    </button>
                    <button onclick="javascript:openQuiz('${selectedDate}','${params.dailyTestId}','practice','true',false);">
                        <img src='${assetPath(src: 'prepJoy/prepjoy-website/learn_ws.svg')}' style='width:20px !important' alt="study quiz">
                        Study
                    </button>
                    <button onclick="javascript:openQuiz('${selectedDate}','${params.dailyTestId}','practice','false',true);">
                        <img src='${assetPath(src: 'prepJoy/prepjoy-website/history_ws.svg')}' style='width:20px !important' alt="quiz history">
                        History
                    </button>
                </div>
            </div>
        </section>
        <%}%>
        <section class="mockTests__currentMonth">
            <h2 class="mockTests__currentMonth-title" id="currentMonthTitle"></h2>
            <div class="" id="mockTestCurrentMonthItems">

            </div>
        </section>
        <section id="mockTestPreviousMonthsListItems" class="mockTestPreviousMonthsListItems">

        </section>



    </div>
</main>

<g:render template="/${session["entryController"]}/footer_new"></g:render>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>
<script>
    var latestDate = "${latestDate}".split('-');
    var startingDate = "${startingDate}".split('-');
    var testName = "${testName}";
    var latestYear = latestDate[0];
    var latestMonth = latestDate[1];
    var latestDay = latestDate[2];
    var displayYear = latestDate[0];
    var displayMonth = latestDate[1];
    var displayDay = latestDate[2];
    var startingYear = startingDate[0];
    var startingMonth = startingDate[1];
    var startingDay = startingDate[2];
    var daySelected = false;
    var selectedDay;
    let pageNo = 0;
    let publisherId=null;
    let subscriptionBooks = false;
    var endMonth,startMonth;
    var previousMonths="";
    var day="";
    let mockTestCurrentMonthHTML = "";
    let mockTestPreviousMonthHTML = "";
    var currentDate = new Date();

    // Get the current month (0-11)
    var currentMonth = currentDate.getMonth() + 1; // Adding 1 because getMonth() returns zero-based index

    // Get the current year
    var currentYear = currentDate.getFullYear();
    // Create an array of month names
    const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];

    const currentMonthTitle = document.getElementById('currentMonthTitle');
    const mockTestCurrentMonthItems = document.getElementById('mockTestCurrentMonthItems');
    const mockTestPreviousMonthsListItems = document.getElementById('mockTestPreviousMonthsListItems');
    var i=0;
    var stopDay=0;

    <%if(!"all".equals(params.dateRange)){%>
      var incomingMonthYear = "${params.dateRange}".split('-');
      displayMonth = incomingMonthYear[1];
      displayYear = incomingMonthYear[0];
      displayDay = getNumberOfDaysInMonth(displayYear,displayMonth);
      parseInt(displayMonth)==parseInt(startingMonth)&&parseInt(displayYear)==parseInt(startingYear) ? stopDay=(startingDay-1) : stopDay=0;
     if((parseInt(currentMonth)==parseInt(latestMonth)&&parseInt(currentYear)==parseInt(startingYear))||(parseInt(displayMonth)==parseInt(latestMonth)&&parseInt(displayYear)==parseInt(latestYear))) {
        i=latestDay
      } else {
        i=getNumberOfDaysInMonth(displayYear,displayMonth);
      }

      if(incomingMonthYear.length==3){
          daySelected = true;
          selectedDay = incomingMonthYear[2];
      }
    <%}else {%>
    displayMonth = latestMonth;
    displayYear = latestYear;
    displayDay = getNumberOfDaysInMonth(displayYear,displayMonth);
    parseInt(displayMonth)==parseInt(startingMonth)&&parseInt(displayYear)==parseInt(startingYear) ? stopDay=(startingDay-1) : stopDay=0;
    if((parseInt(currentMonth)==parseInt(latestMonth)&&parseInt(currentYear)==parseInt(latestYear))||(parseInt(displayMonth)==parseInt(latestMonth)&&parseInt(displayYear)==parseInt(latestYear))) {
        i=latestDay
    } else {
        i=getNumberOfDaysInMonth(displayYear,displayMonth);
    }

    <%}%>
    currentMonthTitle.innerHTML = getMonthName(displayMonth-1) + " " +displayYear+ " MockTests";

    for(;i>stopDay;i--){
        day=i;
        if(i<10) day="0"+day;
        let title="";
        <%if(selectedDate!=null){%>
            title = '${examTitle}'+' : '+ i +" "+ getMonthName(displayMonth-1) + " "+displayYear
        <%} else {%>
            if('${params}'.includes('all')){
                title = '${title}'+' : '+ i +" "+ getMonthName(displayMonth-1)+" "+displayYear;
            }else{
                title = '${title}'.split('-')[0]+' : '+ i +" "+ getMonthName(displayMonth-1)+" "+displayYear;
            }
        <%}%>
        mockTestCurrentMonthHTML +=
            "<div class='currentMonthItem currentMonthItemBox' data-disYear='"+displayYear+"' data-latMonth='"+latestMonth+"' data-day='"+i+"'>"+
                "<div class='currentMonthItemWrapper' style='padding: 12px;'>"+
                    "<div class='test__nameWrapper'>"+
                        "<h3>"+title+"</h3>"+
                        "<div class='freeTag'><p>Free</p></div>"+
                    "</div>"+
                    "<button class='openTestBtn'>Open Test</button>"+
                "</div>"+
            "</div>";
    }
    mockTestCurrentMonthItems.innerHTML = mockTestCurrentMonthHTML

    for(var i = latestYear;i>=startingYear;i--){
        startMonth=1;
        endMonth=12;
        if(i==latestYear) endMonth = latestMonth;
        if(i==startingYear) {
            startMonth = startingMonth;
        }
        previousMonths +="<h2>"+i+"</h2>";
        mockTestPreviousMonthHTML += "<div class='previousMonthsList-item'>"+
                                        "<h4 class='mockTests__currentMonth-title' id='previousMonthTitle'>"+i+"</h4>" +
            "<div class='mockTest__examCards' id='mockTestPreviousMonthItems'>";
        for(var j=startMonth;j<=endMonth;j++){
            const borderColor = getRandomHexColor();
            mockTestPreviousMonthHTML +=
                "<div class='mockTest__examCards-card examCard previousMonthsItem' data-latestYear='"+i+"' data-startMonth='"+j+"'  style='border-left-color: "+borderColor+"'>"+
                    "<div class='examCard__examName'>"+
                        "<h5>"+getMonthName(j-1)+"</h5>"+
                    "</div>"+
                    "<i class='fa-solid fa-chevron-right examCard__arrow'></i>"+
                "</div>";
        }
        mockTestPreviousMonthHTML +="</div>" +
            "</div>";
    }
    mockTestPreviousMonthsListItems.innerHTML = mockTestPreviousMonthHTML;

    function getRandomHexColor() {
        return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
    }
    // Function to retrieve month name from index
    function getMonthName(index) {
        // Check if the index is within the valid range
        if (index >= 0 && index < months.length) {
            return months[index];
        } else {
            return 'Invalid index';
        }
    }

    function goToExam(monthyear){
        var examGroup = "${dailyTestsMst.examGroup}";
        var examId="${params.dailyTestId}";
        var link = "/"+examGroup.toLowerCase().replaceAll(' ','-')+"/mocktests/"+monthyear+"/"+examId;
        window.open(link, '_blank');
    }
    const currentMonthItem = document.querySelectorAll('.currentMonthItemBox');
    const previousMonthsItem = document.querySelectorAll('.previousMonthsItem');
    currentMonthItem.forEach(item=>{
        item.addEventListener('click',()=>{
            const disYear = item.getAttribute('data-disYear');
            const latMonth = item.getAttribute('data-latMonth');
            const day = item.getAttribute('data-day');
            const date = disYear + '-'+latMonth+'-'+day;
            goToExam(date)
        })
    })
    previousMonthsItem.forEach(item=>{
        item.addEventListener('click',()=>{
            const disYear = item.getAttribute('data-latestYear');
            const latMonth = item.getAttribute('data-startMonth');
            const date = disYear + '-'+latMonth;
            goToExam(date)
        })
    })

    function getNumberOfDaysInMonth(year, month) {
        // The month parameter in JavaScript is zero-based (0 for January, 11 for December)
        // So, we subtract 1 from the provided month to get the correct month value for the Date object.
        const date = new Date(year, month - 1, 1);

        // Move to the next month and subtract 1 day to get the last day of the provided month
        date.setMonth(date.getMonth() + 1);
        date.setDate(date.getDate() - 1);

        // The getDate() method now returns the number of days in the month
        const numberOfDays = date.getDate();

        return numberOfDays;
    }

</script>

<%if(selectedDate!=null){%>
//daily test scripts
<script>
    var quizDate;
    var quizResId;
    var qType;
    var lrnVal;
    var histry;

    //API 4: TO GET DAILY TEST QUESTIONS
    function openQuiz(d,i,type,lrn,history){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        quizDate = d;
        quizResId = i;
        qType=type;
        lrnVal = lrn;
        histry = history;
        $.ajax({
            url:'/prepjoy/getDailyTests?dailyTestId='+quizResId+'&dateInput='+quizDate+'&siteId='+siteId,
            type:'GET' ,
            success: function(data) {
                validateQuestions(data)
            },
            error: function(data){
                validateQuestions(data)
            },
        });
    }

    <%if(selectedDate!=null){%>
        function checkQuiz(){
            $.ajax({
                url:'/prepjoy/getDailyTests?dailyTestId='+'${params.dailyTestId}'+'&dateInput='+'${selectedDate}'+'&siteId='+siteId,
                type:'GET' ,
                success: function(data) {
                    updateQuizDetails(data)
                },
                error: function(data){
                    validateQuestions(data)
                },
            });
        }
        function updateQuizDetails(data){
            const quizDetails = JSON.parse(data.results)
            const qNos = document.getElementById('qNos');
            const qTime = document.getElementById('qTime');
            const defaultMinPerQuestion = 45; //in secs
            const totalQuestions = quizDetails.length;
            let totalSecs = totalQuestions*defaultMinPerQuestion/60;
            qNos.innerHTML = totalQuestions;
            qTime.innerHTML = totalSecs+" mins";
        }
        checkQuiz();
    <%}%>
    function openHistory(dailyTestId){
        window.location.href = '/prepjoy/history?siteId='+"${session['siteId']}"+'&resId='+dailyTestId+'&quizType=dailyTests';
    }
    //FUNCTION TO REDIRECT TO QUIZ
    function validateQuestions(data){
        if (data.status=='OK'){
            if (lrnVal == 'false' && !histry && qType!='flashCard'){
                var url = "/prepjoy/prepJoyGame?dateInput="+quizDate+"&dailyTestId="+quizResId+"&quizType="+qType+"&learn=false&pubDesk=false&dailyTest=true";
                setTimeout(() => {
                    window.open(url, '_blank');
                })
            }else if(lrnVal == 'false' && histry && qType!='flashCard'){
                var url = '/prepjoy/history?siteId='+"${session['siteId']}"+'&resId='+data.realDailyTestDtlId+'&quizType=dailyTests';
                setTimeout(() => {
                    window.open(url, '_blank');
                })
            }else if (lrnVal=='false' && !histry && qType=='flashCard'){
                var url = "/resources/displayFlashCards?dateInput="+quizDate+"&dailyTestId="+quizResId+"&fromQuiz=true";
                setTimeout(() => {
                    window.open(url, '_blank');
                })
            }else{
                var url = "/funlearn/quiz?dateInput="+quizDate+"&dailyTestId="+quizResId+"&quizMode=learn&pubDesk=false&dailyTest=true";
                setTimeout(() => {
                    window.open(url, '_blank');
                })
            }
            if (!prepjoySite){
                $('.loading-icon').addClass('hidden');
            }else{
                $('#loading').hide();
            }
        }else {
            if (!prepjoySite){
                $('.loading-icon').addClass('hidden');
            }else{
                $('#loading').hide();
            }
            Swal.fire({
                title: "Quiz Not Available",
                icon: "error",
                button: "OK",
            });
        }
    }

    function openFlashCard(date,id){
        window.location.href="/resources/displayFlashCards?dateInput="+date+"&dailyTestId="+id+"&fromQuiz=true";
    }

</script>
<%}%>
