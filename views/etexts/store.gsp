<%@ page import  = 'javax.servlet.http.Cookie' %>
<g:render template="/etexts/navheader_new"/>
<style>
.modal.fade .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(.5);transform: translate(0,0)scale(.5);}
.modal.show .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(1);transform: translate(0,0)scale(1);}
.signupDummy,#agree{
    width: 100%;
    background: #e18b1e;
    color: #ffffff;
}
</style>
<section class="evidyaStore mt-5">
    <div class="loading-icon">
        <div class="loader-wrapper">
            <div class="loader">Loading</div>
        </div>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-sm-12 col-lg-4 d-block d-lg-block">
                <div class="show_filters_btn text-center">
                    <button class="btn btn-primary col-sm-4" type="button" data-toggle="collapse" data-target="#showFilters" aria-expanded="false" aria-controls="showFilters">
                        SHOW FILTERS
                    </button>
                </div>
                <div id="showFilters" class="Sidewrapper collapse">
                    <input type="text" class="search" placeholder="Search(title,subject,author)" id="search-book" autocomplete="off" value="${params.searchString}">
                    <h4 class="mt-4 ml-1">- Filter by Language</h4>
                    <div class="card">
                        <ul class="mt-2" id="langaugeList">
                            <li><a href="javascript:displayBooksDisp('language','English')">English</a></li>
                            <li><a href="javascript:displayBooksDisp('language','Hindi')">Hindi</a></li>
                            <li><a href="javascript:displayBooksDisp('language','Marathi')">Marathi</a></li>
                        </ul>
                    </div>
                    <h4 class="mt-4 ml-1">- Filter by Discipline</h4>
                    <ul class="card subCategory">
                        <li class="main-list mt-2">
                            <a class="categories" id="humanitiestoggle"><span>Humanities and Social Science</span> <i class="material-icons add">add</i><i class="material-icons minimize">remove</i> </a>
                            <ul class="sub-menu" id="Humanities">
                            </ul>
                        </li>
                        <li class="main-list">
                            <a class="categories" id="managementtoggle"><span>Management</span> <i class="material-icons add">add</i><i class="material-icons minimize">remove</i></a>
                            <ul class="sub-menu" id="Management">
                            </ul>
                        </li>
                        <li class="main-list">
                            <a class="categories" id="upsctoggle"><span>UPSC Resources</span> <i class="material-icons add">add</i><i class="material-icons minimize">remove</i></a>
                            <ul class="sub-menu" id="UPSC">
                            </ul>
                        </li>

                    </ul>

                </div>
            </div>
            <div class="col-sm-12 col-lg-8" >
                <div class="d-flex align-items-center result-wrapper" id="displayResults">
                </div>
                <div class="d-flex align-items-center justify-content-between mt-4 showResult">
                    <div>
                        <p class="itemLabel" id="topPaginationMessage"></p>
                    </div>
                    <div>

                        <select id="sortBy" name="sortBy" onchange="sortDisplay();">
                            <option selected>Sort by</option>
                            <option value="title">Title(A-Z)</option>
                            <option value="title">Title(Z-A)</option>
                            <option value="author">Author(A-Z)</option>
                            <option value="author">Author(Z-A)</option>
                        </select>
                    </div>
                </div>
                <p id="searcherrormsg" style="display: none"></p>
                <div class="mt-4 bg-gray" id="booksDisplayList">

                </div>
                <div class="row ml-0 mr-0 align-items-center mt-4 bg-gray border-pagination" id="bottomPagination" style="display: none">
                    <div class="col-6">
                        <ul class="pagination" id="paginationList"></ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section>
    <div class="modal fade" id="popupLogin">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <p>We're so glad you liked this title! <a class="cmn-login loginnow" onclick="javascript:evidyaloginOpen();">Login Now</a> to add this book to your library or create a customized wishlist.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal -->
<div class="modal fade" id="evidyaTCModal" data-keyboard="false" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="evidyaTCModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-zoom" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-center" id="evidyaTCModalLabel">Terms and conditions</h5>
            </div>
            <div class="modal-body">
                <p class="">Our T&C has been revised, please <a href="/etexts/termsCondition" style="font-size: 16px" target="_blank">click</a> to read and accept to enjoy seamless services in your account</p>
                <div class="">
                    <div class="mt-3 d-flex align-items-center">
                        <input class="mr-2" type="checkbox" id="termsConditionCheck" name="termsConditionCheck" required>
                        <label for="termsConditionCheck" class="mb-0">I have read and agree to the  <a class="mt-2" href="/etexts/termsCondition" target="_blank">Terms of Service</a></label>
                    </div>
                    <div id="agreeButtonWrapper"></div>
                    <input type="button" id="agreeDummy" class="btn btn-lg continue mt-3 signupDummy" value="Continue" disabled>
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/etexts/footer_new"/>
<script>
    $("#humanitiestoggle").click(function(){
        $('#Humanities').toggle();
        $(this).toggleClass('minus');
    });
    $("#managementtoggle").click(function(){
        $('#Management').toggle();
        $(this).toggleClass('minus');
    });
    $("#upsctoggle").click(function(){
        $('#UPSC').toggle();
        $(this).toggleClass('minus');
    });

    var searchMode=false;
    var books ;
    var booksTags;
    var indexSubject="${params.subject}";
    var homepage="${params.homepage}";
    var indexGrade="${params.grade}";
    var indexLanguage="${params.language}";
    var sortBy="title";
    var previousMode="";
    var previousModeValue="";
    var bkRetry = 1;
    var callingFirstTime=true;
    var booksserch=[];
    function getBookCategories(){
        <g:remoteFunction controller="etexts" action="getBookCategories"  onSuccess='intitializeCategories(data);'  params="'categories=level&level=College&apiMode=optimized'"/>
    }

    function sortDisplay(){
        if(document.getElementById("sortBy").selectedIndex==1) {
            books.sort(SortByTitle);
        }else if(document.getElementById("sortBy").selectedIndex==2) {
            books.sort(SortByTitle);
            books.reverse(SortByTitle);
        } else if(document.getElementById("sortBy").selectedIndex==3) {
            books.sort(SortByAuthor);
        }else if(document.getElementById("sortBy").selectedIndex==4) {
            books.sort(SortByAuthor);
            books.reverse(SortByAuthor);
        }

        displayBooks(previousMode,previousModeValue);
    }

    function intitializeCategories(data){
        if((data==null || data.bookCategories==null || data.results==null || data.length==0) && bkRetry==1) {
            bkRetry++;
            getBookCategories();
            return;
        }

        if(data.bookCategories!=null&&"true"==data.bookCategories) bookCategories=true;

        if(data.status!="Nothing present"){
            var data1 = JSON.parse(data.results);

            var formattedTopicMapIndex = formatDataIndex(JSON.parse(data.results));
            var disciplines = formattedTopicMapIndex.subjects["CollegeSAGEHumanitiesandSocialScience"];
            var disciplineStr="";
            for(j=0;j<disciplines.length;j++){
                disciplineStr+="<li>\n" +
                    "    <a href=\"javascript:displayBooksDisp('subject','"+disciplines[j]+"','Humanities and Social Science')\">"+disciplines[j]+"</a>\n" +
                    "    </li>";
            }
            document.getElementById("Humanities").innerHTML=disciplineStr;

            disciplines = formattedTopicMapIndex.subjects["CollegeSAGEManagement"];
            disciplineStr="";
            for(j=0;j<disciplines.length;j++){
                disciplineStr+="<li>\n" +
                    "    <a href=\"javascript:displayBooksDisp('subject','"+disciplines[j]+"','Management')\">"+disciplines[j]+"</a>\n" +
                    "    </li>";
            }
            document.getElementById("Management").innerHTML=disciplineStr;

            disciplines = formattedTopicMapIndex.subjects["CollegeSAGEUPSCResources"];
            disciplineStr="";
            for(j=0;j<disciplines.length;j++){
                disciplineStr+="<li>\n" +
                    "    <a href=\"javascript:displayBooksDisp('subject','"+disciplines[j]+"','UPSC Resources')\">"+disciplines[j]+"</a>\n" +
                    "    </li>";
            }
            document.getElementById("UPSC").innerHTML=disciplineStr;

            myLibraryMode=false;
            instituteLibraryCalled=false;
            if(homepage!="filter") {
                storeBooksReceived(data);
            }
            getBooksList();

        }
    }

    function getBooksList() {
        $('.loading-icon').removeClass('hidden');
        myLibraryMode=false;
        instituteLibraryCalled=false;
        <g:remoteFunction controller="wonderpublish" action="getBooksListEvidya"  onSuccess='storeBooksReceived(data);'
               params="'categories=level&level=College'" />
    }

    var blRetry = 1;
    function storeBooksReceived(data){
        if((data==null || data.length==0) && blRetry==1) {
            blRetry++;
            <g:remoteFunction controller="wonderpublish" action="getBooksListEvidya"  onSuccess='storeBooksReceived(data);'
                    params="'categories=level&level=College'" />
            return;
        }
        storeBooksData = data;
        <g:remoteFunction controller="institute" action="getLibraryBookIds"  onSuccess='libraryBooksReceived(data);' />
    }

    var lbRetry = 1;
    function libraryBooksReceived(data){
        if((data==null || data.length==0) && lbRetry==1) {
            lbRetry++;
            <g:remoteFunction controller="institute" action="getLibraryBookIds"  onSuccess='libraryBooksReceived(data);' />
            return;
        }

        if(data.status=="OK") {
            libraryBooksData = data;
            libraryBooksList = data.libraryBookIds;
        }
        if(searchMode) submitSearch();
        else booksListReceived1(storeBooksData);
    }

    function SortByTitle(x,y) {
        return ((x.title == y.title) ? 0 : ((x.title > y.title) ? 1 : -1 ));
    }

    function SortByAuthor(x,y) {
        return ((x.authors == y.authors) ? 0 : ((x.authors > y.authors) ? 1 : -1 ));
    }
</script>
<asset:javascript src="searchContents.js"/>
<g:render template="/wonderpublish/buyOrAdd"/>
<g:render template="/etexts/storeHolder"/>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<script>
    $('#search-book').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks2(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch();
        }
    });

    $(document).on("keypress", "#search-book", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                if(document.getElementById("search-book").value == '' || document.getElementById("search-book").value == undefined || document.getElementById("search-book").value == null) location.reload();
                else submitSearch();
            }
        }
    });

    function submitSearch(){
        $('.loading-icon').removeClass('hidden');
        var searchString =encodeURIComponent(document.getElementById("search-book").value);
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="discover" action="search"  onSuccess='searchResults(data);'
                params="'searchString='+searchString" />
    }

    // function searchResults(data){
    //
    //     if(data.search) searchMode = true;
    //     books = data.books;
    //     booksTags = data.booksTag;
    //     displayBooks("all","");
    // }


    function searchResults(data) {
        if (data.status != "Nothing present") {
            $('.loading-icon').addClass('hidden');
            $("#booksDisplayList").show();
            if (data.search) searchMode = true;
            books = data.books;
            booksTags = data.booksTag;
            displayBooks("all", "");
        } else {
            if (elementExists("searcherrormsg")) {
                $('.loading-icon').addClass('hidden');
                document.getElementById("searcherrormsg").innerText = "No Results Found";
                $("#searcherrormsg").show();
                $("#booksDisplayList").hide();
                $("#paginationList").hide();
                $("#topPaginationMessage").hide();

            }


        }
    }
    <%  if("true"==params.search){%>
    searchMode=true;
    getBooksList();
    <%  }  %>



    // $(document).ready(function() {
    //     searchMode=false;
    //     alert("Hello, world!");
    //     console.log("inininininiininiready");
    //     getBooksList();
    //
    //
    // });


    if('${params.suggestBookId}'){
        var suggestBookId = '${params.suggestBookId}'
        <g:remoteFunction controller="wonderpublish" action="getBookById"  onSuccess='searchResults(data)'
                params="'id='+suggestBookId" />
    }else{
        getBookCategories();
    }

    $(window).on('load', function() {

        <%if(session['userdetails']!=null&&session['userdetails'].username!=null && session['userdetails'].termsCondition!="true"){%>
        $('#evidyaTCModal').modal('show');
        $('#evidyaTCModal').modal({backdrop: 'static', keyboard: false});
        <%} else { %>
        //do nothing..
        <%}%>
    });
    var termsChecked
    $('#termsConditionCheck').on('change', function() {
        termsChecked = this.checked
        if (termsChecked){
            $("#agreeButtonWrapper").html("<input type='button' id='agree' onclick='acceptTerms()' class='btn btn-lg continue mt-3' value='Continue'>")
            $("#agreeDummy").hide();
        }else{
            $("#agreeButtonWrapper").html("");
            $("#agreeDummy").show();
        }
    });

    function acceptTerms(){
        <g:remoteFunction controller="log" action="storeSageTermsDtl"  onSuccess='closeTermsModal(data)'
                params="'termsCondition='+termsChecked" />
    }

    function closeTermsModal(data){
        if(data.status == "OK"){
            $('#evidyaTCModal').modal('hide');
        }
    }

</script>
