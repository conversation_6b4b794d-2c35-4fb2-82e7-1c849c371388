<asset:javascript src="generic.js"/>
<%String syllabusType;
if(request.getRequestURL().toString().indexOf("success")>-1) syllabusType="success";
else syllabusType="school";
String country=session.getAttribute("country")==null?"India":session.getAttribute("country");%>
<div class="modal fade" data-backdrop="static" data-keyboard="false" id="addTopic" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <div class="row">
                <button type="button" class="close" data-dismiss="modal"  aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <g:form name="topicForm" url="[action:'topiccreation',controller:'creation']"  method="post">

                    <b>&nbsp;&nbsp;Add Chapter/Topic name:</b><br><br>
                    <div class="form-group col-sm-2 col-xs-12"><g:select id="addlevel" class="form-control" name="syllabusType" from="${com.wonderslate.data.LevelsMst.listOrderBySortBy()}" optionKey="name" optionValue="name"
                                                                         noSelection="['':'Select']" onchange="javascript:getSubjectsList(this.value)"/></div>

                    <div class="form-group col-sm-2 col-xs-12"><select id="addsyllabus" class="form-control" name="syllabus" onchange="javascript:syllabusChanged(this.value)">
                        <option value="">Choose Syllabus</option>
                    </select></div>

                    <div class="form-group col-sm-2 col-xs-12"><select id="addgrade" class="form-control" name="grade" onchange="javascript:gradeChanged(this.value)"><option value="">Choose Exam</option></select></div>

                    <div class="form-group col-sm-2 col-xs-12"><select id="addsubject" class="form-control" name="subject"><option value="">Choose Subject</option></select></div>
                    <div class="form-group col-sm-4 col-xs-12"><g:textField id="topic" class="form-control" name="topic" placeholder="Chapter/Topic Name"/></div>


                    <br><br>
                    <div class="alert alert-warning col-sm-12 text-left" style="display: none;">
                        ** Please complete required fields marked in red.
                    </div>

                    <div class="form-group">
                        <div class=" col-sm-12 text-center">
                            <button type="button" onclick="javascript:submitTopic()" class="btn btn-primary">Add</button>
                        </div>
                    </div>


                    <input type="hidden" name="country" value="${country}">
                </g:form>
                </div>

            </div>
            <div class="row smallerText text-center">
                ** If you do not find the Syllabus / Subject you want to add, please write to us at <a href="mailto:<EMAIL>"><EMAIL></a>. We will add them soon.<br>
            </div><br><br>
        </div>
    </div>
</div>
<script>
    var formattedSubjectsMap = {};
    function addTopic(){
        $("#addTopic").modal("show");
        var level = getCookie('level');
        if(level.indexOf('"')!=-1) level = level.substr(1,(level.length-2));
        getSubjectsList(level);
        setSelectedValue(document.getElementById('addlevel'), level);

    }

    function submitTopic() {

        var flds = new Array ('addlevel','addsyllabus','addgrade','addsubject','topic');

        if (genericValidate(flds)) {
            <g:remoteFunction controller="creation" action="checkTopicNameExists" onSuccess = 'checkNameExists(data);'
            params="'syllabusType='+document.topicForm.syllabusType[document.topicForm.syllabusType.selectedIndex].value+'&subject='+document.topicForm.subject[document.topicForm.subject.selectedIndex].value+'&syllabus='+document.topicForm.syllabus[document.topicForm.syllabus.selectedIndex].value+'&grade='+document.topicForm.grade[document.topicForm.grade.selectedIndex].value+'&topic='+document.topicForm.topic.value"/>
        }
    }


    function checkNameExists(a){
        "0"==a?document.topicForm.submit():window.open("${request.contextPath}/funlearn/topic?topicId="+a,"_self");
    }

    function getSubjectsList(level){

        <g:remoteFunction controller="funlearn" action="getSyllabusGradeSubject"  onSuccess='initializeSyllabusGradeSubject(data);'
        params="'syllabusType='+level"/>
    }

    function initializeSyllabusGradeSubject(data1){

        var boards = formattedSubjectsMap.boards = [],
                grades = formattedSubjectsMap.grades = {},
                subjects = formattedSubjectsMap.subjects = {};
        var level,board, grade, subject,subjectkey,boardkey,gradekey;
        if("School"==data1.syllabusType){
            var tempsubjects = data1.subjects;
            var subjectLength = tempsubjects.length;
            var tempgrades = data1.grades;
            var gradesLength = tempgrades.length;
            var tempboards = data1.boards;
            var boardsLength = tempboards.length;


            for(var i=0; i < boardsLength; i++){
                board = tempboards[i].board;
                boardkey = board.replace(/\W+/g, '');//removing the space in between
                for(var j=0;j<gradesLength; j++){
                    grade = tempgrades[j].grade;
                    gradekey = grade.replace(/\W+/g, '');//removing the space in between
                    for(var k=0;k<subjectLength;k++){
                        subject = tempsubjects[k].subject;
                        subjectkey = subject.replace(/\W+/g, ''); //removing the space in between
                        if (boards.indexOf(board) < 0) //Compatible IE 9 or later
                            boards.push(board);

                        if (!grades[boardkey])
                            grades[boardkey] = [];
                        if (grades[boardkey].indexOf(grade) < 0)
                            grades[boardkey].push(grade);

                        if (!subjects[boardkey + gradekey])
                            subjects[boardkey + gradekey] = [];
                        if (subjects[boardkey + gradekey].indexOf(subject) < 0)
                            subjects[boardkey + gradekey].push(subject);

                    }
                }
            }
        }
        else {
            var data = data1.results;
            var length = data.length;

            for (var i = 0; i < length; ++i) {
                board = data[i].syllabus;
                boardkey = board.replace(/\W+/g, '');//removing the space in between
                grade = data[i].grade;
                gradekey = grade.replace(/\W+/g, '');//removing the space in between
                subject = data[i].subject;
                subjectkey = subject.replace(/\W+/g, ''); //removing the space in between

                if (boards.indexOf(board) < 0) //Compatible IE 9 or later
                    boards.push(board);

                if (!grades[boardkey])
                    grades[boardkey] = [];
                if (grades[boardkey].indexOf(grade) < 0)
                    grades[boardkey].push(grade);

                if (!subjects[boardkey + gradekey])
                    subjects[boardkey + gradekey] = [];
                if (subjects[boardkey + gradekey].indexOf(subject) < 0)
                    subjects[boardkey + gradekey].push(subject);
            }
        }
        populateDropDowns('addsyllabus', formattedSubjectsMap.boards);
        board = getCookie('syllabus');
        grade = getCookie('grade');
        level = getCookie('level');

        if (board) {
            setSelectedValue(document.getElementById('addsyllabus'), board);
            syllabusChanged(board);
            if (grade) {
                setSelectedValue(document.getElementById('addgrade'), grade);
                gradeChanged(grade);

            }
        }

    }

    function populateDropDowns(fieldName,options){

        var select = document.getElementById(fieldName);
        select.options.length = 1;
        // if("selectedSubject"==fieldName) select.options.length = 2;

        for(var i=0; typeof options!='undefined' && i<options.length; i++) {
            var opt = options[i];
            var el = document.createElement("option");
            el.textContent = opt;
            el.value = opt;
            select.appendChild(el);
        }

        select.focus();

    }

    function syllabusChanged(value){
        if(value!="") {
            var syllabuskey = value.replace(/\W+/g, '');
            populateDropDowns('addgrade', formattedSubjectsMap.grades[syllabuskey]);
        }
        else {
            select = document.getElementById("addgrade");
            select.options.length = 1;
            select = document.getElementById("addsubject");
            select.options.length = 1;
        }
    }

    function gradeChanged(value){
        if(value!="") {
            var syllabus = document.getElementById('addsyllabus');
            var syllabuskey = syllabus.options[syllabus.selectedIndex].value.replace(/\W+/g, '');
            var gradekey = value.replace(/\W+/g, '');
            populateDropDowns('addsubject',formattedSubjectsMap.subjects[syllabuskey+gradekey]);
        } else {
            select = document.getElementById("addsubject");
            select.options.length = 1;
        }
    }




</script>