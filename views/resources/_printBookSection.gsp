<link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />

<%if("1".equals(""+session["siteId"])||"true".equals(session['prepjoySite'])){%>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<%}%>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.4/lottie.min.js">

<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>
<style>
.new-categoryList {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 1rem;
    margin-top: 2rem;
}
@media (max-width: 768px) {
    .new-categoryList {
        grid-template-columns: repeat(1, 1fr);
    }
}
.new-categoryList_item-link {
    position: relative;
}
.new-categoryList_item-link img {
    width: 100%;
}
.new-categoryList_item-link button {
    position: absolute;
    top: 35px;
    right: 20px;
    background: transparent;
    border: 1px solid rgba(0, 0, 0, 0.6);
    width: 125px;
    height: 32px;
    color: rgba(0, 0, 0, 0.6);
}
h2{
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 1rem;
}
</style>
<style>

.modal.fade .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(.5);transform: translate(0,0)scale(.5);}
.modal.show .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(1);transform: translate(0,0)scale(1);}

.ordText{
    border-bottom: 1px solid #999;
    padding-bottom: 10px;
}
#addbk,#freeBk{
    position: relative;
    margin: 0 auto;
    top: 0;
}
#addBookToLibContent,#purchasePepjoyBook{
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='450' height='390' preserveAspectRatio='none' viewBox='0 0 450 390'%3e%3cg clip-path='url(%26quot%3b%23SvgjsClipPath1963%26quot%3b)' fill='none'%3e%3crect width='450' height='390' x='0' y='0' fill='rgba(15%2c 8%2c 57%2c 0.74)'%3e%3c/rect%3e%3ccircle r='27.995' cx='90.82' cy='22.23' fill='url(%23SvgjsLinearGradient1964)'%3e%3c/circle%3e%3ccircle r='18.41' cx='345.73' cy='8.55' fill='url(%23SvgjsLinearGradient1965)'%3e%3c/circle%3e%3ccircle r='31.615' cx='117.63' cy='340.56' fill='url(%23SvgjsLinearGradient1966)'%3e%3c/circle%3e%3ccircle r='14.57' cx='97.27' cy='191.19' fill='%2343468b'%3e%3c/circle%3e%3c/g%3e%3cdefs%3e%3cclipPath id='SvgjsClipPath1963'%3e%3crect width='450' height='390' x='0' y='0'%3e%3c/rect%3e%3c/clipPath%3e%3clinearGradient x1='34.82999999999999' y1='22.229999999999997' x2='146.81' y2='22.229999999999997' gradientUnits='userSpaceOnUse' id='SvgjsLinearGradient1964'%3e%3cstop stop-color='%23ab3c51' offset='0.1'%3e%3c/stop%3e%3cstop stop-color='%234f4484' offset='0.9'%3e%3c/stop%3e%3c/linearGradient%3e%3clinearGradient x1='308.91' y1='8.55' x2='382.55' y2='8.55' gradientUnits='userSpaceOnUse' id='SvgjsLinearGradient1965'%3e%3cstop stop-color='%2332325d' offset='0.1'%3e%3c/stop%3e%3cstop stop-color='%23424488' offset='0.9'%3e%3c/stop%3e%3c/linearGradient%3e%3clinearGradient x1='54.4' y1='340.56' x2='180.85999999999999' y2='340.56' gradientUnits='userSpaceOnUse' id='SvgjsLinearGradient1966'%3e%3cstop stop-color='%23ab3c51' offset='0.1'%3e%3c/stop%3e%3cstop stop-color='%234f4484' offset='0.9'%3e%3c/stop%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e");
    border-radius: 10px;
}
.modal-modifier .modal-content-modifier{
    border-radius: 13px !important;
}

.ebook_detail .book_info .book_variants a.card .offer_price{
    font-size: 25px !important;
    margin-top: 10px;
}
.book_description h1{
    font-size: 2rem;
}
.starRating{
    margin-top: 5px;
}
.starRating i{
   font-size: 12px;
    color: #ff9900;
}

.customerReviews_title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 5px;
}

.customerReviews_details{
    display: flex;
    align-items: center;
    gap: 10px;
}
.customerReviews_details-stars i{
    color: #ff9900;
    font-size: 14px;
}
.customerReviews_details-count p{
    font-size:16px;
    font-weight: 500;
}
.customerReviews_view{
    margin-top: 5px;
}
</style>
<section class="page-main-wrapper mdl-js pb-5 pt-0 ebook_detail">
    <div class="container ebook_detail">
        <div class="page-row-filter mb-4">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <%  for(int i=0;i<categoryBreadCrumbsLink.size();i++){ %>
                    <li class="breadcrumb-item"><a href="${categoryBreadCrumbsLink[i]}">${categoryBreadCrumbsName[i]}</a></li>
                    <%}%>
                </ol>
            </nav>
        </div>
    </div>
    <div class="container d-flex flex-wrap py-4 py-md-5 mt-3 mt-md-0">
        <div class="col-sm-12 d-flex flex-wrap px-0">
            <div class="image_wrapper col-12 col-sm-4 mb-4 text-center px-0">
                <div class="book_image">
                    <div class="bookShadow">
                        <div class="image-wrapper">
                            <% if(printBooksMst.coverImage==null){%>
                            <div class="uncoverdetail">
                                <p>${printBooksMst.title}</p>
                            </div>
                            <%  }else{%>
                            <img src='${printBooksMst.coverImage}' class="img-responsive" />
                            <%}%>
                        </div>
                    </div>
                </div>
            </div>
            <div class="book_info col-12 col-sm-8 pl-md-5 px-0">
                <div class="book_description">
                    <h1 class="mb-3 text-left"><strong>${printBooksMst.title.replace('?','')}</strong></h1>
                    <%if(publisherName!=null){%>
                    <p class="mb-3 book-publisher-name text-left">By <a href="/${(""+publisherName).replaceAll(' ','-').replaceAll('/','-').toLowerCase()}/seller/${publisherId}" target="_blank">${publisherName}</a></p>
                    <%}%>

                    <div class="details-book-about-cart mb-3">
                        <% if(printBooksMst.isbn == null || printBooksMst.isbn == '') { %>
                        <%} else { %>
                        <div class="wrp-details-about-cart">
                            <span class="first-table-cart">ISBN</span>
                            <span class="second-table-cart">:</span>
                            <span class="isbn_no">${printBooksMst.isbn}</span>
                        </div>
                        <%}%>
                        <div class="wrp-details-about-cart">
                            <span class="first-table-cart">eBook</span>
                            <span class="second-table-cart">:</span>
                            <span class="isbn_no">Not yet available</span>
                        </div>
                    </div>

                    <div class="d-flex align-items-center">
                        <span class="dicprice d-none" style="color: #555">Discount &#x20b9<span class="discp"></span></span>
                        <i class="fa fa-check-circle ml-2 d-none" style="color: green"></i>
                    </div>

                    <div class="book_variants row mt-3 col-12 col-md-11 col-xl-8 px-0 mx-0">
                        <div class="col pr-2 pr-lg-3 pl-0">
                            <a class="card card-modifier shadow-sm mb-3 amazonPriceCard">
                                <div class="card-body p-3 d-flex align-items-center justify-content-center flex-column shimmer">
                                    <img src='${assetPath(src: 'resource/amazonAff.svg')}' class='amazonLogo d-none'>
                                    <div class="priceLoader text-center " id="aFetch">
                                        <span class="span-1">Getting Price from</span>
                                        <span class="span-2">
                                            Amazon
                                            <div class="dots d-flex align-items-center">
                                                <div class="dot"></div>
                                                <div class="dot"></div>
                                                <div class="dot"></div>
                                            </div>
                                        </span>
                                    </div>
                                    <span class="offer_price d-block amazonPrice"></span>
                                    <p class="d-none ctaPrice text-center" style="color: rgba(0, 0, 0, 0.4)!important;">Click here to check details</p>
                                </div>
                            </a>
                        </div>
                        <div class="col pr-2 pr-lg-3 pl-0" style="display: none">
                            <a class="card card-modifier shadow-sm mb-3 flipkartPriceCard">
                                <div class="card-body p-3 d-flex align-items-center justify-content-center flex-column shimmer">
                                    <img src='${assetPath(src: 'resource/flipkartAff.svg')}' class='flipkartLogo d-none'>
                                    <div class="priceLoader text-center " id="fFetch">
                                        <span class="span-1">Getting Price from</span>
                                        <span class="span-2" style="color: rgba(18, 123, 211,0.85) !important">
                                            Flipkart
                                            <div class="dots d-flex align-items-center">
                                                <div class="dot"></div>
                                                <div class="dot"></div>
                                                <div class="dot"></div>
                                            </div>
                                        </span>
                                    </div>
                                    <span class="offer_price d-none flipkartPrice"></span>
                                    <p class="d-none ctaPrice text-center" style="color: rgba(0, 0, 0, 0.4)!important;">Click here to check details</p>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="customerReviews row pr-2 pr-lg-3 pl-0 col-12 col-md-11 col-xl-8 mx-0 flex-column">
                        <p class="customerReviews_title">Customer reviews</p>
                        <div class="card card-modifier p-3" style="min-height: 80px;">
                            <div class="customerReviews_details shimmer">
                                <div class="customerReviews_details-stars">
                                    Fetching Reviews...
                                </div>
                                <div class="customerReviews_details-count">
                                    <p></p>
                                </div>
                            </div>
                            <div class="customerReviews_view shimmer">
                                <a href="https://amazon.in"></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%if(categoryDescription!=null){%>
    <div class="bookDetails__container" style="margin-top: 1.5rem;">
        <section class="ebookFeatures">
            <br> <div>
            <h2>About ${categoryName.replaceAll("-"," ")}</h2>
            <p id="categoryDescription">${categoryDescription}</p>
            <p><a href="${blogLink}" target="_blank">${categoryName.replaceAll("-"," ")} - Full information </a> </p>
        </div>
        </section>
    </div>
    <%}%>
    <%if(publisherDescription!=null){%>
    <div class="bookDetails__container" style="margin-top: 1.5rem;">
        <section class="ebookFeatures">
            <br> <div>
            <h2>About ${publisherName}</h2>
            <p id="publisherDescription">${publisherDescription}</p>
        </div>
        </section>
    </div>
    <%}%>
    <!-- Modal for  prepjoy -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>

    <div id="relatedDoubts" class="books-list">
        <g:render template="/resources/relatedBooks"></g:render>
    </div>
</section>
<%if("ibookso".equals(session["siteName"])){%>
<div class="categories-section">
    <div class="container">

        <div class="row">
            <div class="col-md-10 col-lg-7">
                <div>
                    <h4>Categories</h4>
                </div>
            </div>
        </div>

        <g:render template="/printbooks/categories"></g:render>

    </div>
</div>
<%}%>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.9.2/jquery-ui.min.js"></script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js" async></script>
<script src="/assets/prepjoy/lottie.min.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>


<asset:javascript src="searchContents.js"/>
<asset:javascript src="hopscotch-0.1.1.js"/>
<asset:javascript src="addcontentspopover.js"/>
<asset:javascript src="addcontents.js"/>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>
<g:render template="/${session['entryController']}/footer_new"></g:render>
%{--<g:render template="/funlearn/topicscripts"></g:render>--}%

<asset:javascript src="wonderslate/trunk8.js"/>

<script>
    var siteName="";
    var amazonPrice = "";
    var amazonLink = "";
    var flipkartPrice ="";
    var flipkartLink = "";
    var amazonRating="";
    var amazonReviewsCount="";
    siteName="${session['siteName']}";
    if (siteName.includes("currentaffairs")) siteName="currentaffairs";

    $(document).ready(function() {
        //Popular Searches Slider
        $("#relatedbook").slick({
            dots: false,
            arrows: true,
            infinite: false,
            speed: 1000,
            slidesToShow: 6,
            slidesToScroll: 1,
            adaptiveHeight: false,
            autoplay: false,
            autoplaySpeed: 5000,
            responsive: [
                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 4,
                        slidesToScroll: 1,
                        infinite: false,
                        dots: false,
                        arrows: true
                    }
                },
                {
                    breakpoint: 767,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1,
                        infinite: false,
                        dots: false,
                        arrows: true
                    }
                },
                {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                        infinite: false,
                        dots: false,
                        arrows: true
                    }
                }
            ]
        });
    });

    getAffiliationPrices();

    function getAffiliationPrices() {
        <g:remoteFunction controller="affiliation" action="getAffiliationPrices"  onSuccess='receivedAffiliationPrices(data);'
          params="'bookId=${printBooksMst.id}&bookType=print'" />
    }

    function openAffliatePage(bookId, linkType, productLink) {
        <g:remoteFunction controller="log" action="createAffLog" params="'bookId=${printBooksMst.id}&bookType=print&source=web&siteId=${session["siteId"]}&linkType='+linkType+'&productLink='+productLink"/>
        window.open(productLink, '_blank');
    }

    function receivedAffiliationPrices(data) {
        amazonPrice = data.amazonPrice;
        amazonLink = data.amazonLink;
        flipkartPrice = data.flipkartPrice;
        flipkartLink = data.flipkartLink;
        amazonRating = data.ratings;
        amazonReviewsCount = data.reviews;
        var flipkartPriceCard = document.querySelector('.flipkartPriceCard');
        var amazonPriceCard = document.querySelector('.amazonPriceCard');
        var amazonPriceTag = document.querySelector('.amazonPrice');
        var flipkartPriceTag = document.querySelector('.flipkartPrice');
        var reviewStars = document.querySelector('.customerReviews_details-stars');
        var reviewsCount = document.querySelector('.customerReviews_details-count');
        var viewReviews = document.querySelector('.customerReviews_view');
        var amazonLogo = document.querySelector('.amazonLogo');
        var flipkartLogo = document.querySelector('.flipkartLogo');
        var ctaPrice = document.querySelectorAll('.ctaPrice');
        var priceLoader = document.querySelectorAll('.priceLoader');
        var amazonCardHtml="";
        var amazonReviewHtml="";
        if ((amazonPrice != null && amazonLink != null) && (!amazonPrice == "" && !amazonPrice == "0")) {
            amazonPriceCard.setAttribute("href","javascript:openAffliatePage(${printBooksMst.id},'Amazon','" + amazonLink + "')");
            amazonCardHtml = '<span class="rupee-symbol">&#x20b9</span> '+amazonPrice;
        } else {
            amazonCardHtml = '<p>Buy</p>';
            amazonPriceTag.style.textAlign='center';
            amazonPriceCard.setAttribute("href","https://www.amazon.in/dp/${printBooksMst.asin}?tag=wonderslate-21&linkCode=w13");
            amazonPriceCard.setAttribute("target","_blank");
        }

        if (amazonRating!="" && amazonRating!=null){
            amazonCardHtml +="<div class='starRating'>";
            if (amazonRating%1 <= 0.5 && amazonRating%1!=0){
                for(var r=0;r<Math.floor(amazonRating);r++){
                    amazonReviewHtml +="<i class='fa-solid fa-star'></i>";
                }
                amazonReviewHtml += "<i class='fa-solid fa-star-half-stroke'></i>";
            }else if (amazonRating%1 >= 0.7) {
                for (var r = 0; r < Math.round(amazonRating); r++) {
                    amazonReviewHtml += "<i class='fa-solid fa-star'></i>";
                }
            }else if (amazonRating%1 == 0) {
                for (var r = 0; r < Math.floor(amazonRating); r++) {
                    amazonReviewHtml += "<i class='fa-solid fa-star'></i>";
                }
            }
            amazonCardHtml +="</div>";
            reviewStars.innerHTML = amazonReviewHtml;
            reviewsCount.innerHTML = '<p>'+amazonRating+' out of 5</p>';
            viewReviews.innerHTML = "<a href='"+amazonLink+"#customerReviews'>See all reviews ("+amazonReviewsCount+")</a>";
        }else{
            $('.customerReviews').hide();
        }
        if ((flipkartPrice != null && flipkartLink != null) && (!flipkartPrice == "" && !flipkartPrice == "0")) {
            flipkartPriceCard.setAttribute("href","javascript:openAffliatePage(${printBooksMst.id},'Flipkart','" + flipkartLink + "')")
            flipkartPriceTag.innerHTML = '<span class="rupee-symbol">&#x20b9</span> '+flipkartPrice;
        } else if (flipkartLink != null && flipkartPrice == null) {
            flipkartPriceCard.setAttribute("href","javascript:openAffliatePage(${printBooksMst.id},'Flipkart','" + flipkartLink + "')")
            flipkartPriceTag.innerHTML = '<p>Not Available</p>';
        }else if(flipkartPrice==null || flipkartLink==null){
            flipkartPriceTag.innerHTML = '<p>Not Available</p>';
            flipkartPriceTag.style.textAlign='center';
            flipkartPriceCard.setAttribute("href","https://www.flipkart.com/search?q="+'${printBooksMst.title}'.substr(0, 20)+"&affid=wonderslate");
            flipkartPriceCard.setAttribute("target","_blank");
        }
        setTimeout(function (){
            amazonPriceTag.innerHTML = amazonCardHtml;
            flipkartPriceTag.classList.remove('d-none');
            flipkartPriceTag.classList.add('d-block');
            amazonLogo.classList.remove('d-none');
            flipkartLogo.classList.remove('d-none');

            ctaPrice.forEach(item=>{
                item.classList.remove('d-none');
            });
            priceLoader.forEach(item=>{
                item.classList.add('d-none');
            });
        },1000)
        const shimmerSelector = document.querySelectorAll('.shimmer');
        shimmerSelector.forEach((element) => {
            element.classList.remove('shimmer');
        });
    }

    function replaceAll(str, find, replace) {
        if (str == undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }

    function openAffliatePage(bookId,linkType,productLink){
        <g:remoteFunction controller="log" action="createAffLog" params="'bookId=${printBooksMst.id}&bookType=print&source=web&siteId=${session["siteId"]}&linkType='+linkType+'&productLink='+productLink"/>
        window.open(productLink, '_blank');
    }
    <%if(categoryDescription!=null){%>
    document.getElementById("categoryDescription").innerHTML = document.getElementById("categoryDescription").innerText;
    <%}%>
</script>

<%if("Books".equals(printBooksMst.baseCategory)){%>
<script type="application/ld+json">
{
"@context": "https://schema.org/",
  "@type": "Book",
  "name":"${printBooksMst.title}",
  "image": "${printBooksMst.coverImage}",
  "isbn": "${printBooksMst.isbn}",
  "educationalUse": "Study Materials",
  "publisher":"${publisherName}"

 }
</script>
<%}else{%>
<script type="application/ld+json">
{
"@context": "https://schema.org/",
  "@type": "Product",
  "name":"${printBooksMst.title}",
  "image": "${printBooksMst.coverImage}",
  "isbn": "${printBooksMst.isbn}",
  "category": "${printBooksMst.baseCategory}",
  "manufacturer":"${publisherName}"

 }
</script>
<%}%>
</body>
</html>
