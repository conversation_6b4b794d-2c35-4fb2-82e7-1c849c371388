<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="sharer.min.js"/>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<%if(("ios".equals(session["appType"]))){%>
<style>
input,textarea{
    font-size: 16px !important;
}
</style>
<%}%>
<div class="modal modal-modifier fade" id="publicModal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered" role="document">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <h5 class="mt-3">Are you sure to continue?</h5>
                <p>This will make your <span class="text-lowercase">${resourceType}</span> visible to everybody.</p>

                <div class="d-flex justify-content-center py-3">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 mr-2" onclick="javascript:cancelPublic();">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0 col-5 col-md-4 ml-2" onclick="javascript:makePublic();">Set Public</button>
                </div>
            </div>

        </div>
    </div>
</div>
<div class="modal modal-modifier fade" id="addFolder" data-backdrop="static">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered" role="document">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-header modal-header-modifier">
                <h4 class="modal-title d-flex align-items-center"><i class="material-icons mr-2">create_new_folder</i> Create Folder</h4>
            </div>

            <div class="modal-body modal-body-modifier">
                <form id="createFolder" class="mb-4">
                    <div class="form-group form-group-modifier">
                        <input type="text" class="form-control form-control-modifier border-bottom" name="folderName" id="folderName" placeholder="Folder Name">
                    </div>
                    <p id="addFolderAlert" class="form-text form-text-modifier text-danger" style="display: none;">Please enter the folder name.</p>
                </form>

                <div class="d-flex justify-content-center py-3">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 mr-2" onclick="javascript:closeAddFolder();">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0 col-5 col-md-4 ml-2" onclick="javascript:addFolder();">Create</button>
                </div>
            </div>

        </div>
    </div>
</div>
<div class="modal modal-modifier fade" id="deleteSetModal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered" role="document">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <h5 class="mt-3">Are you sure?</h5>
                <p>You want to delete this?</p>

                <div class="d-flex justify-content-center py-3">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 mr-2" onclick="javascript:cancelSetDelete();">Cancel</button>
                    <button type="button" class="btn btn-lg btn-danger btn-danger-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0 col-5 col-md-4 ml-2" onclick="javascript:deleteSetConfirm();">Yes, Delete</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div id="overlay-bg"></div>

<section class="page-main-wrapper mdl-js pb-5 pt-0 my-materials flashcard-home">
    <div class="container pt-4 pt-md-5 mt-3 mt-md-0">
        <div class="row justify-content-center page_title">
            <div class="col-12 row">
                <button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:location.href='/books/home'">keyboard_backspace</button>
                <div class="mdl-tooltip" data-mdl-for="goBack">Back</div>
                <% if(resourceType=="Flash Cards") {%>
                <h3><strong>Flashcard</strong></h3>
                <% } else {%>
                <h3><strong>${resourceType}</strong></h3>
                <% } %>
                <sec:ifLoggedIn>
                    <button class="btn btn-outline-primary btn-outline-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect ml-2" onclick="showFavourites()" id="favButton"><i class="material-icons">favorite</i> View Favorites</button>
                </sec:ifLoggedIn>
                <sec:ifNotLoggedIn>
                    <button class="btn btn-outline-primary btn-outline-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect ml-2" onclick="loginOpenWithFunction('showFavourites','Please login to see favourites');" id="favButton"><i class="material-icons">favorite</i> View Favorites</button>
                </sec:ifNotLoggedIn>
                <button class="btn btn-outline-primary btn-outline-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect ml-2" onclick="getUserFlashCardSets()" id="allButton" style="display: none"><i class="material-icons">favorite</i> Show All</button>
                %{--                <button class="btn btn-flashcard d-none d-lg-block">Revise</button>--}%
            </div>
        </div>
        <div class="row justify-content-center">
            <div class="col-12">
                <form class="form-inline mt-4 flex-nowrap">
                    <div class="input-group col-7 px-0">
                        <input type="text" class="form-control form-control-modifier border-primary-modifier border-right-0 search typeahead" name="search" id="search-book" autocomplete="off" placeholder="Search<%if(resourceType=="Flash Cards"){%> Flashcard <%}else{%> ${resourceType} <%}%>">
                        <div class="input-group-append">
                            <button type="button" class="btn btn-lg submit-search-btn border-primary-modifier border-left-0 text-primary text-primary-modifier bg-white px-2" onclick="submitSearch()" id="search-btn"><i class="material-icons">search</i></button>
                        </div>
                    </div>
                    <sec:ifLoggedIn>
                        <a href="javascript:showAddFolder();" class="btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect ml-3 new-folder" id="showAddFolder"><i class="material-icons">create_new_folder</i>New Folder</a>
                    </sec:ifLoggedIn>
                    <sec:ifNotLoggedIn>
                        <a href="javascript:loginOpenWithFunction('showAddFolder','Please login to add folder');" class="btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect ml-3 new-folder"><i class="material-icons">create_new_folder</i>New Folder</a>
                    </sec:ifNotLoggedIn>

                </form>

            </div>

        </div>
        <div class="row">
            <div class="col-12">
                <div class="container mt-4" id="folders">

                </div>

                <div class="container mt-4" id="flashCardSets">

                </div>
                <div class="container mt-4" id="recentFlashCardSets">

                </div>
            </div>
        </div>

    </div>

</section>

<div class="loading-icon">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div id="relatedFlashcards" class="books-list">
    <g:render template="/resources/relatedBooks"></g:render>

</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<g:render template="/resources/shareContent"></g:render>

<div class="modal modal-modifier fade" id="videoModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-modifier modal-lg modal-dialog-centered">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close" id="youtubeclose">
                <span aria-hidden="true">x</span>
            </button>
            <div class="modal-body modal-body-modifier">
                <iframe width="100%" height="450px" src="" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>

<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<g:render template="/resources/videoSection"></g:render>
<g:render template="/resources/weblinksSection"></g:render>
<script>
    var appDisplay = false;
    <%if((("android".equals(session["appType"]))||("ios".equals(session["appType"])))){%>
    appDisplay = true;
    $('#goBack').addClass('d-none');
    <%}%>
    function openModal() {
        $('#footer-menu-popover').modal('toggle');
    }
    $('#footer-menu-popover').on('show.bs.modal', function(){
        $('.menu-close-icon-footer').toggleClass("hidden");
        $('.menu-icon-footer').toggleClass("hidden");
    });
    $('#footer-menu-popover').on('hide.bs.modal', function(){
        $('.menu-close-icon-footer').toggleClass("hidden");
        $('.menu-icon-footer').toggleClass("hidden");
    });
    function handleMenuClick(url){
        <%if("android".equals(session["appType"])){%>
        if(url=='/books/index'){
            JSInterface.backToHome();
        }
        else if(url=='/wsLibrary/myLibrary'){
            JSInterface.backToLibrary();
        }
        else if(url=='/dashboard'){
            JSInterface.backToDashboard();
        }
        else if(url=='/doubts'){
            JSInterface.backToDoubts();
        }
        else if(url=='/flashcards'){
            JSInterface.backToFlashcard();
        }

        <%}else if("ios".equals(session["appType"])) {%>
        if(url=='/books/index'){
            webkit.messageHandlers.backToHome.postMessage('');
        }
        else if(url=='/wsLibrary/myLibrary'){
            webkit.messageHandlers.backToLibrary.postMessage('');
        }
        else if(url=='/dashboard'){
            webkit.messageHandlers.backToDashboard.postMessage('');
        }
        else if(url=='/doubts'){
            webkit.messageHandlers.backToDoubts.postMessage('');
        }
        else if(url=='/flashcards'){
            webkit.messageHandlers.backToFlashcard.postMessage('');
        }
        <%} else{%>

        // If a mobile browser is being used
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;
        if (/iPad|iPhone|iPod|Android/.test(userAgent)) {
            window.location.href = url; // this url is passed as an argument to function from menu items
            return;
        }
        <%}%>
    }
</script>
<script>
    var pageNo;
    var isAjaxCalled;
    var newlyCreatedFolderName="";
    var resType = "KeyValues";
    <%if(params.resType!=null){%>
    resType = "${params.resType}";
    <%}%>

    //set the user folders
    var htmlStr='';
    var userFoldersString = "${userFolders}";
    userFoldersString = userFoldersString.replace(/&quot;/g,'"');
    userFoldersString = userFoldersString.replace('u0027','\'');
    var userFolders;
    var favouriteResIds = "${resIds}";
    var favArray = favouriteResIds.split(',');
    var previousChapterId = '';
    var loggedInUser = false;
    var searchMode=false;
    <sec:ifLoggedIn>
    loggedInUser = true;
    </sec:ifLoggedIn>
    if(userFoldersString) {
        userFolders = JSON.parse(userFoldersString);
    }

    htmlStr += "<div class='row'>"+" <div class='col-12 px-0'>"+
        "<div class='col-12 px-0'>";
    if(userFolders ===undefined || userFolders.length==0){

    }
    else {
        htmlStr += "<div class='pt-3'>" +

            "<p class='text-primary text-primary-modifier'><small><em>Folders</em></small></p>" +

            "</div>";
        htmlStr +="<div class='d-flex flex-wrap'>";
        for (var i = 0; i < userFolders.length; i++) {

            htmlStr +=   "<div class='col-6 col-md-3 col-lg-2 pl-0 folder-box'>" +

                "<a href='/folder/folderHome?folderId=" + userFolders[i].folderId + "&folderName=" + userFolders[i].folderName + "' class='inside-folder-text mt-3 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'> "+
                "<i class='material-icons'>folder</i>"+
                "<span>" + userFolders[i].folderName +"</span>"+
                "</a>" +
                "</div>" ;

        }
        htmlStr +="</div>";

    }
    htmlStr += "</div></div>";


    document.getElementById("folders").innerHTML = htmlStr;



    function getUserFlashCardSets(){
        $('.loading-icon').removeClass('hidden');
        $("#allButton").hide();
        $("#favButton").show();
        setAllFavorite=false;
        <g:remoteFunction controller="resources" action="getUserFlashCards" params="'resType='+resType" onSuccess='flashCardSetsReceived(data);'></g:remoteFunction>
        $('#recentFlashCardSets').show();
    }

    function flashCardSetsReceived(data) {

        if (setAllFavorite) {
            if (data.flashCardSets != null) $('#recentFlashCardSets').hide();
            displayFlashCardSets(data.flashCardSets, false);
        } else {
            if (data.flashCardSets != null) displayFlashCardSets(JSON.parse(data.flashCardSets), false);
            displayFlashCardSets(JSON.parse(data.latestFlashCards), true);

        }
        $('.loading-icon').addClass('hidden');
    }

    function showAddFolder(){
        $('#addFolder').modal('show');
    }
    function closeAddFolder(){
        $('#addFolder').modal('hide');
    }
    function addFolder(){
        $("#addFolderAlert").hide();
        if(document.getElementById("folderName").value==""){
            $("#addFolderAlert").show();
            $("#folderName").focus();
        }
        else {
            $('.loading-icon').removeClass('hidden');
            $('#addFolder').modal('hide');
            newlyCreatedFolderName=document.getElementById("folderName").value;
            <g:remoteFunction controller="folder" action="addFolder" params="'folderName='+newlyCreatedFolderName" onSuccess='folderAdded(data);'/>
        }
    }
    function gotoCreateFlashCard(){
        window.location.href="/resources/createFlashCards";
    }
    function folderAdded(data){
        $("#folderName").val("");
        window.location.href="/folder/folderHome?folderId="+data.folderId+"&folderName="+newlyCreatedFolderName;
    }
    $('.loading-icon').addClass('hidden');

    getUserFlashCardSets();

    //search related stuff
    $('#search-book').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList?resourceType='+resType,
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch();
        }
    });
    $(document).ready(function(){
        $('#search-btn').attr('disabled',true);
        $('#search-book').keyup(function(){
            if($(this).val().length !=0)
                $('#search-btn').attr('disabled', false);
            else
                $('#search-btn').attr('disabled',true);
        })
    });
    $(document).on("keypress", "#search-book", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                // to prevent submitting of the page
                e.preventDefault();
                submitSearch();
            }
        }
    });

    function submitSearch(){
        var searchString =encodeURIComponent(document.getElementById("search-book").value);
        searchMode = true;
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="discover" action="resourceSearch"  onSuccess='resourcesReceived(data);'
         params="'searchString='+searchString+'&resourceType=KeyValues'" />

    }

    function resourcesReceived(data){
        if("OK"==data.status) {
            displayFlashCardSets(data.resources,false);
        }else{
        }
        $('.loading-icon').addClass('hidden');
    }

    function createResourceCard(resId,title,recent,editAction,privacyLevel,shareLink,actionString,createdBy,description,canEdit,cardLabel,cardColor,labelBackgroundColor){
        var  htmlStr ="            <div class=\"col-12 col-md-6 col-lg-6 mt-3 pl-md-0 pr-md-4 p-0 container-wrapper\" id=\"flash"+resId+"\">\n" +
            "                <div class=\"d-flex justify-content-between align-items-center resource-set\">\n"+
            "<div class='media '><div class='mymaterial-img '>"+
            "   <div class='box m-0 "+cardColor+"' style='background: "+labelBackgroundColor+";'>" +
            actionString+
            "<p class='cardLabel'>"+cardLabel+"</p>"+
            "</a>"+"</div>"+
            "</div><div class='col material-none pl-3 pr-0'>"+
            "                    <div class=\"title-wrapper d-flex align-items-start justify-content-between pl-0 pr-4 pt-4\">\n" +
            "                        <h5 class='title px-0'>"+actionString.concat(title) +"</a></h5>\n" +
            "                        <div class=\"resource-actions\">\n";

        if(appDisplay) {
            htmlStr += "<button onclick='" + shareLink + "' class='btn btn-sm share-resource-btn'>" +
                "<i class='material-icons'>share</i>" +
                "</button>";
        }
        if(!recent&&!setAllFavorite&&!searchMode) {
            htmlStr +=editAction;
            if(privacyLevel == null) {
                htmlStr += " <a href='javascript:deleteFlashCard(" + resId + ")' id='del" + resId + "' class='set delete-resource'><i class=\"material-icons\">delete</i></a>\n";
            }
        }
        if(!appDisplay&&(recent||privacyLevel=="public")&&cardLabel!="Link"){
            htmlStr += "<button onclick='"+shareLink+"' class='btn btn-sm share-resource-btn'>" +
                "<i class='material-icons'>share</i>" +
                "</button>";
        }

        if(favArray.includes(""+resId)) {
            htmlStr +=  "<a href='javascript:removeFavourite("+resId+")' id='res"+resId+"' class='active unset'><i class=\"material-icons\">favorite</i></a>\n" ;
            htmlStr +="<a href='javascript:addFavourite("+resId+")' id='res"+resId+"' class='set favorite-resource' style='display: none;'><i class=\"material-icons\">favorite</i></a>\n";
        }else{
            htmlStr +=  " <a href='javascript:addFavourite("+resId+")' id='res"+resId+"' class='set favorite-resource'><i class=\"material-icons\">favorite</i></a>\n" ;
            htmlStr +=  "<a href='javascript:removeFavourite("+resId+")' id='res"+resId+"' class='active unset' style='display: none;'><i class=\"material-icons\">favorite</i></a>\n" ;
        }

        htmlStr += "                        </div>\n" +
            "                    </div>\n" +
            actionString;
        if(recent && createdBy !='') {
            htmlStr +=   "                        <p class='m-0'>By: " + createdBy + "</p>" ;
        }
        if(description){
            htmlStr +=  "<p>"+description+"</p>";
        }
        else {
            htmlStr += "";
        }
        htmlStr += "</a><div class=\"progress d-none\">\n" +
            "    <div class=\"progress-bar green\" role=\"progressbar\" aria-valuenow=\"70\" aria-valuemin=\"0\" aria-valuemax=\"100\" style=\"width:20%\">\n" +

            "    </div>\n" +
            "  </div>";
        if(canEdit=="true"&&privacyLevel=='public'){
            htmlStr += "<div class='d-flex justify-content-end set_public_btn'>" +
                "<div class=\"switch reset-switch toggleSwitch\"><span class='public-text' id=\"public-text"+resId+"\">Public</span>" +
                "</div>"+
                "</div>";
        }

        else if(canEdit=="true"&&privacyLevel=='awaiting_approval'){
            htmlStr += " <div class='d-flex justify-content-end set_public_btn'><div class=\"switch reset-switch toggleSwitch\">" +
                "<span class='public-text' id=\"public-text"+resId+"\">Awaiting Approval</span>" +
                "<label class=\"switch\">\n" +
                "  <input type=\"checkbox\" checked readonly class=\"publicLevel"+resId+"\" id=\"publicLevel"+resId+"\" onchange='javascript:awaitingApproval(this,\""+resId+"\");'>\n" +
                "  <span class=\"slider round\"></span>\n" +
                "</label>" +
                "</div></div>";
        }
        else if(canEdit=="true"&&"rejected"==privacyLevel){
            htmlStr +=  "<div class='d-flex justify-content-end set_public_btn'> <div class=\"switch reset-switch toggleSwitch\">" +
                "<span class='public-text'>Cannot be made public</span>" +
                "<label class=\"switch\">\n" +
                "  <input type=\"checkbox\" disabled readonly id='publicLevel'>\n" +
                "  <span class=\"slider round\"></span>\n" +
                "</label>" +
                "</div></div>";

        }
        else if(canEdit=="true") {
            htmlStr += "<div class='d-flex justify-content-end set_public_btn'>" +

                "<div class=\"switch reset-switch toggleSwitch\"><span class='public-text' id=\"public-text"+resId+"\">Set as Public</span>" +
                "<label class=\"switch\">\n" +
                "  <input type=\"checkbox\" class=\"publicLevel"+resId+"\" id=\"publicLevel"+resId+"\" onchange='javascript:setPublic(this,\""+resId+"\");'>\n" +
                "  <span class=\"slider round\"></span>\n" +
                "</label>" +
                "</div>" +
                "</div>";
        }
        htmlStr += "                </div></div></div>\n"+
            "            </div>\n" ;

        return htmlStr;

    }
    function displayFlashCardSets(flashCardSets,recent){
        if(flashCardSets.length==0)
             $("#showAddFolder").hide();
        var htmlStr="<div class='row'> " +
            "<div class='col-12 px-0'> " +
            "<div class='col-12 px-0'>"+
            "<div class='pt-3 d-flex align-items-center'>" ;

        if(recent) htmlStr +="  <p class='text-primary text-primary-modifier'><small><em>Shared by others</em></small></p>";
        else if(searchMode) htmlStr +="  <p class='text-primary text-primary-modifier'><small><em>Search results</em></small></p>";
        else  htmlStr +="  <p class='text-primary text-primary-modifier resource-label'><small><em><%if(resourceType=='Flash Cards'){%> Flashcard <%}else{%> ${resourceType} <%}%></em></small></p>";

        if(!recent&&!searchMode){

            var actionString="";
            if(window.location.href.indexOf("mymaterials") != -1) {
                actionString += "<div class='materialsAddDropdown dropdown'>\n" +
                    "<button class='btn dropdown-toggle d-flex align-items-center justify-content-center' type='button' id='dropdownMenuButton' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false'>\n" +
                    " <i class='material-icons'>add</i> <span>Add</span>\n" +
                    "</button>\n" +
                    "<div id='materialsAddDropdownMenus' class='dropdown-menu' aria-labelledby='dropdownMenuButton'>"+
                    "<a class='dropdown-item d-flex align-items-center new-folder btn btn-primary btn-primary-modifier mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect' href='/resources/createFlashCards'>" +
                    "<i class='material-icons mr-0 pr-1'>style</i> <span>Flashcard</span>" +
                    "</a>"+
                    "<a class='dropdown-item d-flex align-items-center new-folder btn btn-primary btn-primary-modifier mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect' href='/wonderpublish/quizcreator?resourceType=Multiple%20Choice%20Questions&useType=quiz&mode=create'>" +
                    "<i class='material-icons mr-0 pr-1'>help_outline</i> <span>MCQs</span>" +
                    "</a>";
                <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
                actionString +="<a class='dropdown-item d-flex align-items-center new-folder btn btn-primary btn-primary-modifier mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect' href='addNotesFromMobile();'>" +
                    "<i class='material-icons mr-0 pr-1'>description</i> <span>Notes</span>" +
                    "</a>";
                <%} else{%>
                actionString +="<a class='dropdown-item d-flex align-items-center new-folder btn btn-primary btn-primary-modifier mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect' href='/resourceCreator/addNotes?resourceType=Notes&mode=create'>" +
                    "<i class='material-icons mr-0 pr-1'>description</i> <span>Notes</span>" +
                    "</a>";
                <%}%>
                actionString +="<a class='dropdown-item d-flex align-items-center new-folder btn btn-primary btn-primary-modifier mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect' href='javascript:openVideos();'>" +
                    "<i class='material-icons mr-0 pr-1'>play_circle_outline</i> <span>Videos</span>" +
                    "</a>"+
                    "<a class='dropdown-item d-flex align-items-center new-folder btn btn-primary btn-primary-modifier mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect' href='javascript:openSolution();'>" +
                    "<i class='material-icons mr-0 pr-1'>link</i> <span>Web Link</span>" +
                    "</a>"+
                    "</div>\n" +
                    "</div>";
            } else {
                <%if(params.resType==null||"KeyValues".equals(params.resType)){%>
                actionString += "<a href=\"/resources/createFlashCards\" class=\"btn add-resource-btn new-folder btn btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect\">\n" +
                    "                        <i class='material-icons'>add</i> <span>Add</span>\n" +
                    "                     </a>";
                <%}%>
                <%if("Multiple Choice Questions".equals(params.resType)){%>
                actionString += "<a href=\"/wonderpublish/quizcreator?resourceType=Multiple%20Choice%20Questions&useType=quiz&mode=create\" class=\"btn add-resource-btn new-folder btn btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect\">\n" +
                    "                         <i class='material-icons'>add</i> <span>Add</span>\n" +
                    "                     </a>";
                <%}%>
                <%if("Notes".equals(params.resType)){%>

                actionString +="<a href=\"/resourceCreator/addNotes?resourceType=Notes&mode=create\" class=\"btn add-resource-btn new-folder btn btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect\">\n" +
                    "                         <i class='material-icons'>add</i> <span>Add</span>\n" +
                    "                     </a>";

                <%}%>
                <%if("Reference Videos".equals(params.resType)){%>
                actionString +="<a href=\"javascript:openVideos();\" class=\"btn add-resource-btn new-folder btn btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect\">\n" +
                    "                         <i class='material-icons'>add</i> <span>Add</span>\n" +
                    "                     </a>";
                <%}%>
                <%if("Reference Web Links".equals(params.resType)){%>
                actionString += "<a href=\"javascript:openSolution();\" class=\"btn add-resource-btn new-folder btn btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect\">\n" +
                    "                         <i class='material-icons'>add</i> <span>Add</span>\n" +
                    "                     </a>";
                <%}%>
            }
            htmlStr +=
                "<div class='text-center empty-info'>"+
                "<p class='flashcard-empty-msg bold'>"+actionString+"</p></div>";
        }
        htmlStr +=  "</div>";
        htmlStr +=" <div class=\"d-flex flex-wrap flashcards all-container\">\n";
        var cardLabel="";
        var labelBackgroundColor="";
        var cardColor="";
        var editAction="";
        var privacyLevel=null;
        var shareLink="";
        var actionString="";

        for(var i=0;i<flashCardSets.length;i++){
            cardLabel="";
            labelBackgroundColor="";
            cardColor="";
            editAction="";
            privacyLevel=flashCardSets[i].privacyLevel;
            shareLink="";
            actionString="";
            var color = "#" + "colors".split("").map(function(){return parseInt(Math.random()*0x10).toString(16);}).join("");

            if(flashCardSets[i].resType=='Notes') {
                cardLabel = "Notes";
                cardColor = "blue";
                if(!recent&&privacyLevel == null){
                    editAction = "<a href='/resourceCreator/addNotes?resourceType=Notes&mode=edit&id=" + flashCardSets[i].resId + "'  class='set edit-resource'><i class=\"material-icons\">edit</i></a>\n";
                }
                shareLink = "javascript:shareNotes("+JSON.stringify(flashCardSets[i]).replace(/'/g,"&#39;")+")";
                actionString = "<a href='/resources/notesViewer?resId=" + flashCardSets[i].resId + "&name=" + encodeURIComponent(flashCardSets[i].title).replace(/'/g,"&#39;") + "' class='chapter_txt' onclick='javascript:callAppLoader()'>\n";

            }
            else if(flashCardSets[i].resType=='KeyValues'){
                cardLabel = "Flashcard";
                cardColor = "yellow";
                labelBackgroundColor = color;
                if(!recent&&privacyLevel == null){
                    editAction = "<a href='/resources/displayFlashCards?resId=" + flashCardSets[i].resId + "&name="+encodeURIComponent(flashCardSets[i].title).replace(/'/g,"&#39;")+"&mode=edit'  class='set edit-resource'><i class=\"material-icons\">edit</i></a>";
                }
                shareLink = "javascript:shareFlashCard("+JSON.stringify(flashCardSets[i]).replace(/'/g,"&#39;")+")";
                actionString = "<a href='/resources/displayFlashCards?resId=" + flashCardSets[i].resId + "&name=" + encodeURIComponent(flashCardSets[i].title).replace(/'/g,"&#39;") + "' class='chapter_txt' onclick='javascript:callAppLoader()'>\n";

            }
            else if(flashCardSets[i].resType=='Reference Videos'){
                cardLabel = "Video";
                cardColor = "pink";
                actionString = "<a href='javascript:playVideo(\"" + flashCardSets[i].resLink + "\"," + flashCardSets[i].resId + ")' id='resAction_" + flashCardSets[i].resId + "' class='chapter_txt'>\n";
                shareLink = "javascript:shareNotes("+JSON.stringify(flashCardSets[i]).replace(/'/g,"&#39;")+")";}

            else if(flashCardSets[i].resType=='Reference Web Links'){
                cardLabel = "Link";
                cardColor = "green";
                if(!appDisplay&&(recent||"public"==privacyLevel)){
                    shareLink = "javascript:shareWeblink("+JSON.stringify(flashCardSets[i]).replace(/'/g,'&#39;')+")";
                }
                actionString = "<a  href='javascript:openWebRef(" + flashCardSets[i].resId + ",\""+flashCardSets[i].resLink.replace('#', ':')+"\")' id='resAction_"+flashCardSets[i].resId+"' class='chapter_txt' onclick='javascript:callAppLoader()'>\n";
            }
            else if(flashCardSets[i].resType=='Multiple Choice Questions'){
                cardLabel = "MCQ";
                cardColor = "violet";
                if(!recent&&privacyLevel == null){
                    editAction = " <a href='/wonderpublish/quizcreator?resourceType=Multiple%20Choice%20Questions&useType=quiz&mode=edit&id=" + flashCardSets[i].resId + "'  class='set edit-resource'><i class=\"material-icons\">edit</i></a>\n";
                }
                shareLink = "javascript:shareMCQ("+JSON.stringify(flashCardSets[i]).replace(/'/g,"&#39;")+")";
                actionString = "<a href='/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId=" + flashCardSets[i].resId + "&name=" + encodeURIComponent(flashCardSets[i].title).replace(/'/g,"&#39;") + "' class='chapter_txt' onclick='javascript:callAppLoader()'>\n";

            }

            htmlStr += createResourceCard(flashCardSets[i].resId,flashCardSets[i].title,recent,editAction,privacyLevel,shareLink,actionString,flashCardSets[i].createdBy,flashCardSets[i].description,flashCardSets[i].canEdit,cardLabel,cardColor,labelBackgroundColor);


        }
        searchMode = false;
        showMore();

        if(!recent&&flashCardSets.length==0){
            var resourceTypeMsg = "${resourceType}";
            if(resourceTypeMsg == 'My Materials'){
                resourceTypeMsg = 'Materials';
            }
            if (setAllFavorite) {
                htmlStr +=
                    "<div class='text-center empty-info w-100 py-4'>"+
                    "<p class='resource-empty-msg bold'>Currently you do have not marked any Favorites. <br class='d-md-none'>Start marking resources for your Favorites.</p>";

            }

        }
        htmlStr += "        </div>";
        if(recent) {
            document.getElementById("recentFlashCardSets").innerHTML = htmlStr;

        } else {
            document.getElementById("flashCardSets").innerHTML = htmlStr+="<span class='showMore' style=' margin-top:1%;float: right;margin-right: 35px;font-size: 14px;color: #C633A2;cursor: pointer;>Show more..</span><span class='showLess' style=' margin-top:1%;float: right;margin-right: 35px;font-size: 14px;color: #C633A2;cursor: pointer;'>Show less..</span><div class='d-flex justify-content-end d-md-none mb-2'></div>";
        }
        if (setAllFavorite) {
            $('.showMore').addClass('d-none');
            $('.showLess').addClass('d-none');
            if($('.flashcard-action > a > i').innerText == 'favorite'){
                $('.flashcard-actions > a').addClass('active');
            }}

        $(".materialsAddDropdown").on("show.bs.dropdown", function(){
            $("#overlay-bg").attr('style','opacity:1;visibility:visible;');
            $("#dropdownMenuButton span").text('Close');
            $("#dropdownMenuButton").find('i').addClass('rotated');
            $('.resource-label').attr('style','position:relative;z-index:9992;color:white !important;');
        });
        $(".materialsAddDropdown").on("hide.bs.dropdown", function(){
            $("#overlay-bg").attr('style','opacity:0;visibility:hidden;');
            $("#dropdownMenuButton span").text('Add');
            $("#dropdownMenuButton").find('i').removeClass('rotated');
            $('.resource-label').attr('style','');
        });

    }

    var tempResId;
    function addFavourite(resId){
        tempResId=resId;
        favArray.push(tempResId.toString());
        $('.set#res'+tempResId).hide().removeClass('heart');
        $('.unset#res'+tempResId).show().addClass('heart');
        <g:remoteFunction controller="usermanagement" action="addUserResourceFavourite"  params="'resId='+resId"/>
    }

    function removeFavourite(resId){
        tempResId=resId;
        var index=favArray.indexOf(tempResId.toString());
        if (index > -1) {
            favArray.splice(index, 1);
        }
        $('.unset#res'+tempResId).hide().removeClass('heart');
        $('.set#res'+tempResId).show().addClass('heart');
        <g:remoteFunction controller="usermanagement" action="removeUserResourceFavourite"  params="'resId='+resId"/>
    }

    function shareFlashCard(flashCard){
        var flashcardId = flashCard.resId;
        var flashcardTitle = flashCard.title;
        var flashCardUrl = "https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.origin);
        flashCardUrl = flashCardUrl + encodeURIComponent("/resources/displayFlashCards?resId=" + flashcardId + "&name=" + flashcardTitle.replace(/'/g,"&#39;"));

        openShareContentModal("flashcard", flashCardUrl)
    }
    function shareMCQ(flashCard){
        var flashcardId = flashCard.resId;
        var flashcardTitle = flashCard.title;
        var flashCardUrl = "https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.origin);
        flashCardUrl = flashCardUrl + encodeURIComponent("/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId=" + flashcardId + "&name=" + flashcardTitle.replace(/'/g,"&#39;"));
        if(appDisplay){
            flashCardUrl=window.location.origin+"/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId=" + flashcardId + "&name=" + flashcardTitle.replace(/'/g,"&#39;");
            <%if(("android".equals(session["appType"]))){%>
                  JSInterface.appShareUrl(flashCardUrl);
            <%} else{%>
                  webkit.messageHandlers.appShareUrl.postMessage("'"+flashCardUrl+"'");
            <%}%>

        }
        else {
            openShareContentModal("MCQ", flashCardUrl)
        }
    }
    function shareWeblink(flashCard){
        var flashcardId = flashCard.resId;
        var flashcardTitle = flashCard.title;
        var flashCardUrl = "https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.origin);
        flashCardUrl = flashCardUrl + encodeURIComponent("/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId=" + flashcardId + "&name=" + flashcardTitle.replace(/'/g,"&#39;"));
        openShareContentModal("Web Link", flashCardUrl)
    }
    function shareVideos(flashCard){
        var flashcardId = flashCard.resId;
        var flashcardTitle = flashCard.resLink;
        var flashCardUrl = "https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.origin);
        flashCardUrl = flashCardUrl + encodeURIComponent("/videos?&resId=" + flashcardId + "&name=" + flashcardTitle.replace(/'/g,"&#39;")+"&share=true");
        openShareContentModal("Videos", flashCardUrl)
    }
    function shareNotes(flashCard){
        var flashcardId = flashCard.resId;
        var flashcardTitle = flashCard.title;
        var flashCardUrl = "https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.origin);
        flashCardUrl = flashCardUrl + encodeURIComponent("/resources/notesViewer?&resId=" + flashcardId + "&name=" + flashcardTitle.replace(/'/g,"&#39;"));
        openShareContentModal("Notes", flashCardUrl)
    }


    //      function deleteFlashCard(resId){
    // // remote function to delete flash card from the user account
    //          alert("Please write function to delete [" + resId + "] Flash Card");
    //
    //      }



    var deleteSetId;
    function deleteFlashCard(resId) {
        $('#deleteSetModal').modal('show');
        deleteSetId=resId;

    }
    function deleteSetConfirm() {
        $("#flash" + deleteSetId).remove();
        <g:remoteFunction controller="resources" action="deleteFlashCards" params="'resId='+deleteSetId" onSuccess="deleteSetSuccess()"></g:remoteFunction>
    }

    function cancelSetDelete() {
        $('#deleteSetModal').modal('hide');
    }
    function deleteSetSuccess() {
        $('#deleteSetModal').modal('hide');
    }



    var setAllFavorite;
    function showFavourites(){
        $('.loading-icon').removeClass('hidden');
        $("#favButton").hide();
        $("#allButton").show();
        setAllFavorite=true;
        <g:remoteFunction controller="usermanagement" action="getUserResourceFavourites" params="'resType='+resType" onSuccess='flashCardSetsReceived(data);'></g:remoteFunction>

    }

    function callAppLoader() {
        <%if("android".equals(session["appType"])){%>
        JSInterface.flashCardLoaderEvent();
        <%}%>
        <%if("ios".equals(session["appType"])){%>
        webkit.messageHandlers.flashCardLoaderEvent.postMessage('');
        <%}%>
    }



    <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
    $('#relatedFlashcards').hide();
    <%}%>

    function openSolution() {
        $('#addWeburl').modal('show');
    }
    function closeAddLink(){
        $('#addWeburl').modal('hide');
    }
    function awaitingApproval(field){
        alert("Awaiting approval to set this flashcard public.");
        field.checked = true;
    }
    function alreadySetPublic(field){
        alert("Cannot change, once set to public.");
        field.checked = true;
    }

    var resourceId;
    function setPublic(field,id){
        if(field.checked) {
            $('#publicModal').modal('show');
        }
        resourceId=id;
    }
    function makePublic() {
        <g:remoteFunction controller="resources" action="setPrivacyStatusToAwaitingApproval" onSuccess='statusSet(data,resourceId);' params="'resId='+resourceId"/>
    }
    function cancelPublic() {
        $('#publicModal').modal('hide');
        $('.publicLevel'+resourceId).attr('checked', false);
    }
    function statusSet(data,id){
        if(data.status=='ok') {
            $("#privacyLevel"+id).disable = true;

            $('#public-text'+id).text('Awaiting Approval');

            var clickval = $("#publicLevel"+id).attr("onchange");
            $('#publicLevel'+id).attr('onchange', clickval.replace('setPublic', 'awaitingApproval'));
            $('#publicModal').modal('hide');
            $('.slider-actions').append("<button onclick='javascript:shareFlashcard()' class='dropdown-toggle' data-toggle=\"dropdown\">" +
                "<i class='material-icons'>share</i><span>Share</span> " +
                "</button>");

        }
        else{
            $('#publicModal').modal('show');
        }
    }

    var resourceId;
    var shareValue = "${params.share}";
    var emailMod="${params.fromEmail}";
    var shareLink,shareId;
    if(shareValue){
        shareLink="${params.name}";
        shareId="${params.resId}";
        playVideo(shareLink,shareId);
    }
    if(emailMod){
        shareLink="${params.resLink}";
        shareId="${params.resId}";
        playVideo(shareLink,shareId);
    }



    function addNotesFromMobile(){
        <%if(("android".equals(session["appType"]))){%>
        JSInterface.addNotes();
        <%} else{%>
        webkit.messageHandlers.addNotes.postMessage('');
        <%}%>
    }

    $(document).ready(function(){
        $("#addFolder").on("shown.bs.modal", function () {
            $("#folderName").focus();
        });
        $("#folderName").keyup(function () {
            $("#addFolderAlert").hide();
        });
    });


    $(window).on('load',function() {
        pageNo = 0;
        isAjaxCalled = false;
        <g:remoteFunction controller="resources" action="getUserFlashCards" params="'resType='+resType+'&pageNo='+pageNo"  onSuccess='flashCardSetsReceived(data);'/>
        $(window).scroll(function () {
            $(document).on('scroll', function () {
                if ($(this).scrollTop() >= $('#recentFlashCardSets').position().top + $('#recentFlashCardSets').outerHeight() - 450 && !isAjaxCalled) {
                    isAjaxCalled = true;
                    pageNo = pageNo + 1;

                    getPaginatedFlashcardData(resType, pageNo);
                }
            });
        });
    });
// show more
    function showMore() {
        var list = $('#flashCardSets .container-wrapper').size();

        if ($(window).width() >= 769) {
            if (list > 20) {
                $('.container-wrapper').hide();
                $('#flashCardSets .flashcards.all-container').find('.container-wrapper:lt(20)').show();
                $('.showLess').hide();
                $('.showMore').click(function (ev) {
                    $(ev.currentTarget).parent().find('.container-wrapper').show();
                    $('.showLess').show();
                    $(this).hide();
                });
                $('.showLess').click(function (ev) {
                    $(ev.currentTarget).parent().find('.container-wrapper').not(':lt(20)').hide();
                    $('.showMore').show();
                    $(this).hide();
                    $('html, body').animate({scrollTop: 0}, 'slow');
                });
            } else {
                $('.showMore, .showLess').hide();
            }
        } else if ($(window).width() <= 768) {
            if (list > 20) {
                $('.container-wrapper').hide();
                $('#flashCardSets .flashcards.all-container').find('.container-wrapper:lt(20)').show();
                $('.showLess').hide();
                $('.showMore').click(function (ev) {
                    $(ev.currentTarget).parent().find('.container-wrapper').show();
                    $('.showLess').show();
                    $(this).hide();
                });
                $('.showLess').click(function (ev) {
                    $(ev.currentTarget).parent().find('.container-wrapper').not(':lt(20)').hide();
                    $('.showMore').show();
                    $(this).hide();
                    $('html, body').animate({scrollTop: 0}, 'slow');
                });
            } else {
                $('.showMore, .showLess').hide();
            }
        } else {
            $('.showMore, .showLess').hide();
        }
    }


    //Paginated books
    function getPaginatedFlashcardData(resType, pageNo) {
        <g:remoteFunction controller="resources" action="getUserFlashCards" params="'resType='+resType+'&pageNo='+pageNo"  onSuccess='receivedPaginatedFlashcardData(data);'/>
    }

    function receivedPaginatedFlashcardData(data) {
        displayPaginatedFlashcardData(JSON.parse(data.latestFlashCards),true);
    }

    function displayPaginatedFlashcardData(flashcardNew,recent) {

        var htmlStrr="";

        if(flashcardNew == '' || flashcardNew == null || flashcardNew == 'null') {
            $('#infiniteLoading').fadeOut('slow');
            isAjaxCalled = true;
        } else {
            $('#infiniteLoading').html("<div class='text-center loading-div'><p>Scroll to Load</p><img src=\"${assetPath(src: 'ws/icon-scrolldown-arrow.gif')}\"></div>");
            isAjaxCalled = false;

            var cardLabel="";
            var labelBackgroundColor="";
            var cardColor="";
            var editAction="";
            var privacyLevel=null;
            var shareLink="";
            var actionString="";
            if(flashcardNew.length > 0 ){
                for(var i=0;i<flashcardNew.length;i++ ){

                    cardLabel="";
                    labelBackgroundColor="";
                    cardColor="";
                    editAction="";
                    privacyLevel=flashcardNew[i].privacyLevel;
                    shareLink="";
                    actionString="";

                    var color = "#" + "colors".split("").map(function(){return parseInt(Math.random()*0x10).toString(16);}).join("");

                    if(flashcardNew[i].resType=='Notes') {
                        cardLabel = "Notes";
                        cardColor = "blue";
                        if(privacyLevel == null){
                            editAction = "<a href='/resourceCreator/addNotes?resourceType=Notes&mode=edit&id=" + flashcardNew[i].resId + "'  class='set edit-resource'><i class=\"material-icons\">edit</i></a>\n";
                        }
                        shareLink = "javascript:shareNotes("+JSON.stringify(flashcardNew[i]).replace(/'/g,"&#39;")+")";
                        actionString = "<a href='/resources/notesViewer?resId=" + flashcardNew[i].resId + "&name=" + encodeURIComponent(flashcardNew[i].title).replace(/'/g,"&#39;") + "' class='chapter_txt' onclick='javascript:callAppLoader()'>\n";

                    }
                    else if(flashcardNew[i].resType=='KeyValues'){
                        cardLabel = "Flashcard";
                        cardColor = "yellow";
                        labelBackgroundColor = color;
                        if(privacyLevel == null){
                            editAction = "<a href='/resources/displayFlashCards?resId=" + flashcardNew[i].resId + "&name="+encodeURIComponent(flashcardNew[i].title).replace(/'/g,"&#39;")+"&mode=edit'  class='set edit-resource'><i class=\"material-icons\">edit</i></a>";
                        }
                        shareLink = "javascript:shareFlashCard("+JSON.stringify(flashcardNew[i]).replace(/'/g,"&#39;")+")";
                        actionString = "<a href='/resources/displayFlashCards?resId=" + flashcardNew[i].resId + "&name=" + encodeURIComponent(flashcardNew[i].title).replace(/'/g,"&#39;") + "' class='chapter_txt' onclick='javascript:callAppLoader()'>\n";

                    }
                    else if(flashcardNew[i].resType=='Reference Videos'){
                        cardLabel = "Video";
                        cardColor = "pink";
                        actionString = "<a href='javascript:playVideo(\"" + flashcardNew[i].resLink + "\"," + flashcardNew[i].resId + ")' id='resAction_" + flashcardNew[i].resId + "' class='chapter_txt'>\n";
                        shareLink = "javascript:shareNotes("+JSON.stringify(flashcardNew[i]).replace(/'/g,"&#39;")+")";}

                    else if(flashcardNew[i].resType=='Reference Web Links'){
                        cardLabel = "Link";
                        cardColor = "green";
                        if(!appDisplay&&(recent||"public"==privacyLevel)){
                            shareLink = "javascript:shareWeblink("+JSON.stringify(flashcardNew[i]).replace(/'/g,'&#39;')+")";
                        }
                        actionString = "<a  href='javascript:openWebRef(" + flashcardNew[i].resId + ",\""+flashcardNew[i].resLink.replace('#', ':')+"\")' id='resAction_"+flashcardNew[i].resId+"' class='chapter_txt' onclick='javascript:callAppLoader()'>\n";
                    }
                    else if(flashcardNew[i].resType=='Multiple Choice Questions'){
                        cardLabel = "MCQ";
                        cardColor = "violet";
                        if(privacyLevel == null){
                            editAction = " <a href='/wonderpublish/quizcreator?resourceType=Multiple%20Choice%20Questions&useType=quiz&mode=edit&id=" + flashcardNew[i].resId + "'  class='set edit-resource'><i class=\"material-icons\">edit</i></a>\n";
                        }
                        shareLink = "javascript:shareMCQ("+JSON.stringify(flashcardNew[i]).replace(/'/g,"&#39;")+")";
                        actionString = "<a href='/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId=" + flashcardNew[i].resId + "&name=" + encodeURIComponent(flashcardNew[i].title).replace(/'/g,"&#39;") + "' class='chapter_txt' onclick='javascript:callAppLoader()'>\n";

                    }

                    htmlStrr +=createResourceCard(flashcardNew[i].resId,flashcardNew[i].title,recent,editAction,privacyLevel,shareLink,actionString,flashcardNew[i].createdBy,flashcardNew[i].description,flashcardNew[i].canEdit,cardLabel,cardColor,labelBackgroundColor);

                    // htmlStrr.classList.add('fadein-animated')

                }
                $("#recentFlashCardSets").find(".all-container ").append(htmlStrr);
                $("#recentFlashCardSets").find(".all-container .container-wrapper ").addClass('fadein-animated');


            }
        }
    }
</script>
