<!DOCTYPE html>
<%@ page import="javax.servlet.http.Cookie" %>
<asset:stylesheet href="wonderslate/ebookDetailsNew.css" async="true"/>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<%if("true".equals(eBook) || "bookgpt".equals(bookType)){%>
<g:render template="eBookSection_new"></g:render>
<%}else{%>
<g:render template="printBookSection"></g:render>
<%}%>
<!-- something simple to compile also-->
