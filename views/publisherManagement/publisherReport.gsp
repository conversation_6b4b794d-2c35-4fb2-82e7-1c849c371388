<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chartist/0.11.4/chartist.min.css" crossorigin="anonymous" />

<script src="https://unpkg.com/ionicons@5.2.3/dist/ionicons.js"></script>
<style>
.chartist-tooltip {
    position: absolute;
    display: inline-block;
    opacity: 0;
    min-width: 5em;
    padding: .5em;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #CCC;
    border-radius: 5px;
    color: #000000;
    /*font-family: Arial,sans-serif;*/
    font-weight: 500;
    font-size: 80%;
    text-align: center;
    pointer-events: none;
    z-index: 1;
    -webkit-transition: opacity .2s linear;
    -moz-transition: opacity .2s linear;
    -o-transition: opacity .2s linear;
    transition: opacity .2s linear;
}
.chartist-tooltip:before {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    width: 0;
    height: 0;
    margin-left: -15px;
    border: 15px solid transparent;
    border-top-color: #FFFFFF;
    display: none;
}
.chartist-tooltip.tooltip-show {
    opacity: 1;
}
.ct-area, .ct-line {
    pointer-events: none;
}
.ct-series-b .ct-line, .ct-series-b .ct-point {
    stroke: #00A5A9;
}
.ct-labels .ct-label {
    color: #333;
    fill: #333;
}
.chart-titles h4 {
    font-size: 24px;
    background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
#topCourses h4 {
    font-size: 24px;
    background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-flex;
}
#topCourses table tr th {
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
}
#accessReport .card {
    display: block;
}
#courseAccessReport, #courseSalesAmountReport, #courseSalesNumberReport {
    position: relative;
}
#salesAmtReport .ct-series-a .ct-bar {
    stroke: #00A5A9;
    stroke-width: 30px;
}
#salesNoReport .ct-series-a .ct-bar {
    stroke: #FF7D4D;
    stroke-width: 30px;
}
#content-books .dflex_pub .form-control {
margin: 0;
}
#publishingInfo .card {
    -webkit-box-shadow: 0 1px 15px 1px rgba(62, 57, 107, 0.07);
    box-shadow: 0 1px 15px 1px rgba(62, 57, 107, 0.07);
    color: #000;
    border: none;
    overflow: hidden;
    border-radius: 10px;
}
#publishingInfo .card h6 {
    font-family: 'Poppins', sans-serif;
    color: #000;
    opacity: 0.7;
}
#publishingInfo .card h3 {
    color: #fff;
}
#publishingInfo .card h3#totalBooks {
    text-shadow: 2px 2px 1px #b66823;
}
#publishingInfo .card h3#publishedBooks {
    text-shadow: 2px 2px 1px #04766e;
}
#publishingInfo .card h3#unpublishedBooks {
    text-shadow: 2px 2px 1px #4E0096;
}
#publishingInfo .card.noofbooks {
    background: radial-gradient(142.42% 142.42% at 8.23% 0%, #F2C74C 0%, #F2994A 46.86%);
}
#publishingInfo .card.publishedbooks {
    background: radial-gradient(106.28% 106.45% at 0% 0%, #3ED8CF 0%, #068B82 99.48%);
}
#publishingInfo .card.publishedbooks ion-icon {
    transform: rotateZ(0);
}
#publishingInfo .card.unpublishedbooks {
    background: radial-gradient(142.42% 142.42% at 0% 0%, #DB8AFA 0%, #4E0096 99.48%);
}
#publishingInfo .card ion-icon {
    font-size: 5rem;
    opacity: 0.1;
    position: absolute;
    right: 0;
    top: 0rem;
    transform: rotateZ(45deg);
}
</style>
<div class="container-fluid publish-management" style="min-height: calc(100vh - 160px);">
    <div class='row'>
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group mpub">

                    <label for="publisherId" style="display: block;">Publisher Reports</label>
                    <div class="dflex_pub">
                        <g:select id="publisherId" class="form-control mr-3" optionKey="id" optionValue="name"
                                  name="publisherId" from="${publishers}" noSelection="['':'Select']"/>
                        <select id="noOfDays" name="noOfDays" class="form-control col-3 mr-3">
                        <option value="7">Last 7 days</option>
                        <option value="30">Last 30 days</option>
                    </select>
                        <button class="btn btn-lg btn-primary mr-2" onclick="getPublisherReports()">Get Report</button>



                    </div>

                </div>
                <div class="row mb-5 mt-4" id="publishingInfo" style="display: none">
                    <div class="col-md-4 col-12">
                        <div class="card pull-up noofbooks">
                            <div class="card-content">
                                <div class="card-body">
                                    <div class="media d-flex">
                                        <div class="media-body text-left">
                                            <h6>Number of eBooks</h6>
                                            <h3 id="totalBooks"></h3>
                                        </div>
                                        <div class="align-self-center">
                                            <ion-icon name="book"></ion-icon>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12">
                        <div class="card pull-up publishedbooks">
                            <div class="card-content">
                                <div class="card-body">
                                    <div class="media d-flex">
                                        <div class="media-body text-left">
                                            <h6>Published</h6>
                                            <h3 id="publishedBooks">431</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <ion-icon name="checkmark-done"></ion-icon>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12">
                        <div class="card pull-up unpublishedbooks">
                            <div class="card-content">
                                <div class="card-body">
                                    <div class="media d-flex">
                                        <div class="media-body text-left">
                                            <h6>Unpublished</h6>
                                            <h3 id="unpublishedBooks">137</h3>
                                        </div>
                                        <div class="align-self-center">
                                            <ion-icon name="document-text"></ion-icon>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="accessReport" class="mb-5" style="display: none">
                    <div class="chart-titles d-flex mb-3 justify-content-center">
                        <h4>Course Access Report</h4>
                    </div>
                    <ul class="d-flex text-center list-inline justify-content-center">
                        <li class="d-flex align-items-center mr-2">
                            <ion-icon name="ellipse" class="pr-1" style="color:#d70206;"></ion-icon>
                            <p>Library views</p>
                        </li>
                        <li class="d-flex align-items-center mr-2">
                            <ion-icon name="ellipse" class="pr-1" style="color:#00A5A9;"></ion-icon>
                            <p>Book previews</p>
                        </li>
                        <li class="d-flex align-items-center">
                            <ion-icon name="ellipse" class="pr-1" style="color:#f4c63d;"></ion-icon>
                            <p>Store views</p>
                        </li>
                    </ul>
                    <div id="emptyData" class="jumbotron" style="display: none;">
                        <div class="d-flex justify-content-center align-items-center">
                            <h4>No data available!</h4>
                        </div>
                    </div>
                    <div class="" id="courseAccessReport" style="display:none;"></div>
                </div>

                <div id="salesAmtReport" class="mb-5" style="display: none">
                    <div class="chart-titles d-flex mb-3 justify-content-center">
                        <h4>Course Sales Report</h4>
                    </div>
                    <ul class="d-flex text-center list-inline justify-content-center">
                        <li class="d-flex align-items-center mr-2">
                            <ion-icon name="ellipse" class="pr-1" style="color:#00A5A9;"></ion-icon>
                            <p>Sales amount</p>
                        </li>
                    </ul>
                    <div id="emptyData1" class="jumbotron" style="display: none;">
                        <div class="d-flex justify-content-center align-items-center">
                            <h4>No data available!</h4>
                        </div>
                    </div>
                    <div class="" id="courseSalesAmountReport" style="display: none;"></div>
                </div>

                <div id="salesNoReport" class="" style="display: none">
                    <ul class="d-flex text-center list-inline justify-content-center">
                        <li class="d-flex align-items-center mr-2">
                            <ion-icon name="ellipse" class="pr-1" style="color:#FF7D4D;"></ion-icon>
                            <p>Sales number</p>
                        </li>
                    </ul>
                    <div id="emptyData2" class="jumbotron" style="display: none;">
                        <div class="d-flex justify-content-center align-items-center">
                            <h4>No data available!</h4>
                        </div>
                    </div>
                    <div class="" id="courseSalesNumberReport" style="display: none;"></div>
                </div>

                <div  id="topCourses"></div>
            </div>

        </div>
        </div>
</div>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/chartist/0.11.4/chartist.min.js" crossorigin="anonymous"></script>
<script src="https://unpkg.com/chartist-plugin-tooltips@0.0.17/dist/chartist-plugin-tooltip.js"></script>
<script>
    function getPublisherReports(){
        if(document.getElementById("publisherId").value == ""){
            alert("Select Publisher")
        }else{
            $('.loading-icon').removeClass('hidden');
            $("#publishingInfo").hide();
            document.getElementById("topCourses").innerHTML = "";
            var publisherId = document.getElementById("publisherId").value;
            <g:remoteFunction controller="reports" action="getBooksCountForPublisher" params="'publisherId='+publisherId" onSuccess='updatePublishData(data)'></g:remoteFunction>
            <g:remoteFunction controller="reports" action="getBooksAccessReportForPublisher" params="'publisherId='+publisherId" onSuccess='updatePublisherReportData(data)'></g:remoteFunction>
            <g:remoteFunction controller="reports" action="getSalesReportForPublisher" params="'publisherId='+publisherId" onSuccess='updatePublisherSalesReportData(data)'></g:remoteFunction>
            <g:remoteFunction controller="reports" action="getSalesReportForTopCourses" params="'publisherId='+publisherId" onSuccess='updateSalesReportForTopCourses(data)'></g:remoteFunction>

        }

          }
    function updatePublishData(data){
        $('.loading-icon').addClass('hidden');
        $("#publishingInfo").show();
        document.getElementById("publishedBooks").innerText=data.publishCount;
        document.getElementById("unpublishedBooks").innerText=data.unpublishCount;
        document.getElementById("totalBooks").innerText = parseInt(data.publishCount)+parseInt(data.unpublishCount);
    }

    function updatePublisherReportData(data){
        $("#accessReport").css('display','block');
         var date;
        var results = data.results;

        var label = [];
        var librarySeries = [];
        var previewSeries = [];
        var storeSeries = [];
        var noOfDays = document.getElementById("noOfDays")[document.getElementById("noOfDays").selectedIndex].value;
        if(results.length>0){

            for(var i=noOfDays;i>=0;i--)
            {
                date = new Date();
                date.setDate(date.getDate() - i);

                var day = date.getDate();
                if(day<10) day = "0"+day;
                var month = date.getMonth()+1;
                if(month<10) month = "0"+month;
                label.push(day);
                var dateString = day+"/"+month+"/"+date.getFullYear();
                //library first
                var matchFound=false;
                for(var j=0;j<results.length;j++){
                    if(results[j].viewDate==dateString&&"library"==results[j].viewType){
                        matchFound = true;
                        librarySeries.push(results[j].viewCount);
                        break;

                    }
                }
                if(!matchFound) librarySeries.push(0);
                //check for preview views
                matchFound=false;
                for(var j=0;j<results.length;j++){
                    if(results[j].viewDate==dateString&&"preview"==results[j].viewType){
                        matchFound = true;
                        previewSeries.push(results[j].viewCount);
                        break;

                    }
                }
                if(!matchFound) previewSeries.push(0);

                //store views
                //check for preview views
                matchFound=false;
                for(var j=0;j<results.length;j++){
                    if(results[j].viewDate==dateString&&"store"==results[j].viewType){
                        matchFound = true;
                        storeSeries.push(results[j].viewCount);
                        break;

                    }
                }
                if(!matchFound) storeSeries.push(0);


            }

        }

        var data = {
            // A labels array that can contain any sort of values
            labels: label,
            // Our series array that contains series objects or in this case series data arrays
            series: [
                {meta:"Library views", data: librarySeries},
                {meta:"Book previews", data: previewSeries},
                {meta:"Store views", data: storeSeries}
            ]
        };

        var options = {
            //width: 800,
            height: 300,
            fullWidth: true,
            lineSmooth: true,
            chartPadding: {
                top: 20,
                right: 0,
                bottom: 30,
                left: 0
            },
            axisY: {
                onlyInteger: true
            },
            plugins: [
                Chartist.plugins.tooltip()
            ]
        };

        new Chartist.Line('#courseAccessReport', data,options);

        if(results.length>0) {
            $("#emptyData").hide();
            $("#courseAccessReport").show();

        } else {
            $("#emptyData").show();
            $("#courseAccessReport").hide();
        }

    }

    function updatePublisherSalesReportData(data){
        $("#salesAmtReport, #salesNoReport").css('display','block');
        var date;
        var results = data.results;

        var label = [];
        var amountSeries = [];
        var numberSeries = [];

        var noOfDays = document.getElementById("noOfDays")[document.getElementById("noOfDays").selectedIndex].value;
        if(results.length>0){

            for(var i=noOfDays;i>=0;i--)
            {
                date = new Date();
                date.setDate(date.getDate() - i);

                var day = date.getDate();
                if(day<10) day = "0"+day;
                var month = date.getMonth()+1;
                if(month<10) month = "0"+month;
                label.push(day);
                var dateString = day+"/"+month+"/"+date.getFullYear();
                //library first
                var matchFound=false;
                for(var j=0;j<results.length;j++){
                    if(results[j].poDate==dateString){
                        matchFound = true;
                        amountSeries.push(results[j].poAmount);
                        numberSeries.push(results[j].poCount)
                        break;

                    }
                }
                if(!matchFound) {
                    amountSeries.push(0);
                    numberSeries.push(0);
                }
            }

        }

        var data = {
            // A labels array that can contain any sort of values
            labels: label,
            // Our series array that contains series objects or in this case series data arrays
            series: [
                {meta:"Sales Amount", data: amountSeries}
                //amountSeries
            ]
        };

        var options = {
            height: 300,
            fullWidth: true,
            seriesBarDistance: 30,
            axisX: {
                showGrid:false
            },
            axisY: {
                onlyInteger: true
            },
            plugins: [
                Chartist.plugins.tooltip()
            ]
        };

        new Chartist.Bar('#courseSalesAmountReport', data,options);

        if(amountSeries.length>0) {
            $("#emptyData1").hide();
            $("#courseSalesAmountReport").show();

        } else {
            $("#emptyData1").show();
            $("#courseSalesAmountReport").hide();
        }

         data = {
            // A labels array that can contain any sort of values
            labels: label,
            // Our series array that contains series objects or in this case series data arrays
            series: [
                {meta:"Sales Number", data: numberSeries}
                //numberSeries
            ]
        };

        new Chartist.Bar('#courseSalesNumberReport', data,options);

        if(numberSeries.length>0) {
            $("#emptyData2").hide();
            $("#courseSalesNumberReport").show();

        } else {
            $("#emptyData2").show();
            $("#courseSalesNumberReport").hide();
        }

         // if(numberSeries.length>0) {
         //     new Chartist.Bar('#courseSalesNumberReport', data,options);
         // } else {
         //     var htmlStr = "<div class='jumbotron'>" +
         //         "<div class='d-flex justify-content-center align-items-center'>" +
         //         "<h4>No data available!</h4>" +
         //         "</div>" +
         //         "</div>";
         //     document.getElementById("courseSalesNumberReport").innerHTML = htmlStr;
         // }

    }

    function updateSalesReportForTopCourses(data){
        var results = data.results;
        var htmlStr = "<h4 class='mb-3 mt-5'>Top Selling Courses</h4><table class='table table-bordered'><thead><tr><th>Title</th><th>ISBN</th><th>Amount sold</th><th>Number of copies sold</th></tr></thead><tbody>";

        if(results.length>0) {
            for(var i=0;i<results.length;i++){
                htmlStr += "<tr><td>"+results[i].title+"</td><td>"+results[i].isbn+"</td><td>"+results[i].poAmount+"</td><td>"+results[i].poCount+"</td></tr>";
            }
        } else {
            htmlStr += "<tr class='text-center bg-light'><td colspan='3'>No records found!</td></tr>";
        }

        htmlStr +="</tbody></table>";
        document.getElementById("topCourses").innerHTML = htmlStr;
    }

</script>
