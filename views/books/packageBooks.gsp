<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js p-md-5 direct-sales">
    <div class="container">
        <div class="d-flex justify-content-center align-items-center mt-5 mt-md-0">
            <h3><strong>Package Books</strong></h3>
        </div>
        <form class="card card-modifier card-shadow border-0 col-md-8 mx-auto mt-4 p-4 p-lg-5" id="directSales" name="directSales">

            <div class="form-group col-md-10 mx-auto">
                <label>Package <span class="text-danger">*</span></label>
                <g:select id="bookId" class="form-control" optionKey="id" optionValue="title"
                          name="bookId" from="${subscriptionPackages}" noSelection="['':'Select']" onchange="javascript:getPackageBooks();"/>
            </div>
           <div id="packageBooksList" class="package-books">
            </div>
        </form>
    </div>

    </div>
</section>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>

    function getPackageBooks(){
        if(document.getElementById("bookId").selectedIndex==0){
            alert("Please select the book package to be added");
            document.getElementById("bookId").focus();
            $("#bookId").addClass("input-error");
        }else{

                //submit the form
                var bookId = document.getElementById("bookId")[document.getElementById("bookId").selectedIndex].value;
               <g:remoteFunction controller="wsshop" action="getPackageBooksDetail" params="'bookId='+bookId" onSuccess = "displayBooks(data);"/>
                $('.loading-icon').removeClass('hidden');

        }

    }

   function displayBooks(data){

       var books = JSON.parse(data.books);

       var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
       var noOfEbooks  = 0;
       var ebookHtmlStr="<h5 class='text-center'>Package contains: <strong>"+books.length+" eBooks</strong></h5><br><div class='d-block d-md-flex flex-wrap'>";
       var imgSrc = "";
       for(var i=0; i<books.length && i<=9; i++){
           imgSrc = books[i].coverImage;
           if (books[i].coverImage!=null && books[i].coverImage.startsWith("https")) {
               imgSrc = books[i].coverImage;
               imgSrc = imgSrc.replace("~", ":");
           } else {
               imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=webp";

           }
           ebookHtmlStr += "<div class='col-12 col-md-6 fadein-animated mb-4'>" ;


           ebookHtmlStr +=    "<div class='img-wrapper shadow'>";

           ebookHtmlStr += "<div class='bookShadow'>";

           if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
               ebookHtmlStr +="<div class='uncover'>"+
                   "<p>"+books[i].title+"</p>"+
                   "</div>";
           }
           else {
               ebookHtmlStr += "<img src='" + imgSrc + "' alt=''/>" ;
           }
           ebookHtmlStr +="</div>";

           ebookHtmlStr += "</div>" +
               "</div>";
           noOfEbooks++;


       }

       ebookHtmlStr += "</div>";
       document.getElementById('packageBooksList').innerHTML=ebookHtmlStr;

       $('.loading-icon').addClass('hidden');

   }
</script>
