<%if("1".equals(""+session["siteId"])){%>
<%if(!(("android".equals(session["appType"]))||("ios".equals(session["appType"])))){%>
<div class="db-main pt-3">
    <div class="container">
        <div class="mb-3 d-flex align-items-center more-options-title">
            <h5><strong>More Options</strong></h5>
        </div>
        <div class="card card-modifier card-shadow border-0 db-common-info db-general col-12 py-4 px-2 px-md-4 above-four-cards">
            <div class="row mx-0">
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="javascript:checkLoginAndProceed('/current-affairs');">
                        <asset:image src="/ws/icon-db-current-affairs.svg"></asset:image>
                        <p>Current Affairs</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="javascript:checkLoginAndProceed('/wsLibrary/myLibrary?mode=mybooks');">
                        <asset:image src="/ws/icon-db-my-books.svg"></asset:image>
                        <p>My Books</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="javascript:checkLoginAndProceed('/test-generator');">
                        <asset:image src="/ws/icon-create-test.svg"></asset:image>
                        <p>Create Test</p>
                    </a>
                </div>

                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="javascript:checkLoginAndProceed('/usermanagement/myTracker');">
                        <asset:image src="/ws/icon-db-trophy-room.svg"></asset:image>
                        <p>Trophy Room</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="javascript:checkLoginAndProceed('/books/myActivity');">
                        <asset:image src="/ws/icon-db-learning-history.svg"></asset:image>
                        <p>My Learning History</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="javascript:checkLoginAndProceed('/toDo/toDoListAndUpdate');">
                        <span class="position-relative todo-count" >
                            <asset:image src="/ws/icon-db-todo-list.svg"></asset:image>
                            <div id="todoLink">
                            <%if(session.getAttribute("userPendingTodoCount")!=null&&!"0".equals(session.getAttribute("userPendingTodoCount"))){%>
                            <small><%=session.getAttribute("userPendingTodoCount")%></small>
                            <%}%>
                        </div>
                        </span>
                        <p>My To-do List</p>
                    </a>
                </div>

                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="javascript:checkLoginAndProceed('/notes');">
                        <asset:image src="/ws/icon-db-notes.svg"></asset:image>
                        <p>My Notes</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="javascript:checkLoginAndProceed('/videos');">
                        <asset:image src="/ws/icon-db-videos.svg"></asset:image>
                        <p>Videos</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="javascript:checkLoginAndProceed('/tests');">
                        <asset:image src="/ws/icon-db-mcqs.svg"></asset:image>
                        <p>MCQs / Tests</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="javascript:checkLoginAndProceed('/flashCards');">
                        <asset:image src="/ws/icon-db-flashcards.svg"></asset:image>
                        <p>Flashcard</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/essays">
                        <asset:image src="/ws/ws-essays.svg"></asset:image>
                        <p>Essays</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="javascript:checkLoginAndProceed('/wonderslateWall');">
                        <asset:image src="/groups/groupwall.jpeg"></asset:image>
                        <%if(session.getAttribute("groupWallNotification")==null || (session.getAttribute("groupWallNotification")== true)){%>
                        <small></small>
                        <%}%>
                        <p>Cafe</p>
                    </a>
                </div>

            </div>
        </div>
    </div>
    <!-- Information centre -->
    <div class="container mt-5">
        <div class="db-section-title my-3">
            <h5 class="text-secondary text-secondary-modifier"><strong>Information Centre</strong></h5>
        </div>
        <div class="card card-modifier card-shadow border-0 db-common-info db-general db-publish col-12 py-4 px-2 px-md-4">
            <div class="row mx-0">

                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/exam-notifications">
                        <asset:image src="/ws/ws-examresults.svg"></asset:image>
                        <p>Exam Dates & Results</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/internships">
                        <asset:image src="/ws/ws-internships.svg"></asset:image>
                        <p>Internships</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/job-notifications">
                        <asset:image src="/ws/ws-jobs.svg"></asset:image>
                        <p>Job Notifications</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/scholarships">
                        <asset:image src="/ws/ws-scholarships.svg"></asset:image>
                        <p>Scholarships</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/teacher-vacancies">
                        <asset:image src="/ws/ws-teachervacancies.svg"></asset:image>
                        <p>Teacher Vacancies</p>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Fun & Learn -->
    <div class="container mt-5">
        <div class="db-section-title my-3">
            <h5 class="text-secondary text-secondary-modifier"><strong>Fun & Learn</strong></h5>
        </div>
        <div class="card card-modifier card-shadow border-0 db-common-info db-general db-publish col-12 py-4 px-2 px-md-4">
            <div class="row mx-0">
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/puzzles">
                        <asset:image src="/ws/ws-puzzles.svg"></asset:image>
                        <p>Puzzles</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">

                    <a href="/interesting-facts">
                        <asset:image src="/ws/ws-interestingfacts.svg"></asset:image>
                        <p>Interesting-facts</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/games">
                        <asset:image src="/ws/ws-games.jpeg"></asset:image>
                        <p>Games - Educational</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/jokes">
                        <asset:image src="/ws/ws-jokes.png"></asset:image>
                        <p>Jokes</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/english-gyan">
                        <asset:image src="/ws/ws-englishgyan.svg"></asset:image>
                        <p>English Gyan</p>
                    </a>
                </div>


                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/infographics">
                        <asset:image src="/ws/ws-infographics.jpeg"></asset:image>
                        <p>Infographics</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/academy/index">
                        <asset:image src="/ws/academy.png"></asset:image>
                        <p>Academy</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>


<script>

</script>
<%}
}%>
