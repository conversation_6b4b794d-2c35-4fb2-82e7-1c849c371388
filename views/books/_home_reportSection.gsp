<sec:ifLoggedIn>
    <div class="card card-modifier border-0 home-welcome home-report order-0">
        <div class="card-body">
            <h5 class="card-title text-black-50 mb-0">Hello, <strong><%= session["userdetails"]!=null?session["userdetails"].name:"" %>!</strong></h5>
            <div class="current-badge pt-1">
                <p>Badge : <strong>${currentBadge}</strong>
%{--                    <a tabindex="0" data-toggle="popover" data-trigger="focus" data-placement="bottom" title="Your Current Badge" data-content="To progress to next badge, learn and practice more!">--}%
%{--                        <i class="material-icons-round">info_outline</i>--}%
%{--                    </a>--}%
                </p>
            </div>
            <div class="seven-days-report mt-3">
                <p><strong><u>Last 7 days:</u></strong></p>
                <div class="d-flex flex-wrap flex-md-nowrap align-items-stretch pt-3">
                    <div class="flex-fill col">
                        <p class="pb-2" >Total Time: <br class="d-md-none"><strong id="totalTime"></strong></p>
                        <div id='pieChartReport' class="position-relative d-flex justify-content-center align-items-center mt-3" style="min-height: 150px;">
                            <span class="chart-loader"></span>
                        </div>
                    </div>
                    <div class="flex-fill col" id="lastSevenDaysPracticedTime">
                        <p class="pb-2">Practice Time: <br class="d-md-none"><strong id="practiceTime"></strong></p>
                        <p>Correct : <strong id="correct"></strong></p>
                        <p>Incorrect : <strong id="incorrect"></strong></p>
                        <p>Skipped : <strong id="skipped"></strong></p>
                    </div>
                </div>
                <div class="d-flex justify-content-end pt-3 progress-hyperlink">
                    <p>See full <a href="/progress/progressReport" class="btn btn-sm btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect ml-1">Progress Report</a></p>
                </div>
            </div>
        </div>
    </div>

</sec:ifLoggedIn>

<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

<script>
    $(function () {
        $('[data-toggle="popover"]').popover({
            trigger: 'focus'
        });
    });

    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }

    var subjectsChartElement = document.getElementById('pieChartReport');

    function secondsToMinutes(seconds){
        var minutes = Math.floor(seconds / 60);
        var extraSeconds = seconds % 60;
        if(extraSeconds>20) minutes++;
        return minutes
    }

    function secondsToDhms(seconds) {
        seconds = Number(seconds);
        var d = Math.floor(seconds / (3600*24));
        var h = Math.floor(seconds % (3600*24) / 3600);
        var m = Math.floor(seconds % 3600 / 60);
        var s = Math.floor(seconds % 60);

        var dDisplay = d > 0 ? d + (d == 1 ? " day, " : " days, ") : "";
        var hDisplay = h > 0 ? h + (h == 1 ? " hour, " : " hours, ") : "";
        var mDisplay = m > 0 ? m + (m == 1 ? " minute, " : " minutes, ") : "";
        var sDisplay = s > 0 ? s + (s == 1 ? " second" : " seconds") : "";
        return dDisplay + hDisplay + mDisplay + sDisplay;
    }

    var totalTimeSpent = ${totalTime};
    if(getCookie("pomodoroCurrentDuration"))
    {
        totalTimeSpent += Number(getCookie("pomodoroCurrentDuration"));
        document.getElementById("totalTime").innerHTML=secondsToDhms(totalTimeSpent);

    }else{
        document.getElementById("totalTime").innerHTML=secondsToDhms(${totalTime});
    }

    document.getElementById("practiceTime").innerHTML=secondsToDhms(${practiceTime});

    //now the correct,incorrect and skipped info
    var testAttempted = JSON.parse("${testAttemptInfo}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));

    document.getElementById("correct").innerHTML = testAttempted[0].correct == undefined?0:testAttempted[0].correct;
    document.getElementById("incorrect").innerHTML = testAttempted[0].incorrect == undefined?0:testAttempted[0].incorrect;
    document.getElementById("skipped").innerHTML = testAttempted[0].skipped == undefined?0:testAttempted[0].skipped;

    if('${practiceTime}' == 0 && testAttempted[0].correct == undefined && testAttempted[0].incorrect == undefined && testAttempted[0].skipped == undefined) {
        document.querySelector("#lastSevenDaysPracticedTime").innerHTML = "<p class='pb-4'>Practice Time : <b>0</b></p>" +
            "<div class='d-flex flex-column align-items-center justify-content-center' style='min-height: 80px;'>" +
            "<p>You have not attempted any quiz in last 7 days?</p>" +
            "<a id='practiceNowBtn' href='/prepjoy/dailyTest' class='btn btn-sm btn-light text-primary px-3 mt-2'>Practice now</a>" +
            "</div>";

        const practiceNowBtn = document.querySelector('#practiceNowBtn');
        practiceNowBtn.addEventListener('click', function () {
            practiceNowBtn.textContent = 'Loading...';
        });
    }

    //subjectwise graph
    var subjectwiseInfo = JSON.parse("${subjectwiseInfo}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));

    setTimeout(function () {
        drawPieChart(subjectwiseInfo);
    }, 5000);

    function drawPieChart(subjectwiseInfo) {
        var seriesData = [];
        var labelsData = [];

        for(var i=0;i<subjectwiseInfo.length;i++){
            seriesData.unshift(subjectwiseInfo[i].numberOfQuestions);
            labelsData.unshift(subjectwiseInfo[i].subject);
        }

        var options = {
            series: seriesData,
            chart: {
                width: 300,
                type: 'pie',
            },
            labels: labelsData,
            legend: {
                show: true,
                position: 'bottom',
                horizontalAlign: 'left',
                markers: {
                    width: 8,
                    height: 8
                },
                itemMargin: {
                    horizontal: 20,
                    vertical: 0
                },
                formatter: function(value, opts) {
                    const legendName = opts.w.globals.labels[opts.seriesIndex] + ' : <b>' + opts.w.config.series[opts.seriesIndex] + '</b>'
                    return [legendName];
                }
            },
            dataLabels: {
                enabled: true,
                formatter: function (value, opts) {
                    const subject = opts.w.globals.labels[opts.seriesIndex]
                    return [subject, opts.w.config.series[opts.seriesIndex]]
                },
                style: {
                    fontSize: '10px',
                    colors: ['#212121'],
                    fontWeight: '100',
                    fontFamily: 'inherit',
                },
                background: {
                    enabled: true,
                    foreColor: '#FFF',
                    padding: 4,
                    borderRadius: 2,
                    borderWidth: 1,
                    borderColor: 'transparent',
                    opacity: 0.9
                }
            },
            tooltip: {
                enabled: false
            },
            plotOptions: {
                pie: {
                    startAngle: 0,
                    endAngle: 360,
                    dataLabels: {
                        minAngleToShowLabel: 5,
                        offset: 10,
                    }
                }
            },
            responsive: [{
                breakpoint: 757,
                options: {
                    chart: {
                        width: 320
                    },
                    legend: {
                        show: true
                    }
                }
            }]
        };

        if(seriesData.length > 0) {
            var chart = new ApexCharts(subjectsChartElement, options);
            chart.render();
            document.querySelector('#pieChartReport .chart-loader').setAttribute('style', 'display: none');
        } else {
            subjectsChartElement.innerHTML = "<h6>No data available!</h6";
        }
    }


</script>
