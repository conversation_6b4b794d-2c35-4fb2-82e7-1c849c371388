
<g:render template="/funlearn/pnavheader"></g:render>


<!--<div>-->

<div class="container-fluid">
    <div class="row"><br>
        <div class="col-md-3 text-right">
            <h4 class="greytext robotoslab">EXPLORE BOOKS</h4>
        </div>
        <div class="col-md-2 col-centered">
            <select required id="selectedLevel" name="selectedLevel" class="form-control greytext" onchange="javascript:changeBoard(this.value)">
                <option value=""  selected>Select</option>
            </select>
        </div>
        <div class="col-md-2 col-centered">
            <select required id="selectedBoard" name="selectedBoard" class="form-control greytext" onchange="javascript:changeGrade(this.value)">
                <option value=""  selected>Board</option>

            </select>
        </div>
        <div class="col-md-2 col-centered">
            <select required id="selectedGrade" name="selectedGrade"  class="form-control greytext" onchange="javascript:changeSubject(this.value)">
                <option value="" selected >Grade</option>
            </select>
        </div>
        <div class="col-md-2 col-centered">
            <select required id="selectedSubject" name="selectedSubject" class="form-control greytext" onchange="javascript:changeTopic(this.value)">
                <option value=""  selected >Subject</option>
            </select>
        </div>
    </div>


    <br>
</div>
<div class="container-fluid" style="background-color: white">
    <div  class='row maincontent'>
        <div class="col-md-2"><br>

        </div>

        <div class='col-md-8 '>
            <div id="content-books">
                <BR>

                <div id="content-data-books" class="row  row-centered">


                </div><br><br>



                <br>
            </div>



        </div>

    </div>
</div>



<g:render template="/funlearn/footer"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js" async></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<script>
    function getBooksList(getBooksList){

        <g:remoteFunction controller="books" action="getBooksList"  onSuccess='displayBooks(data);'
                />
    }

    function displayBooks(data){
        var books = data.books;
        var htmlStr="";
        for(var i = 0; i <books.length; ++i){
            htmlStr+=" <div class='col-md-3'>" +
                    "<div><a href='book?bookId="+books[i].id+"'><img src='/funlearn/showImage?id="+books[i].id+"&fileName="+books[i].coverImage+"&type=books' height='150' class='boxifybook'></a></div>"+
                    "</div>";

        }
        document.getElementById("content-data-books").innerHTML=htmlStr;
        $("#content-books").css("display","block");
    }

    getBooksList();
    getTopicsMap('topic');
</script>

<%
    if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>

<asset:javascript src="analytics.js"/>
<% }%>


</body>
</html>