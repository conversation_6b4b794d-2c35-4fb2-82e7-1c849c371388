<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <%if(header!=null&&!"notadded".equals(header)){%>
    <%= header %>
    <%}else{%>
    <title><%= title!=null?title:"Next-gen Learning Platform - UPSC | JEE | NEET - Wonderslate"%></title>
    <meta name="description" content="Next-gen learning platform which offers Smart eBooks for competitive exams such as JEE, NEET, UPSC, Banking and much more. Learn, practice and analyze your preparation now!">
    <%}%>
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">

    <meta name="theme-color" content="#2EBAC6" />
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500&display=swap" rel="stylesheet">
    <asset:stylesheet href="landingpage/bootstrap.min.css" async="true"/>
    <asset:stylesheet href="wonderslate/header.css" async="true"/>
    %{--<link rel="apple-touch-icon" href="1.png">--}%
    <link rel="android-touch-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}"/>
    <link rel="windows-touch-icon" href="icon.png" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" async>


%{--    <asset:stylesheet href="landingpage/homepageStyle.css" async="true"/>--}%

    <asset:stylesheet href="landingpage/fonts/flaticon.css" async="true"/>
    %{--<link rel="stylesheet" href="smartbanner.css">--}%
    <asset:stylesheet href="landingpage/smartbanner.css" async="true"/>
    <asset:stylesheet href="landingpage/fonts/flaticon.css" async="true"/>
    <link rel="stylesheet" href="/assets/katex.min.css">

    %{--<link rel="stylesheet" type="text/css" href="../dist/smart-app-banner.css">--}%
<style>
@media only screen and (max-device-width: 767px) and (min-device-width: 320px) {

    iframe#drift-widget {
        top: 0 !important;
    }
}
@media only screen and (max-width: 767px) {
    .bar {
        margin-top: 0;
    }
    .bar ul.dropdown-menu  {
        margin-top: 33px;
        width: 300px;
    }
    .bar ul.dropdown-menu li a:last-child{
        color: black !important;
    }
}
@media only screen and (min-width: 768px) {
    .bar ul.dropdown-menu  {
        margin-top: 33px;
        width: 450px;
    }
    .bar ul.dropdown-menu li a:last-child{
        color: black !important;
    }
}
</style>
</head>
<script type='text/javascript' src='//platform-api.sharethis.com/js/sharethis.js#property=5c5823335ee7f900110ea418&product=inline-share-buttons' async='async'></script>
<body data-spy="scroll" data-target=".ws-header" data-offset="50" oncontextmenu="return false;">
<div class="body-background"></div>
<g:render template="/funlearn/mobileSignup"></g:render>

<%if(showHeader==null||"true".equals(showHeader)){%>
%{--<div class="container smartbannerwrapper">--}%
    %{--<h1><a href="#" onclick="run('ios')">ios</a></h1>--}%
    %{--<h1><a href="#" onclick="run('android')">android</a></h1>--}%
        %{--<h1><a href="#" onclick="run('windows')">windows</a></h1>--}%
%{--</div>--}%

    <header class="normalHeader">


    <div class="floaters">
        <div class="shaper">
            <img src="${assetPath(src: 'ws/shape.svg')}" alt="" class="shape">
            <img src="${assetPath(src: 'ws/shape1.svg')}" alt="" class="shape1">
        </div>

     <img src="${assetPath(src: 'ws/baloon.svg')}" alt="" class="baloon">
    </div>
    <nav class="ws-header container navbar navbar-expand-md navbar-default">
        <a class="navbar-brand" href="/books/store?mode=browse">
            <img class="logo" src="${assetPath(src: 'ws/WonderslateLogo.svg')}" alt="wonderslate">
            <img class="logotext" src="${assetPath(src: 'ws/Wonderslate.svg')}" alt="wonderslate">
        </a>
        <div class="d-sm-none mobile-profile">
            <%if(session["userdetails"]==null){%>
                <li class="nav-item" style="margin-right: 0.5rem;">
                    <a class="nav-link" onclick="loginOpen()">LOGIN</a>
                </li>
            <li class="nav-item">
                <a class="nav-link" onclick="signUpOpen()">SIGN UP</a>
            </li>
            <%}else{%>
                <li class="nav-item">
                    <a class="nav-link" class="dropdown-toggle" data-toggle="dropdown">
                        <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                        <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle">
                        <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle">
                        <%}%>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <div class="media p-3">
                            <a href="/creation/userProfile">
                                <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                                <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle drop-profile">
                                <%}%>
                                <a href="/creation/userProfile" class="edit-btn"><i class="material-icons">edit</i></a>
                            </a>
                            <div class="media-body">
                                <p class="user-name">Hello,<span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span></p>
                                <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                            </div>
                        </div>
                        <a class="dropdown-item order-pr" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
                        %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
                        <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
                    </div>
                </li>
            <%}%>
        </div>

        %{--<a class="mobile-toggle d-md-none" onclick="openNav()"><i class="material-icons">menu</i></a>--}%
        <ul class="navbar-nav mr-auto d-none d-md-flex">

%{--            <li class="nav-item">--}%
%{--                <a class="nav-link" href="#ws-ebooks">Why WS eBooks?</a>--}%
%{--            </li>--}%
        </ul>
        <ul class="navbar-nav right-menu d-none d-md-flex align-items-center mt-2 mt-md-0">
            <li class="nav-item active">
                <a class="nav-link" href="/store?mode=grade">Store</a>
            </li>
            <sec:ifNotLoggedIn>
                <li class="nav-item">
                    <a class="nav-link" onclick="loginOpen()">My Library</a>
                </li>
            </sec:ifNotLoggedIn>
            <sec:ifLoggedIn>
                <li class="nav-item">
                    <a class="nav-link" href="/library">My Library</a>
                </li>
                <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
                    <li class="nav-item dropdown active">
                        <a class="nav-link dropdown-toggle" href="#" id="publishing" data-toggle="dropdown">
                            Publishing
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="/publishing-desk">Publishing Desk</a>
                            <a class="dropdown-item" href="/wonderpublish/manageTabs">Manage Tabs</a>
                            <a class="dropdown-item" href="/wonderpublish/manageExams">Manage Exams</a>
                            <a class="dropdown-item" href="/youtube/index">Wonderslate Kids</a>
                            <a class="dropdown-item" href="/wonderpublish/bannerManagement">Banner Management</a>
                        </div>
                    </li>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_DIGITAL_MARKETER">
                    <li class="nav-item dropdown active">
                        <a class="nav-link dropdown-toggle" href="#" id="" data-toggle="dropdown">
                            Digital Marketing
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="/header/index">Header Management</a>
                            %{--                 <a class="dropdown-item" href="/wonderpublish/pubDesk?mode=print">Print books</a>--}%
                            %{--                 <a class="dropdown-item" href="/printbooks/printbooksmanagement">Print books Management</a>--}%
                        </div>
                    </li>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_FINANCE">
                    <li class="nav-item">
                        <a href="/publishing-sales" class="nav-link">Sales</a>
                    </li>
                </sec:ifAllGranted>
                <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_WS_CONTENT_ADMIN,ROLE_NOTIFICATION,ROLE_USER_LOGIN_RESET_MANAGER,ROLE_APP_ADMIN,ROLE_INSTITUTE_REPORT_MANAGER,ROLE_WEB_CHAT,ROLE_WS_EDITOR">

                    <li class="nav-item dropdown active">
                        <a class="nav-link dropdown-toggle" href="#" id="ad" data-toggle="dropdown">
                            Admin
                        </a>
                        <div class="dropdown-menu">
                            <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                                <a class="dropdown-item" href="/admin/priceList">Price List</a>
                            </sec:ifAllGranted>
                            <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                                <a class="dropdown-item" href="/admin/managePublishers">Publisher Management</a>
                            </sec:ifAllGranted>
                            <sec:ifAllGranted roles="ROLE_DELETE_USER">
                                <a class="dropdown-item" href="/admin/deleteuser">Delete User</a>
                            </sec:ifAllGranted>
                            <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                                <a class="dropdown-item" href="/institute/admin">Institute Management</a>
                            </sec:ifAllGranted>
                            <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_INSTITUTE_REPORT_MANAGER">
                                <a class="dropdown-item" href="/reports/instituteReport">Institute reports</a>
                            </sec:ifAnyGranted>
                            <sec:ifAllGranted roles="ROLE_PUBLISHER">
                                <a class="dropdown-item" href="/log/quizissues">Quiz Issues</a>
                            </sec:ifAllGranted>
                            <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                                <a class="dropdown-item" href="/log/notification">Notification</a>
                            </sec:ifAllGranted>
                            <sec:ifAllGranted roles="ROLE_USER_LOGIN_RESET_MANAGER">
                                <a class="dropdown-item" href="/log/userManagement">Force Logout</a>
                            </sec:ifAllGranted>
                            <sec:ifAllGranted roles="ROLE_USER_LOGIN_RESET_MANAGER">
                                <a class="dropdown-item" href="/log/migrateuser">Migrate User</a>
                            </sec:ifAllGranted>
                            <sec:ifAllGranted roles="ROLE_ACCESS_CONTROLL">
                                <a class="dropdown-item" href="/log/userAccess">User Access</a>
                            </sec:ifAllGranted>
                            <sec:ifAllGranted roles="ROLE_APP_ADMIN">
                                <a class="dropdown-item" href="/log/appVersionManagement">App Version Management</a>
                            </sec:ifAllGranted>
                            <sec:ifAllGranted roles="ROLE_WS_EDITOR">
                                <a class="dropdown-item" href="/wonderpublish/wseditor">WS Editor</a>
                            </sec:ifAllGranted>
                            <sec:ifAnyGranted roles="ROLE_WEB_CHAT,ROLE_WS_CONTENT_ADMIN">
                                <a class="dropdown-item" href="/comments/chatLogin">Live Web Chat</a>
                            </sec:ifAnyGranted>
                            <sec:ifAnyGranted roles="ROLE_WEB_CHAT,ROLE_WS_CONTENT_ADMIN">
                                <a class="dropdown-item" href="/comments/downloadWebChat">Download Web Chat</a>
                            </sec:ifAnyGranted>
                            <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                                <a class="dropdown-item" href="/log/notificationManagement">Notification Management</a>
                            </sec:ifAllGranted>
                        </div>
                    </li>
                </sec:ifAnyGranted>

            </sec:ifLoggedIn>
            <li class="nav-item active">
                <a class="nav-link" href="https://play.google.com/store/apps/details?id=com.wonderslate.wonderpublish&utm_source=signup%20mailer&utm_medium=email&utm_campaign=Wonderslate_App&utm_term=IITJEE" target="_blank">DOWNLOAD APP</a>
            </li>
        <sec:ifNotLoggedIn>
            <li class="nav-item">
                <a class="nav-link loginButton" onclick="loginOpen()">LOGIN</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" onclick="signUpOpen()">SIGN UP</a>
            </li>
        </sec:ifNotLoggedIn>
        <sec:ifLoggedIn>
            <li class="nav-item notification">
                <a class="nav-link" onclick=""><i class="material-icons">notifications</i></a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link" class="dropdown-toggle" data-toggle="dropdown">
                    <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                    <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle">
                    <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle">
                    <%}%>
                </a>
                <div class="dropdown-menu dropdown-menu-right">
                    <div class="media p-3">
                        <a href="/creation/userProfile">
                            <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                            <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle drop-profile">
                            <%}%>
                            <a href="/creation/userProfile" class="edit-btn"><i class="material-icons">edit</i></a>
                        </a>
                        <div class="media-body">
                        <p class="user-name">Hello,<span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span></p>
                            <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                        </div>
                    </div>
                    <a class="dropdown-item order-pr" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
                    %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
                    <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
                </div>
            </li>
        </sec:ifLoggedIn>
        </ul>
    </nav>
</header>
<%}%>



    <div class="modal fade" id="openApps">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                  <div class="customAppbg">

                  </div>
                <!-- Modal Header -->
                <div class="logoWs">
                <img src="${assetPath(src: 'landingpageImages/ws-tabicon.svg')}" width="46px" height="46px">
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <h4>There is more in the app</h4>
                    <p>Unlock the full Wonderslate learning experience</p>
                    <button onclick="javascript:visitPage();" class="btn get-app">Open in App</button>
                </div>

                <!-- Modal footer -->
                <div class="modal-footer justify-content-center">
                        <a class="" onclick="javascript:closeApp();">No thanks, let me continue.</a>
                 </div>

            </div>
        </div>
    </div>

<!--Signup-->
<script>
    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
</script>








