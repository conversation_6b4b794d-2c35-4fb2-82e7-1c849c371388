<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="katex.min.css"/>
<asset:stylesheet href="informationpage.css" />
<style>
.information #contents li table{
    overflow-x: scroll !important;
}
</style>
<script>
    var loggedIn=false;
    $('link[data-role="baseline"]').attr('href', '');
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="page-main-wrapper mdl-js information information-detail ebooks pb-5">
    <div class="container pt-4 pt-md-5 mt-3 mt-md-0">
        <div class="d-flex justify-content-start align-items-start page_title">
            <button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button>
            <div class="mdl-tooltip" data-mdl-for="goBack">Back</div>
            <h3 id="header"></h3>
            <div class="ml-2 bg-success bg-success-modifier rounded-circle d-flex justify-content-center align-items-center btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect share-ebooks-page" data-toggle="modal" onclick="javascript:openShareContentModalGeneric('Interesting ${params.resType} found. Please click on the link to see the ${params.resType}!','${request.getRequestURL()+"?"+request.getQueryString()}')">
                <i class="material-icons text-white">share</i>
            </div>
        </div>
        <div class="outer-showmoretab">
        <div class="mt-4 showmoretab" id="contents">
        </div>
        </div>


    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<g:render template="/resources/shareContent"></g:render>
<script src="/assets/katex.min.js"></script>
<script src="/assets/auto-render.min.js"></script>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>
<script>
    var selectedId='${params.id}';
    function getDetails() {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="admin" action="getInformationDetails"  onSuccess='displayInformation(data);'
         params="'id=${params.id}&resType=${params.resType}'" />
    }
    function displayInformation(data){
        var list = JSON.parse(data.infoDetails);
        var currentIndex=0;

         for(var i=0;i<list.length;i++){
             if(parseInt(selectedId)==parseInt(list[i].id)){
                 currentIndex=i;
             }
         }
        document.getElementById("header").innerHTML="<strong>"+list[currentIndex].title+"</strong>";
        var htmlStr = "";
        htmlStr += " <div class='row'><p class='col-12 created-date'>"+moment.utc(list[currentIndex].dateCreated ,'DD/MM/YYYY').local().format("Do MMMM YYYY")+"</p></div>\n";
        htmlStr +=     "<div class='row'><div class='col-12 info-description'> "+list[currentIndex].description+"</div></div>" ;

        if(list[currentIndex].answer){
            htmlStr +=     "<br><div class='row'><div class='col-12'><p class='answers'><b>Answer</b>: "+list[currentIndex].answer+"</p></div></div>";
        }
        if(list[currentIndex].videoLink){
            htmlStr +=     "<br><div class='row'><div class='col-12'><p><b>Video</b>: <a href='"+list[currentIndex].videoLink+"' target='blank'>"+list[currentIndex].videoLink+"</a></p></div></div>";
        }
        if(list[currentIndex].referenceLink){
            htmlStr +=     "<br><div class='row'><div class='col-12'><p><b>Web reference</b>: <a href='"+list[currentIndex].referenceLink+"' target='blank'>"+list[currentIndex].referenceLink+"</a></p></div></div>";
        }
        var previousTitle,previousLink,nextTitle,nextLink;
        if(list.length==2){
            if(currentIndex==1){
                nextTitle = list[0].title;
                nextLink = "/${params.pageName}/"+list[1].title.replaceAll(' ','-').replaceAll('\'','')+"?id="+list[0].id;
            }
            if(currentIndex==0){
                previousTitle = list[0].title;
                previousLink = "/${params.pageName}/"+list[1].title.replaceAll(' ','-').replaceAll('\'','')+"?id="+list[1].id;
            }
        }
        if(list.length>2){
                previousTitle = list[2].title;
                previousLink = "/${params.pageName}/"+list[2].title.replaceAll(' ','-').replaceAll('\'','')+"?id="+list[2].id;
            nextTitle = list[0].title;
            nextLink = "/${params.pageName}/"+list[0].title.replaceAll(' ','-').replaceAll('\'','')+"?id="+list[0].id;

        }
        htmlStr += "<br><div class='row'>";
        if(previousTitle) {
            htmlStr += "<div class='col text-left prev-info-link'><p><small>Previous</small></p><a href='" + previousLink + "'>" + previousTitle.slice(0, 50) + (previousTitle.length > 50 ? "..." : "") + "</a></div>";
        }
        if(nextTitle) {
            htmlStr +=  "<div class='col text-right next-info-link'><p><small>Next</small></p><a href='" + nextLink + "' >" + nextTitle.slice(0, 50) + (nextTitle.length > 50 ? "..." : "") + "</a></div>";
        }
        htmlStr += "</div>";
        document.getElementById("contents").innerHTML = htmlStr;
        $('.loading-icon').addClass('hidden');
        var anchors = document.querySelectorAll('.info-description a');
        for (var i=0; i<anchors.length; i++){
            anchors[i].setAttribute('target', '_blank');
        }
        shiftrow();
    }
    getDetails();

    function shiftrow(){
        $( "#contents" ).after( $( "div#contents .row:last-child" ) );
    }
</script>
</body>
</html>
