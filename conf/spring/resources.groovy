import com.wonderslate.usermanagement.HttpSessionServletListener
import org.springframework.security.core.session.SessionRegistryImpl
import org.springframework.security.web.authentication.session.ConcurrentSessionControlAuthenticationStrategy
import org.springframework.security.web.authentication.session.SessionFixationProtectionStrategy
import com.wonderslate.usermanagement.CustomSessionLogoutHandler

// Place your Spring DSL code here
beans = {

    sessionRegistry(SessionRegistryImpl)
    httpSessionServletListener(HttpSessionServletListener,ref('sessionRegistry'))
    customSessionLogoutHandler(CustomSessionLogoutHandler,ref('sessionRegistry'))

    concurrentSessionControlAuthenticationStrategy(ConcurrentSessionControlAuthenticationStrategy,ref('sessionRegistry')){
        exceptionIfMaximumExceeded = true
        maximumSessions = application.config.grails.appServer.maximumSessions
    }

    sessionFixationProtectionStrategy(SessionFixationProtectionStrategy){
        migrateSessionAttributes = true
        alwaysCreateSession = true
    }

}