<svg width="324" height="146" viewBox="0 0 324 146" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path d="M10 16C10 10.4772 14.4772 6 20 6H304C309.523 6 314 10.4772 314 16V132C252.338 97.4601 183.545 98.811 10 132V16Z" fill="url(#paint0_radial)"/>
</g>
<defs>
<filter id="filter0_d" x="0" y="0" width="324" height="146" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-25.9807 -62.3625) rotate(51.7644) scale(650.183 18987.9)">
<stop stop-color="#A9F286"/>
<stop offset="1" stop-color="#54CA1B"/>
</radialGradient>
</defs>
</svg>
