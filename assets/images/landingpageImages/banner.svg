<svg xmlns="http://www.w3.org/2000/svg" width="1020.79" height="951.921" viewBox="0 0 1020.79 951.921">
  <g id="Group_122" data-name="Group 122" transform="translate(-1316.673 -111.509)">
    <path id="Rectangle_2" data-name="Rectangle 2" d="M628.789,1003.165l-565.3,9.258c-125.7,9.489-31.778-26.622,10.689-197.623,27.115-110.239,8.922-42.37,29.087-119.706,45.845-110.676,84.517-136.134,84.238-135.916C324.924,411.064,213.915,485.953,309.582,316.6,411.353,111.463,513.009,65.9,860.355,116.281S540.906,236.521,574.946,239.332,628.789,1003.165,628.789,1003.165Z" transform="matrix(1, 0.017, -0.017, 1, 1378.405, 32.996)" fill="#f05a2a"/>
    <path id="Rectangle_2-2" data-name="Rectangle 2" d="M618.059,1003.165,62.741,1020.219c-123.673,9.489-32.114-34.419,9.667-205.42,26.677-110.239,8.778-42.37,28.617-119.706,45.1-110.676,83.151-136.134,82.877-135.915C319.1,411.063,209.883,485.951,304,316.6,404.13,111.462,521.623,53.2,863.359,103.58S531.592,236.52,565.082,239.332,618.059,1003.165,618.059,1003.165Z" transform="matrix(1, 0.017, -0.017, 1, 1334.066, 25.007)" fill="#f05a2a" opacity="0.602"/>
    <path id="Path_1" data-name="Path 1" d="M599.711,397.4v334.54a44.265,44.265,0,0,1-44.265,44.265H295.393a44.265,44.265,0,0,1-44.265-44.265V68.09a44.272,44.272,0,0,1,44.265-44.265H555.446A44.272,44.272,0,0,1,599.711,68.09Z" transform="translate(1467 247)" fill="#fff"/>
    <path id="Path_2" data-name="Path 2" d="M501.75,6.352H349.082a10.6,10.6,0,0,0-10.6,10.6V39.163a10.6,10.6,0,0,0,10.6,10.6H501.75a10.6,10.6,0,0,0,10.6-10.6V16.949A10.6,10.6,0,0,0,501.75,6.352Z" transform="translate(1547 247)" fill="#f3f3f3"/>
    <path id="Path_3" data-name="Path 3" d="M556.78,0H294.044A64.085,64.085,0,0,0,230,64.085v671.83A64.085,64.085,0,0,0,294.044,800H556.78a64.085,64.085,0,0,0,64.085-64.085V64.085A64.085,64.085,0,0,0,556.78,0Zm42.93,397.4V731.945a44.265,44.265,0,0,1-44.264,44.264H295.392a44.265,44.265,0,0,1-44.264-44.264V68.089a44.272,44.272,0,0,1,44.264-44.265H555.446A44.27,44.27,0,0,1,599.71,68.089Z" transform="translate(1467 247)" fill="#f3f3f3"/>
    <path id="Path_4" data-name="Path 4" d="M568.314,96.612H282.521V236.565H568.314Z" transform="translate(1467 247)" fill="#f3f3f3"/>
    <path id="Path_5" data-name="Path 5" d="M679.791,350.294H298.512v13.167H679.791Z" transform="translate(1467 247)" fill="#f3f3f3"/>
    <path id="Path_6" data-name="Path 6" d="M679.791,384.919H298.512v13.167H679.791Z" transform="translate(1467 247)" fill="#f3f3f3"/>
    <path id="Path_7" data-name="Path 7" d="M679.791,419.537H298.512V432.7H679.791Z" transform="translate(1467 247)" fill="#f3f3f3"/>
    <path id="Path_8" data-name="Path 8" d="M679.791,454.162H298.512v13.166H679.791Z" transform="translate(1467 247)" fill="#f3f3f3"/>
    <path id="Path_9" data-name="Path 9" d="M679.791,488.786H298.512v13.166H679.791Z" transform="translate(1427 247)" fill="#f3f3f3"/>
    <path id="Path_10" data-name="Path 10" d="M679.791,523.41H298.512v13.166H679.791Z" transform="translate(1427 247)" fill="#f3f3f3"/>
    <path id="Path_11" data-name="Path 11" d="M679.791,558.035H298.512V571.2H679.791Z" transform="translate(1427 247)" fill="#f3f3f3"/>
    <path id="Path_12" data-name="Path 12" d="M679.791,592.66H298.512v13.166H679.791Z" transform="translate(1427 247)" fill="#f3f3f3"/>
    <path id="Path_15" data-name="Path 15" d="M323.911,314.781H280.859v13.672h43.052Z" transform="translate(1427 247)" fill="#f3f3f3"/>
    <path id="Path_16" data-name="Path 16" d="M377.6,314.781H334.547v13.672H377.6Z" transform="translate(1427 247)" fill="#f3f3f3"/>
    <path id="Path_18" data-name="Path 18" d="M329.55,699.936a28.617,28.617,0,1,0-28.617-28.617A28.617,28.617,0,0,0,329.55,699.936Z" transform="translate(1427 247)" fill="#f3f3f3"/>
    <path id="Path_19" data-name="Path 19" d="M653.054,261.989H298.512v28.1H653.054Z" transform="translate(1427 247)" fill="#e2e2e2"/>
    <path id="Path_20" data-name="Path 20" d="M215.1,684.587l-14,18.7,16.684,12.495,14.005-18.7Z" transform="translate(1507 283)" fill="#de8e68"/>
    <path id="Path_21" data-name="Path 21" d="M186.924,724.292H161.488V740.86h25.436Z" transform="translate(1507 283)" fill="#de8e68"/>
    <path id="Path_22" data-name="Path 22" d="M109.211,409.619S57.817,585.13,57.817,588.688s143.444,120.78,146.1,120.78,13.7-21.412,13.7-25.134-34.145-31.89-34.145-31.89Z" transform="translate(1507 283)" fill="#56cad8"/>
    <path id="Path_23" data-name="Path 23" d="M187.947,724.657,203.9,406.741c-7.494,5.378-46.085,16.374-94.689,2.878,3.375,27.911,51.01,316.764,51.01,316.764h27.57Z" transform="translate(1507 283)" fill="#74d5de"/>
    <path id="Path_24" data-name="Path 24" d="M106.478,314.995c-6.43-3.52-74.7-48.679-85.872-48.679s-10.252,6.587-10.252,8.577,1.373,15.151,32.6,23.263c2.752,3.369,48.365,49.592,61.987,55.1C105.093,338.718,106.478,314.995,106.478,314.995Z" transform="translate(1507 283)" fill="#de8e68"/>
    <path id="Path_25" data-name="Path 25" d="M159.546,176.424c-84.758,0-54.99,226.678-50.336,233.2s91.125,10.97,96.44-5.586C209.17,393.076,227.263,176.424,159.546,176.424Z" transform="translate(1507 283)" fill="#fed385"/>
    <path id="Path_27" data-name="Path 27" d="M166.329,271.2c-.157,6.3,5.233,49.013,12.985,69.543-2.934,15.114-19.062,99.708-19.062,99.708l11.335,14.66s41.476-88.076,43.573-101.849-2.935-82.062-3.779-85.413S166.329,271.2,166.329,271.2Z" transform="translate(1507 283)" fill="#de8e68"/>
    <path id="Path_28" data-name="Path 28" d="M186.66,197.973c25.3-.554,32.413,66.672,28.149,72.642s-49.328,10.523-54.448,4.836S159.883,198.559,186.66,197.973Z" transform="translate(1507 283)" fill="#fed892"/>
    <path id="Path_29" data-name="Path 29" d="M167.342,143.319H139.174v44.259c0,5.667,28.168,5.334,28.168,0Z" transform="translate(1507 283)" fill="#d37c59"/>
    <path id="Path_30" data-name="Path 30" d="M173.126,124.165C174.559,99,163.856,77.932,149.22,77.1s-27.663,18.887-29.1,44.048,9.27,46.234,23.906,47.068S171.693,149.326,173.126,124.165Z" transform="translate(1507 283)" fill="#de8e68"/>
    <path id="Path_31" data-name="Path 31" d="M91.236,108.972c0-4.409,2.519-9.447,2.519-14.485,0-4.408-1.889-9.446-1.889-15.744C91.866,68.038,102.572,63,108.24,63c10.705,0,13.224,7.557,30.227,7.557,10.076,0,14.485-2.519,20.152-2.519,17.633,0,20.152,22.041,20.152,24.56,0,10.706,14.485,11.336,14.485,22.671,0,10.706-11.336,23.3-20.152,23.3s-17-8.187-17-10.706c0-5.668,1.26-6.927,1.26-10.706,0-13.224-28.817-16.373-33.226-16.373a79.844,79.844,0,0,0-3.929,20.782,28.093,28.093,0,0,1-10.076,1.889C106.98,123.456,91.236,119.677,91.236,108.972Z" transform="translate(1507 283)" fill="#5e2316"/>
    <path id="Path_32" data-name="Path 32" d="M168.382,146.757a6.613,6.613,0,1,0-6.612-6.612A6.612,6.612,0,0,0,168.382,146.757Z" transform="translate(1507 283)" fill="#de8e68"/>
    <path id="Path_33" data-name="Path 33" d="M187.444,738.984H160.409l-43.44,17.16v6.531h70.475Z" transform="translate(1507 283)"/>
    <path id="Path_34" data-name="Path 34" d="M230.682,695.7l-14.887,19.875-11.3,41.387,4.8,3.6L248.1,708.744Z" transform="translate(1507 283)"/>
    <path id="Path_13" data-name="Path 13" d="M638.683,652.713H373.419v13.166H638.683Z" transform="translate(1467 247)" fill="#f3f3f3"/>
    <path id="Path_14" data-name="Path 14" d="M665.845,680.393H373.419v13.166H665.845Z" transform="translate(1467 247)" fill="#f3f3f3"/>
    <path id="Path_17" data-name="Path 17" d="M434.893,314.781H391.842v13.672h43.051Z" transform="translate(1467 247)" fill="#f3f3f3"/>
    <path id="Path_35" data-name="Path 35" d="M453.443,457.915c-4,0-5.876,10.233-5.876,18.7a424.871,424.871,0,0,0,7.022,76.2c.863,0,1.738-.057,2.626-.057,12.041,0,21.8,3.382,21.8,7.557s-9.56,7.482-21.411,7.557c.182.832.359,1.657.547,2.482,11.607.17,20.877,3.482,20.877,7.557,0,3.633-7.413,6.669-17.281,7.387,21.317,82.61,62.075,147.82,89.563,178.218H587V733.43C586.987,680.966,496.644,457.915,453.443,457.915Z" transform="translate(1467 247)" fill="#56cad8"/>
    <g id="Group_107" data-name="Group 107" transform="translate(2 39)">
      <path id="Path_2955" data-name="Path 2955" d="M.8,12.089,44,0a4,4,0,0,1,4,4V72a4,4,0,0,1-4,4H4a4,4,0,0,1-4-4L.8,12.089C.8,9.88-1.413,12.089.8,12.089Z" transform="translate(1490.684 456.841) rotate(-30)" fill="#d5b994"/>
      <rect id="Rectangle_66" data-name="Rectangle 66" width="55" height="76" rx="4" transform="translate(1494.684 464.841) rotate(-30)" fill="#f5a435"/>
    </g>
  </g>
</svg>
