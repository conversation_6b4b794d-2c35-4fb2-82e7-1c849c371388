<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Font Reference - wonderslate</title>
    <link href="http://fonts.googleapis.com/css?family=Dosis:400,500,700" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="styles.css">
    <style type="text/css">html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td{margin:0;padding:0;border:0;outline:0;font-weight:inherit;font-style:inherit;font-family:inherit;font-size:100%;vertical-align:baseline}body{line-height:1;color:#000;background:#fff}ol,ul{list-style:none}table{border-collapse:separate;border-spacing:0;vertical-align:middle}caption,th,td{text-align:left;font-weight:normal;vertical-align:middle}a img{border:none}*{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}body{font-family:'Dosis','Tahoma',sans-serif}.container{margin:15px auto;width:80%}h1{margin:40px 0 20px;font-weight:700;font-size:38px;line-height:32px;color:#fb565e}h2{font-size:18px;padding:0 0 21px 5px;margin:45px 0 0 0;text-transform:uppercase;font-weight:500}.small{font-size:14px;color:#a5adb4;}.small a{color:#a5adb4;}.small a:hover{color:#fb565e}.glyphs.character-mapping{margin:0 0 20px 0;padding:20px 0 20px 30px;color:rgba(0,0,0,0.5);border:1px solid #d8e0e5;-webkit-border-radius:3px;border-radius:3px;}.glyphs.character-mapping li{margin:0 30px 20px 0;display:inline-block;width:90px}.glyphs.character-mapping .icon{margin:10px 0 10px 15px;padding:15px;position:relative;width:55px;height:55px;color:#162a36 !important;overflow:hidden;-webkit-border-radius:3px;border-radius:3px;font-size:32px;}.glyphs.character-mapping .icon svg{fill:#000}.glyphs.character-mapping input{margin:0;padding:5px 0;line-height:12px;font-size:12px;display:block;width:100%;border:1px solid #d8e0e5;-webkit-border-radius:5px;border-radius:5px;text-align:center;outline:0;}.glyphs.character-mapping input:focus{border:1px solid #fbde4a;-webkit-box-shadow:inset 0 0 3px #fbde4a;box-shadow:inset 0 0 3px #fbde4a}.glyphs.character-mapping input:hover{-webkit-box-shadow:inset 0 0 3px #fbde4a;box-shadow:inset 0 0 3px #fbde4a}.glyphs.css-mapping{margin:0 0 60px 0;padding:30px 0 20px 30px;color:rgba(0,0,0,0.5);border:1px solid #d8e0e5;-webkit-border-radius:3px;border-radius:3px;}.glyphs.css-mapping li{margin:0 30px 20px 0;padding:0;display:inline-block;overflow:hidden}.glyphs.css-mapping .icon{margin:0;margin-right:10px;padding:13px;height:50px;width:50px;color:#162a36 !important;overflow:hidden;float:left;font-size:24px}.glyphs.css-mapping input{margin:0;margin-top:5px;padding:8px;line-height:16px;font-size:16px;display:block;width:150px;height:40px;border:1px solid #d8e0e5;-webkit-border-radius:5px;border-radius:5px;background:#fff;outline:0;float:right;}.glyphs.css-mapping input:focus{border:1px solid #fbde4a;-webkit-box-shadow:inset 0 0 3px #fbde4a;box-shadow:inset 0 0 3px #fbde4a}.glyphs.css-mapping input:hover{-webkit-box-shadow:inset 0 0 3px #fbde4a;box-shadow:inset 0 0 3px #fbde4a}</style>
  </head>
  <body>
    <div class="container">
      <h1>wonderslate</h1>
      <p class="small">This font was created with<a href="http://fontastic.me/">Fontastic</a></p>
      <h2>CSS mapping</h2>
      <ul class="glyphs css-mapping">
        <li>
          <div class="icon icon-back"></div>
          <input type="text" readonly="readonly" value="back">
        </li>
        <li>
          <div class="icon icon-checkbox-deselected"></div>
          <input type="text" readonly="readonly" value="checkbox-deselected">
        </li>
        <li>
          <div class="icon icon-checkbox-selected"></div>
          <input type="text" readonly="readonly" value="checkbox-selected">
        </li>
        <li>
          <div class="icon icon-search-light"></div>
          <input type="text" readonly="readonly" value="search-light">
        </li>
        <li>
          <div class="icon icon-search-dark"></div>
          <input type="text" readonly="readonly" value="search-dark">
        </li>
        <li>
          <div class="icon icon-close"></div>
          <input type="text" readonly="readonly" value="close">
        </li>
        <li>
          <div class="icon icon-comment"></div>
          <input type="text" readonly="readonly" value="comment">
        </li>
        <li>
          <div class="icon icon-done"></div>
          <input type="text" readonly="readonly" value="done">
        </li>
        <li>
          <div class="icon icon-error-dark"></div>
          <input type="text" readonly="readonly" value="error-dark">
        </li>
        <li>
          <div class="icon icon-error-light"></div>
          <input type="text" readonly="readonly" value="error-light">
        </li>
        <li>
          <div class="icon icon-filter"></div>
          <input type="text" readonly="readonly" value="filter">
        </li>
        <li>
          <div class="icon icon-help"></div>
          <input type="text" readonly="readonly" value="help">
        </li>
        <li>
          <div class="icon icon-text-format"></div>
          <input type="text" readonly="readonly" value="text-format">
        </li>
        <li>
          <div class="icon icon-list"></div>
          <input type="text" readonly="readonly" value="list">
        </li>
        <li>
          <div class="icon icon-sort"></div>
          <input type="text" readonly="readonly" value="sort">
        </li>
        <li>
          <div class="icon icon-settings"></div>
          <input type="text" readonly="readonly" value="settings">
        </li>
        <li>
          <div class="icon icon-radio-selected"></div>
          <input type="text" readonly="readonly" value="radio-selected">
        </li>
        <li>
          <div class="icon icon-radio-deselected"></div>
          <input type="text" readonly="readonly" value="radio-deselected">
        </li>
        <li>
          <div class="icon icon-add"></div>
          <input type="text" readonly="readonly" value="add">
        </li>
        <li>
          <div class="icon icon-bookmark"></div>
          <input type="text" readonly="readonly" value="bookmark">
        </li>
        <li>
          <div class="icon icon-chevron"></div>
          <input type="text" readonly="readonly" value="chevron">
        </li>
        <li>
          <div class="icon icon-dropdown"></div>
          <input type="text" readonly="readonly" value="dropdown">
        </li>
        <li>
          <div class="icon icon-favorite"></div>
          <input type="text" readonly="readonly" value="favorite">
        </li>
        <li>
          <div class="icon icon-fullscreen"></div>
          <input type="text" readonly="readonly" value="fullscreen">
        </li>
        <li>
          <div class="icon icon-grid"></div>
          <input type="text" readonly="readonly" value="grid">
        </li>
        <li>
          <div class="icon icon-hamburger"></div>
          <input type="text" readonly="readonly" value="hamburger">
        </li>
        <li>
          <div class="icon icon-reload"></div>
          <input type="text" readonly="readonly" value="reload">
        </li>
        <li>
          <div class="icon icon-star-filled"></div>
          <input type="text" readonly="readonly" value="star-filled">
        </li>
        <li>
          <div class="icon icon-star"></div>
          <input type="text" readonly="readonly" value="star">
        </li>
        <li>
          <div class="icon icon-info-outline-black"></div>
          <input type="text" readonly="readonly" value="info-outline-black">
        </li>
        <li>
          <div class="icon icon-fontsize-less"></div>
          <input type="text" readonly="readonly" value="fontsize-less">
        </li>
        <li>
          <div class="icon icon-fontsize-zoom"></div>
          <input type="text" readonly="readonly" value="fontsize-zoom">
        </li>
        <li>
          <div class="icon icon-letter-spacing-decrease"></div>
          <input type="text" readonly="readonly" value="letter-spacing-decrease">
        </li>
        <li>
          <div class="icon icon-letter-spacing-increase"></div>
          <input type="text" readonly="readonly" value="letter-spacing-increase">
        </li>
        <li>
          <div class="icon icon-lineheight"></div>
          <input type="text" readonly="readonly" value="lineheight">
        </li>
        <li>
          <div class="icon icon-lineheight-1"></div>
          <input type="text" readonly="readonly" value="lineheight-1">
        </li>
      </ul>
      <h2>Character mapping</h2>
      <ul class="glyphs character-mapping">
        <li>
          <div data-icon="a" class="icon"></div>
          <input type="text" readonly="readonly" value="a">
        </li>
        <li>
          <div data-icon="b" class="icon"></div>
          <input type="text" readonly="readonly" value="b">
        </li>
        <li>
          <div data-icon="c" class="icon"></div>
          <input type="text" readonly="readonly" value="c">
        </li>
        <li>
          <div data-icon="d" class="icon"></div>
          <input type="text" readonly="readonly" value="d">
        </li>
        <li>
          <div data-icon="e" class="icon"></div>
          <input type="text" readonly="readonly" value="e">
        </li>
        <li>
          <div data-icon="f" class="icon"></div>
          <input type="text" readonly="readonly" value="f">
        </li>
        <li>
          <div data-icon="g" class="icon"></div>
          <input type="text" readonly="readonly" value="g">
        </li>
        <li>
          <div data-icon="h" class="icon"></div>
          <input type="text" readonly="readonly" value="h">
        </li>
        <li>
          <div data-icon="i" class="icon"></div>
          <input type="text" readonly="readonly" value="i">
        </li>
        <li>
          <div data-icon="j" class="icon"></div>
          <input type="text" readonly="readonly" value="j">
        </li>
        <li>
          <div data-icon="k" class="icon"></div>
          <input type="text" readonly="readonly" value="k">
        </li>
        <li>
          <div data-icon="l" class="icon"></div>
          <input type="text" readonly="readonly" value="l">
        </li>
        <li>
          <div data-icon="m" class="icon"></div>
          <input type="text" readonly="readonly" value="m">
        </li>
        <li>
          <div data-icon="o" class="icon"></div>
          <input type="text" readonly="readonly" value="o">
        </li>
        <li>
          <div data-icon="p" class="icon"></div>
          <input type="text" readonly="readonly" value="p">
        </li>
        <li>
          <div data-icon="q" class="icon"></div>
          <input type="text" readonly="readonly" value="q">
        </li>
        <li>
          <div data-icon="r" class="icon"></div>
          <input type="text" readonly="readonly" value="r">
        </li>
        <li>
          <div data-icon="s" class="icon"></div>
          <input type="text" readonly="readonly" value="s">
        </li>
        <li>
          <div data-icon="n" class="icon"></div>
          <input type="text" readonly="readonly" value="n">
        </li>
        <li>
          <div data-icon="t" class="icon"></div>
          <input type="text" readonly="readonly" value="t">
        </li>
        <li>
          <div data-icon="u" class="icon"></div>
          <input type="text" readonly="readonly" value="u">
        </li>
        <li>
          <div data-icon="v" class="icon"></div>
          <input type="text" readonly="readonly" value="v">
        </li>
        <li>
          <div data-icon="w" class="icon"></div>
          <input type="text" readonly="readonly" value="w">
        </li>
        <li>
          <div data-icon="x" class="icon"></div>
          <input type="text" readonly="readonly" value="x">
        </li>
        <li>
          <div data-icon="y" class="icon"></div>
          <input type="text" readonly="readonly" value="y">
        </li>
        <li>
          <div data-icon="z" class="icon"></div>
          <input type="text" readonly="readonly" value="z">
        </li>
        <li>
          <div data-icon="A" class="icon"></div>
          <input type="text" readonly="readonly" value="A">
        </li>
        <li>
          <div data-icon="C" class="icon"></div>
          <input type="text" readonly="readonly" value="C">
        </li>
        <li>
          <div data-icon="B" class="icon"></div>
          <input type="text" readonly="readonly" value="B">
        </li>
        <li>
          <div data-icon="D" class="icon"></div>
          <input type="text" readonly="readonly" value="D">
        </li>
        <li>
          <div data-icon="E" class="icon"></div>
          <input type="text" readonly="readonly" value="E">
        </li>
        <li>
          <div data-icon="F" class="icon"></div>
          <input type="text" readonly="readonly" value="F">
        </li>
        <li>
          <div data-icon="G" class="icon"></div>
          <input type="text" readonly="readonly" value="G">
        </li>
        <li>
          <div data-icon="H" class="icon"></div>
          <input type="text" readonly="readonly" value="H">
        </li>
        <li>
          <div data-icon="I" class="icon"></div>
          <input type="text" readonly="readonly" value="I">
        </li>
        <li>
          <div data-icon="J" class="icon"></div>
          <input type="text" readonly="readonly" value="J">
        </li>
      </ul>
    </div>
    <script>(function() {
  var glyphs, i, len, ref;

  ref = document.getElementsByClassName('glyphs');
  for (i = 0, len = ref.length; i < len; i++) {
    glyphs = ref[i];
    glyphs.addEventListener('click', function(event) {
      if (event.target.tagName === 'INPUT') {
        return event.target.select();
      }
    });
  }

}).call(this);

    </script>
  </body>
</html>