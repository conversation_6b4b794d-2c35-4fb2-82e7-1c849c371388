//=========================================
//              PARTIALS
//=========================================
@import 'partials/variables';
@import 'partials/icons';
body {
  font-family: $font-family-base;
  color: $black-color-dark;
  background-color: $background-color-light-grey;
  line-height: normal;
  font-weight: normal;
  font-size: $font-size-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
}
p,a,h1,h2,h3,h4,h5,h6 {
  color: $black-color-dark;
}
@import 'partials/loader';
@import 'partials/signin';
@import 'partials/signup';
@import 'partials/header';
@import 'partials/tabs';
@import 'partials/books';
@import 'partials/annotator';
@import 'partials/chaptersModal';
@import 'partials/footer';
@import 'partials/waves';
@import 'partials/book-preview';
@import 'partials/book-reviews';
@import 'partials/book-read';
@import 'partials/flash-card';
@import 'partials/mcq';
@import 'partials/book-quiz';
@import 'partials/additional-ref';
@import 'partials/book-quiz-modal';
@import 'partials/book-video';
@import 'partials/user-profile';
@import 'partials/purchase-success';
@import 'partials/publisher-books';
@import 'partials/error-page';
@import 'partials/answer-match';
@import 'partials/arihant';
@import 'partials/snackbar';
@import 'partials/ribbon';
@import 'partials/additional-assignment';
@import 'partials/slider/slider';
@import 'partials/slider/animate';
@import 'partials/slider/autoheight';
@import 'partials/slider/core';
@import 'partials/slider/lazyload';
@import 'partials/slider/theme.green';
@import 'partials/slider/theme.default';
@import 'partials/slider/theme';
@import 'partials/slider/video';
@import 'partials/slider/owl.carousel';
@import 'partials/slider/owl.theme.default';
@import 'partials/slider/owl.theme.green';

@import 'partials/responsive';

.loading-icon {
  width: 100%;
  height: 100%;
  background-color: $font-color-light-grey;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
}
.pagenumber-green {
  height: 21px;
  width: 35px;
  text-align: center;
  border-width: 1px;
  border-color: green;
  border-radius: 3px;
  border-style: solid;
  display: inline-block;
  padding: 0px;
  background-color: green;
  color: white;
}
.main {
  background-color: white;
  margin: 10px;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16), 0 2px 10px 0 rgba(0, 0, 0, .12);
  border-radius: 4px;
}
html, body {
  font-family: "Montserrat", sans-serif !important;
  color: #444444;
}
body {
  font-size: initial !important;
  line-height: initial !important;
  padding: 0 !important;
  text-align: initial !important;
}
h2{
  background: none !important;
  padding: 0;
}
.lining_box,.lining_box1{
  border:none !important;
}
* {
  margin:0;
  padding:0;
}

.chapter {
  text-align:inherit !important;
}

.image {
  text-align:center;
}

.subjectHead {
  text-align:inherit !important;
}

.readfind {
  color:inherit !important;
  margin:0 !important;
  padding:0 !important;
}

.glossaryText {
  color:inherit !important;
}

.author {
  text-align:inherit !important;
}
.bg
{
  background-color: inherit !important;
}
span.char-style-override-6 {
  color:inherit !important;
  font-size:inherit !important;
}
span.char-style-override-10 {
  color:inherit !important;
  font-family:inherit !important;
  font-size:inherit !important;
  font-style:inherit !important;
  font-weight:inherit !important;
}
img
{

}
.note
{
  font-style: inherit !important;

  font-size: inherit !important;

  color: inherit !important;

  text-align:inherit !important;
}
div.layout
{
  text-align: inherit !important;
}
div.chapter_pos
{
  text-align: inherit !important;

  width: inherit !important;

  position:static !important;

}

div.chapter_pos div

{

  background:none !important;

  padding:0 !important;
  line-height:0 !important;
  width:auto !important;
  margin:auto;
  opacity:1 !important;
}

div.chapter_pos div span

{

  font-size:inherit !important;

  color:inherit !important;

  font-weight:normal !important;

}
.footer

{

  display:inherit !important;

}
.cover_img_small
{
  width:auto !important;
}
@media only screen and (max-width: 767px) {

  div.chapter_pos

  {
    top:10%;
    font-size:1em;
  }
  div.chapter_pos div

  {
    width:100% !important;
  }
  .cover_img_small
  {
    width:auto !important;
  }
}
.underline_txt
{
  font-decoration:inherit !important;
}
.bold_txt
{
  font-weight:inherit !important;;
}
.center_element
{
  margin:inherit !important;;
}
.italics_txt
{
  font-style:inherit !important;;
}
.block_element
{
  display:inherit !important;;
}
.img_rt
{
  float:inherit !important;;
  clear:inherit !important;;
}
.img_lft
{
  float:inherit !important;;
}
table
{
  width:100%;
  border:none !important;
  border-collapse:collapse;
}
td
{
  padding:0 !important;
  border:1px solid #000;
  border-collapse:!important;
}

#prelims
{
  line-height:inherit !important;
}
#prelims .char-style-override-18
{
  font-weight:inherit !important;
}
#prelims .heading
{
  font-size: inherit !important;
  color:inherit !important;
}
#prelims .char-style-override-2
{
  font-style:inherit !important;
}
#prelims .subheading
{

  color:inherit !important;
}


h2{
  color: inherit !important;
  font-size: inherit !important;
  background: none !important;
  padding: 0 !important;
}
.eHeading {
  color:inherit !important;
  font-size: inherit !important;
  font-weight: inherit !important;
}
.ebox {
  border: none !important;
  padding: 0 !important;
}
.mt-4{
  margin-top:2rem;
}
.d-flex{
  display: flex;
}
.align-items-center{
  align-items: center;
}
.justify-content-between{
  justify-content:space-between;
}