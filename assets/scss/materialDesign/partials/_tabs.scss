.tabs-holder {
  background-color: $white-color;
  padding-left: 80px;
  padding-right: 80px;
  box-shadow: $tab-header-shadow;
}
.nav-tabs-wrapper {
  border-bottom: 0;
 > li {
    margin-left: 23px;
   > a {
      font-size: $font-size-large;
      font-weight: 500;
      text-align: center;
      color: $font-color-light-grey;
      background-color: transparent;
      padding: 10px 8px;
      border: 0;
      &:hover, &:focus, &:active {
        background-color: transparent;
        border: 0;
      }
    }
    &.active > {
      a {
        font-size: $font-size-large;
        font-weight: 500;
        text-align: center;
        color: $orange-color;
        background-color: transparent;
        border: 0;
        border-bottom: 5px solid $orange-color;
        &:hover, &:focus, &:active {
          color: $orange-color;
          background-color: transparent;
          border: 0;
          border-bottom: 5px solid $orange-color;
        }
      }
    }
    .level-label {
      font-weight: 500;
      font-size: $font-size-xl;
      color: $black-color-dark;
      padding: 10px 15px;
      padding-right: 32px;
      margin: 0;
    }
  }
}

.tab-sub-categories {
  padding-left: 80px;
  padding-right: 80px;
  margin-top: 16px;
}
.tab-sub-categories-wrapper {
  list-style: none;
  padding: 0;
  margin: 0;
}
.tab-sub-categories-item {
  display: inline-block;
  position: relative;
  margin-left: 23px;
  margin-bottom: 10px;
}
.tab-sub-categories-item-btn {
  display: block;
  font-weight: 300;
  font-size: $font-size-medium;
  text-align: center;
  color: $black-color-light-rgba;
  background-color: $white-color;
  border-radius: 4px;
  padding: 8px 35px;
  text-decoration: none;
  border: 1px solid rgba(68, 68, 68, 0.54);
  &:hover {
    color: $orange-color;
    text-decoration: none;
  }
  &.active {
    color: $white-color;
    background-color: $orange-color;
  }
}
.class-selection-div {
  padding-left: 24px;
  border-left: 2px solid $light-grey-border-color;
  .btn-group {
    width: 100%;
  }
  .class-selection-btn {
    min-width: 100%;
    font-weight: 500;
    font-size: $font-size-medium;
    text-align: left;
    background-color: $white-color !important;
    background: url('../images/wonderslate/caret.png');
    background-repeat: no-repeat;
    background-position: 95%;
    padding: 6px 16px;
    border: $button-border;
  }
  .btn-group.open {
    .class-selection-btn {
      background: url('../images/wonderslate/caret-open.png');
      background-repeat: no-repeat;
      background-position: 95%;
    }
    .class-selection-dropdown {
      width: 100%;
      > li {
      a {
        font-weight: 500;
        font-size: $font-size-medium;
        color: $black-color-dark;
      }
     }
    }
  }
}