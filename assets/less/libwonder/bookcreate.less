.create{
  margin-top: 43px;
  margin-bottom:30px;

  margin-left: 73px;
}
.create1{
  margin-left: 77px;
  margin-right: -15px;
}
#bookcover {
  span {
    color: grey;
    font-size: 14px;
  }
  .smallText {
    font-size: 13px;
    height: 150px;
    display: flex;
    align-items: center;
    margin-top: -31px;
  }
}
.form-group{
  padding-bottom: 0px;
  a{
    color:black;
  }
}
.authors a{
  color:black;
}
.cre{
  color: darkred;
}
.bootstrap-select > .dropdown-toggle.bs-placeholder{
  color: black;
  background-color: white;
  border: 1px solid lightgray;
}
.dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #333333;
  white-space: nowrap;
}
.buks {
  margin-left: 0px;
  margin-top: 10px;
  margin-bottom: 27px;
}