@import "_common";

.quizCreatorBulkInput{
.main{
  background-color: #F3F7FA !important;
  padding: 5rem;
  border: 1px solid #dee2e6 !important;
  box-shadow: 0 2px 10px #e9eef5 !important;
  #quizcreatorbulk{
    padding: 0 !important;
  }
  #quizcreatorbulk .row {
    padding: 0 !important;
  }
  textarea{
    height: 120px !important;
    resize: unset !important;
    border: 1px solid #B4CDDE;
  }
 .btn{
   width: 15%;
 }
  .red{
    margin-left: 5px !important;
  }
  .form-group {
    input{

      background: #fff;
      padding: 0.375rem 0.75rem;
      border-radius: 5px;
      border: 1px solid #B4CDDE;
      box-shadow: 0 2px 4px #ececfb;
      font-size: 14px;
      width: 100%;
    }
  }


  input[type="radio"]:checked + label {
    background-color: transparent !important;
  }
  .form-check-inline {
    margin-right: 1rem !important;
    .form-check-input {
      margin-right: 0 !important;
    }
  }
  .alertRow{
    margin:0;
    .alert{
      margin: 0;
    }
  }
.radioQuestion{
  display: flex;
p{
  margin: 0 1rem 0 0;
}
}

}
  @media @extraSmallDevices, @smallDevices {
    .main {
      padding: 3rem !important;
    }

    .btn {
      width: 100% !important;
    }

    .radioQuestion {
      display: block !important;
    }

  }
}