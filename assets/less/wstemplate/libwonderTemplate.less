/* Libwonder Template Styles */

// Components
@import "components/alerts.less";
@import "components/buttons.less";
@import "components/cards.less";
@import "components/data_tables.less";
@import "components/datepicker.less";
@import "components/dropdowns.less";
@import "components/form_elements.less";
@import "components/form_errors.less";
@import "components/modals.less";
@import "components/tables.less";
@import "components/typography.less";

// Admin
@import "admin/admin.less";

// Common
@import "common/preloader.less";
@import "common/page_common.less";

// Pages Styles
@import "pages/_books-list.less"; // Books list section (e.g. Store books)
@import "pages/_related-books.less"; // Related and best seller books section
@import "pages/mylibrary.less"; // My library page
@import "pages/access_code.less"; // Access code page
