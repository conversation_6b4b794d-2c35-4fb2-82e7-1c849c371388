@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Edit Profile Page Styles
.edit-profile {
  .profile{
    text-align: center;
    .image-wrapper{
      width: 85px;
      height:85px;
      background: @white;
      border: 3px solid @white;
      filter: drop-shadow(0px 0px 10px @gray-dark-shadow);
      position: relative;
      border-radius: 50%;
      margin: 0 auto;
      img{
        width: 80px;
        height:80px;
      }
      i{
        position: absolute;
        bottom:0;
        right: 0;
        width: 24px;
        height:24px;
        border-radius: 50%;
        font-size: 14px;
        color:@white;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @theme-primary-color;
      }
    }
    .user-name{
      #firstname{
        font-weight: @bold;
      }
      #secondname{
        font-weight: @regular;
      }
    }
    #grade-rating{
      i{
        background: @yellow;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        display: inline-block !important;
        font-size: 18px;
        margin-right: 5px;
      }
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .wonderwonk{
      color:@dark-gray;
      font-weight: @regular;
      font-size: 11px;
    }
  }
  .btn-makepoint{
    background: @white;
    box-shadow: 0 0 10px @gray-light-shadow;
    border-radius: 36px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: @blue;
    i{
      margin-left: 5px;
      font-size: 16px;
      color: @blue;
    }
  }

  .margin-reset-auto{
    margin: 0 auto;
  }
  form{
    label{
      color: @light-gray;
      font-size: 11px;
      font-style:italic;
    }
    input{
      &.form-control{
        border:1px solid @theme-primary-color;
      }
    }
    select{
      &.form-control{
        border:1px solid @theme-primary-color;
      }
    }
    .btn-submit{
      background: @warning-btn;
      box-shadow: 0 0 5px @gray-light-shadow;
      border-radius: 5px;
      color:@black;
      padding: 0.5rem;
      font-weight: @medium;
    }
  }
  .profile-wrapper{
    .profile{
      margin: 0 auto;
      .user-name{
        font-size: 24px;
        text-align: center;
        color: @theme-primary-color;
        display: inline-block !important;
      }
    }
  }
  #morePoints {
    background: none !important;
    .modal-dialog {
      height: 100%;
      margin: 0;
      max-width: 100%;
      h1,p{
        color:@white;
      }
      .modal-header{
        border:none;
        .close{
          font-weight: @regular;
          color:@white;
          text-shadow: unset;
          opacity: 1;
        }
      }
      .modal-content {
        height: 100%;
        background: @loader-color;
        border-radius: 0;
        .modal-body{
          h2{
            text-align: center;
            color:@white;
            font-weight: @bold;
          }
          p{
            text-align: center;
            font-size: 12px;
            strong{
              font-weight: @bold;
            }
          }
          ul{
            margin-top: 2rem;
            padding: 0;
            li{
              text-align: center;
              color:@white;
              font-size: 10px;
              list-style-type: none;
              display: flex;
              align-items: center;
              justify-content: center;
              i{
                background: @yellow;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                display: inline-block !important;
                font-size: 16px;
              }
              span{
                display: block;
                margin-left: 10px;
              }
            }
          }
        }
      }
      .modal-footer{
        border:none;
      }
    }
  }
  .carousel-item{
    h2,p{
      color:@white;
    }
    &.tutor{
      .circle{
        width: 26.25px;
        height: 26.25px;
        border-radius: 50px;
        border:1px solid @white;
        display: flex;
        align-items: center;
        justify-content: center;
        i{
          color:@white;
          font-size: 16px;
        }
      }
      .btn-answer{
        background: @white;
        border: 1.25px solid @theme-primary-color;
        box-sizing: border-box;
        border-radius: 5px;
        color:@theme-primary-color;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        i{
          margin-right: 10px;
        }
      }
      h2{
        font-size: 36px;
        margin-bottom: 1rem;
      }
      p{
        font-size:14px;
        color:@white;
      }
      .wonderwonk{
        color:@white;
        font-size: 12px;
      }
      #grade-rating{
        display: flex;
        margin-right: 5px;
        i{
          background: @yellow;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          display: inline-block !important;
          font-size: 16px;
        }
      }
    }
  }
}
