@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Contact Page Styles
.contact-us {
  .container {
    max-width: 900px;
  }
  .contact-address {
    flex-direction: row;
    margin: 0 auto;
  }
  .address-info {
    flex-direction: column;
    border-left: 1px solid lighten(@light-gray, 30%);
    h5 {
      font-size: 15px;
      color: @light-gray;
    }
    address {
      font-size: 12px;
      color: @light-gray;
      @media @extraSmallDevices {
        font-size: 12px;
      }
      span {
        font-size: 13px;
        padding-top: 7px;
        display: inline-block;
      }
      a {
        color: @theme-primary-color;
        font-size: 12px;
        &:hover {
          text-decoration: underline;
        }
      }
    }
    .whatsapp-link {
      font-size: 12px;
      color: @light-gray;
      margin-top: -0.5rem;
      align-items: center;
      span {
        padding-right: 3px;
      }
      p {
        font-size: 12px;
        align-items: center;
        padding-left: 3px;
        @media @extraSmallDevices,@smallDevices {
          padding-top: 3px;
          padding-left: 0;
        }
      }
      a {
        padding-right: 3px;
        color: @green;
        span {
          padding-right: 0;
        }
      }
    }
  }
}
#drift-frame-controller {
  @media @extraSmallDevices {
    bottom: 115px !important;
  }
  @media @smallDevices {
    bottom: 75px !important;
  }
}