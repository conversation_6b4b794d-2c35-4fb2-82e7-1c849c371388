@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// My Materials Page Styles
.my-materials {

.media{
  border-radius: 10px !important;
}
  .box{
    min-height: 100px !important;
    height: 100% !important;
    border-radius: 10px 0px 0px 10px !important;
  }
  .cardLabel{
    writing-mode: vertical-lr;
    text-orientation: mixed;
    transform: rotate(
            180deg
    );
    padding: 12px;
    text-align: center;
    color: 	#FFFFFF !important;
    letter-spacing: 2px;
    font-weight: 800 !important;
  }
  .align-self-center{
    transform: rotate(
            270deg
    );
    margin-top: 5px !important;
    margin-left: 5px !important;
  }
.reset-switch{
  padding: 10px !important;
}


  #favButton, #allButton {
    font-size: 12px;
    i {
      font-size: 12px;
    }
  }
  .new-folder {
    font-size: 12px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    @media @extraSmallDevices {
      padding: .5rem;
    }
    i {
      margin-right: 10px;
      @media @extraSmallDevices {
        margin-right: 5px;
      }
    }
  }
  #search-book, .submit-search-btn {
    height: 34px;
    opacity: 1;
    i {
      font-size: 18px;
    }
    &:disabled {
      i {
        opacity: 0.5;
      }
    }
  }
  .folder-box {
    @media @extraSmallDevices {
      &:nth-child(even) {
        padding-right: 0;
      }
    }
    .inside-folder-text {
      border: 1px solid @theme-primary-color;
      box-shadow: 0 0 10px @gray-light-shadow;
      border-radius: 5px;
      background: @white;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: start;
      width: 100%;
      font-size: 14px;
      padding: 0 10px;
      text-align: left;
      &:hover {
        box-shadow: none;
      }
      i {
        color: @theme-primary-color;
      }
      span {
        display: block;
        width: 75%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 12px;
        text-transform: capitalize;
        color: @black;
        padding: 7px;
        font-weight: @regular;
      }
      span.mdl-button__ripple-container {
        width: 100%;
      }
    }
  }
  .materialsAddDropdown {
    button {
      background: @theme-primary-color;
      color: @white;
      box-shadow: 0 0 10px @gray-light-shadow;
      border-radius: 5px;
      font-size: 12px;
      width: 90px;
      height: 36px;
      font-weight: @regular;
      margin-left: 1rem;
      &:hover{
        color:@white;
        box-shadow: 0 0 10px @gray-light-shadow;
      }
      i {
        color: @white;
        font-size: 18px;
        transition: all 0.2s linear;
        -webkit-transition: all 0.2s linear;
        -moz-transition: all 0.2s linear;
        &.rotated {
          transform: rotate(45deg);
          -webkit-transform: rotate(45deg);
          -moz-transform: rotate(45deg);
          -ms-transform: rotate(45deg);
        }
      }
      &:after {
        margin-left: .5em;
        transition: all .2s linear;
        -webkit-transition: all .2s linear;
        -moz-transition: all .2s linear;
      }
    }
    &.show {
      z-index: 9992;
      button {
        background: #000000;
        &:after {
          transform: rotate(180deg);
          -webkit-transform: rotate(180deg);
          -moz-transform: rotate(180deg);
          -ms-transform: rotate(180deg);
        }
      }
    }
    .dropdown-menu {
      padding: 0;
      margin: 0;
      background: none;
      border: none;
      a.dropdown-item {
        margin: 5px 0;
        padding: .375rem .75rem;
        width: 100%;
        justify-content: flex-start;
        transition: all .2s linear;
        -webkit-transition: all .2s linear;
        -moz-transition: all .2s linear;
        &:hover {
          margin-left: 5px;
        }
      }
      a.new-folder {
        //background: @theme-primary-color;
        //color: @white;
        box-shadow: 0 0 10px @gray-light-shadow;
        border-radius: 5px;
        font-weight: normal;
      }
    }
  }
  .add-resource-btn {
    width: 80px;
    margin-left: 1rem;
    i {
      margin-right: 0;
    }
  }
  #flashCardSets {
    .all-container .container-wrapper {
      width: auto;
      padding-top: 10px !important;
    }
  }
  .resource-set {
    .media {
      min-height: 100px;
      align-items: unset !important;
      p {
        font-size: 12px !important;
      }
      .mymaterial-img {
        a {
          min-height: auto;
        }
      }
    }
    &:hover {
      .media {
        box-shadow: 0 0 10px @gray-dark-shadow;
      }
    }
    h5.title {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      a.chapter_txt {
        min-height: auto;
      }
    }
    .resource-actions {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .share-resource-btn {
        background: none;
        border: none;
        text-align: center;
        padding: 0;
        &:hover {
          i {
            color: @green;
          }
        }
      }
      a {
        margin-left: 7px;
      }
      i {
        font-size: 18px;
        color: lighten(@light-gray, 20%);
        &:hover{
          cursor: pointer;
        }
        &.active{
          color: @green;
        }
      }
      .active {
        i {
          color: @green;
        }
      }
      a.favorite-resource {
        &:hover {
          i {
            color: @green;
          }
        }
      }
    }
    .heart {
      animation: heartbeat 0.5s;
      -webkit-animation: heartbeat 0.5s;
      -moz-animation: heartbeat 0.5s;
    }
    .chapter_txt {
      min-height: 35px;
      display: block;
    }
    .set_public_btn {
      .public-text {
        font-size: 10px;
      }
      .toggleSwitch .switch {
        width: 25px;
        height: 13px;
      }
      .toggleSwitch .slider:before {
        height: 11px;
        width: 10px;
      }
      .toggleSwitch input:checked + .slider:before {
        -webkit-transform: translateX(12px);
        -ms-transform: translateX(12px);
        transform: translateX(12px);
      }
    }
    .delete-resource {
      &:hover {
        i {
          color: @red;
        }
      }
    }
    .edit-resource {
      &:hover {
        i {
          color: @blue;
        }
      }
    }
  }
  .resource-empty-msg {
    font-size: 12px;
    font-weight: @medium;
    color: @light-gray;
  }
}

#overlay-bg {
  background-color: rgba(0, 0, 0, 0.8);
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 9991;
  opacity: 0;
  visibility: hidden;
  transition: all .2s linear;
  -webkit-transition: all .2s linear;
  -moz-transition: all .2s linear;
}

#videoModal {
  .modal-body-modifier {
    iframe {
      @media @extraSmallDevices and (orientation : portrait), @smallDevices and (orientation : portrait) {
        height: 400px;
      }
      @media @extraSmallDevices and (orientation : landscape), @smallDevices and (orientation : landscape) {
        height: auto;
      }
    }
  }
}

// After changed the modal - remove this
.web-url,.video-url{
  .modal{
    .modal-header{
      display: none;
    }
    .modal-content{
      width: 100%;
    }
    .modal-body{
      form{
        margin-top: 0;
      }
      input{
        width: 100%;
        border-bottom: 1px solid rgba(68, 68, 68, 0.48);
        padding: 10px;
        margin-bottom: 1rem;
      }
    }
    .modal-footer{
      border-top: none;
      button{
        background: transparent;
        &.cancel{
          color: rgba(68, 68, 68, 0.48);
          text-transform: capitalize;
        }
        &.saveLink{
          color:@theme-primary-color;
          font-size: 14px;
          font-weight: 700;
        }
      }
    }
  }
}
#addVideo,#webForm {
  .mdl-textfield{
    width: 100%;
  }
}
#webForm{
  .mdl-textfield{
    padding: 14px 0;
  }
}
.video-url #videoForm input {
  margin: 0.5rem auto;
  padding: 0.3rem;
}
#videoForm .is-focused {
  padding: 20px 0 !important;
}