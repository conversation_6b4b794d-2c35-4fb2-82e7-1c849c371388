@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Typography Styles
html, body,
h1, h2, h3, h4, h5, h6,
p, li, a,
input, textarea, select, label, span, small,
button,
th, td, dl, dt, dd,
address {
  font-family: @primary-font;
}

h1, h2, h3, h4, h5, h6 {
  color: @black;
}
h1, h2, h3, h4, p {
  margin: 0;
  padding: 0;
}

a:hover, a:active {
  text-decoration: none;
}

.material-icons {
  font-family: 'Material Icons' !important;
}

.text-primary-modifier {
  color: @theme-primary-color !important;
}
a.text-primary-modifier {
  &:hover, &:focus {
    color: @theme-primary-color !important;
  }
}

.text-secondary-modifier {
  color: @gray !important;
}

.text-danger-modifier {
  color: @red !important;
}
.text-success-modifier {
  color: @green !important;
}

.bg-primary-modifier {
  background-color: @theme-primary-color !important;
}

.bg-success-modifier {
  background-color: @success-btn !important;
}

.bg-warning-modifier {
  border-color: @warning-btn !important;
}

.border-primary-modifier {
  border-color: @theme-primary-color !important;
}

.rounded-modifier {
  border-radius: 5px !important;
}
