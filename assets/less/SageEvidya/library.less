@import "variables/color.less";
@import "variables/font.less";

.libraryDimension{
  min-height: calc(100vh - 175px);
  ul.nav{
    display: block;
  }
  .tab-content{
    background: #ffffff;
    padding: 1rem;
    margin: 1rem;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    border-radius: 6px;
    min-height: calc(100vh - 190px);
  }
  h4{
    color:rgba(35,35,35,1);
    font-size: 14px;
    padding: 0 1rem ;
    margin-top: 2rem;
    font-family: @sageFont;
    display: flex;
    img{
      margin-left: 1rem;
    }
  }
  h3{
    color:rgba(119,119,119,1);
    padding: 0 2rem;
    font-family: @sageFont;
    font-weight: @mediumWeight;
    font-size: 14px;
  }
  p{
    padding: 0.5rem 2rem;
    color:rgba(136,136,136,1);
    font-size: 14px;
    img{
      margin-right: 5px;
    }
  }

  .nav-tabs .nav-link{
    border-radius: 0px;
  }
  .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active{
    background:rgba(31, 65, 153, 0.15);
  }
  .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link:focus{
    background:rgba(31, 65, 153, 0.15);
  }
  .nav-tabs{
    border:none;
    border-radius:0px;
    border-top:1px solid rgba(119,119,119,0.1);
    li{
      border-radius: 0px;
      border: none;
      a{
        border: none;
        font-size: 18px;
       &.nav-link{
         padding: 1rem 1.5rem;
       }
        img{
          margin-right: 1rem;
          width: 24px;
          height: 24px;
        }
      }
    }
  }
}
.bgBlue{
  background:rgba(31,65,153,0.15);
}
.bgWhite{
  background: #ffffff;
}
.container-wrapper{
  border-bottom: 1px solid rgb(222,227,240);
  padding-bottom: 0.5rem;
  h3{
     font-size: 16px;
    color:rgba(119,119,119,1);
    font-weight: bold;
    font-family: @sageFont;
  }
  p{
     color:#ffffff;
    background: @sageTheme;
    border-radius: 4px;
    padding: 5px 10px;
    margin: 0;
    font-family: @sageFont;
  }
  select{
    border:none;
    background: none;
    color:rgba(119,119,119,1);
    font-family: @sageFont;
  }
  input.search{
  border-radius: 50px;
    width: 300px;
    color:@sageTheme;
    font-size: 16px;
    background: rgb(222,227,240);
    padding: 5px 10px;
    outline:0;
    font-family: @sageFont;
  }
}
.searchBtn{
  background: none;
  border:none;
  margin-left: -3rem;
  position: relative;
  top: 6px;
  i{
    color:@sageTheme;
  }
}
.bookContainer{
  >div{
    &:first-child{
      width: 18%;
    }
    &:nth-child(2){
      width: 65%;
    }
    &:nth-child(3){
      width:17%;
      text-align: center;
    }
  }
}
.bookContainer {
  .addLibrary {
    a {
      color: #233982;
      align-items: center;
      font-weight: bold;

      i {
        margin-right: 8px;
        color: #233982;
      }
    }
  }
}