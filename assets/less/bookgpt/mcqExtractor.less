@color_1: var(--dark);
@color_2: var(--primary);
@color_3: var(--gray);
@color_4: #2ecc71;
@color_5: var(--danger);
@color_6: #f1c40f;
@color_7: #e74c3c;
@font_family_1: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
@background_color_1: #f5f7fb;
@background_color_2: white;
@background_color_3: var(--light);
@background_color_4: rgba(46, 204, 113, 0.15);
@background_color_5: rgba(241, 196, 15, 0.15);
@background_color_6: rgba(231, 76, 60, 0.15);
@background_color_7: rgba(255, 255, 255, 0.8);





@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}


:root {
  --primary: #4361ee;
  --secondary: #3f37c9;
  --success: #4cc9f0;
  --info: #4895ef;
  --warning: #f72585;
  --danger: #e63946;
  --light: #f8f9fa;
  --dark: #212529;
  --gray: #6c757d;
  --light-gray: #dee2e6;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: @font_family_1;
}
body {
  background-color: @background_color_1;
  color: @color_1;
  min-height: 100vh;
}
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
header {
  background-color: @background_color_2;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: relative;
  z-index: 10;
}
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}
.logo {
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: 1.5rem;
  color: @color_2;
  span {
    margin-left: 8px;
  }
}
.timer-container {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: @background_color_3;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.9rem;
}
.timer-label {
  color: #fff;
  font-weight: 500;
}
.timer {
  color: #fff;
  font-weight: 600;
  min-width: 60px;
  text-align: right;
}
main {
  padding: 2rem 0;
}
.page-title-container {
  margin-bottom: 2rem;
}
.page-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: @color_1;
  margin-bottom: 0.5rem;
}
.page-subtitle {
  font-size: 1rem;
  color: @color_3;
  font-weight: 400;
}
.dashboard {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}
.card {
  background-color: @background_color_2;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }
}
.card-title {
  font-size: 0.9rem;
  text-transform: uppercase;
  color: @color_3;
  margin-bottom: 10px;
  letter-spacing: 0.5px;
}
.card-value {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 5px;
}
.card-change {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}
.positive {
  color: @color_4;
}
.negative {
  color: @color_5;
}
.data-table {
  background-color: @background_color_2;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  overflow: hidden;
}
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--light-gray);
}
.table-title {
  font-size: 1.1rem;
  font-weight: 600;
}
table {
  width: 100%;
  border-collapse: collapse;
}
th {
  padding: 15px 20px;
  text-align: left;
  font-size: 0.9rem;
  text-transform: uppercase;
  color: @color_3;
  font-weight: 600;
  letter-spacing: 0.5px;
}
td {
  padding: 15px 20px;
  text-align: left;
  font-size: 0.95rem;
}
tr {
  border-bottom: 1px solid var(--light-gray);
  &:last-child {
    border-bottom: none;
  }
}
.status {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}
.status-active {
  background-color: @background_color_4;
  color: @color_4;
}
.status-pending {
  background-color: @background_color_5;
  color: @color_6;
}
.status-inactive {
  background-color: @background_color_6;
  color: @color_7;
}
.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: @background_color_7;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(67, 97, 238, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary);
  animation: spin 1s linear infinite;
}
.loader-text {
  margin-top: 20px;
  font-size: 1.1rem;
  color: @color_2;
  font-weight: 500;
}
.loaded {
  .loader-container {
    display: none;
  }
}
.quiz-container {
  padding: 20px;
}

.item-wrap{
  border-bottom: 1px dashed;
  padding-bottom: 16px;
  padding-top: 16px;

  pre{
    white-space: break-spaces;
  }
}
.item-wrap:last-child{
  border-bottom:none;
}
@media (max-width: 768px) {
  .dashboard {
    grid-template-columns: 1fr;
  }
}

/* Progress styles */
#progress-container {
  margin: 20px auto;
  max-width: 800px;
  padding: 20px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.progress-bar-container {
  width: 100%;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 10px;
  margin-bottom: 10px;
  margin-top: 12px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary);
  width: 0%;
  transition: width 0.3s ease;
}

.progress-percentage {
  text-align: center;
  font-weight: bold;
  margin-bottom: 10px;
}

.progress-step {
  text-align: center;
  font-style: italic;
  color: #666;
  margin-bottom: 15px;
}

.progress-log-container {
  margin-top: 20px;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  background-color: #f9f9f9;
}

.progress-log-container details {
  margin: 0;
  padding: 0;
  border: none;
  background-color: transparent;
}

.progress-log-container summary {
  font-weight: bold;
  color: var(--primary);
  cursor: pointer;
  padding: 5px 0;
  margin-bottom: 10px;
}

.log-entry-count {
  display: inline-block;
  background-color: var(--primary);
  color: white;
  border-radius: 10px;
  padding: 2px 8px;
  font-size: 12px;
  margin-left: 8px;
}

.progress-log {
  max-height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.6;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
  scroll-behavior: smooth;
}

.progress-log-entry {
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #ddd;
  animation: fadeIn 0.5s;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.progress-log-entry:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.progress-log-time {
  color: #666;
  margin-right: 10px;
}