@import "../wsTemplate/theme/colors.less";
@import "../wsTemplate/theme/fonts.less";
@import "../wsTemplate/theme/responsive.less";

.reqList {
  min-height: 700px;
}
.alert-message {
  position: fixed;
  top: 15px;
  z-index: 999;
  width: 95%;
  padding: 10px 15px;
  right: 0;
  left: 0;
  margin: 0 auto;
  border-radius: 7px;
  box-shadow: 1px 1px 4px @white;
  transform: translateY(-70px);
  transition: all .3s linear;
}
.alert-message {
  &.show{
    transform: translateY(0px);
  }
  &.hide{
    transform: translateY(-70px);
  }
}

#successMsg {
  background: @light-green;
}
#errorMsg {
  background: @red;
}

.back-btn {
  color: @theme-primary-color;
  text-decoration: none;
}
.header-title{
  h4{
    color: @theme-primary-color;
    font-weight: @bold;
  }
  img {
    width: 25px;
  }
}

.optionList  {
  li{
    list-style: none;
    padding: 15px;
    border-bottom: 1px solid lighgten(@dark-gray,35%);
    a {
      color: lighgten(@dark-gray,25%);
      font-size: 12px;
    }
  }
}

.btn-outline-primary {
  border-color: @theme-primary-color !important;
  border-radius: 5px;
  font-size: 12px;
  color: @theme-primary-color;
}
.btn-primary {
  background: @theme-primary-color !important;
  border: none;
  font-size: 12px;
}

.user-dp {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: @red;
}
.bt {
  button {
    width: 120px;
  }
}

.reqList {
  ul {
    li {
      margin-bottom: 30px;
    }
  }
}
.back-btn {
  color: @theme-primary-color;
  &:active {
    text-decoration: none;
  }
}
.width-size{
  width: 50%;
}
@media @extraSmallDevices,@smallDevices {
  .width-size {
    width: 100%;
  }
  .reqList{
    position: relative;
    top: 50px;
  }
  .admin-panel-header{
    position: relative;
    top: 0;
  }
}