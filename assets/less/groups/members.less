@import "../wsTemplate/theme/colors.less";
@import "../wsTemplate/theme/fonts.less";
@import "../wsTemplate/theme/responsive.less";

main{
  height: 100vh;
}
.settings-icon {
  border: none;
  background: @theme-primary-color;
  box-shadow: 0 0 10px @gray-light-shadow;
  border-radius: 50%;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
}
.header-title {
  h4{
    color: @theme-primary-color;
    font-weight: @bold;
  }
  img {
    width: 30px;
  }
}
.optionList  {
  li{
    list-style: none;
    padding: 15px;
    border-bottom: 1px solid lighten(@dark-gray,35%);

    a{
      color: lighten(@dark-gray,25%);
      font-size: 12px;
    }
  }
}

.div {
  width: 2px;
  height: 15px;
  background: @light-gray;
  opacity: 0.3;
}
.memsec-header  {
  a{
    font-size: 12px;
    font-weight: @bold;
    color:lighten(@dark-gray,35%);
    text-decoration: none;
  }
}
.active {
  color: @theme-primary-color !important;
}
.userimg {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: @red;
}
.btn-outline-primary {
  border-color: @theme-primary-color !important;
  border-radius: 5px;
  font-size: 12px;
  color: @theme-primary-color;

}

.reqList  {
  ul {
    li{
      margin-bottom: 30px;
    }
  }

}
.user-dp {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: @red;
}
.back-btn {
  color: @theme-primary-color;
  &:active{
    text-decoration: none;
  }
}
.width-size{
  width: 50% !important;
}
.user-profile-img{
  width: 30px;
}
@media @extraSmallDevices,@smallDevices{
  .user-dp{
    width: auto;
    height: auto;
    padding: 5px;
  }
  .user-dp img{
    width: 35px;
    height: 35px;
  }
  .width-size{
    width: 100%;
  }
  .bt button {
    width: auto !important;
  }
  .reqList{
    top: 0 !important;
  }
}
