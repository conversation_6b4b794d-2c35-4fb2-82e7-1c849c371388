@import "../wstemplate/theme/colors.less";
@import "../wstemplate/theme/fonts.less";
@import "../wstemplate/theme/responsive.less";

// Overwriting Fonts
html, body,
h1, h2, h3, h4, h5, h6,
p, li, a,
input, textarea, select, label, span, small, strong,
button,
th, td, dl, dt, dd,
address {
  font-family: @primary-font;
}

//Modal Styles
.modal {
  z-index: 9992;
}

// Loader Styles
.loading-icon {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  background: @loader-color;
  z-index: 9999;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  .loader-wrapper {
    background-image: url("../../images/wonderslate/preloader.webp");
    background-repeat: no-repeat;
    width: 200px;
    height: 200px;
    background-color: transparent;
    top: 0 !important;
    -webkit-transform: unset;
    -moz-transform: unset;
    -ms-transform: unset;
    -o-transform: unset;
    transform: unset;
    border-radius: 0;
    box-shadow: none;
  }
  .loader {
    display: none;
    &::before,&::after {
      display: none;
    }
  }
}

::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: @primary-font;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: lighten(@light-gray,25%) !important;
}

:-ms-input-placeholder { /* Internet Explorer 10-11 */
  font-family: @primary-font;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: lighten(@light-gray,25%) !important;

}

::-ms-input-placeholder { /* Microsoft Edge */
  font-family: @primary-font;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: lighten(@light-gray,25%) !important;

}

//Profile page
.user_profile {
  #profile {
    #updateButton {
      background: @orange;
      color: @white;
      font-weight: @medium;
    }
  }
  .change-password {
    color: @theme-primary-color;
  }
}
#change-password-modal {
  .password-content button:last-child {
    color: @theme-primary-color;
  }
}
