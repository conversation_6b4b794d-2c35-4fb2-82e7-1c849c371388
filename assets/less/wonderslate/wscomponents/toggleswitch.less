.switch{
  display: flex;
  position: absolute;
  right: 0;
  top:0;
  &.reset-switch{
    position: static;
    display: flex;
    align-items: center;
    margin-left: 3rem;
    label {
      margin-bottom: 0 !important;
    }
  }
}
.public-text{
margin-right: 10px;
  display: block;
  font-style: italic;
  font-size: 12px;
  color:@theme-primary-color;
  @media @extraSmallDevices,@smallDevices,@mediumDevices{
    color:@white;
  }

}
.setname-wrapper {
  @media @extraSmallDevices,@smallDevices,@mediumDevices{
    .public-text {
      color: @black;
    }
  }
}
.toggleSwitch{
  .switch {
    position: relative;
    display: inline-block;
    width: 30px;
    height: 15px;
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
  }

  .slider:before {
    position: absolute;
    content: "";
    height: 13px;
    width: 13px;
    left: 2px;
    bottom: 1px;
    background-color: @white;
    -webkit-transition: .4s;
    transition: .4s;
  }

  input:checked + .slider {
    background: @green;
  }

  input:focus + .slider {
    box-shadow: 0 0 1px @cyan;
  }

  input:checked + .slider:before {
    -webkit-transform: translateX(14px);
    -ms-transform: translateX(14px);
    transform: translateX(14px);
  }

  /* Rounded sliders */
  .slider.round {
    border-radius: 10px;
  }

  .slider.round:before {
    border-radius: 50%;
  }
}
