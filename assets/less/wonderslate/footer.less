@import "../wstemplate/theme/colors.less";
@import "../wstemplate/theme/fonts.less";
@import "../wstemplate/theme/responsive.less";

footer{
  .footer-fluid{
    padding-top: 2rem;
    @media @extraSmallDevices,@smallDevices{
      padding-bottom: 5rem;
      padding-top: 0;
    }
  }
  .footer_logo_wrapper {
    padding: 0;
    @media @extraSmallDevices, @smallDevices {
      display: flex;
      align-items: center;
      justify-content: center;
      .desktop {
        padding-left: 1rem;
        margin-left: 1rem;
        border-left: 1px solid @light-gray;
      }
    }
  }
  .ipadVis{
    @media @mediumDevices{
      display: flex;
    }
    #explore {
      p{
        color: @orange;
        font-size: 1.5rem;
      }
      @media @mediumDevices {
        margin-left: 100px;
      }
    }
  }
  #vl {
    border-left: 2px solid @white;
    height: 150px;
    display: none;
    @media @mediumDevices{
      display: block;
    }
  }
  #featuredPublishers{
    ul{
      li{
        color: white;
      }
    }
  }
  .explore-section{
    display: flex;
    justify-content: center;
    @media @extraSmallDevices, @smallDevices,@mediumDevices {
      justify-content: left;
    }
  }
  .mobile{
    display: none;
    @media @extraSmallDevices, @smallDevices,@mediumDevices {
      display: flex;
    }
  }
  //.desktop{
  //  @media @extraSmallDevices, @smallDevices,@mediumDevices {
  //    display: none;
  //  }
  //}

  background:@black;
  min-height: 344px;
  //margin-top: 4rem;
  position: relative;
  @media screen and (max-width: 991px) {
    min-height: 400px;
    z-index: inherit;
  }
  //img{
  //  width: auto;
  //  height: auto;
  //}
}
.widget-ftr ul{
  padding: 0;
  list-style: none;
  margin-bottom: 1rem;
}
.widget-ftr h4,
.widget-ftr p{
  color: #F76e11 !important;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}
.widget-ftr a{
  color: @white !important;
  &:hover {
    color: @orange !important;
    text-decoration: underline;
  }
}
.widget-ftr button{
  font-size: 16px;
  font-weight: @bold;
  border: none;
}
.publisher-btn{
  background: #F76E11;
  color: @white;
  margin-bottom: 1rem;

}
.digitalLib-btn{
  background: #2fbdca;
  color: @white !important;
  a {
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    &:hover {
      color: @white !important;
      text-decoration: none;
    }
  }
}
.copyright-footer {
  font-size: 12px;
  color: @white !important;
  text-align: left !important;
  @media @extraSmallDevices, @smallDevices {
    text-align: center !important;
  }
}
.accept-payments {
  img {
    @media @extraSmallDevices {
      width: 100% !important;
      height: auto !important;
    }
  }
}
.footer-secRow{
  border-top: 1px solid @white;
  margin: 20px 0 0;
  padding: 10px 0 0;
}
.company-name{
  font-size: 12px;
  display: flex;
  justify-content: end;
  color: @white;
  align-self: center;
  @media @extraSmallDevices, @smallDevices {
    justify-content: center !important;
  }
}
.social-icons{
  justify-content: flex-start !important;
}
.logo-wrapper-new{
  text-align: center;
}
.download-btns{
  margin: 0 auto;
  display: flex;
  padding: 0.5rem;
  justify-content: center;
  background-color:transparent !important;
  align-items: center;
  width: 80%;
  border: none;
  justify-content: space-evenly;
  p{
    strong{
      font-size: 14px !important;
      @media @extraSmallDevices, @smallDevices {
        font-size: 20px !important;
      }
    }
    font-size: 10px;
  }

}

.logo-wrapper{
  text-align: center;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  .footer-logo{
    position: absolute;
    margin: 0 auto;
  }
}
.social-icons{
  display: flex;
  justify-content: center;
  padding: 0;
  margin: 0;
  li{
    list-style-type: none;
    padding: 0 8px 10px;
    @media @mediumDevices {
      padding: 0 7px 10px;
    }
    a{
      text-decoration: none;
      i{
        color:@white;
        font-size: 24px;
      }
    }
  }
}
[class^="flaticon-"]:before, [class*=" flaticon-"]:before, [class^="flaticon-"]:after, [class*=" flaticon-"]:after{
  margin-left: 0;
}
.support-link {
  p {
    font-weight: 300;
    color: @white;
  }
  a {
    text-decoration: underline;
    color: @white;
    font-weight: 500;
    &:hover {
      color: @orange;
    }
  }
}
.mobile-footer-nav {
  background: #000;
  box-shadow: 0 -4px 10px @gray-light-shadow;
  -webkit-box-shadow: 0 -4px 10px @gray-light-shadow;
  -moz-box-shadow: 0 -4px 10px @gray-light-shadow;
  border-radius: 20px 20px 0 0;
  bottom:0;
  left:0;
  right: 0;
  margin: 0 auto;
  width: 100%;
  height: 70px;
  position: fixed;
  z-index: 9991;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  &.hide-menus {
    bottom: -75px;
    transition: all 0.5s linear;
    -webkit-transition: all 0.5s linear;
    -moz-transition: all 0.5s linear;
    -o-transition: all 0.5s linear;
  }
  a{
    text-decoration: none;
    flex-wrap: wrap;
    text-align: center;
    color: @white;
    &:focus {
      text-decoration: none;
    }
    &:visited {
      text-decoration: none;
    }
    img {
      margin: 0 auto;
      width: 22px;
      height: 22px;
    }
    p {
      width: 100%;
      font-size: 13px;
      line-height: 1.2;
      margin-bottom: 0;
    }
    &.institute-menu {
      img {
        width: 26px;
      }
    }
    &.cart-menu {
      justify-content: center;
    }
  }

  a.my_material_btn {
    position: relative;
    top: -30px;
    opacity: 1;
    p {
      color: @theme-primary-color
    }
    &:before {
      content: '';
      position: absolute;
      width: 75px;
      height: 75px;
      right: 0;
      left: 0;
      border-radius: 50%;
      background: #fff;
      z-index: -1;
    }
    &:after {
      content: '';
      position: absolute;
      width: 70px;
      height: 70px;
      right: 0;
      left: 0;
      background: @white;
      box-shadow: 0 4px 10px @gray-light-shadow;
      border-radius: 50%;
      margin: 0 auto;
      z-index: -1;
    }
  }

  i {
    color: @white;
  }
  .active-menu {
    opacity: 1;
  }
  .common-footer-nav {
    opacity: 0.7;
  }

  #toggleModules i {
    margin: 0 auto;
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

#toggleModulesList {
  overflow: hidden;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
  -moz-transition: all 0.3s linear;
  height: 0;
  display: flex;
  flex-wrap: wrap;
  position: fixed;
  justify-content: flex-end;
  bottom: 110px;
  right: 15px;
  z-index: 9991;
  #list {
    list-style: none;
    margin-bottom: 0;
    padding-left: 0;
    li {
      width: 100%;
      background: @theme-primary-color;
      margin-top: 10px;
      border-radius: 50px;
    }
  }
  a {
    width: 100%;
    padding: 8px 15px;
    color: @white;
    font-size: 12px;
    min-width: 170px;
    min-height: 40px;
    display: flex;
    align-items: center;
    text-decoration: none !important;
    i {
      font-size: 20px;
    }
  }
  &.open {
    visibility: visible;
    opacity: 1;
  }
}
.toggleModulesBg {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: @theme-primary-color;
  z-index: 999;
}
#guestUser,
#loginOpen,
#signup,
#forgotPasswordmodal,
#shareContentModal,
#deleteBook,
#change-password-modal,
#currentPomodoro,
#pomodoroSessionCompletion,
#submit-test,
#report-que,
#force-submit-test,
#videoModal,
#image-modal,
#continue-test,
#successModalOrders,
#removePhone,
#libraryExpiredModal,
#bookQueueModal {
  z-index: 9992;
  //background: transparent !important;
}
.time-saved{
  font-size: 40px;
  color:@light-gray;
  font-weight: @semi-bold;
}
.timer-text{
  color:@light-gray;
  font-size: 12px;
}
#pomodoroSessionCompletion {
  .modal-body {
    background: @theme-primary-color;
    border: 2px solid @white;
    box-sizing: border-box;
    box-shadow: 0 0 10px @gray-light-shadow;
    border-radius: 10px;
    text-align: center;
    p{
      color:@white;
      &:first-child{
        font-weight: @semi-bold;
      }
    }
  }
}
#pomodoroTimer{
  background: @white;
  box-shadow: 0 2px 4px @gray-light-shadow;
  border-radius: 36px;
  width: 77px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  color: rgba(68, 68, 68, 0.48);
  &:hover{
    text-decoration: none;
  }
}

.tree3{
  top: -65px;
  left: 12px;
  position: relative;
}
.btn-forest{
  background: @white;
  border: 1px solid @theme-primary-color;
  box-sizing: border-box;
  box-shadow: 0 0 10px @gray-light-shadow;
  border-radius: 5px;
  color:@theme-primary-color;
  font-size: 9px;
}
.info-rank {
  p {
    font-size: 11px;
    color: @light-gray;
    font-weight: @regular;
  }
}
#relatedBooks .slick-track,
#bestSellerBooks .slick-track {
  width: 1900px !important;
}
#relatedBooks .slick-track .slick-slide,
#bestSellerBooks .slick-track .slick-slide {
  width: 190px !important;
}
.guest-user-alert {
  background:#FF6B6B;
  text-align:center;
  padding:4px;
  position:absolute;
  width:100%;
  max-width:100%;
  top: 0;
  span {
    font-size: 13px;
    color: black;
  }
  a {
    background: @white;
    box-shadow: 0 2px 10px rgb(128 128 128 / 25%);
    border-radius: 10px;
    margin-left: 10px;
    color: black;
    font-size: 12px;
    line-height: normal;
    padding: 2px 10px;
  }
}
.modal-backdrop.show {
  opacity: 0.85;
  z-index: 9991;
}
.mdl-layout__container_ebook{
  .prepjoy_cta {
    margin-top: 0rem !important;
  }
}
.prepjoy_cta {
  background-image: linear-gradient(229deg, #212121, #000000d1);
  position: relative;
  margin-top: 3rem;
  @media @extraSmallDevices, @smallDevices {
    padding: 2.5rem 1.5rem 3rem;
  }
  .curved-bg {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    width: 100%;
    height: 400px;
    position: absolute;
    opacity: 0.15;
    top: 20px;
    right: 0;
    left:0;
  }
  .cta_inner {
    border: 1px solid @orange;
    padding: 20px;
    border-radius: 20px;
    box-shadow: 0 2px 4px #000;
  }
  .cta_info {
    @media @extraSmallDevices, @smallDevices {
      width: 100%;
    }
    h3 {
      color: @white;
      font-weight: @thin;
      line-height: 35px;
      span {
        font-weight: @semi-bold;
      }
    }
    h4,p {
      font-weight: normal;
      font-size: 1.4rem;
      span {
        font-size: 15px;
        @media @extraSmallDevices, @smallDevices {
          padding: 5px 0;
          display: inline-block;
        }
      }
      a {
        color: @orange !important;
        &:hover {
          opacity: 0.7;
        }
      }
    }
  }
  .cta_btn {
    @media @extraSmallDevices, @smallDevices {
      width: 100%;
    }
    p {
      color: @white;
    }
    a {
      padding: 10px 20px;
      font-size: 16px;
      font-weight: @semi-bold;
      color: @white;
      background-color: @orange;
      border-color: @orange;
      border-radius: 7px;
      box-shadow: 0 2px 4px @gray-dark-shadow;
      -webkit-box-shadow: 0 2px 4px @gray-dark-shadow;
      -moz-box-shadow: 0 2px 4px @gray-dark-shadow;
      &:hover,&:active,&:focus,&:active:focus {
        background-color: @orange !important;
        border-color: @orange !important;
        box-shadow: none !important;
        outline: 0 !important;
      }
      @media @extraSmallDevices, @smallDevices {
        margin-top: 10px !important;
        margin-bottom: 10px !important;
      }
    }
  }
}