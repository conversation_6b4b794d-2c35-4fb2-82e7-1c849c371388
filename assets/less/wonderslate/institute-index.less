@import "../variables/responsive.less";
@import "color.less";
@import "common.less";

@theme_oldfont: '<PERSON><PERSON><PERSON>', sans-serif;
@theme_font: 'Poppins', sans-serif;

* {
  margin: 0;
}
main {
  display: flex;
  position: relative;
  flex-direction: column;
  width: 100%;
  min-height: 500px;

  .institute-main-img {
    position: absolute;
    right: 0;
    top: 0;
    width: 50%;
    z-index: 0;
    @media @iPhone {
      width: 70%;
    }
  }
  div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    z-index: 1;
    .logo {
      display: flex;
      flex-direction: row;
      width: 40%;
      margin-left: 100px;
      margin-top: 30px;
      align-items: center;
      justify-content: flex-start;
      @media @iPhone {
        flex-direction: column;
        width: unset;
        margin-left: 20px;
      }
      .ws-logo-align {
        margin-left: 20px;
        img {
          width: 100%;
        }
      }
      .pub-logo {
        max-height: 100px;
        width: auto;
      }
      img {
        margin-right: 30px;
      }
    }
    button {
      padding: 10px 40px;
      border-radius: 15px;
      background: transparent;
      cursor: pointer;
      font-size: 12px;
      font-weight: 400;
      color: white;
      margin-right: 100px;
      border: 2px solid #fff;
      margin-top: 30px;
      outline: none !important;
      @media @iPhone {

        font-size: 32px;
      }
    }
  }
  .institute-info {
    display: flex;
    flex-direction: column;
    margin-top: 20vh;
    width:40%;
    margin-left: 100px;

    @media @iPhone {
      width:80%;
      margin-top: 30vh;
      margin-left: 40px;
    }
    h1 {
      padding-bottom: 20px;
      font-weight: 700;
      font-size: 22px;
      color: rgba(68, 68, 68, 1);
      @media @iPhone {
        font-size: 32px;
      }
    }
    p {
      padding-bottom: 20px;
      font-weight: 400;
      font-size: 14px;
      line-height: 25px;
      color: rgba(68, 68, 68, 1);
      @media @iPhone {
        font-size: 30px;
      }
    }
    .button-box {
      margin-top: 30px;
      .login {
        padding: 10px 50px;
        border-radius: 15px;
        background: transparent;
        cursor: pointer;
        font-size: 12px;
        font-weight: 400;
        outline: none;
        border: 2px solid #333;
        color: #333;
        @media @iPhone {
          padding: 20px 90px;
          font-size: 32px;
        }
      }
      .logout {
        padding: 10px 50px;
        border-radius: 15px;
        background: radial-gradient(235.67% 2987.36% at -21.77% -33.82%, #DA6AFF 0%, #9B05CE 100%);
        cursor: pointer;
        font-size: 12px;
        font-weight: 400;
        outline: none;
        border-color: transparent;
        color: white;
        @media @iPhone {
          padding: 20px 90px;
          font-size: 32px;
        }
      }
    }
  }
}

.aboutus {
  display: flex;
  justify-content: center;
  width: 80%;
  flex-direction: column;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  position: relative;
  border-radius: 15px;
  margin-top: 250px;
  .section-heading {
    position: absolute;
    top: -14px;
    right: 15%;
    background-color: white;
    font-size: 22px;
    color:rgba(68, 68, 68, 0.34);
    font-weight: 700;
  }
  .row {
    display: flex;
    justify-content: space-between;
    padding: 50px;
    margin-top: 70px;
    @media @iPhone {
      flex-direction: column;
    }

  }
  .row-reverse {
    flex-direction: row-reverse;

    @media @iPhone {
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }
  .left {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    @media @iPhone {
      width: 100%;
    }
  }
  .right {
    width: 50%;
    display: flex;
    flex-direction: column;
    align-self: center;
    padding: 20px;
    @media @iPhone {
      width: 100%;
    }
    h1 {
      padding-bottom: 20px;
      font-size: 22px;
      font-weight: 700;
      color: rgba(68, 68, 68, 1);
      line-height: 25px;
      @media @iPhone {
        font-size: 32px;
      }

    }
    p {
      font-size: 14px;
      font-weight: 400;
      color: rgba(68, 68, 68, 1);
      line-height: 25px;
      @media @iPhone {
        font-size: 20px;
      }
    }
  }
}
.institute-footer {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
  width: 100%;
  background: rgba(33, 33, 33, 1);
  color: white;
  padding: 100px 0px;
  margin-top: 100px;
  .institute-footer-logo {
    text-align: center;
    img {
      margin-bottom: 30px;
      max-height: 70px;
      height: auto;
    }
  }
  .footer-right-content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .sm {
      display: flex;
      flex-direction: column;
      margin-top: 10px;

      ul {
        display: flex;
        flex-direction: row;
        padding-left: 0px;

        li {
          list-style-type: none;
          display: flex;


          a {
            text-decoration: none;

            i {
              color: white;
              font-size: 12px;
            }
          }
        }
      }
    }
    .contact-info {
      display: flex;
      flex-direction: column;
      .phone {
        display: flex;
        flex-direction: row;
        margin: 10px;
        justify-content: center;
        align-items: center;
        p {
          padding: 10px;
        }
      }
      .email-id {
        display: flex;
        justify-content: center;
      }
    }
  }
}

.store-link {
  .d-flex {
    border: 1px solid #CCC;
    border-radius: 20px;
    background-color: #FFF;
    box-shadow: 0 2px 4px #DDD;
  }
  h3 {
    @media @iPhone {
      font-size: 28px;
    }
  }
  button {
    padding: 10px 40px;
    border-radius: 15px;
    border: 1px solid #9B05CE;
    cursor: pointer;
    font-size: 15px;
    font-weight: 400;
    outline: none;
    color: #9B05CE;
    background-color: #FFF;
    box-shadow: 0 2px 4px #DDD;
    @media @iPhone {
      font-size: 30px;
    }
  }
  lottie-player {
    width: 300px;
  }
}