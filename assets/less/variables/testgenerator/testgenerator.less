@import "../ws_color_theme.less";
@import "../ws_fonts.less";
@import "../responsive.less";

/*reset*/
#test-gen-modal{
padding-right: 0 !important;
  .modal-dialog{
    height: 100vh;
    max-width: 100vw;
    margin:0;
    overflow: hidden;
    .modal-content {
      border: none;
      border-radius: 0;
      .modal-header {
        position: relative;
        z-index: 9;
        height: 48px;
        border: none;
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.24), 0px 0px 4px rgba(0, 0, 0, 0.12);
        h4 {
          font-size: @ws-header-fontSize;
          color: fade(@ws-darkBlack, 72%);
          font-family: @ws-header-font;
          font-weight: normal;
        }
        .close {
          padding: 0;
          margin: 0;
        }
        .test-text {
          color: #fff;
        }
      }
      .modal-body {
        padding: 0;
        .test-gen-books {
          height: calc(100vh - 120px);
          overflow: hidden;
          overflow-y: auto;
        }
      }
      .modal-footer{
        position: relative;
        z-index: 9;
        height:72px;
        box-shadow: 0px -1px 0px rgba(68, 68, 68, 0.24);
        border:none;
      }
    }
  }
  .next-btn{
    width: 140px;
    height: 40px;
    background: linear-gradient(270deg, @ws-blue-start 0%, @ws-blude-end 100%);
    border-radius: 4px;
    color:@ws-white;
  }
  .previous-btn{
    color:fade(@ws-darkBlack,72%);
  }
}
.overlay-testgen-book{
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: fade(@ws-white,50%);
  .book-selected{
    text-align: center;
    i{
      position: relative;
      top:6rem;
      color:@ws-darkOrange;
      font-size: @ws-header-fontSize * 2;
      @media @iPhone{
        top:5rem;
      }
    }
  }
}
.test-gen-chapters{
  height: calc(100vh - 120px);
  overflow: hidden;
  overflow-y: auto;
}

