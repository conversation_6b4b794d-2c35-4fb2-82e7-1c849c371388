@import "ws_color_theme.less";
@import "ws_fonts.less";
@import "responsive.less";

.blog-article {
  padding-top: 2rem;
  padding-bottom: 2rem;
  #upcomingExams {
    > h4 {
      color: fade(@ws-darkBlack, 40%);
      font-family: @ws-header-font;
      font-size: @ws-menu-fontSize - 2;
      font-weight: @ws-header-fontWeight;
      padding: 16px 20px;
      margin: 0;
    }
    .upcomingExams {
      .item {
        padding: 10px;
        .content-wrapper {
          width: 184px;
          min-height: 246px;
          background: @ws-white;
          -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
          -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
          box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
          padding: 16px;
          h3 {
            font-family: @ws-header-font;
            font-size: @ws-header-fontSize;
            font-weight: @ws-header-fontWeight;
            color: @ws-darkBlack;
            text-transform: uppercase;
          }
          .short-text {
            font-family: @ws-header-font;
            font-size: @ws-menu-fontSize - 2;
            color: @ws-darkBlack;
            .morelink {
              color: @ws-lightOrange;
            }
          }
        }
      }
    }
  }
  .blog {
    > h4 {
      color: fade(@ws-darkBlack, 40%);
      font-family: @ws-header-font;
      font-size: @ws-menu-fontSize - 2;
      font-weight: @ws-header-fontWeight;
      padding: 16px 20px;
      margin: 0;
    }
    .item {
      padding: 10px;
      .card {
          min-height: 250px;
       @media @iPhone{
         width: 280px;
       }
        img{
          width:320px;
          height:180px;
          @media @iPhone{
            width: 280px;
          }
        }
        .card-body {
          width: 320px;
          padding: 0.6rem 1.25rem;
          @media @iPad-portrait{
          }
          img {
            height: 180px;
          }
          p {
            font-size: @ws-header-fontSize;
            color: @ws-darkBlack;
             margin-bottom: 0;
            @media @iPhone{
              width:270px;
            }
          }
        }
      }
    }
  }
}