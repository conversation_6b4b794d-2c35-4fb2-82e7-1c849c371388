@import "../ws_color_theme.less";
@import "../ws_fonts.less";
@import "../responsive.less";

.library {
 padding-top: 2rem;
  min-height: 100vh;
  background: #F5F5F5;
  @media @iPhone {
    margin-top: 2rem;
  }
  .nav-tabs {
    border: none;
    .nav-link {
      cursor: pointer;
      font-size: @ws-header-fontSize + 10;
      font-weight: @ws-header-fontWeight;
      color: fade(@ws-darkBlack, 70%);
      font-family: @ws-banner-font;
      &.active {
        border: none;
        background: none;
        border-bottom: 3px solid @ws-lightOrange;
        color: @ws-darkBlack;
      }
      &:hover {
        border: none;
        border-bottom: 3px solid @ws-lightOrange;
      }
    }
    li {
      &:last-child {
        margin-left: 1rem;
        @media @iPhone {
          margin-left: 0;
        }
      }
    }
  }
  .username {
    p {
      font-size: @ws-header-fontSize * 2;
      font-family: @ws-banner-font;
      font-weight: bold;
      color: fade(@ws-darkBlack, 24%);
      margin-bottom: 0.5rem;
      span {
        color: @ws-darkBlack;
        text-transform: capitalize;
      }
      &:last-child {
        font-size: @ws-header-fontSize + 4;
        font-family: @ws-header-font;
        font-weight: @ws-header-fontWeight;
        color: fade(@ws-darkBlack, 72%);
      }
    }
  }
  .generate {
    background: @ws-white;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
    border-radius: 600px;
    color: @ws-darkBlack;
    img {
      width: 24px;
    }
  }
  .tab-content {
    padding-bottom: 2rem;
    margin-top: 2rem;
    .books-content-wrapper;
  }
  #recentRead {
    #recentBooks {
      a {
        .item {
           .content-wrapper{
             height:auto;
           }
        }
      }
    }
  }
}
.books-content-wrapper {
  margin-top: 2rem;
  h4{
    font-family: @ws-header-font;
    font-size: @ws-header-fontSize + 4;
    color:@ws-darkBlack;
    padding: 10px;
  }
  a {
    &:hover {
      text-decoration: none;
    }
  }
  .card {
    padding: 10px;
    width: 175px;
    border: none;
    background: none;
    >div{
      &.share-wrapper{
        position: relative;
      }
    }
    @media @iPhone {
      width: 100%;
    }
    img {
      width: 154px;
      height: 192px;
      box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
      //border-radius: 4px;
      @media @iPhone,@iPhone6-portrait,@iPhone5-portrait,@iPhone7-portrait{
        height: auto;
        width: 100%;
      }
    }
    .card-body {
      padding: 0;
      .card-text {
        color: @ws-darkBlack;
        margin-top: 0.5rem;
        width: 152px;
        height:60px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        @media @iPhone {
          width: 100%;
        }
        &:hover{
          color:@ws-lightOrange;
          text-decoration: underline;
        }
      }

    }
    &:hover {
      //box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
      .share-content{
        display: block;
      }
    }
    .btn.dropdown-toggle{
      background: none;
    }
  }
  .dropup .dropdown-toggle::after{
    display: none;
  }
  .dropdown-menu{
    min-width: 100px;
    top:15px !important;
    a{
      &.dropdown-item{
          color:darkgreen;
        &:last-child{
          color:@ws-red;
        }
      }
    }
  }
}
.lib-showcase{
  position: relative;
  z-index: 9;
  min-height: 192px;
  border-radius: 4px;
  color:#fff;
  &:hover{
    p{
      color: #fff;
    }
  }
}
.share-content{
  display: none;
  position: absolute;
  width:100%;
  height:100%;
  background: fade(@ws-darkBlack,50%);
  z-index: 999;
}
.btn-none{
  background: none;
  border:none;
  outline:0;
  display: none;
  &:hover{
    color:@ws-lightOrange;
  }
  &:focus{
    outline: none;
  }
}
.headerSlider{
  a{
    &:hover{
      text-decoration: none;
    }
  }
}

.quiz-buttons{
  .btn.disabled{
    background: transparent;
  }
}
.uncoverdetail{
  height: 165px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: #F79420;
  p{
    text-align: center;
    text-transform: capitalize;
    font-size: 16px;
    font-style: italic;
    color:#fff;
  }
}
.uncover{
  height: 192px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  p{
    text-align: center;
    text-transform: capitalize;
    font-size: 16px;
    font-style: italic;
    color:#fff;
  }
}
.bookShadow{
  .uncover{
    height: 165px;
    p{
      color:#fff;
      font-size: 14px;
    }
  }
}

#ExportToStudySet{
  display: none;
}
