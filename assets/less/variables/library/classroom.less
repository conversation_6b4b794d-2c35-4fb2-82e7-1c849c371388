@import "../ws_color_theme.less";
@import "../ws_fonts.less";
@import "../responsive.less";

#classroom{
  #libraryAssignment{
    .media{
      padding: 1rem;
      background: @ws-white;
      border-left:5px solid @ws-bluePrimary;
      margin-bottom: 2rem;
      border-radius: 3px;
      box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
      .media-body{
        p{
          margin-bottom: 0;
        }
        h4{
          color:@ws-darkBlack;
          font-size: @ws-header-fontSize + 4;
          font-family: @ws-header-font;
          font-weight: normal;
          margin-left: 0.5rem;
          text-transform: capitalize;
          padding: 0;
        }
        .instructor-name{
          margin-top: 2rem;
          p{
            color:@ws-darkBlack;
            font-size: @ws-header-fontSize;
            span{
              color:fade(@ws-darkBlack,70%);
              margin-right: 0.5rem;
            }
          }
          border-bottom: 1px solid fade(@ws-darkBlack,50%);
        }
        .assignment-done{
          display: flex;
          align-items: center;
          color:@ws-gradient-start;
          width: 50%;
          justify-content: center;
          margin-left: -1.8rem;
          i{
            padding-right: 0.5rem;
            color:@ws-gradient-start;
          }
        }
        .not-done{
          color:fade(@ws-darkBlack,90%);
          width: 50%;
          justify-content: center;
          margin-left:-0.8rem;
          i{
            padding-right: 0.5rem;
            color:fade(@ws-darkBlack,90%);
          }
        }
        .mark-complete{
          display: flex;
          align-items: center;
          justify-content: center;
          color:fade(@ws-darkBlack,90%);
          background: none;
          outline:0;
          font-weight: normal;
          font-family: @ws-header-font;
          width: 50%;
          &.complete{
            i{
              color:@ws-gradient-start;
            }
            span{
              color:@ws-gradient-start;
            }
          }
         span{
           font-size: @ws-menu-fontSize;
         }
          i{
            padding-right: 0.5rem;
            color:fade(@ws-darkBlack,90%);
          }
          &:hover{
            color:#000;
          }
        }
        .attempt-assignment-btn{
          background: linear-gradient(270deg, @ws-gradient-start 0%, @ws-gradient-end 100%);
          color:@ws-white;
          font-weight: @ws-header-fontWeight;
          border: none;
          width: 120px;
          @media @iPhone{
            width: auto;
          }
        }
        div{
          button{
            background: linear-gradient(270deg, @ws-gradient-start 0%, @ws-gradient-end 100%);
            color:@ws-white;
            font-weight: @ws-header-fontWeight;
            border: none;
          }
          .button-wrapper{
            width: 50%;
            text-align: center;
            border-left:1px solid fade(@ws-darkBlack,40%);
            height:34px;
            p{
              display: flex;
              align-items: center;
              justify-content: center;
              height:100%;
            }
          }
        }
      }
    }
  }
}