@import "ws_color_theme.less";
@import "ws_fonts.less";
@import "responsive.less";

@font-face {
  font-family: 'Rubik-medium';
  src: url('../../stylesheets/landingpage/fonts/Rubik-Medium.ttf');
}
@font-face {
  font-family: 'Rubik', sans-serif;
  src: url('../../stylesheets/landingpage/fonts/Rubik-Regular.ttf');
}
@font-face {
  font-family: 'Merriweather';
  src: url('../../stylesheets/landingpage/fonts/Merriweather-Regular.ttf');
}
html{
  //scroll-behavior: smooth;
  //overflow-x: hidden;
}

html, body,applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,caption,
article, aside, canvas, details, embed,
figure, figcaption,header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font-family: 'Rubik', sans-serif;
  vertical-align: baseline;
}
/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}
body {
  line-height: 1;
}
ol, ul {
  list-style: none;
}
blockquote, q {
  quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
  content: none;
}

table {
  margin: 0 auto;
  line-height: 1.5;
}
table,td{
  border:1px solid #EEEEEE;
  text-align: center;
}
@media screen and (max-width: 768px) {
  table {
    width: 90% !important;
    margin: 0px 15px 15px 15px;
    line-height: 1.5;
  }
  .indication-wrapper{
    position: fixed;
    width: 75%;
    z-index: 99;
    background: @ws-white;
    padding-bottom: 20px;
  }
  .que-side-menu{
    .nav-tabs{
      position: fixed;
      background: @ws-white;
      width: 75%;
      top:5rem;
      z-index: 99;
    }
    .tab{
      .tab-content{
        margin-top: 7rem;
      }
    }
  }
}
p,a,.table td, .table th{
  font-family: 'Rubik', sans-serif;
  font-size: 14px;
}

.result-menu{
  position: fixed;
  width: 100%;
  z-index: 99;
  top:0;
  background: @ws-white;
  display: none;
  >div{
    height:56px;
    i{
      cursor: pointer;
      &:last-child{

      }
    }
    h2{
      color:@ws-darkBlack;
      font-size: 16px;
      font-family: 'Rubik-medium';
      font-weight: @ws-header-fontWeight;
    }
  }
  .language {
    background-image: url('../../images/mobile/language1.svg');
    width: 24px;
    height: 24px;
    background-size: contain;
    background-color: transparent;
    background-repeat: no-repeat;
    border: none;
    margin-right: 1rem;
    outline:0;
    &.rotate{
      background-image: url('../../images/mobile/language.svg');
    }
  }
}
.sub-header {
  height: 88px;
  background: @ws-white;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  position: fixed;
  width: 100%;
  z-index: 99;
  top:0;
  .submit {
    height: 56px;
    .container{
      height: 100%;
      >div{
        height:100%;
      }
    }
    .btn-submit{
      background: none;
      border:none;
      color:@ws-darkBlack;
      font-size: 16px;
      font-family: 'Rubik',sans-serif;
      cursor: pointer;
      outline:0;
      &:hover{
        color:@ws-lightOrange;
      }
    }
    select{
      border:none;
      background: none;
      height: 40px;
      outline:none;
    }
  }
  .options {
    height: 32px;
    border-top: 0.5px solid fade(@ws-darkBlack, 40%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    p {

      font-size: @ws-header-fontSize;
      color: @ws-darkBlack;
      span {
        font-family: 'Merriweather';
      }
    }
    .menu-wrapper {
      display: flex;
      .language {
        background-image: url('../../images/mobile/language1.svg');
        width: 24px;
        height: 24px;
        background-size: contain;
        background-color: transparent;
        background-repeat: no-repeat;
        border: none;
        margin-right: 1rem;
        outline:0;
        &.rotate{
          background-image: url('../../images/mobile/language.svg');
        }
      }
      .menu {
        background-image: url('../../images/mobile/ham-icon.svg');
        width: 24px;
        height: 24px;
        background-size: contain;
        background-color: transparent;
        background-repeat: no-repeat;
        border: none;
      }
    }
  }

  #sections{
    border:none;
    outline:none;
    padding: 0 0.8rem;
    height: auto;
    font-family: 'Rubik-medium';
    font-weight: @ws-header-fontWeight;
    &:focus{
      outline:none;
    }
  }
  .timer{
    font-size: 12px;
    color:fade(@ws-darkBlack,60%);
    padding: 0 1rem;
    font-family: 'Merriweather';
    position: relative;
    top: -0.5rem;
  }
}
.mt-fixed{
  //margin-top: 90px;
  padding-top: 1rem;
}
.close-menu {
  display: none;
  outline:none;
  position: absolute;
  top: 4rem;
  left: 18%;
  z-index: 9999;
  background-color: transparent;
  border:none;
  span{
    background-image: url('../../images/mobile/closeIcon.svg');
    background-size: contain;
    background-repeat: no-repeat;
    width: 24px;
    height: 24px;
    display: block;
  }
}
.que-side-menu {
  position: fixed;
  top: 0;
  right: 0;
  width:0;
  height: 100vh;
  background: @ws-white;
  box-shadow: -4px 5px 4px rgba(0, 0, 0, 0.25);
  z-index: 999;
  overflow-x: hidden;
  overflow-y: auto;
  padding-bottom: 1rem;

  .indicator{
    margin-top:1rem;
    .answered {
      .circle {
        background: #2F80ED;
      }
    }
    .unanswered {
      .circle {
        background: #fff;
        border: 1px solid @ws-darkBlack;
      }
    }
    .review {
      .circle {
        background: #A056E5;
      }
    }
    .notseen {
      .circle {
        background: #888888;
      }
    }

    .circle{
      height:15px;
      width: 15px;
      border-radius: 35px;
      display: block;
      margin-right: 10px;
    }
    p{
      color:@ws-darkBlack;
      font-size: 12px;
      font-family: 'Rubik', sans-serif;
      display: flex;
    }
  }
  .tab{
    display: none;
    margin-top: 1.5rem;
    .nav-tabs{
      background: #F8F8F8;
      border:none;
      display: flex;
      justify-content: space-evenly;
    }
    .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active{
      border: none;
      background: none;
      border-bottom: 2px solid @ws-lightOrange !important;
      font-family: 'Rubik-medium', sans-serif;
      font-size: 14px;
      color:@ws-lightOrange;
    }

    .nav-tabs .nav-item .nav-link, .nav-tabs .nav-link{
      border-bottom: 2px solid transparent;
      font-family: 'Rubik-medium';
      font-weight: @ws-header-fontWeight;
      font-size: 14px;
    }


  }
}
.hideref{
  color: transparent;
}
#sectionSelectionDiv{
  select{
    width: 160px !important;
    option{
     width:80px;
    }
  }
}
#question-block{
  //margin-top: 6rem;
  .read{
    color:fade(@ws-darkBlack,72%);
    font-size: 12px;
    margin-bottom: 4px;
  }
  .direction-passage{
    color:@ws-darkBlack;
    font-size: 14px;
    margin-bottom: 1rem;
  }
  .total-direction-que{
    color:fade(@ws-darkBlack,72%);
    font-size: 12px;
   margin-top: 2rem;
  }
  .question-wrapper {
    background: @ws-white;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.22);
    margin-top: 2rem;
    table,td{
      border:1px solid #444444;
    }
    ul{
      list-style: unset;
      padding: 0px 15px 15px 15px;
      margin-left: 1rem;
    }
    >P{
      padding: 5px 15px;
      line-height: 1.5;
    }
    .que-menu {
      padding: 15px;
      display: flex;
      justify-content: space-between;
      span {
        font-family: 'Rubik-medium';
        color: @ws-darkBlack;
        font-weight: 500;
      }
    }
    .question {
      color: @ws-darkBlack;
      padding: 0px 15px 15px 15px;
      line-height:1.5;
    }

    .options-string {
      .answer{
        margin-bottom: 0;
        line-height: 1.5;
      }
      label {
        display: flex;
        align-items: center;
        min-height:70px;
        border-bottom: 0.5px solid fade(@ws-darkBlack, 40%);
        padding-bottom: 0.5rem;
        padding-top: 0.5rem;
        overflow: hidden;
        overflow-x: auto;
        input[type=radio]{
          display: none;
        }
      }
      &.active{
        border-bottom: 3px solid @ws-blue-start;
       // background: fade(@ws-blue-start,10%);
        label{
          border-bottom: 0.5px solid transparent;
        }
        .choice{
          background: @ws-blue-start;
        }
      }
      .choice{
        background: fade(@ws-darkBlack,40%);
        width:24px;
        height:24px;
        border-radius: 35px;
        display: flex;
        justify-content: center;
        align-items: center;
        color:@ws-white;
        font-family: 'Merriweather';
        font-size: 12px;
        margin-right: 1rem;
      }
    }
    &:last-child{
      margin-bottom: 2rem;
    }
  }

  .error-btn{
    background-image: url('../../images/mobile/error.svg');
    width:15px;
    height: 15px;
    background-size: contain;
    background-color: transparent;
    background-repeat: no-repeat;
    border: none;
    margin-right: 1rem;
  }
  .bookmark-btn{
    width:15px;
    height: 15px;
    background-size: contain;
    background-color: transparent;
    background-repeat: no-repeat;
    border: none;
    i{
      color: fade(@ws-darkBlack,60%);
      font-size: 18px;
      &.bookmark{
        display: none;
        color: #A056E5;
      }
    }
    &.marked{
      i{
        display: none;
        &.bookmark{
          display: block;
        }
      }
    }
  }
  .btn-wrapper{
    display: flex;
    justify-content: space-between;
  }
  .border-start{
    p {
      border-bottom: 0.5px solid fade(@ws-darkBlack, 40%);
      height: 1px;
    }
  }
  >.container{
    margin-top: 1rem;
    p{
      line-height: 1.2;
      font-style: italic;
    }
  }
}
.overlay-container{
  width: 100%;
  height: 100vh;
  background: @ws-darkBlack;
  opacity: 0.8;
  position: absolute;
  top:0;
  z-index: 99;
  overflow: hidden;
  display: none;
}

.list{
  .que-wrapper{
    .que-no-list{
      font-size: 14px;
      color:@ws-white;
      font-family: 'Merriweather';
      width: 30px;
      height:30px;
      border-radius: 50%;
      background: @ws-blue-start;
      margin-right: 1rem;
      border: 2px solid #FFFFFF;
      box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
      display: flex;
      justify-content: center;
      align-items: center;
      &.answered{
        background: #2F80ED;
      }
      &.unanswered{
        background: #fff;
        border: 1px solid #444444;
      }
      &.marked{
        background: #A056E5;
      }
      &.notseen{
        background: #888888;
      }
    }
    .question{
          p{
            font-size: 16px;
            color: #444444;
            //white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 160px;
            &:hover{
              cursor: pointer;
            }
          }
      table,td,tr{
        &:hover{
          cursor: pointer;
        }
      }
    }
  }
}
.grid{
  margin-top: 0rem;
  div.que-wrapper{
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 0.5rem;
  }
  #accordion {
    .card-link {
      margin: 1rem 0;
      display: block;
      position: relative;
      font-family: 'Rubik-medium', sans-serif;
      color:@ws-darkBlack;
      &:hover{
        color:@ws-darkBlack;
      }
      &::after{
        width: 10px;
        height: 10px;
        content: url('../../images/mobile/arrow-up.svg');
        position: absolute;
        right: 12px;
        top: -4px;
        opacity: 0.6;
      }
      &.collapsed{
        &::after{
          width: 10px;
          height: 10px;
          content: url('../../images/mobile/arrow-down.svg');
          position: absolute;
          right: 12px;
          top: -4px;
          opacity: 0.6;
        }
      }
    }
  }
  .progress{
    height: 4px;
  }
  div.que-no{
    font-size: 14px;
    color:@ws-darkBlack;
    font-family: 'Merriweather';
    width: 30px;
    height:30px;
    border-radius: 50%;
    //background: linear-gradient(270deg, @ws-blue-start 0%, @ws-blude-end 100%);
    background: @ws-white;
    margin-right: 1rem;
    margin-top: 1rem;
    border: 1px solid @ws-darkBlack;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
    display: flex;
    justify-content: center;
    align-items: center;
  a{
    color:@ws-darkBlack;
    text-decoration: none;
  }
  &.answered{
    background: #2F80ED;
    border: none;
    a{
      color:@ws-white;
    }
  }
    &.unanswered{
      background: #fff;
      border: 1px solid #444444;
      a{
        color:@ws-darkBlack;
      }
    }
    &.marked{
      background: #A056E5;
      border: none;
      a{
        color:@ws-white;
      }
    }
    &.notseen{
      background: #888888;
      a{
        color:@ws-white;
      }
    }
  }
}
#answer-block{
  //margin-top: 2rem;
  padding-bottom: 2rem;
  overflow-x: hidden;
  .button-wrapper{
    a{
      color:@ws-white;
      background: @ws-blue-start;
      box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.24);
      border-radius: 2px;
      border:none;
      height:28px;
      font-size: 14px;
      font-family: 'Rubik', sans-serif;
      padding: 0 0.8rem;
      display: flex;
      align-items: center;
      text-decoration: none;
      width: auto;
      margin: 1rem auto;
      justify-content: center;
      &:hover{
        background: @ws-blue-start !important;
      }
      &:focus{
        background: @ws-blue-start !important;
      }
      &:active{
        background: @ws-blue-start !important;
      }
    }
  }
  .rank-wrapper{
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
    margin-top: 2rem;
    padding: 1rem 0;
    text-align: center;
    .medal{
      background: url("../../images/mobile/medalResult.svg") center center no-repeat;
      background-size: contain;
      width:32px;
      height:32px;
      margin: 0 auto;
    }
    p{
      margin-top: 0.5rem;
      font-family: @ws-header-font;
     &.rankText{
       color:fade(@ws-darkBlack,40%);
     }
      &.dateText{
        color:@ws-darkBlack;
        font-weight: @ws-header-fontWeight;
      }
    }
  }
}
.practice-score-container{
  background: @ws-blue-start;
  padding:2rem 0;
  color:@ws-white;
  text-align: center;
  display: none;
  .practice-score-string{
    p{
      font-family: 'Merriweather';
      color:@ws-white;
      font-size: 20px;
    }
    .board{
      p{
        color: @ws-white;
      }
      >div{
        p{
          color:rgba(0, 0, 0, 0.6);
          font-size: 12px;
          &.practice-score-string-score{
            font-size: 18px;
            color:@ws-white;
            font-family: 'Merriweather';
            margin-top:2px;
          }
        }
      }
    }
  }
  .medal-picture{
    img{
      height:100px;
    }
  }
}
.answer-summary{
  margin: 1rem 0;
  padding-bottom: 0.5rem;
  box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.1);
  .dummy{
    //width: 13.33%;
  }
  .summary-heading{
    text-align: center;
    font-size:16px ;
    font-family: 'Rubik', sans-serif;
    color:@ws-darkBlack;
    font-weight: bold;
  }
  .short-heading{
    text-align: center;
    font-size:14px ;
    font-family: 'Rubik', sans-serif;
    color:@ws-darkBlack;
    margin-top: 3px;
  }
  .share{
    font-size: 12px;
    color:@ws-lightOrange;
    font-family: 'Rubik', sans-serif;
  }
  .score-summary{
    margin-top: 1rem;
    >div{
      text-align: center;
      &:last-child {
        > div {
          color: @ws-darkBlack;
        }
      }
      >div{
        margin-top: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color:@ws-white;
        font-size: 18px;
      }
    }
    .correct-answers{
      height:64px;
      width: 64px;
      border-radius: 8px;
      background: @ws-blue-start;
    }
    .wrong-answers{
      height:64px;
      width: 64px;
      border-radius: 8px;
      background: linear-gradient(270deg, #97160D 0%, #BF3E35 100%);
    }
    .skipped-answers{
      height:64px;
      width: 64px;
      border-radius: 8px;
      background:@ws-white;
      border:1px solid rgba(68, 68, 68, 0.72);
    }
  }
  .accuracy-summary{
    margin-top: 1rem;
    >div{
      display: flex;
      align-items: center;
      >div{
        margin-left: 0.3rem;
        >span{
          margin-left: 5px;
          display: block;
          text-align: center;
          color:@ws-darkBlack;
          font-size: 14px;
          &+p{
            color:fade(@ws-darkBlack,40%);
            font-size: 10px;
            text-align: center;
          }
        }
      }
    }
    padding-bottom:0.5rem ;
    .time-taken{
      background: url("../../images/mobile/clock.svg") center center no-repeat;
      background-size: contain;
      width:32px;
      height:32px;
    }
    .answer-accuracy{
      background: url("../../images/mobile/accuracy.svg") center center no-repeat;
      background-size: contain;
      width:32px;
      height:32px;
    }
    .total-hours{
      background: url("../../images/mobile/hour.svg") center center no-repeat;
      background-size: contain;
      width:32px;
      height:32px;
    }
  }
  .button-wrapper{
    display: flex;
    justify-content: space-around;
    margin:1rem;
    .reattempt{
      font-size: 14px;
      font-family: 'Rubik', sans-serif;
      font-weight: @ws-header-fontWeight;
      color:@ws-lightOrange;
      background: none;
      border:none;
    }
    .viewsolution{
      color:@ws-white;
      background: @ws-blue-start;
      box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.24);
      border-radius: 2px;
      border:none;
      height:28px;
      font-size: 14px;
      font-family: 'Rubik', sans-serif;
      padding: 0 0.8rem;
      display: flex;
      align-items: center;
      text-decoration: none;
    }
  }
}
.analysis{
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  margin-top: 2rem;
  padding: 1rem 0;
  >div{
    h2{
      text-align: center;
      font-weight: @ws-header-fontWeight;
      font-size: 14px;
      font-family: 'Rubik-medium';
    }
  }
  .nav-tabs .nav-link{
    color:@ws-darkBlack;
  }
  .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active{
    border:none;
    background: none;
    border-bottom: 2px solid @ws-lightOrange;
    color:@ws-lightOrange;
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
  }
  ul{
    background: rgba(237, 237, 237, 0.5);
    border:none;
    flex-wrap: nowrap;
    overflow-y: auto;
    margin-top: 1rem;
    li{
      white-space: nowrap;
    }
  }
  .tab-content{
    >.tab-pane{
      height: 1px;
      overflow: hidden;
      display: block;
      visibility: hidden;
    }
    >.active{
      height: auto;
      overflow: auto;
      visibility: visible;
    }
    margin-top: 1rem;
    .button-wrapper{
      padding-bottom: 1rem;
      text-align: center;
      .viewsolution{
        color:@ws-white;
        background: @ws-blue-start;
        box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.24);
        border-radius: 2px;
        border:none;
        height:28px;
        font-size: 14px;
        font-family: 'Rubik', sans-serif;
        padding: 0 0.8rem;
        display: flex;
        align-items: center;
        text-decoration: none;
        width: 130px;
        justify-content: center;
        margin: 0 auto;
      }
    }
  }
  >div{
    &:first-child{
      width: 100%;
    }
  }
}
.notes-assignent{
  background: url("../../images/mobile/score.svg") center center no-repeat;
  background-size: contain;
  width: 24px;
  height:24px;
}
.rank{
  .notes-assignent{
    background: url("../../images/mobile/scorrerank.svg") center center no-repeat;
    background-size: contain;
    width: 24px;
    height:24px;
  }
}
.score{
  width: 50px;
  height:50px;
  background: #0F4C6F;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}
.analysis-summary,.suggestions-for-user{
  display: none;
}
.graph{
  height:200px;
}
.top-scores{
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  margin-top: 2rem;
  padding: 1rem 0;
  h4{
    color:fade(@ws-darkBlack,70%);
    font-size: 12px;
    padding-bottom: 0.5rem;
    font-family: 'Rubik-medium';
    font-weight: @ws-header-fontWeight;
  }
  a{
    color:@blue;
    font-size: 12px;
  }
  h2{
    color:@ws-darkBlack;
    font-size: 14px;
    font-weight: @ws-header-fontWeight;
  }
  .t-header{
    padding: 0 1.3rem;
  }
}
.rank1{
  background: url('../../images/mobile/rank1.svg') center no-repeat;
  background-size: contain;
  width:17px;
  height:20px;
  display: block;
  margin: 0 auto;
}
.rank2{
  background: url('../../images/mobile/rank2.svg') center no-repeat;
  background-size: contain;
  width:17px;
  height:20px;
  display: block;
  margin: 0 auto;
}
.rank3{
  background: url('../../images/mobile/rank3.svg') center no-repeat;
  background-size: contain;
  width:17px;
  height:20px;
  display: block;
  margin: 0 auto;
}
.feedback{
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  margin-top: 2rem;
  padding: 1rem 0;
  h2{
    color: rgba(68, 68, 68, 0.7);
    font-size: 12px;
    padding-bottom: 0.5rem;
    font-family: 'Rubik-medium';
    font-weight: 500;
    margin-left: 0.5rem;
  }
  p{
    margin-left: 0.5rem;
    font-size: 20px;
  }
  .starRating{
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
    margin-top: 1.5rem;
    input{
      display: none;
    }
    &:not(:checked) > label{
      width:1em;
      padding:0 .1em;
      overflow:hidden;
      white-space:nowrap;
      cursor:pointer;
      font-size:54px;
      line-height:1.2;
      color:#ddd;
      //text-shadow:1px 1px #bbb, 2px 2px #666, .1em .1em .2em rgba(0,0,0,.5);
      &:before{
        content: '★ ';
      }
      &:hover{
        color: gold;
        //text-shadow:1px 1px goldenrod, 2px 2px #B57340, .1em .1em .2em rgba(0,0,0,.5);
        & ~ label{
          color: gold;
          //text-shadow:1px 1px goldenrod, 2px 2px #B57340, .1em .1em .2em rgba(0,0,0,.5);
        }
      }
    }
    > input{
      &:checked{
        & + label {
          &:hover{
            color: #ea0;
            //text-shadow:1px 1px goldenrod, 2px 2px #B57340, .1em .1em .2em rgba(0,0,0,.5);
            & ~label{
              color: #ea0;
              //text-shadow:1px 1px goldenrod, 2px 2px #B57340, .1em .1em .2em rgba(0,0,0,.5);
            }
          }
        }
        & ~ label{
          color: #ea0;
          //text-shadow:1px 1px #c60, 2px 2px #940, .1em .1em .2em rgba(0,0,0,.5);
          &:hover{
            color: #ea0;
            //text-shadow:1px 1px goldenrod, 2px 2px #B57340, .1em .1em .2em rgba(0,0,0,.5);
            & ~label{
              color: #ea0;
              //text-shadow:1px 1px goldenrod, 2px 2px #B57340, .1em .1em .2em rgba(0,0,0,.5);
            }
          }
        }
      }
    }
    > label{
      &:hover{
        & ~ input{
          &:checked {
            & ~ label{
              color: #ea0;
              //text-shadow:1px 1px goldenrod, 2px 2px #B57340, .1em .1em .2em rgba(0,0,0,.5);
            }
          }
        }
      }
    }
  }
  .rating-submit{
    background: none;
    color:@ws-lightOrange;
    border:none;
    padding: 1rem;
    text-align: center;
    width: 100%;
  }
  textarea{
    padding: 1rem;
    min-height: 90px;
    border-radius: 4px;
    width: 100%;
    border: 0.5px solid fade(@ws-darkBlack,40%);
  }
}
/*the container must be positioned relative:*/


.setters {
  position: absolute;
  left: 85px;
  top: 75px;
}

.minutes-set {
  float: left;
  margin-right: 28px;
}

.seconds-set { float: right; }

.controlls {
  position: absolute;
  left: 75px;
  top: 105px;
  text-align: center;
}

.display-remain-time {
  font-family: 'Roboto';
  font-weight: 100;
  font-size: 65px;
  color: #F7958E;
}

#pause {
  outline: none;
  background: transparent;
  border: none;
  position: relative;
}

.play::before {
  display: block;
  content: "";
  position: absolute;
  top: -5px;
  left: -16px;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 8px solid #A8A8A8;
}

.pause::after {
  content: "";
  position: absolute;
  top: -4px;
  left: -18px;
  width: 9px;
  height: 10px;
  background-color: transparent;
  border-radius: 1px;
  border: 2px solid #A8A8A8;
  border-top: none;
  border-bottom: none;
}

#pause:hover { opacity: 0.8; }

.e-c-base {
  fill: none;
  stroke: #A8A8A8;
  stroke-width: 12px
}

.e-c-progress {
  fill: none;
  stroke:@ws-lightOrange;
  stroke-width: 12px;
  transition: stroke-dashoffset 0.7s;
}

.e-c-pointer {
  fill: #FFF;
  stroke: #F7958E;
  stroke-width: 2px;
}
#time-progress{
  transform: rotate(-90deg);
}

#e-pointer { transition: transform 0.7s; }

#report-que{
  .modal-content{
    border: none;
    border-radius: 0px;
    width: 80%;
    margin: 0px auto;
  }
  .modal-header{
    border-bottom: none;
    padding: 0.5rem 1rem;

  }
  .modal-footer{
    justify-content: center;
    border-top: none;
    padding: 0.5rem 1rem;
    .btn-submit{
      font-family: 'Rubik-medium';
      background: none;
      text-transform: uppercase;
      color:@ws-lightOrange;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .modal-title{
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
    color:fade(@ws-darkBlack,40%);
    display: flex;
    align-items: center;
    margin-top: 1rem;
    i{
      margin-left: 10px;
      color:#E95353;
      font-size: 14px;

    }
  }
}

/*checkbox component*/
.containers {
  display: block;
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 14px;
  color:@ws-darkBlack;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }
&:hover input ~ .checkmark {
  background-color: #ccc;
}
  .checkmark:after {
    left: 5px;
    top: 1px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }
  input:checked ~ .checkmark:after {
    display: block;
  }
  input:checked ~ .checkmark {
    background-color: @ws-lightOrange;
  }
}

/* Hide the browser's default checkbox */
.modal-body {
  /* Create a custom checkbox */
  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 14px;
    width: 14px;
    background-color: #eee;
  }

  /* Create the checkmark/indicator (hidden when not checked) */
  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }
  .letusknow {
    border: 1px solid fade(@ws-darkBlack, 54%);
    width: 200px;
    height: 60px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    textarea {
      border: none;
      border-bottom: 1px solid fade(@ws-darkBlack, 20%);
      outline: none;
    }
  }
}
#continue-test{
  .modal-header{
    border-bottom: none;
  }
  .modal-footer{
    border-top:none;
    justify-content: center;
  }
  .modal-body{
    div{

    }
  }
}
.img-success{
  background: url('../../images/mobile/clapping.svg') center no-repeat;
  height:40px;
  width: 40px;
  background-size: contain;
  margin:0 auto;
  margin-bottom:1rem;
}
#completed-subject{
 text-align: center;
  color:fade(@ws-darkBlack,72%);
  font-size: 14px;
  font-family: 'Rubik-medium';
  font-weight: @ws-header-fontWeight;
  margin-top: 2rem;
  padding: 0 3rem;
}
.comingup{
  margin-top:1rem;
  color:fade(@ws-darkBlack,40%) !important;
  font-size: 12px;
  font-family: 'Rubik-medium';
  font-weight: @ws-header-fontWeight;
  text-transform: uppercase;
  text-align: center;

  span{
    color:@ws-darkBlack;
    font-size: 14px;
    font-weight: @ws-header-fontWeight;
    font-family: 'Rubik-medium';
  }
}

.btn-continue{
  width: 75%;
  background: @ws-blue-start;
  color: #ffffff;
  text-transform: uppercase;
  font-size: 14px;
  font-family: 'Rubik-medium';
  font-weight: @ws-header-fontWeight;
}


.timeup{
  font-size: 24px;
  color:#B72319;
  font-family: 'Merriweather';
  text-align: center;
  font-weight: bold;
}

#submit-test{
  .modal-header{
    border-bottom:none;
    justify-content: center;
    h1{
      text-align: center;
      color:@ws-darkBlack;
      font-family: @ws-banner-font;
      font-weight: bold;
      font-size: 24px;
    }
  }
  .modal-body{
    background: #f8f8f8;
    h1{
      color:@ws-darkBlack;
      font-size: 16px;
      font-family: 'Rubik-medium';
      font-weight: @ws-header-fontWeight;

    }
    p{
      &.summary{
        font-size:14px;
        margin: 0;
        padding: 0;
        font-family: 'Rubik', sans-serif;
        font-weight: 500;
      }
    }
    span{
      font-size: 12px;
      font-weight: normal;
    }
    >div {
      margin: 0 auto;
      > div {
        p {
          font-size: 16px;
          margin-left: 1rem;
        }
        .circle {
          width: 40px;
          height: 40px;
          border-radius: 50px;
          display: inline-block;
          span {
            display: inline-block;
            text-align: center;
            width: 100%;
            margin-top: 0.8rem;
            font-size: 14px;
          }
          &.answered {
            background: linear-gradient(41.97deg, #2F80ED 14.96%, #579EFF 100%);
            color: #fff;
          }
          &.unanswered {
            border: 2px solid #444444;
            color: @ws-darkBlack;
          }
          &.review {
            background: linear-gradient(42.99deg, #A056E5 20.42%, #BF7AFF 100%);
            color: #fff;
          }
        }
      }
    }
  }
  .modal-footer{
    .Resume{
      background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
      border-radius: 4px;
      color:#fff;
    }
    justify-content: space-around;
    border-top:none;
    button{
      background: none;
      border:none;
      color:@ws-darkBlack;
      font-family: 'Rubik-medium';
      font-weight: @ws-header-fontWeight;
      font-size: 14px;
      outline:0;
      &.submit{
        color:@ws-lightOrange;
      }
    }
  }
}

.question-box{
  >.question{
    border-bottom: 1px solid fade(@ws-darkBlack,40%);
  }
  .question{
    p{
      padding: 0.4rem;
      font-size: 14px;
      color:@ws-darkBlack;
      font-family: 'Rubik', sans-serif;
      line-height:1.5;
      &.que-no{
        color:@ws-white;
        font-family: 'Rubik-medium';
        color: @ws-darkBlack;
        font-weight: 500;
      }
      &.question{
        //border-bottom: 1px solid fade(@ws-darkBlack,40%);
      }
    }
  }
  .your-answer{
    position:relative;
    min-height: 80px;
    display: flex;
    align-items: center;
    //background: rgba(207, 102, 95, 0.1);
    border-bottom:4px solid #B72319;
    padding-top: 0.3rem;
    padding-bottom: 0.3rem;
    >div{
      width: 100%;
      overflow: hidden;
      overflow-x: auto;
    }
    p{
      span{
        &.skipAnswer,&.correct-answer-by-user,&.wrong-answer-label{
          margin-top: 0.5rem;
        }
      }
      &.d-flex{
        align-items: center;
        min-height: 40px;
        span{

          &:last-child{
            line-height: 1.2;
            //width: 95%;
            //overflow: hidden;
            //overflow-x: auto;
          }
        }
      }
    }
    &.ans-correct{
      //background:  rgba(143, 242, 109, 0.1);
      border-bottom:4px solid #46B520;
      .choice{
        display: none;
      }
      .correct-answer{
        i{
          position: static;
        }
      }
    }
    &.correct-answer-by-user{
     // background:  rgba(143, 242, 109, 0.1);
      border-bottom:4px solid #46B520;
    }
    .choice{
      background: fade(@ws-darkBlack,40%);
      width:24px;
      height:24px;
      border-radius: 35px;
      display: flex;
      justify-content: center;
      align-items: center;
      color:@ws-white !important;
      font-family: 'Merriweather';
      font-size: 12px;
      margin-right: 1rem;
    }
  }
  .wrong-answer-label{
    color:#B72319;
    font-size: 8px;
    text-transform: uppercase;
    padding-bottom: 0.5rem;
    display: block;
  }
  .wrong-answer-by-user{
    color:@ws-darkBlack;
    display: flex;
    align-items: center;
    width: 75%;
    i{
      color:#B72319;
      margin-right: 1rem;
      position: absolute;
      right: 0;
      &.check{
        display: none;
      }
    }
  }
  span{
    &.skipAnswer{
      color: #0AAEF9;
      font-size: 8px;
      text-transform: uppercase;
      padding-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }
  }
  span.correct-answer-by-user{
    &.err-answer{
      color:@ws-darkBlack;
      font-size: @ws-menu-fontSize;
      text-transform: none;
    }
    span{
      color:@ws-darkBlack;
    }
    color:#46B520;
    font-size:8px;
    text-transform: uppercase;
    padding-bottom: 0.5rem;
    display: flex;
    align-items: center;
    i{
      color:#B72319;
      margin-right: 1rem;
      &.check{
        display: block;
        color:#46B520;
      }
      &.err{
        display: none;
      }
    }
  }
 .correct-answer{
    color:@ws-darkBlack;
    display: flex;
    align-items: center;
    i{
      color:#46B520;
      margin-right: 1rem;
      position: absolute;
      right: 0;
    }
  }
}
.answer-explanation-row{
min-height: 80px;
  display: flex;
  align-items: center;
  padding: 1rem 0;
}
.question-box{
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  margin-top: 2rem;
  padding: 1rem 0;
}
.show-explanation-btn{
  color:fade(@ws-darkBlack,72%) !important;
  font-size:8px;
  text-transform: uppercase;
  padding-bottom: 0.5rem;
  display: block;

}
.que-wrappers {
  padding: 1rem 0;
  &#scrollto {
    > .question-box {
      &:first-child {
        margin-top: 1rem;
      }
    }
  }
}
.markbutn{
  display: none;
  &+ label{
    background: url('../../images/mobile/bookmark.svg') center no-repeat;
    height:16px;
    width: 14px;
    background-size: contain;
    opacity: 0.7;
     cursor: pointer;
  }
  &:checked{
    &+ label{
      background: url('../../images/mobile/bookmarked.svg') center no-repeat;
      height:16px;
      width: 14px;
      background-size: contain;
    }
  }
}
.onclickScrollsList{
  border-bottom: 0.5px solid fade(@ws-darkBlack,30%);
  padding-bottom: 0.5rem;
  &:last-child{
    border: none;
  }
  &:first-child{
    >div{
      padding-top: 0.5rem;
    }
  }
}
#list{
  .question {
    //height: 15px;
    //overflow-y: hidden;
    width: 160px;
    padding: 0.5rem 0;
    >p {
     display: none;
      &:first-child{
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient:vertical;
        line-height: 1.5;
        font-size: 14px;
        //padding: 0.3rem 0;
        &:empty{
          display: none;
          + p {
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient:vertical;
            line-height: 1.5;
            font-size: 14px;
          }
        }
      }

      span{
        font-family: unset;
      }

    }
    > p + table{
      display: none;
    }
    > p{
      table,img{
        display: none;
      }
    }

  }
  div.que-no-list{
    font-size: 14px;
    color:@ws-darkBlack;
    font-family: 'Merriweather';
    width: 30px;
    height:30px;
    border-radius: 50%;
    background: @ws-white;
    margin-right: 1rem;
    margin-top: 0.3rem;
    border: 1px solid @ws-darkBlack;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
    display: flex;
    justify-content: center;
    align-items: center;
    a{
      color:@ws-darkBlack;
      text-decoration: none;
    }
    &.answered{
      background: #2F80ED;
      border:none;
      a {
        color: @ws-white;
      }
    }
    &.unanswered{
      background: #fff;
      border: 1px solid #444444;
      a{
        color:@ws-darkBlack;
      }
    }
    &.marked{
      background: #A056E5;
      border:none;
      a {
        color: @ws-white;
      }
    }
    &.notseen{
      background: #888888;
    }
  }
}

.web-mcq {
  table td {
    text-align: left;
    padding: 5px;
  }
  .onclickScrollsList{
    &:last-child{
      padding-bottom: 4rem;
    }
  }
  #collapseOne{
    padding-bottom: 5rem;
  }
  .menu-wrapper{
    .menu{
      display: none;
    }
  }
  min-height: 100vh;
  .sub-header {
    position: fixed;
    top:64px;
    /*z-index: 999;*/
  }
  .result-menu{
    top:4rem;
  }
  #question-block{
    //margin-top: 7rem;
  }
  .que-side-menu {
    position: unset;
    box-shadow: none;
    z-index: 0;
    border-right: 1px solid #ededed;

    .tab {
      display: block;
      padding-bottom: 2rem;
    }
  }
  .mt-fixed {
    margin-top: 3rem;
    padding-top: 2rem;
  }
  .grid, .list {
    margin-top: 0;
  }
  .web-position {
    height: 100%;
    width: 100%;
    overflow: hidden;
    > div {
      &:first-child {
        width: 260px;
        overflow-y: auto;
        top: 12rem;
        height: 100vh;
        position:fixed;
        padding-bottom: 7rem;
        overflow-x: auto;
        @media @iPad-landscape{
          left:6px;
        }
        @media @iPad-portrait,@iPhone,@iphoneX-landscape,@iPhone6-landscape{
          top:0;
          position: absolute;
          height: 100%;
        }
      }
    }
  }
  @media @iPad-portrait,@iPhone,@iphoneX-landscape,@iPhone6-landscape{
    .web-position {
      > div {
        &:first-child {

        }
      }

    }
    .menu-wrapper{
      .menu{
        display: block;
      }
    }
    .que-side-menu {
      position:fixed;
      top: 0;
      right: -31px;
      width:0;
      height: 100vh;
      background: @ws-white;
      box-shadow: -4px 5px 4px rgba(0, 0, 0, 0.25);
      z-index: 999;
      .close-menu {
        display: none;
        outline:none;
        width:24px;
        height:24px;
        border-radius: 50px;
        background: @ws-white;
        border: none;

        top: 3rem;

        span {
          background-image: url('../../images/mobile/close.svg');
          width: 11px;
          height: 11px;
          background-size: contain;
          background-repeat: no-repeat;
          display: block;

        }
      }
      .indicator{
        margin-top:1rem;
        .answered {
          .circle {
            background: #2F80ED;
          }
        }
        .unanswered {
          .circle {
            background: #fff;
            border: 1px solid @ws-darkBlack;
          }
        }
        .review {
          .circle {
            background: #A056E5;
          }
        }
        .notseen {
          .circle {
            background: #888888;
          }
        }

        .circle{
          height:15px;
          width: 15px;
          border-radius: 35px;
          display: block;
          margin-right: 10px;
        }
        p{
          color:@ws-darkBlack;
          font-size: 12px;
          font-family: 'Rubik', sans-serif;
          display: flex;
        }
      }
      .tab{
        display: none;
        margin-top: 1.5rem;
        .nav-tabs{
          background: #F8F8F8;
          border:none;
          display: flex;
          justify-content: space-evenly;
        }
        .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active{
          border: none;
          background: none;
          border-bottom: 2px solid @ws-lightOrange !important;
          font-family: 'Rubik-medium', sans-serif;
          font-size: 14px;
          color:@ws-lightOrange;
        }

        .nav-tabs .nav-item .nav-link, .nav-tabs .nav-link{
          border-bottom: 2px solid transparent;
          font-family: 'Rubik-medium';
          font-weight: @ws-header-fontWeight;
          font-size: 14px;
        }


      }
    }
  }

}

.read-backarrow{
  display:none;
  cursor: pointer;
}
.start-test{

  .header{
    display: flex;
    align-items: center;
    justify-content: center;
    height:56px;
    background: #FFFFFF;
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.12);
    margin-bottom: 2rem;
    p{
      font-size: 20px;
      font-weight: 500;
    }
  }
  .card{
    min-width: 320px;
    min-height: 64px;
    background: @ws-white;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
    margin:0 auto;
    .disclaimer{
      h4{
        color:fade(@ws-darkBlack,72%);
        font-family: @ws-header-font;
        font-size:20px;
        font-weight: normal;
        padding: 1rem;
        margin-left: 3rem;
      }
      p{
        text-align: center;
        font-size: 20px;
        font-family: @ws-header-font;
        font-weight: @ws-header-fontWeight;
        padding: 0.5rem 3rem;
      }
    }
   >.language{
     min-height: 64px;
     p{
       display: flex;
       align-items: center;
       font-size: 24px;
       @media @iPhone{
         font-size: @ws-header-fontSize;
       }
       &:before{
         background: url('../../images/mobile/language.svg') no-repeat;
         background-size: contain;
         width: 38px;
         height: 38px;
         content:'';
       }
     }
     select{
       border:none;
       color:fade(@ws-darkBlack,72%);
     }

   }
    .patterns{
      &.section {
        p {
          &.lang {
            font-size: 18px;
            &:before {
              background: url('../../images/mobile/secpattern.svg') no-repeat;
              background-size: contain;
              width: 24px;
              height: 24px;
              position: relative;
              content: '';
              left: -15px;
            }
          }
        }
      }
      >div{
        border-bottom: 1px solid #ededed;
        padding: 1rem 0;
      }
      h4{
        color:fade(@ws-darkBlack,72%);
        font-family: @ws-header-font;
        font-size:20px;
        font-weight: normal;
        padding: 1rem;
        //margin-left: 3rem;
      }

      p{
        font-family: @ws-header-font;
        font-size:24px;
        font-weight: normal;
        color:@ws-darkBlack;
        display: flex;
        justify-content: center;
        align-items: center;
        @media @iPhone{
          font-size: 18px;
        }
        span{
          font-size: 20px;
          font-weight: @ws-header-fontWeight;
          font-family: @ws-header-font;
          &.positive{
            color:#46B520;
          }
          &.negative{
            color:@ws-red;
          }
        }
        &:first-child{
          width: 40%;
          @media @iPhone{
            width: auto;
          }
        }
        &:last-child{
          width: 40%;
          @media @iPhone{
            width: auto;
          }
        }
        &.markval{
          font-weight: @ws-header-fontWeight;
        }
        &.noque{
          @media @iPhone{
            font-size: 18px;
          }
           &:before{
             background: url('../../images/mobile/noque.svg') no-repeat;
             background-size: contain;
             width: 24px;
             height: 24px;
             position: relative;
             content: '';
             left: -15px;
           }
        }
        &.timers{
          &:before{
            background: url('../../images/mobile/timer.svg') no-repeat;
            background-size: contain;
            width: 24px;
            height: 24px;
            position: relative;
            content: '';
            left: -15px;
          }
        }
        &.markss{
          &:before{
            background: url('../../images/mobile/marks.svg') no-repeat;
            background-size: contain;
            width: 24px;
            height: 24px;
            position: relative;
            content: '';
            left: -15px;
          }
        }
      }
    }
  }
.starttest-wrapper{
  //height:100vh;
  overflow-y: auto;
}
  .str-test{
    //height:calc(100vh - 143px);
    margin-bottom: 4rem;
    min-height: 70vh;
  }
  .footer-test{
    height:87px;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
   >.container{
     height:87px;
     display: flex;
     align-items: center;
     justify-content: center;
   }
    .btn-starts{
      background: @ws-blue-start;
      border-radius: 4px;
      color:@ws-white;
    }
  }
}
.btn-starts{
  background: @ws-blue-start;
  border-radius: 4px;
  color:@ws-white;
  &:hover{
    color:@ws-white;
  }
  &:focus{
    color:@ws-darkBlack;
  }
}
button{
  cursor: pointer;
}
//.loading-icon {
//  width: 100%;
//  height: 100%;
//  background-color: rgba(68, 68, 68, 0.64);
//  position: fixed;
//  top: 0;
//  right: 0;
//  bottom: 0;
//  left: 0;
//  z-index: 9999; }
//.loader-wrapper {
//  width: 139px;
//  height: 65px;
//  background-color: #FFFFFF;
//  position: relative;
//  top: 50% !important;
//  -webkit-transform: translateY(-50%);
//  -moz-transform: translateY(-50%);
//  -ms-transform: translateY(-50%);
//  -o-transform: translateY(-50%);
//  transform: translateY(-50%);
//  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
//  margin: 0 auto;
//  border-radius: 4px; }
//
//.loader,
//.loader:before,
//.loader:after {
//  border-radius: 50%;
//  width: 2.5em;
//  height: 2.5em;
//  -webkit-animation-fill-mode: both;
//  animation-fill-mode: both;
//  -webkit-animation: load7 1.8s infinite ease-in-out;
//  animation: load7 1.8s infinite ease-in-out; }
//
//.loader {
//  color: #F05A2A;
//  font-size: 9px;
//  margin: 0 auto;
//  position: relative;
//  text-indent: -9999em;
//  -webkit-transform: translateZ(0);
//  -ms-transform: translateZ(0);
//  transform: translateZ(0);
//  -webkit-animation-delay: -0.16s;
//  animation-delay: -0.16s; }
//
//.loader:before,
//.loader:after {
//  content: '';
//  position: absolute;
//  top: 0; }
//
//.loader:before {
//  left: -3.5em;
//  -webkit-animation-delay: -0.32s;
//  animation-delay: -0.32s; }
//
//.loader:after {
//  left: 3.5em; }
//
//@-webkit-keyframes load7 {
//  0%,
//  80%,
//  100% {
//    box-shadow: 0 2.5em 0 -1.3em; }
//  40% {
//    box-shadow: 0 2.5em 0 0; } }
//@keyframes load7 {
//  0%,
//  80%,
//  100% {
//    box-shadow: 0 2.5em 0 -1.3em; }
//  40% {
//    box-shadow: 0 2.5em 0 0; } }
.hidden{
  display: none;
}

.correct-answer-explanation{
  line-height: 1.5;
  font-size: 14px;
  p{

  }
}
.ans-neutral{
  background: @ws-white !important;
  border-bottom: 1px solid fade(@ws-darkBlack,50%)!important;
  min-height: 80px;
  display: flex;
  align-items: center;
  p{
    span{
      color:@ws-darkBlack !important;
      i{
        color:@ws-darkBlack !important;
      }
    }
  }
  i.err{
    display: none;
  }
}

.ans-tab{
  margin-top: 3rem;
  display: none;
  .nav-tabs{
    background: rgba(237, 237, 237, 0.3);
    flex-wrap: nowrap;
    white-space: nowrap;
    overflow: hidden;
    overflow-x: auto;
    border:none;
    .nav-item{
      &.show{
        .nav-link{
          border-color:transparent;
        }
      }
    }
    .nav-link{
      color:fade(@ws-darkBlack,72%);
      font-size: @ws-menu-fontSize;
      font-family: @ws-header-font;
      display: flex;
      align-items: center;
      &.active{
        border-color:transparent;
        color:@ws-lightOrange;
        border-bottom: 3px solid @ws-lightOrange;
        background: rgba(237, 237, 237, 0.3);
      }
      span{
        margin-right: 0.3rem;
        font-size: 24px;
      }
      &.correct{
         span{
           color:#29C42E;
         }
      }
      &.incorrect {
        span {
          color: @ws-red;
        }
      }
        &.skipped{
          span{
            color: #F2C94C;
          }
        }
      }
    }
}
.medal-picture{
  >div{
    width: 90px;
    height: 90px;
    margin: 0 auto;
    margin-bottom: 1rem;
    &.medalnone{
      background: url('../../images/mobile/medal-none.png') center no-repeat;
      background-size: contain;
    }
    &.gold{
      background: url('../../images/mobile/medal-gold.png') center no-repeat;
      background-size: contain;
    }
    &.silver{
      background: url('../../images/mobile/medal-silver.png') center no-repeat;
      background-size: contain;
    }
    &.bronze{
      background: url('../../images/mobile/medal-bronz.png') center no-repeat;
      background-size: contain;
    }
  }
}
#sectionSelection{
  width: 150px;
}

#width_tmp_select{
  display : none;
}
.directions {
  margin-top: 1rem;
  margin-bottom: 1rem;
  p {
    line-height: 1.5;
  }
}
.question-wrapper + .container p{
  padding-bottom: 0.5rem;
}
.question-wrapper + .container p span{
  line-height: 1.5;
}
.katex{
  line-height: 3 !important;
  white-space: normal !important;
}
.question .katex{
  line-height: 2 !important;
}
.katex .base{
  white-space:normal !important;
  width:unset !important;
}
.web-mcq .que-side-menu .tab .tab-content{
  margin-top: 0rem;
}
.web-mcq .que-side-menu .nav-tabs{
  position: static;
  width: auto;
  top:auto;
}
.video-wrapper {
  position: relative;
  padding-bottom: 50%; /* 16:9 */
  padding-top: 25px;
  height: 0;
}
.video-wrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.flex_st{
  display: flex;
}
.que-wrappers{
  table, td {
    border: 1px solid @ws-darkBlack;
    text-align: center;
  }
}
#videoContent{
  .modal-content{
    background: transparent;
    border: none;
  }
  .modal-header{
    border: none;
  }
  .modal-body{
    padding: 0;
  }
  .close{
    opacity: 1;
    text-shadow: none;
    color:#F79420;
  }
}
.showVideobtn{
  background: none;
  border:none;
  color:#F79420;
  display: flex;
  align-items: center;
  margin: 5px auto;
  i{
    margin-right:5px;
  }
}
#answer-block ul{
  list-style-type: inherit;
  margin-left: 2rem;
}

.ws-header {
  .navbar-nav {
    .nav-item {
      .dropdown-menu {
        a.dropdown-item {
          padding: 0.5rem 1.5rem;
        }
        .media {
          a.edit-btn i {
            margin-top: 4px;
          }
        }
        .media-body p {
          margin-bottom: 20px;
        }
      }
    }
  }
}
