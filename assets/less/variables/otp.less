@import "ws_color_theme.less";
@import "ws_fonts.less";
@import "responsive.less";
.otp-screens{
  [data-verify="otp-code"]{
    .req-otpwrapper,#verify-otp,.mobile-text{
      display: block;
    }
    .mobile-wrapper,.otp-text,#get-otp{
      display: none;
    }
  }
  h4{
    font-size: 24px;
    font-family: @ws-header-font;
    color:@ws-darkBlack;
    font-weight: normal;
  }
  p{
    font-size: 12px;
    color:fade(@ws-darkBlack,40%);
    padding: 0.5rem 2rem;
    span{
      font-size: 12px;
      font-weight: @ws-header-fontWeight;
      color:@ws-darkBlack;
    }
  }
  .btn-continue{
    width: 90%;
  }
  .modal-header{
    border-bottom: none;
    padding: 1.5rem 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    .close{
      padding: 0;
      margin: 0;
    }
  }
 .modal-footer {
    border-top: none;
   justify-content:space-around ;
   margin-bottom: 1rem;
  }
  .input-login {
    overflow: hidden;
    position: relative;
    z-index: 1;
    display: inline-block;
    margin: 0.3rem;
    width: 312px;
    input {
      border: none;
      outline: 0;
      margin-top: 1rem;
      padding: 0.85em 0.15em;
      width: 100%;
      background: transparent;
      color: #595F6E;
      &:focus {
        & + label {
          &::after {
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
          }
          > span {
            -webkit-animation: anim-1 0.3s forwards;
            animation: anim-1 0.3s forwards;
          }
        }
      }
    }

    label {
      position: absolute;
      bottom: 0;
      left: 0;
      padding: 0 0.25em;
      width: 100%;
      height: calc(100% - 1em);
      text-align: left;
      pointer-events: none;
      > span {
        position: absolute;
        color:fade(@ws-darkBlack,40%);
        text-align: center;
        width: 100%;
        color:fade(@ws-darkBlack,40%);
        font-size: 12px;
        &::after {
          border-color: hsl(200, 100%, 50%);
        }
      }
      &::before {
        content: '';
        position: absolute;
        top: 8px;
        left: 0;
        width: 100%;
        height: calc(100% - 10px);
        border-bottom: 1px solid #B9C1CA;
      }
      &::after {
        content: '';
        position: absolute;
        top: 7px;
        left: 0;
        width: 100%;
        height: calc(100% - 10px);
        border-bottom: 1px solid #B9C1CA;
        margin-top: 2px;
        border-bottom: 2px solid @ws-lightOrange;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
        -webkit-transition: -webkit-transform 0.3s;
        transition: transform 0.3s;
      }
    }

    @-webkit-keyframes anim-1 {
      50% {
        opacity: 0;
        -webkit-transform: translate3d(1em, 0, 0);
        transform: translate3d(1em, 0, 0);
      }
      51% {
        opacity: 0;
        -webkit-transform: translate3d(-1em, -40%, 0);
        transform: translate3d(-1em, -40%, 0);
      }
      100% {
        opacity: 1;
        -webkit-transform: translate3d(0, -40%, 0);
        transform: translate3d(0, -40%, 0);
      }
    }

    @keyframes anim-1 {
      50% {
        opacity: 0;
        -webkit-transform: translate3d(1em, 0, 0);
        transform: translate3d(1em, 0, 0);
      }
      51% {
        opacity: 0;
        -webkit-transform: translate3d(-1em, -40%, 0);
        transform: translate3d(-1em, -40%, 0);
      }
      100% {
        opacity: 1;
        -webkit-transform: translate3d(0, -40%, 0);
        transform: translate3d(0, -40%, 0);
      }
    }
  }
}
.resend-otp{
  color:@ws-lightOrange;
  font-weight: @ws-header-fontWeight;
  &:hover{
    color:@ws-lightOrange;
  }
}
#otp-error{
  color:@ws-red;
}
@media (min-width: 576px){
.otp-screens .modal-dialog {
  width: 400px;
}
}
.modal {
  background: rgba(0, 0, 0, 0.3) !important;
}
.btn-continue{
  background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
  color: #ffffff;
  text-transform: uppercase;
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
}

.req-otpwrapper,#verify-otp,.mobile-text{
  display: none;
}
.recieve-msg{
  color:fade(@ws-darkBlack,40%);
  font-size: 12px;
  text-align: center;
  display: block;
}
#otp-next,#otp-confirm,.otp-confirm{
  .modal-body{
    p{
      font-size: 16px;
      color:@ws-darkBlack;
    }
  }
  .btn-continue{
    width: 50%;
  }
}