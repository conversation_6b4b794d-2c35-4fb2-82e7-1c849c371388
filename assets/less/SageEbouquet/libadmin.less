.flex_st {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.adminForm{
  .btn-primary {
    color: #ffffff;
    background-color: #f15b2a;
    border-color: #ef4912;
  }
}
//label{
//  font-size: 20px;
//}
//.lib{
//  .form-control{
//    font-size: 20px;
//    //color: #ced4da;
//  }
//}
::-webkit-input-placeholder{
  font-size: 15px;
  color: #cccccc;
}

.lib2{
  font-size: 20px;
}
#addBooks{
  .form-group{
    font-size:18px;
  }
}
.table-responsive {
  overflow-x: auto;
  min-height: 0.01%;
}
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
}
.table > tbody > tr > td {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #dddddd;
}
.table-bordered th{
  width:470px;
}
.adminForm .btadd {
  color: #ffffff;
  background-color: #f15b2a;
  border-color: #ef4912;
  margin-left: 2px;
  margin-top: -2px;
  margin-bottom: 0px;

}
#content-books1 {
  .alert {
    position: relative;
    margin-bottom: 1rem;
    margin-left: 0px;
    margin-right: 32px;
    border: 1px solid red;
    border-radius: 0.25rem;
  }
  .alert-danger {
    color:red;
  }
}
#batchUsers{
  p{
    margin-bottom: 0;
  }
}
.liad{
  font-weight: normal;
}
#published-books .evidya-Title td.pub-buk a{
  word-wrap: break-word;
  white-space: initial;
  padding: 0;
}
