@import "variables/color.less";
@import "variables/fonts.less";
footer {
  //height:90px;
  //display: flex;
  align-items: center;
  //border-top: 1px solid fade(#444444,50%);
  background: #DCDDDE;
  position: relative;
  //border-top: 1px solid #37474f;
  /*&:before {
    content: '';
    position: absolute;
    top: 0;
    //left: -490px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #37474f;
  }*/
  .social-icons {
    img {
      width: 45px;
      height: 45px;
    }
  }
  .promise-icon {
    img {
      width: 150px;
      height: auto;
    }
  }
  &.footer-menu {
    >.row{
      display: flex;
      align-items: center;
    }
    ul {
      li {
        list-style-type: none;
        a {
          color: @black;
        }
      }
    }
  }
  p {
    color:black;
  }
  a {
    color: black;
    padding-right: .25rem;
  }
}
