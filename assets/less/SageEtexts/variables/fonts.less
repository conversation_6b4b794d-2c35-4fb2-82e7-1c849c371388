@font-face {
  font-family: 'HelveticaNeue-Roman';
  src: url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Roman.eot?#iefix') format('embedded-opentype'),  url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Roman.otf')  format('opentype'),
  url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Roman.woff') format('woff'), url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Roman.ttf')  format('truetype'), url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Roman.svg#HelveticaNeue-Roman') format('svg');
}
@font-face {
  font-family: 'HelveticaNeue-Medium';
  src: url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Medium.eot?#iefix') format('embedded-opentype'),  url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Medium.otf')  format('opentype'),
  url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Medium.woff') format('woff'), url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Medium.ttf')  format('truetype'), url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Medium.svg#HelveticaNeue-Medium') format('svg');
}
@font-face {
  font-family: 'HelveticaNeue-Bold';
  src: url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Bold.eot?#iefix') format('embedded-opentype'),  url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Bold.otf')  format('opentype'),
  url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Bold.woff') format('woff'), url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Bold.ttf')  format('truetype'), url('fonts/sageEtexts/HelveticaNeue/HelveticaNeue-Bold.svg#HelveticaNeue-Bold') format('svg');
}
@font-face {
  font-family: 'MyriadPro-Regular';
  src: url('fonts/sageEtexts/MyriadPro/MyriadPro-Regular.eot?#iefix') format('embedded-opentype'),  url('fonts/sageEtexts/MyriadPro/MyriadPro-Regular.otf')  format('opentype'),
  url('fonts/sageEtexts/MyriadPro/MyriadPro-Regular.woff') format('woff'), url('fonts/sageEtexts/MyriadPro/MyriadPro-Regular.ttf')  format('truetype'), url('fonts/sageEtexts/MyriadPro/MyriadPro-Regular.svg#MyriadPro-Regular') format('svg');
}
@font-face {
  font-family: 'MyriadPro-Semibold';
  src: url('fonts/sageEtexts/MyriadPro/MyriadPro-Semibold.eot?#iefix') format('embedded-opentype'),  url('fonts/sageEtexts/MyriadPro/MyriadPro-Semibold.otf')  format('opentype'),
  url('fonts/sageEtexts/MyriadPro/MyriadPro-Semibold.woff') format('woff'), url('fonts/sageEtexts/MyriadPro/MyriadPro-Semibold.ttf')  format('truetype'), url('fonts/sageEtexts/MyriadPro/MyriadPro-Semibold.svg#MyriadPro-Semibold') format('svg');
}
@Monserrat: 'Montserrat', sans-serif;
@Helvetica-Roman: 'HelveticaNeue-Roman';
@Helvetica-Medium: 'HelveticaNeue-Medium';
@Helvetica-Bold: 'HelveticaNeue-Bold';
@MyriadPro-Regular: 'MyriadPro-Regular';
@MyriadPro-Semibold: 'MyriadPro-Semibold';
@sageFont:'HelveticaNeue-Roman';
@mediumWeight:500;
@regular:400;
