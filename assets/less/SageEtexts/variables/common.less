@import "color.less";
@import "fonts.less";
body {
  font-family: @Helvetica-Roman;
  margin: 0;
  padding: 0;
  font-size: 15px;
}
.btn {
  font-family: @Helvetica-Medium;
}
.btn-warning {
  background-color: @etext-Orange !important;
  border-color: @etext-Orange !important;
  color: white !important;
}
.btn-primary {
  background-color: @etext-Blue !important;
  border-color: @etext-Blue !important;
  color: white !important;
}
.btn-outline-primary {
  border-color: @etext-Blue !important;
  color: @etext-Blue !important;
  &:hover {
    background-color: transparent !important;
  }
}
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px white inset;
}
::-webkit-input-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: @sageFont;
  font-size: 15px !important;
}
::-moz-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: @sageFont;
  font-size: 15px !important;
}
:-ms-input-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: @sageFont;
  font-size: 15px !important;
}
:-moz-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: @sageFont;
  font-size: 15px !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  color: rgba(68, 68, 68, 0.3);
  font-family: @sageFont;
  font-size: 15px !important;
}
input[type='text'] {
  font-family: @sageFont;
}
