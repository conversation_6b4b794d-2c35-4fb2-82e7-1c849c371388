@import "../wstemplate/theme/colors.less";
@import "../wstemplate/theme/fonts.less";
@import "../wstemplate/theme/responsive.less";
body.whitelabel_ws {
  width:100%;
  min-height:100% !important;
  height:100% !important;
  max-height:100%;
  padding-top:5%;

  div#allAddButton {
    display:none !important;
  }
}



.global-search input[type="text"] {
  padding-left: 10px;
  padding-right: 40px;
  z-index: 3;
}
@media (max-width: 320px) {
  .global-search input[type="text"] {
    padding-right: 20px;
  }
}
.global-search button {
  width: auto;
  height: 33px;
  margin-left: -38px;
  padding: 4px;
  position: relative;
  z-index: 10;
  color: #EF7215 !important;
}
.global-search button .material-icons {
  line-height: normal;
}
form.form-inline.rounded.rounded-modifier.col-12.col-lg-10.col-xl-8.pl-0.px-md-3 {
  margin:0;
  box-shadow: 0 1px 5px 0 rgb(0 0 0 / 10%), 0 1px 10px 0 rgb(0 0 0 / 5%);

}
p#checkcoupons {
  display:none !important;
}
.bookShadow .uncover
{
  height:100% !important;
  min-height:165px !important;
}
a#buyNow {
  color:white;
}
@media (max-width:575px) {
  body.whitelabel_ws {

    padding-top: 12%;
  }

  ul.there-social-footer-link-wrp {
    width:65%;
    margin:auto;
    margin-bottom:5%;
  }

  footer.pt-0.mt-4 {
    min-height:320px;
  }

  .col-12.col-md-4.text-center {}

  p.copyright-footer {
    padding-top:2%
  }


  .d-flex.justify-content-start.global-search.pr-0.pr-md-3.col {
    width:80% !important;
    margin-right:15%;
  }
  .ws-header div.mobile-profile
  {
    display: flex;
    position: absolute;
    z-index: 9999;
    right: 25px !important;
  }

  input#search-book-header::placeholder  {
    font-size:12px !important;
  }

}
ul.typeahead.dropdown-menu {
  left:0% !important;
  width:100%;
}
ul.navbar-nav.mr-auto.d-none.d-md-flex {
  position:relative;

}
div#innerMobile {
  position:absolute;
  left:0%;
  width:100%;
  background:white;
  top:100%;
  overflow: scroll;
  box-shadow: 0 1px 5px 0 rgb(0 0 0 / 10%), 0 1px 10px 0 rgb(0 0 0 / 5%);


}
#innerMobile .dropdown-menu
{
  border:none;
}
.navbar-nav.mr-auto.d-block.d-md-none #innerMobile {
  display:none;
}
.navbar-nav.mr-auto.d-block.d-md-none.highlight #innerMobile {
  display:block;
}
.there-social-footer-link-wrp li a {
  padding: 4px 10px;
  display: inline-block;
  color: white;
  -ms-transition: 0.3s all ease-in-out;
  -o-transition: 0.3s all ease-in-out;
  -moz-transition: 0.3s all ease-in-out;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
  font-size: 19px;

}
.there-social-footer-link-wrp li  {
  display: inline-block;
  color: white;
  -ms-transition: 0.3s all ease-in-out;
  -o-transition: 0.3s all ease-in-out;
  -moz-transition: 0.3s all ease-in-out;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
  font-size: 19px;


}
.there-social-footer-link-wrp
{
  list-style:none !important;
  padding-left: 0% !important;
}
footer .android-app-link {
  border: 1px solid #ddd;
  padding: 2px 0 5px;
  border-radius: 7px;
  min-width: 100px;
  background-color: #e5e5e5;
  color:black;
}
footer  .android-app-link img {
  width: 20px;
  margin-right: 10px;
  height: auto;
}
footer .android-app-link span {
  line-height: normal;
  font-size: 10px;
}
ul.footer-menu.text-center.text-md-left.pl-0 li, ul.footer-menu.text-center.text-md-left.pl-0 li a{
  font-size:14px !important;
}

.web-mcq .sub-header {
  position: fixed;
  top: 64px !important;
  padding-top: 10px !important;

}
div#filters {
  width:100% !important;
}

.web-mcq .result-menu
{
  top: 64px !important;
  padding-top: 10px;
}
.web-mcq .mt-fixed {
  padding-top:7rem !important;
}
nav.mobile-menu img.mb-1.active {
  width:28px;
  height:28px;
  padding:0;
  width:100%;
}
nav.mobile-menu  a{
  padding:0 !important;
}

header.position-sticky {
  position:fixed !important;
  top:0;
}

footer.pt-0.mt-4 a
{
  text-decoration:none;
}
div#mobileNav img
{
  display:none;
}
.books-list .topSchoolBooks a{
  text-decoration:none;
}
.mobile-menu .navbar-nav .nav-item a:first-child {
  background: url(../landingpageImages/winners-store.svg) center center no-repeat;
  background-size: contain;
  width: 28px;
  height: 28px;
}

.mobile-menu .navbar-nav .nav-item a:first-child.active {
  background: url(../landingpageImages/winners-store1.svg) center center no-repeat;
  background-size: contain;
  width: 28px;
  height: 28px;
}
div#innerMobile a{
  color:black !important;
}