function userPlay(newTime,userAns,correctOption,storeTimer,answer) {

    myCounter = newTime;
    console.log(myCounter);
    console.log(storeTimer);
    userAns=userAns;
    // answer=answer;
    var userCounter = 100 / myCounter;
    var divTime = timeValue / 100;
    var userAnswerTime = ((100 / userCounter) * (100 / divTime)) + '%';
    var userTimeTaken;
    userTimeTaken = (timeValue / 100) - myCounter;   //total 15sec - that counter='user answer time'
    userTimeTaken = userTimeTaken.toFixed(2);
// userAns=userAns.trim();

    if (!testGen){
        que_answer = que_answer.trim();
        que_answer = que_answer.replace(' />', '>');
    }

    if (userAns == correctOption) { //if user selected option is equal to array's correct answer
        if (source == 'web') {
            answerCorrectAudio('/funlearn/showImage?id=2&fileName=correct.mp3&imgType=audio');
        }
        userScore += 1; //upgrading score value with 1
        console.log(userScore);
        answer.classList.add("correct"); //adding green color to correct selected option
        if(quizMode=='test' || quizMode=='testSeries'){
            answer.classList.add("clicked");
        }
        $('#playerScore').text(userScore + ' pts');
        $('.progress-bar-vertical.player1').addClass('progress-correct');
        $('#player1').css({
            'height': userAnswerTime,
            'background': '#42B538'
        });
         if(quizMode !=''){
             userTimeTaken=storeTimer;
         }
        sendUserAnswer = {userOption: correctOption, userTime: userTimeTaken};
        console.log(sendUserAnswer);

    } else {
        if (source == 'web') {
            answerInCorrectAudio('/funlearn/showImage?id=2&fileName=incorrect.mp3&imgType=audio');
        }
        answer.classList.add("incorrect"); //adding red color to correct selected option
        if(quizMode=='test' || quizMode=='testSeries'){
            answer.classList.add("clicked");
        }
        selectUserAnswerIndex = $('.incorrect').index() + 1;
        $('.progress-bar-vertical.player1').addClass('progress-incorrect');
        $('#player1').css({'height': userAnswerTime, 'background': '#FF4141'});
        if(quizMode !=''){
            userTimeTaken=storeTimer;
        }
        sendUserAnswer = {userOption: selectUserAnswerIndex, userTime: userTimeTaken}
        console.log(sendUserAnswer);
    }
    console.log(sendUserAnswer);
    userTimeTaken = Number(userTimeTaken);
    userTotalTime = Number(userTotalTime);
    if(quizMode!='practice') {
        userTotalTime = (userTotalTime + userTimeTaken).toFixed(2);
    }
    else{
        userTotalTime=0;
    }

    $.each(sendUserAnswer,function(key,value){
        answerObj[key] = value;
    }) //reassign values to particular id of answerobj
    if (botAnswerFirst) {
        userAnswerFirst = false;
        if (myCounter >= 4) {
            if(quizMode=='') {
                setTimeout(showCorrectAnswer, 2000);
            }
            setTimeout(clearImmediate, 3000);
            setTimeout(nextQue, 4000);
        }
    } else {
        userAnswerFirst = true;
        chatBotAnswerTime = (myCounter - 3).toFixed(2);
        botRun(botAnswer, chatBotAnswerTime);
    }

    if(quizMode=='practice'){
        setTimeout(showCorrectAnswer,500);
    }
}