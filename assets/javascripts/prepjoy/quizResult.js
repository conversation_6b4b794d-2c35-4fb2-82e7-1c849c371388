var anim;
var animdata;
var userWin='';
var userTotalPoints;
var maxValueSlider=0;
var nextChallenger;
var nextchallengerPlace;
var hunter = 0;
var warrior = 100;
var gladiator = 300;
var knight = 800;
var ninja = 2000;
var samurai = 4000;
var ultimate = 8000;
var userTotalPointsVal = 0;
var currentBadgeVal = '';
var newBadge = null;
var newMedal = null;
var medalPresent = false;
var badgePresent = false;

var monthlyRankList = null;
var monthlyRankInstList = null;
var weeklyRankList = null;
var weeklyRankInstList = null;
var dailyRankList = null
var dailyRankInstList = null;
var rankPresent = false;
var instRankPresent=false;

function gameResult(){
    if(source=='web') {
        stopAudios();
    }
    $('#winnerModal').modal('show');
    animdata ={
        container: document.getElementById("winner"),
        renderer: "svg",
        loop: true,
        autoplay: true,
        path: ""
    };
    if(userScore>botScore){
        $('#user-status').text('You won the game');
        userWin='win';
        if(source=='web'){
            animdata.path = '/assets/prepJoy/win.json';
            winAudios('/funlearn/showImage?id=2&fileName=won.mp3&imgType=audio');
        }
    }
    else if(userScore==botScore){
        $('#user-status,.user-status').text('Oh! its a tie').addClass('red');
        userWin='Draw';
        if(source=='web'){
            animdata.path = '/assets/prepJoy/tie.json';
            tieAudios('/funlearn/showImage?id=2&fileName=tie.mp3&imgType=audio');
        }
    }
    else{
        $('#user-status,.user-status').text('You lost the game').addClass('red');
        userWin='Lose';
        if(source=='web'){
            animdata.path = '/assets/prepJoy/lose.json';
            loseAudios('/funlearn/showImage?id=2&fileName=lose.mp3&imgType=audio');
        }
    }
    anim = bodymovin.loadAnimation(animdata);
    $("#winnerModal").on('shown.bs.modal', function(){
        $('.quizes,.quiz-profile').addClass('d-none').removeClass('d-flex');
        setTimeout(submitAnswers,1000);
    });
}

function currentBadge(data) {
    var nextBadge = '';
    var balancePoints;
    var newRanks='';
    var badgeDetails = data.prepJoyUserDetails;
    nextChallenger = data.challengerName;
    nextchallengerPlace = data.challengerPlace;
    badgeDetails = badgeDetails.split(",").reduce(function (obj, str, index) {
        var strParts = str.split(":");
        if (strParts[0] && strParts[1]) { //<-- Make sure the key & value are not undefined
            obj[strParts[0].replace(/\s+/g, '')] = strParts[1].trim(); //<-- Get rid of extra spaces at beginning of value strings
        }
        return obj;
    }, {});

    newBadge = data.newBadge;
    newMedal = data.newMedal;
    var totalPoints = badgeDetails.totalPoints;
    currentBadgeVal = badgeDetails.currentBadge;
    var totalMedals = badgeDetails.totalMedals;
    userTotalPointsVal = data.totalPoints;


    monthlyRankList = data.newMonthlyRanks;
    monthlyRankInstList = data.newMonthlyRanksInstitute;
    weeklyRankList = data.newWeeklyRanks;
    weeklyRankInstList = data.newWeeklyRanksInstitute;
    dailyRankList = data.newRanks;
    dailyRankInstList = data.newRanksInstitute;


    if(badgeDetails.newRanks) {
        newRanks = badgeDetails.newRanks;
        showRank(newRanks);
    }
    userTotalPoints = Number(totalPoints);

    newBadge !=null ? badgePresent = true : badgePresent = false;
    newMedal !=null ? medalPresent = true : medalPresent = false;

    showAlertsModal();
    showAllAnswers();
}
///Progress bar of user badge and score on result page
function scoreProgress() {
    var handle = $( ".custom-handle" );
    $( ".slider" ).slider({
        orientation: "horizontal",
        range: "min",
        max: maxValueSlider,
        value: userTotalPoints,
        disabled:true,
        create: function() {
            var value=$( this ).slider( "value" );
            handle.html("<p>You're here</p>"+"<p>"+value+" pts</p>");
        },
        slide: function( event, ui ) {
            handle.html("<p>You're here</p>"+"<p>"+ui.value+" pts</p>");
        }
    });
    $('.slider').removeClass('ui-state-disabled');
}
//If user Earns Badge come here
function showBadge(newBadge){
    $('#badgeModal').modal('show');
    var userNextBadge = '';
    var remainingPoints = 0;
    if (userTotalPointsVal<warrior){
        userNextBadge = 'pWarrior';
    }else if (userTotalPointsVal<gladiator){
        userNextBadge = 'pGladiator';
    }else if (userTotalPointsVal<knight){
        userNextBadge = 'pKnight';
    }else if (userTotalPointsVal<ninja){
        userNextBadge = 'pNinja';
    }else if (userTotalPointsVal<samurai){
        userNextBadge = 'pSamurai';
    }

    document.getElementById('newBadgeName').innerHTML = 'Congratulations on winning the badge '+'<span>'+newBadge+'</span>';
}


function showMedal(){
    $('#medalModal').modal('show');
    if (newMedal == 'gold') {
        $('#win-message').text("You've won 8 Consecutive times.");
        $('#medal-user').addClass('gold');
        document.getElementById('medal-name').innerHTML = "Congratulations on winning the "+"<span class='goldColor'>Gold Medal</span>";
        document.getElementById('goldMedal').style.display='block';
    } else if (newMedal == 'silver') {
        $('#win-message').text("You've won 5 Consecutive times.");
        $('#medal-user').addClass('silver');
        $('#medal-name').text("You've earned Silver Medal");
        document.getElementById('silverMedal').style.display='block';
        document.getElementById('medal-name').innerHTML = "Congratulations on winning the "+"<span class='silverColor'>Silver Medal</span>";
        document.getElementById('silverMedal').style.display='block';
    } else if (newMedal == 'bronze') {
        $('#win-message').text("You've won 3 Consecutive times.");
        $('#medal-user').addClass('bronze');
        $('#medal-name').text("You've earned Bronze Medal");
        document.getElementById('bronzeMedal').style.display='block';
        document.getElementById('medal-name').innerHTML = "Congratulations on winning the "+"<span class='bronzeColor'>Bronze Medal</span>";
        document.getElementById('bronzeMedal').style.display='block';
    }
}
//if user earns rank
function showRank(rank){
    $('#rankModal').modal('show');
    if((rank=='1') || (rank==1)) {
        $('#rank-message').text('You have got ' + rank + 'st' + ' rank');
    }
    else if(rank=='2' || rank==2){
        $('#rank-message').text('You have got ' + rank + 'nd' + ' rank');
    }
    else if(rank=='3' || rank==3){
        $('#rank-message').text('You have got ' + rank + 'rd' + ' rank');
    }
    else{
        $('#rank-message').text('You have got ' + rank + 'th' + ' rank');
    }
    var anim;
    var animdata ={
        container: document.getElementById("rank-cup"),
        renderer: "svg",
        loop: 3,
        autoplay: true,
        path: "https://assets3.lottiefiles.com/packages/lf20_r7bfvyke.json"
    };
    anim = bodymovin.loadAnimation(animdata);
}


if (localStorage.getItem('allIndrankDtl')==undefined){
    var allIndrankDtl = {
        rank1:0,
        rank2:0,
        rank3:0,
    };
    localStorage.setItem('allIndrankDtl',JSON.stringify(allIndrankDtl));
}
if (localStorage.getItem('instRankDtl')==undefined){
    var allIndrankDtl = {
        rank1:0,
        rank2:0,
        rank3:0,
    };
    localStorage.setItem('instRankDtl',JSON.stringify(allIndrankDtl));
}

function allIndiaRankModal(type,rankData1,rankData2,rankData3){
    var currentRankType = type;
    var exRankDtl = localStorage.getItem('allIndrankDtl');
    var exData1 = "";
    var exData2 = "";
    var exData3 = "";
    var message="";
    var showModal=false;


    if (exRankDtl!=undefined){
        exRankDtl = JSON.parse(exRankDtl);
        exData1 = exRankDtl.rank1;
        exData2 = exRankDtl.rank2;
        exData3 = exRankDtl.rank3;
    }


    if (rankData1!="" && rankData2=="" && rankData3==""){
        if (exData1!=rankData1){
            if (currentRankType=='daily'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on All India <span>daily leaderboard</span>';
                showModal = true;
            }else if (currentRankType=='weekly'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on All India <span>weekly leaderboard</span>';
                showModal = true;
            }else if (currentRankType=='monthly'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on All India <span>monthly leaderboard</span>';
                showModal = true;
            }
        }
    }

    if (rankData1!="" && rankData2!="" && rankData3==""){
       if (exData1 !=rankData1 && exData2 !=rankData2){
           // both the  ranks have been updated, 2 messages show
           if (currentRankType=='dailyweekly'){
               message = 'Congratulations on reaching number <span>'+rankData1+'</span> on All India <span>daily</span> and number <span>'+rankData2+'</span> on All India <span>weekly</span> leaderboard';
               showModal = true;
           }else if (currentRankType=='dailymonthly'){
               message = 'Congratulations on reaching number <span>'+rankData1+'</span> on All India <span>daily</span> and number <span>'+rankData2+'</span> on All India <span>monthly</span> leaderboard';
               showModal = true;
           }else if (currentRankType=='weeklymonthly'){
               message = 'Congratulations on reaching number <span>'+rankData1+'</span> on All India <span>weekly</span> and number <span>'+rankData2+'</span> on All India <span>monthly</span> leaderboard';
               showModal = true;
           }
       }else if (exData1==rankData1 && exData2 !=rankData2){
           if (currentRankType=='dailyweekly'){
               message = 'Congratulations on reaching number <span>'+rankData2+'</span> on All India <span>weekly leaderboard</span>';
               showModal = true;
           }else if (currentRankType=='dailymonthly' || currentRankType=='weeklymonthly'){
               message = 'Congratulations on reaching number <span>'+rankData2+'</span> on All India <span>monthly leaderboard</span>';
               showModal = true;
           }
       }else if (exData1!=rankData1 && exData2 ==rankData2){
           if (currentRankType=='dailyweekly' || currentRankType=='dailymonthly'){
               message = 'Congratulations on reaching number <span>'+rankData1+'</span> on All India <span>daily leaderboard</span>';
               showModal = true;
           }else if (currentRankType=='weeklymonthly'){
               message = 'Congratulations on reaching number <span>'+rankData1+'</span> on All India <span>weekly leaderboard</span>';
               showModal = true;
           }
       }
    }

    if (rankData1!="" && rankData2!="" && rankData3!=""){
       if (exData1!=rankData1 && exData2 !=rankData2 && exData3!=rankData3){ //d,w,m
           if (currentRankType=='dailyweeklymonthly'){
               message = 'Congratulations on reaching number <span>'+rankData1+'</span> on All India <span>daily</span>, number <span>'+rankData2+'</span> on All India weekly and number <span>'+rankData3+'</span> on All India monthly leaderboard';
               showModal = true;
           }
       }else if (exData1==rankData1 && exData2 !=rankData2 && exData3!=rankData3){ //w,m
           if (currentRankType=='dailyweeklymonthly'){
               message = 'Congratulations on reaching number <span>'+rankData2+'</span> on All India weekly and number <span>'+rankData3+'</span> on All India monthly leaderboard';
               showModal = true;
           }
       }else if (exData1==rankData1 && exData2==rankData2 && exData3!=rankData3){//m
           if (currentRankType=='dailyweeklymonthly'){
               message = 'Congratulations on reaching number <span>'+rankData3+'</span> on All India <span>monthly</span> leaderboard';
               showModal = true;
           }
       }else if (exData1!=rankData1 && exData2 ==rankData2 && exData3!=rankData3){ //d,m
           if (currentRankType=='dailyweeklymonthly'){
               message = 'Congratulations on reaching number <span>'+rankData1+'</span> on All India <span>daily</span>and number <span>'+rankData3+'</span> on All India monthly leaderboard';
               showModal = true;
           }
       }else if (exData1!=rankData1 && exData2!=rankData2 && exData3==rankData3){//d,w
           if (currentRankType=='dailyweeklymonthly'){
               message = 'Congratulations on reaching number <span>'+rankData1+'</span> on All India <span>daily</span> and number <span>'+rankData2+'</span> on All India weekly leaderboard';
               showModal = true;
           }
       }else if (exData1==rankData1 && exData2!=rankData2 && exData3==rankData3){//w
           if (currentRankType=='dailyweeklymonthly'){
               message = 'Congratulations on reaching number <span>'+rankData2+'</span> on All India weekly leaderboard';
               showModal = true;
           }
       }else if (exData1!=rankData1 && exData2==rankData2 && exData3==rankData3){//w
           if (currentRankType=='dailyweeklymonthly'){
               message = 'Congratulations on reaching number <span>'+rankData1+'</span> on All India daily leaderboard';
               showModal = true;
           }
       }
    }

    if (showModal){
        document.getElementById('rankDisplayText').innerHTML = message;
        $('#rankUpdateModal').modal('show');
    }else {
        if (medalPresent){
            showMedal();
        }
    }


    var rankDtl1={};
    exData1!=rankData1 ? rankDtl1.rank1 = rankData1 :rankDtl1.rank1 = exData1;
    exData2!=rankData2 ? rankDtl1.rank2 = rankData2 :rankDtl1.rank2 = exData2;
    exData3!=rankData3 ? rankDtl1.rank3 = rankData3 :rankDtl1.rank3 = exData3;

    localStorage.setItem('allIndrankDtl',JSON.stringify(rankDtl1));

}

function instRankModal(type,rankData1,rankData2,rankData3){
    var currentRankType = type;
    var exRankDtl = localStorage.getItem('instRankDtl');
    var exData1 = "";
    var exData2 = "";
    var exData3 = "";
    var message="";
    var showInstModal=false;


    if (exRankDtl!=undefined){
        exRankDtl = JSON.parse(exRankDtl);
        exData1 = exRankDtl.rank1;
        exData2 = exRankDtl.rank2;
        exData3 = exRankDtl.rank3;
    }


    if (rankData1!="" && rankData2=="" && rankData3==""){
        if (exData1!=rankData1){
            if (currentRankType=='dailyInst'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on your institute\'s <span>daily leaderboard</span>';
                showInstModal = true;
            }else if (currentRankType=='weeklyInst'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on your institute\'s <span>weekly leaderboard</span>';
                showInstModal = true;
            }else if (currentRankType=='monthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on your institute\'s <span>monthly leaderboard</span>';
                showInstModal = true;
            }
        }
    }

    if (rankData1!="" && rankData2!="" && rankData3==""){
        if (exData1 !=rankData1 && exData2 !=rankData2){
            // both the  ranks have been updated, 2 messages show
            if (currentRankType=='dailyInstweeklyInst'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on your institute\'s <span>daily</span> and number <span>'+rankData2+'</span> on your institute\'s <span>weekly</span> leaderboard';
                showInstModal = true;
            }else if (currentRankType=='dailyInstmonthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on your institute\'s <span>daily</span> and number <span>'+rankData2+'</span> on your institute\'s <span>monthly</span> leaderboard';
                showInstModal = true;
            }else if (currentRankType=='weeklyInstmonthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on your institute\'s <span>weekly</span> and number <span>'+rankData2+'</span> on your institute\'s <span>monthly</span> leaderboard';
                showInstModal = true;
            }
        }else if (exData1==rankData1 && exData2 !=rankData2){
            if (currentRankType=='dailyInstweeklyInst'){
                message = 'Congratulations on reaching number <span>'+rankData2+'</span> on your institute\'s <span>weekly leaderboard</span>';
                showInstModal = true;
            }else if (currentRankType=='dailyInstmonthlyInst' || currentRankType=='weeklyInstmonthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData2+'</span> on your institute\'s <span>monthly leaderboard</span>';
                showInstModal = true;
            }
        }else if (exData1!=rankData1 && exData2 ==rankData2){
            if (currentRankType=='dailyInstweeklyInst' || currentRankType=='dailyInstmonthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on your institute\'s <span>daily leaderboard</span>';
                showInstModal = true;
            }else if (currentRankType=='weeklyInstmonthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on your institute\'s <span>weekly leaderboard</span>';
                showInstModal = true;
            }
        }
    }

    if (rankData1!="" && rankData2!="" && rankData3!=""){
        if (exData1!=rankData1 && exData2 !=rankData2 && exData3!=rankData3){ //d,w,m
            if (currentRankType=='dailyInstweeklyInstmonthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on your institute\'s <span>daily</span>, number <span>'+rankData2+'</span> on your institute\'s weekly and number <span>'+rankData3+'</span> on your institute\'s monthly leaderboard';
                showInstModal = true;
            }
        }else if (exData1==rankData1 && exData2 !=rankData2 && exData3!=rankData3){ //w,m
            if (currentRankType=='dailyInstweeklyInstmonthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData2+'</span> on your institute\'s weekly and number <span>'+rankData3+'</span> on your institute\'s monthly leaderboard';
                showInstModal = true;
            }
        }else if (exData1==rankData1 && exData2==rankData2 && exData3!=rankData3){//m
            if (currentRankType=='dailyInstweeklyInstmonthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData3+'</span> on your institute\'s <span>monthly</span> leaderboard';
                showInstModal = true;
            }
        }else if (exData1!=rankData1 && exData2 ==rankData2 && exData3!=rankData3){ //d,m
            if (currentRankType=='dailyInstweeklyInstmonthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on your institute\'s <span>daily</span>and number <span>'+rankData3+'</span> on your institute\'s monthly leaderboard';
                showInstModal = true;
            }
        }else if (exData1!=rankData1 && exData2!=rankData2 && exData3==rankData3){//d,w
            if (currentRankType=='dailyInstweeklyInstmonthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on your institute\'s <span>daily</span> and number <span>'+rankData2+'</span> on your institute\'s weekly leaderboard';
                showInstModal = true;
            }
        }else if (exData1==rankData1 && exData2!=rankData2 && exData3==rankData3){//w
            if (currentRankType=='dailyInstweeklyInstmonthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData2+'</span> on your institute\'s weekly leaderboard';
                showInstModal = true;
            }
        }else if (exData1!=rankData1 && exData2==rankData2 && exData3==rankData3){//w
            if (currentRankType=='dailyInstweeklyInstmonthlyInst'){
                message = 'Congratulations on reaching number <span>'+rankData1+'</span> on your institute\'s daily leaderboard';
                showInstModal = true;
            }
        }
    }

    if (showInstModal){
        document.getElementById('rankInstDisplayText').innerHTML = message;
        $('#rankInstUpdateModal').modal('show');
    }else {
        if (medalPresent){
            showMedal();
        }
    }


    var rankDtl1={};
    exData1!=rankData1 ? rankDtl1.rank1 = rankData1 :rankDtl1.rank1 = exData1;
    exData2!=rankData2 ? rankDtl1.rank2 = rankData2 :rankDtl1.rank2 = exData2;
    exData3!=rankData3 ? rankDtl1.rank3 = rankData3 :rankDtl1.rank3 = exData3;

    localStorage.setItem('instRankDtl',JSON.stringify(rankDtl1));
}

function showAlertsModal(){
    var rankType = "";
    var rankType1 = "";

    var rankData1 = "";
    var rankData2 = "";
    var rankData3 = "";
    var rankDataInst1 = "";
    var rankDataInst2 = "";
    var rankDataInst3 = "";

    if(dailyRankList !=null && weeklyRankList !=null && monthlyRankList!=null){
        dailyRankList = JSON.parse(dailyRankList);
        weeklyRankList = JSON.parse(weeklyRankList);
        monthlyRankList = JSON.parse(monthlyRankList);
        for (var d=0;d<dailyRankList.length;d++){
            if (dailyRankList[d].userId == userlistId){
                rankType = 'daily';
                rankData1 = dailyRankList[d].rank;
            }
        }

        for (var w=0;w<weeklyRankList.length;w++){
            if (weeklyRankList[w].userId == userlistId){
                rankType += 'weekly';
                rankData2 = weeklyRankList[w].rank;
            }
        }
        for (var m=0;m<monthlyRankList.length;m++){
            if (monthlyRankList[m].userId == userlistId){
                rankType += 'monthly';
                rankData3 = monthlyRankList[m].rank;
            }
        }
        rankPresent = true;
    }else if(dailyRankList !=null && weeklyRankList !=null){
        dailyRankList = JSON.parse(dailyRankList);
        weeklyRankList = JSON.parse(weeklyRankList);
        for (var d=0;d<dailyRankList.length;d++){
            if (dailyRankList[d].userId == userlistId){
                rankType = 'daily';
                rankData1 = dailyRankList[d].rank;
            }
        }

        for (var w=0;w<weeklyRankList.length;w++){
            if (weeklyRankList[w].userId == userlistId){
                rankType += 'weekly';
                rankData2 = weeklyRankList[w].rank;
            }
        }
        rankPresent = true;
    }else if(dailyRankList !=null && monthlyRankList !=null){
        dailyRankList = JSON.parse(dailyRankList);
        monthlyRankList = JSON.parse(monthlyRankList);

        for (var d=0;d<dailyRankList.length;d++){
            if (dailyRankList[d].userId == userlistId){
                rankType = 'daily';
                rankData1 = dailyRankList[d].rank;
            }
        }
        for (var m=0;m<monthlyRankList.length;m++){
            if (monthlyRankList[m].userId == userlistId){
                rankType += 'monthly';
                rankData2 = monthlyRankList[m].rank;
            }
        }
        rankPresent = true;
    }else if(weeklyRankList !=null && monthlyRankList !=null){
        weeklyRankList = JSON.parse(weeklyRankList);
        monthlyRankList = JSON.parse(monthlyRankList);

        for (var w=0;w<weeklyRankList.length;w++){
            if (weeklyRankList[w].userId == userlistId){
                rankType += 'weekly';
                rankData1 = weeklyRankList[w].rank;
            }
        }
        for (var m=0;m<monthlyRankList.length;m++){
            if (monthlyRankList[m].userId == userlistId){
                rankType += 'monthly';
                rankData2 = monthlyRankList[m].rank;
            }
        }
        rankPresent = true;
    }else if (monthlyRankList!=null){
        monthlyRankList = JSON.parse(monthlyRankList);
        for (var m=0;m<monthlyRankList.length;m++){
            if (monthlyRankList[m].userId == userlistId){
                rankType = 'monthly';
                rankData1 = monthlyRankList[m].rank;
            }
        }
        rankPresent = true;
    }else if (weeklyRankList!=null){
        weeklyRankList = JSON.parse(weeklyRankList);
        for (var w=0;w<weeklyRankList.length;w++){
            if (weeklyRankList[w].userId == userlistId){
                rankType = 'weekly';
                rankData1 = weeklyRankList[w].rank;
            }
        }
        rankPresent = true;
    }else if (dailyRankList!=null){
        dailyRankList = JSON.parse(dailyRankList);
        for (var d=0;d<dailyRankList.length;d++){
            if (dailyRankList[d].userId == userlistId){
                rankType = 'daily';
                rankData1 = dailyRankList[d].rank;
            }
        }
        rankPresent = true;
    }


    //INSTITUTE
    if(dailyRankInstList !=null && weeklyRankInstList !=null && monthlyRankInstList!=null){
        dailyRankInstList = JSON.parse(dailyRankInstList);
        weeklyRankInstList = JSON.parse(weeklyRankInstList);
        monthlyRankInstList = JSON.parse(monthlyRankInstList);
        for (var d=0;d<dailyRankInstList.length;d++){
            if (dailyRankInstList[d].userId == userlistId){
                rankType1 = 'dailyInst';
                rankDataInst1 = dailyRankInstList[d].rank;
            }
        }

        for (var w=0;w<weeklyRankInstList.length;w++){
            if (weeklyRankInstList[w].userId == userlistId){
                rankType1 += 'weeklyInst';
                rankDataInst2 = weeklyRankInstList[w].rank;
            }
        }
        for (var m=0;m<monthlyRankInstList.length;m++){
            if (monthlyRankInstList[m].userId == userlistId){
                rankType1 += 'monthly';
                rankDataInst3 = monthlyRankInstList[m].rank;
            }
        }
        rankPresent = true;
    }else if(dailyRankInstList !=null && weeklyRankInstList !=null){
        dailyRankInstList = JSON.parse(dailyRankInstList);
        weeklyRankInstList = JSON.parse(weeklyRankInstList);
        for (var d=0;d<dailyRankInstList.length;d++){
            if (dailyRankInstList[d].userId == userlistId){
                rankType1 = 'dailyInst';
                rankDataInst1 = dailyRankInstList[d].rank;
            }
        }

        for (var w=0;w<weeklyRankInstList.length;w++){
            if (weeklyRankInstList[w].userId == userlistId){
                rankType1 += 'weeklyInst';
                rankDataInst2 = weeklyRankInstList[w].rank;
            }
        }
        rankPresent = true;
    }else if(dailyRankInstList !=null && monthlyRankInstList !=null){
        dailyRankInstList = JSON.parse(dailyRankInstList);
        monthlyRankInstList = JSON.parse(monthlyRankInstList);

        for (var d=0;d<dailyRankInstList.length;d++){
            if (dailyRankInstList[d].userId == userlistId){
                rankType1 = 'dailyInst';
                rankDataInst1 = dailyRankInstList[d].rank;
            }
        }
        for (var m=0;m<monthlyRankInstList.length;m++){
            if (monthlyRankInstList[m].userId == userlistId){
                rankType1 += 'monthlyInst';
                rankDataInst2 = monthlyRankInstList[m].rank;
            }
        }
        rankPresent = true;
    }else if(weeklyRankInstList !=null && monthlyRankInstList !=null){
        weeklyRankInstList = JSON.parse(weeklyRankInstList);
        monthlyRankInstList = JSON.parse(monthlyRankInstList);

        for (var w=0;w<weeklyRankInstList.length;w++){
            if (weeklyRankInstList[w].userId == userlistId){
                rankType1 += 'weeklyInst';
                rankDataInst1 = weeklyRankInstList[w].rank;
            }
        }
        for (var m=0;m<monthlyRankInstList.length;m++){
            if (monthlyRankInstList[m].userId == userlistId){
                rankType1 += 'monthlyInst';
                rankDataInst2 = monthlyRankInstList[m].rank;
            }
        }
        rankPresent = true;
    }else if (dailyRankInstList!=null){
        dailyRankInstList = JSON.parse(dailyRankInstList);
        for (var d=0;d<dailyRankInstList.length;d++){
            if (dailyRankInstList[d].userId == userlistId){
                rankType1 = 'dailyInst';
                rankDataInst1 = dailyRankInstList[d].rank;
            }
        }
        rankPresent = true;
    }else if (weeklyRankInstList!=null){
         weeklyRankInstList = JSON.parse(weeklyRankInstList);
         for (var w=0;w<weeklyRankInstList.length;w++){
             if (weeklyRankInstList[w].userId == userlistId){
                 rankType1 = 'weeklyInst';
                 rankDataInst1 = weeklyRankInstList[w].rank;
             }
         }
         rankPresent = true;
     }else if (monthlyRankInstList!=null){
         monthlyRankInstList = JSON.parse(monthlyRankInstList);
         for (var m=0;m<monthlyRankInstList.length;m++){
             if (monthlyRankInstList[m].userId == userlistId){
                 rankType1 = 'monthlyInst';
                 rankDataInst1 = monthlyRankInstList[m].rank;
             }
         }
         rankPresent = true;
     }

    if (dailyRankInstList!=null || weeklyRankInstList !=null || monthlyRankInstList !=null){
        instRankPresent=true;
    }

    if (medalPresent && badgePresent && rankPresent){
        showBadge(newBadge);
        $('.badgeSummaryBtn').text('Next');
        $('.badgeSummaryBtn').on('click',function (){
            if (instRankPresent){
                instRankModal(rankType1,rankDataInst1,rankDataInst2,rankDataInst3);
                $('.rankInstSummaryBtn').text('Next');
                $('.rankInstSummaryBtn').on('click',function (){
                    allIndiaRankModal(rankType,rankData1,rankData2,rankData3);
                })
            }else{
                allIndiaRankModal(rankType,rankData1,rankData2,rankData3);
            }

        });
        $('.rankSummaryBtn').text('Next');
        $('.rankSummaryBtn').on('click',function (){
            showMedal();
        })
    }else if (medalPresent && !badgePresent && !rankPresent){
        showMedal()
    }else if (badgePresent && !medalPresent && !rankPresent){
        showBadge(newBadge);
    }else if (rankPresent && !badgePresent && !medalPresent){
        if (instRankPresent){
            instRankModal(rankType1,rankDataInst1,rankDataInst2,rankDataInst3);
            $('.rankInstSummaryBtn').text('Next');
            $('.rankInstSummaryBtn').on('click',function (){
                allIndiaRankModal(rankType,rankData1,rankData2,rankData3);
            })
        }else{
            allIndiaRankModal(rankType,rankData1,rankData2,rankData3);
        }
    }else if (badgePresent && rankPresent){
        showBadge(newBadge);
        $('.badgeSummaryBtn').text('Next');
        $('.badgeSummaryBtn').on('click',function (){
            if (instRankPresent){
                instRankModal(rankType1,rankDataInst1,rankDataInst2,rankDataInst3);
                $('.rankInstSummaryBtn').text('Next');
                $('.rankInstSummaryBtn').on('click',function (){
                    allIndiaRankModal(rankType,rankData1,rankData2,rankData3);
                })
            }else{
                allIndiaRankModal(rankType,rankData1,rankData2,rankData3);
            }
        });
    }else if (medalPresent && rankPresent){

        if (instRankPresent){
            instRankModal(rankType1,rankDataInst1,rankDataInst2,rankDataInst3);
            $('.rankInstSummaryBtn').text('Next');
            $('.rankInstSummaryBtn').on('click',function (){
                allIndiaRankModal(rankType,rankData1,rankData2,rankData3);
            })
        }else{
            allIndiaRankModal(rankType,rankData1,rankData2,rankData3);
        }
        $('.rankSummaryBtn').text('Next');
        $('.rankSummaryBtn').on('click',function (){
            showMedal();
        })
    }else if (medalPresent && badgePresent){
        showBadge(newBadge);
        $('.badgeSummaryBtn').text('Next');
        $('.badgeSummaryBtn').on('click',function (){
            showMedal();
        });
    }
}