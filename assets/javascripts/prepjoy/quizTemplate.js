function previousQuiz(){
    if(source!='web'){
        if(source =='android'){
            JSInterface.prevQuiz();
        }
        else{
            webkit.messageHandlers.prevQuiz.postMessage('');
        }
    }
}
function nextQuiz(){
    if(source!='web'){
        if(source =='android'){
            JSInterface.nextQuiz();
        }
        else{
            webkit.messageHandlers.nextQuiz.postMessage('');
        }
    }
}
function showDescription(){
    $('.explanation-wrapper').show().addClass('animate__animated animate__bounceInUp');
    addMathjax();
}

function showVideoResult(index){
    $('.video-explanation-wrapper-'+index).show().addClass('animate__animated animate__bounceInUp');
}
function closeVideoExplanation(index){
    $('.video-explanation-wrapper-'+index).addClass('animate__animated animate__bounceOutDown');
    setTimeout(resetVideoExplanation,1000);
    var leg=$('.video_iframe').attr("src");
    $('.video_iframe').attr("src",leg);
}
function correctAnswers(){
    displayTabAnswers('correct');
}
function incorrectAnswers(){
    displayTabAnswers('incorrect');
}
function skippedAnswers(){
    displayTabAnswers('skipped');
}
function markedForReview(){
    displayTabAnswers('markedForReview');
}
var explainationLink =null;
var yt_url;
var mergedUserAnswersList;
function displayTabAnswers(tabType){
    var answerByUser=JSON.parse(qaObj);
    var qaAnswers=answerByUser.userAnswers;
    var htmlStr='';

    var list = questions.map(t1 => ({...t1, ...qaAnswers.find(t2 => t2.id == t1.id)}))
    mergedUserAnswersList = list;
    htmlStr +="<div class='container'>";
    for(var i=0;i<mergedUserAnswersList.length;i++) {
        var ques = mergedUserAnswersList[i].ps;

        var directions=mergedUserAnswersList[i].directions;

        if(directions !=null) {
            directions=replaceSymbols(directions);
            directions=checkLanguageAndImage(directions);
            directions=replaceImageUrlForApps(directions);

        }

        ques=replaceSymbols(ques);
        ques=checkLanguageAndImage(ques);
        ques=replaceImageUrlForApps(ques);
        var option1 = mergedUserAnswersList[i].op1;
        option1=replaceSymbols(option1);
        option1=checkLanguageAndImage(option1);
        option1=replaceImageUrlForApps(option1);
        var option2 = mergedUserAnswersList[i].op2;
        option2=replaceSymbols(option2);
        option2=checkLanguageAndImage(option2);
        option2=replaceImageUrlForApps(option2);
        var option3 = mergedUserAnswersList[i].op3;
        option3=replaceSymbols(option3);
        option3=checkLanguageAndImage(option3);
        option3=replaceImageUrlForApps(option3);
        var option4 = mergedUserAnswersList[i].op4;
        option4=replaceSymbols(option4);
        option4=checkLanguageAndImage(option4);
        option4=replaceImageUrlForApps(option4);
        if(mergedUserAnswersList[i].op5){
            var option5=mergedUserAnswersList[i].op5;
            option5=replaceSymbols(option5);
            option5=checkLanguageAndImage(option5);
            option5=replaceImageUrlForApps(option5);
        }
        else{
            var option5='';
        }

        var explanation='';
        var explanationLink=mergedUserAnswersList[i].explainLink;
        explainationLink = explanationLink;
        if(mergedUserAnswersList[i].answerDescription != ''){
            explanation=replaceSymbols(mergedUserAnswersList[i].answerDescription);
            explanation=checkLanguageAndImage(explanation);
            explanation=replaceImageUrlForApps(explanation);
        }

        if(tabType=='incorrect'){
            if((mergedUserAnswersList[i].userOption !=-'1') && (mergedUserAnswersList[i].correctOption!=mergedUserAnswersList[i].userOption)){
                htmlStr+=answerTemplate(mergedUserAnswersList[i],ques,i,option1,option2,option3,option4,option5,mergedUserAnswersList[i],directions,explanation, mergedUserAnswersList[i].id);
            }
            if (mergedUserAnswersList[i].explainLink!= '' && mergedUserAnswersList[i].explainLink != 'null' && mergedUserAnswersList[i].explainLink != null) {
                $('.show-video-explanation').show();
                if(explanationLink.includes("/")){
                    yt_url=mergedUserAnswersList[i].explainLink;
                }else{
                    yt_url = "https://www.youtube.com/embed/" + explanationLink;
                }
                $('#videoExplanation').html('<iframe class="video_iframe" src="'+yt_url+'" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" style="width: 100%;" allowfullscreen></iframe>');
            } else {
                $('.show-video-explanation').hide();
            }
            document.getElementById('incorrectAnswers').innerHTML = htmlStr;
            if($('#incorrectAnswers .mcq-answers').length ==0){
                document.getElementById('incorrectAnswers').innerHTML = '<h4 class="validate-answers text-white">No Incorrect Answers</h4>';
            }
        }
        else if(tabType=='correct'){
            if(mergedUserAnswersList[i].correctOption==mergedUserAnswersList[i].userOption) {
                htmlStr += answerTemplate(mergedUserAnswersList[i], ques, i, option1, option2, option3, option4,option5, mergedUserAnswersList[i],directions,explanation, mergedUserAnswersList[i].id);
            }
            if (mergedUserAnswersList[i].explainLink!= '' && mergedUserAnswersList[i].explainLink != 'null' && mergedUserAnswersList[i].explainLink != null) {
                $('.show-video-explanation').show();
                if(explanationLink.includes("/")){
                    yt_url=mergedUserAnswersList[i].explainLink;
                }else{
                    yt_url = "https://www.youtube.com/embed/" + explanationLink;
                }
                $('#videoExplanation').html('<iframe class="video_iframe" src="'+yt_url+'" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" style="width: 100%;" allowfullscreen></iframe>');
            } else {
                $('.show-video-explanation').hide();
            }
            document.getElementById('correctAnswers').innerHTML = htmlStr;
            if($('#correctAnswers .mcq-answers').length ==0){
                document.getElementById('correctAnswers').innerHTML = '<h4 class="validate-answers text-white">No Correct Answers</h4>';
            }
        }
        else if(tabType=='skipped'){
            if(mergedUserAnswersList[i].userOption=='-1'){
                htmlStr+=answerTemplate(mergedUserAnswersList[i],ques,i,option1,option2,option3,option4,option5,mergedUserAnswersList[i],directions,explanation, mergedUserAnswersList[i].id);
            }
            if (mergedUserAnswersList[i].explainLink!= '' && mergedUserAnswersList[i].explainLink != 'null' && mergedUserAnswersList[i].explainLink != null) {
                $('.show-video-explanation').show();
                if(explanationLink.includes("/")){
                    yt_url=mergedUserAnswersList[i].explainLink;
                }else{
                    yt_url = "https://www.youtube.com/embed/" + explanationLink;
                }
            } else {
                $('.show-video-explanation').hide();
            }
            document.getElementById('skippedAnswers').innerHTML = htmlStr;
            if($('#skippedAnswers .mcq-answers').length ==0){
                document.getElementById('skippedAnswers').innerHTML = '<h4 class="validate-answers text-white">No Skipped Questions</h4>';
            }
        }else if(tabType=='markedForReview'){
            if(mergedUserAnswersList[i].reviewedQ == "true"){
                htmlStr+=answerTemplate(mergedUserAnswersList[i],ques,i,option1,option2,option3,option4,option5,mergedUserAnswersList[i],directions,explanation, mergedUserAnswersList[i].id);
            }
            if (mergedUserAnswersList[i].explainLink!= '' && questions[i].explainLink != 'null' && mergedUserAnswersList[i].explainLink != null) {
                $('.show-video-explanation').show();
                if(explanationLink.includes("/")){
                    yt_url=mergedUserAnswersList[i].explainLink;
                }else{
                    yt_url = "https://www.youtube.com/embed/" + explanationLink;
                }
            } else {
                $('.show-video-explanation').hide();
            }
            document.getElementById('markedForReview').innerHTML = htmlStr;
            if($('#markedForReview .mcq-answers').length ==0){
                document.getElementById('markedForReview').innerHTML = '<h4 class="validate-answers text-white">No questions has been marked for review</h4>';
            }
        }
    }
    addMathjax();

    if (quizMode==''){
        $('.validate-answers').addClass('text-white');
    }
}
function answerTemplate(questions,ques,index,option1,option2,option3,option4,option5,qaAnswers,directions,explanation, id){
    var htmlStr='';
    htmlStr += "<div class='mcq-answers'>" +
        "<div id='que-no'>Q" + (index + 1) + ".</div>" +
        "<div class='question-wrapper' id='quz-" + (index) + "'>" ;
    if(directions !=null) {
        htmlStr +=   "<div class=\"directions\">\n" +
            "                                <p id=\"direction\" class='more'>" + directions + "</p>\n" +
            "                        </div>";
    }
    htmlStr += "<p class='que_text'>" + ques + "</p>" +
        "</div>" +
        "<div class='que-options-wrapper mt-4'>" +
        "<div class='que-options' id='que-" + index + "'>" +
        "<div id=ans-" + index + "' class='option-qa'>";
    if (questions.ans1 == 'Yes') {
        htmlStr += '<div class="option correct">';
    } else if ((questions.ans1 != 'Yes') && (qaAnswers.userOption == '1')) {
        htmlStr += '<div class="option incorrect">';
    } else {
        htmlStr += '<div class="option">';
    }
    htmlStr += '<span>' + option1 + '</span>' +
        '</div>' +
        "</div>" +
        "<div id=ans-" + index + "' class='option-qa'>";
    if (questions.ans2 == 'Yes') {
        htmlStr += '<div class="option correct">';
    } else if ((questions.ans2 != 'Yes') && (qaAnswers.userOption == '2')) {
        htmlStr += '<div class="option incorrect">';
    } else {
        htmlStr += '<div class="option">';
    }
    htmlStr += '<span>' + option2 + '</span></div>' +
        "</div>" +
        "<div id=ans-" + index + "' class='option-qa'>";
    if (questions.ans3 == 'Yes') {
        htmlStr += '<div class="option correct">';
    } else if ((questions.ans3 != 'Yes') && (qaAnswers.userOption == '3')) {
        htmlStr += '<div class="option incorrect">';
    } else {
        htmlStr += '<div class="option">';
    }
    htmlStr += '<span>' + option3 + '</span>' +
        '</div>' +
        "</div>";
    htmlStr += "<div id=ans-" + index + "' class='option-qa'>";
    if (questions.ans4 == 'Yes') {
        htmlStr += '<div class="option correct">';
    } else if ((questions.ans4 != 'Yes') && (qaAnswers.userOption == '4')) {
        htmlStr += '<div class="option incorrect">';
    } else {
        htmlStr += '<div class="option">';
    }
    htmlStr += '<span>' + option4 + '</span></div>' +
        "</div>" ;
    if((option5 !=null)&&(option5 !='')){
        htmlStr += "<div id=ans-" + index + "' class='option-qa'>";
        if (questions.ans5 == 'Yes') {
            htmlStr += '<div class="option correct">';
        } else if ((questions.ans5 != 'Yes') && (qaAnswers.userOption == '5')) {
            htmlStr += '<div class="option incorrect">';
        } else {
            htmlStr += '<div class="option">';
        }
        htmlStr += '<span>' + option5 + '</span></div>' +
            "</div>" ;
    }
    //for statistics if pressent
    if(quizStatisticsList.length>0){
        for(var s=0;s<quizStatisticsList.length;s++){
            if(quizStatisticsList[s].objId==questions.id){
                htmlStr +=  '<div class="mt-4 explanation-wrappers"><h3 class="head-text" style="margin-bottom: 2px;">Question Analytics:</h3><table border="1" cellpadding="3" cellspacing="3">' +
                    '<tr style="background: #FFF0F5;font-weight: bold">' +
                    '<td>Attempted</td>' +
                    '<td>Solved Correctly</td>' +
                    '<td>Solved Incorrectly</td>' +
                    '<td>Accuracy</td>' +
                    '<td>Your time</td>' +
                    '<td>Average solving time</td>' +
                    '<td>Fastest solving time</td>' +
                    '</tr>';
                htmlStr +="<tr>"+
                    "<td align='center'>"+(Number(quizStatisticsList[s].correctAnswers)+Number(quizStatisticsList[s].incorrectAnswers)+Number(quizStatisticsList[s].skippedAnswers))+"</td>"+
                    "<td align='center'>"+quizStatisticsList[s].correctAnswers+"</td>"+
                    "<td align='center'>"+quizStatisticsList[s].incorrectAnswers+"</td>"+
                    "<td align='center'>"+quizStatisticsList[s].accuracy.toFixed(2)+"%</td>"+
                    "<td align='center'>"+questions.userTime+" SECS</td>"+
                    "<td align='center'>"+quizStatisticsList[s].averageTimeTaken.toFixed(2)+" SECS</td>"+
                    "<td align='center'>"+(""+quizStatisticsList[s].fastestTime!="null"?quizStatisticsList[s].fastestTime.toFixed(2)+" SECS":"")+" </td>"+
                    "</tr>";
                htmlStr+="</table></div>";
            }
        }

    }
        if((explanation !=null)&&(explanation !='')){
            htmlStr +=  '<div class="mt-4 explanation-wrappers"><h3 class="head-text">Explanation:</h3><p class="explanation-text">' + explanation + '</p></div>';
        }

        htmlStr +=  '<div class="mt-4 video__explanation-wrappers d-flex justify-content-center align-items-center" id="videoExplanation">';
            htmlStr += '<div class="mcqChatBtns d-flex align-items-center justify-content-center mt-3 w-100">';
            if((explainationLink !=null) && (explainationLink !='')) {
                htmlStr += '<h3 class="head-text mb-0" onclick=\'showVideoResult("' + (index + 1) + '"\'><i class="material-icons mr-2">ondemand_video</i>Video Explanation</h3>';
            }
            if(gpt == "true"){
                htmlStr += '<button class="btn d-flex align-items-center mcqChatEx hin mc-primary" onclick="askDoubt(\'hint\', ' + id + ')">' +
                    '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>' +
                    ' Give Hint' +
                    '</button>' +
                    '<button class="btn d-flex align-items-center mcqChatEx exp mc-secondary" onclick="askDoubt(\'explain\', ' + id + ')">' +
                    '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>' +
                    ' Explain MCQ' +
                    '</button>' +
                    '<button class="btn d-flex align-items-center mcqChatEx squ mc-tertiary" onclick="askDoubt(\'similarMCQ\', ' + id + ')">' +
                    '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path></svg>' +
                    ' Create similar MCQs' +
                    '</button>' +
                    '</div>';
            }

    htmlStr +='</div>';

        if((explainationLink !=null) && (explainationLink !="")) {
            htmlStr += '<div class=\'video-explanation-wrapper  video-explanation-wrapper-1 video-explanation-wrapper-' + (index + 1) + '\'>\n' +
                            '<div class="explanation-header">\n' +
                                '<div class="d-flex justify-content-between">\n' +
                                    '<h4>Video Explanation:</h4>\n' +
                                    '<i class="material-icons" onclick=\'closeVideoExplanation("' + (index + 1) + '")\'>close</i>\n' +
                                '</div>\n' +
                            '</div>\n' +
                            '<div class="answer-explanation">' +
                                '<div id="videoExplanation-"+i>' +
                                    '<iframe class="video_iframe" src="' + yt_url + '" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" style="width: 100%;height: 300px" allowfullscreen></iframe>' +
                                '</div>\n' +
                            '</div>\n';
        }
        htmlStr +=    '</div>';

    htmlStr +="</div>" +
        "</div>" +
        "</div>";
    return htmlStr;
}
function showSummary(){
    $('.loading-icon').addClass('hidden');
    correctAnswers();
    incorrectAnswers();
    skippedAnswers();
    showAllAnswers();
    $('.answer-wrapper').show();
    $('.tab-wrapper').removeClass('d-none');
    if (learn){
        $('.nav-tabs').hide();
        $('.answer-wrapper').hide();
    }
}









