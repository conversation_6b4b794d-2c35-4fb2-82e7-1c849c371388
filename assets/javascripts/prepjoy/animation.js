// Ui bot Animation
function slideBots(){
    $('.bot-anim-wrapper ').removeClass('d-none').addClass('animate__animated animate__fadeInLeft');
}

//user animation
function showUser(){
    if(source=='android'){
        JSInterface.showBot();
    }
    else if(source=='ios'){
        webkit.messageHandlers.showBot.postMessage('');
    }
    $('.bot-wrapper').addClass('d-none');
    $('.botProfile').removeClass('d-none').addClass('animate__animated animate__fadeInRight');
    $('.botbg').removeClass('botselect').addClass('animate__animated animate__fadeInUp');
}

//Answer Animation

function answerAnimation(){
    if(source=='android'){
        JSInterface.showAnswerAudio();
    }
    else if(source=='ios'){
        webkit.messageHandlers.showAnswerAudio.postMessage('');
    }
    if(quizMode!='practice'){
        $('.que-options').show();
    }
    $('.que-options .option:first-child').addClass("animate__animated animate__fadeInDown");
    $('.que-options .option:nth-child(2)').addClass("animate__animated animate__fadeInLeft");
    $('.que-options .option:nth-child(3)').addClass("animate__animated animate__fadeInRight");
    $('.que-options .option:last-child').addClass("animate__animated animate__fadeInUp");
}


//Show 1,2,3 before game starts

function showCounter(){
    $('.gamer-profile').addClass('d-none').removeClass('d-block');
    $('.no-counter').removeClass('d-none');
    var readyGo = document.getElementById("no-counter");
    var paragraph;

    setTimeout(three,100);

    function three(){
        if( paragraph ){
            paragraph.remove();
        }
        paragraph = document.createElement("p");
        paragraph.textContent = "3"
        paragraph.className = "three nums text-white";
        readyGo.appendChild(paragraph);
    }

    setTimeout(two, 800);

    function two(){
        if( paragraph ){
            paragraph.remove();
        }
        paragraph = document.createElement("p");
        paragraph.textContent = "2";
        paragraph.className = "two nums text-white";
        readyGo.appendChild(paragraph);
    }

    setTimeout(one, 1600);
    setTimeout(quizDetails,1900);

    function one(){
        if( paragraph ){
            paragraph.remove();
        }
        paragraph = document.createElement("p");
        paragraph.textContent = "1";
        paragraph.className = "one nums text-white";
        readyGo.appendChild(paragraph);
    }
}
