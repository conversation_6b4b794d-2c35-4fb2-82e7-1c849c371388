function showNextQuestionFIB(index){
    var questionDiv = document.createElement('div');
    var questionNumberDiv = document.createElement('div');
    quiz.currentIndex = index;
    questionNumberDiv.innerHTML ="<b>"+ (index+1)+" / "+(quiz.questions.length)+"</b>";
    questionDiv.innerHTML= "<div class='col-md-1' style='width:4%;padding-right:0;padding-left:2px;'><b>"+(index+1)+"."+"</b>"+"</div>"+"<span class='col-md-11' style='padding-left:0;'><b>"+quiz.questions[index].ps;+"</b></span>";
    $("#fibModal").find('.question').text(""); // to remove old ones
    $("#fibModal").find('#question')[0].appendChild(questionDiv);
    resetTextInput(index);
    MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
}

function formatQuestions(questions) {
    var formattedQuestions = [];
    var numberOfQuestions = questions.length;
	
    for(var i = 0; i < numberOfQuestions; ++i) {
        var qstn = questions[i].ps;
        qstn = qstn.split('__')
        var ps = '';
		
        for(var j = 0; j < qstn.length-1; ++j){
            var blank = '<input class="input-text-quiz" type="text" placeholder="Type your answer here" id="fibans'+(j+1)+'" name="fibans'+(j+1)+'">';
            ps+=(qstn[j] + blank);
        }
		
        ps += qstn[j];
        formattedQuestions.push({ps: ps});
    }
	
    return formattedQuestions;
}

function saveAnswerFIB(index) {
    userAnswers[index] = {};
    var qstn = quiz.originalQuestions[index].ps;
    qstn = qstn.split('__');

    var ans;
    var skipped = false;
    for(var i=1; i<qstn.length;i++){
        ans='ans'+i;
        
		if(!$('#fibans'+i).val()){
            userAnswers[index][ans] = '';
            skipped = true;
            if(!skipable) return false;
        } else userAnswers[index][ans] = $('#fibans'+i).val();
    }

    if(!skipped) return true;
    else {
        userAnswers[i]["skipped"]='true';
        return false;
    }
}

function getScoreFIB(answers) {
    var score={}, isIncorrect = false;
    var length = userAnswers.length;
    var answer,userAnswer="";
    score.correctAnswers = 0;
    score.wrongAnswers=0;
    score.skipped=0;
    score.totalQuesions=answers.length;

    for(var i = 0; i < length; ++i){

        var tempAnswers = [answers[i]['ans1'],answers[i]['ans2'],answers[i]['ans3'],answers[i]['ans4']];
        if(!(userAnswers[i]['ans1']==''||userAnswers[i]['ans1']==undefined)||!(userAnswers[i]['ans2']==''||userAnswers[i]['ans2']==undefined)||!(userAnswers[i]['ans3']==''||userAnswers[i]['ans3']==undefined)||!(userAnswers[i]['ans4']==''||userAnswers[i]['ans4']==undefined)) {
            $.each(userAnswers[i], function (key, value) {
                userAnswer = userAnswers[i][key].toLowerCase().replace(/\W+/g, '');
                answer = answers[i][key].toLowerCase().replace(/\W+/g, '');
                if (containsAnswer(tempAnswers, userAnswers[i][key]) == -1) {
                    isIncorrect = true;
                }
            });

            if (!isIncorrect) {
                userAnswers[i].correctAnswer="true";
                score.correctAnswers++;
            }
            else{
                userAnswers[i]['correctAnswer']="false";
                score.wrongAnswers++;
            }

            isIncorrect = false;
        } else {
            score.skipped++;
        }
		
        userAnswers[i]['id']=answers[i]['id'];
    }
	
    return score;
}

function containsAnswer(a, obj) {
    var returnValue=-1;
	
    for(var i=0;i< a.length;i++){
        if ((""+a[i]).toLowerCase().replace(/\W+/g, '') === obj.toLowerCase().replace(/\W+/g, '')){
            returnValue = i;
            a.splice(i,1);
            break;
        }
    }
	
    return returnValue;
}

function scoreAndShowAnswersFIB(data){
    var readMode=false;
    var msgStr;
    quiz.answers=data.results;
    quiz.score = 0;
	
    if(quiz.mode == "read"){
        readMode = true;
        quiz.originalQuestions = data.results;
        quiz.questions = formatQuestions(data.results);
        quiz.userAnswers = quiz.answers;
        $("#fibModal").modal('show');
        modalName = "fibModal";
        $("#fibModal").find('.score-container').hide();
    } else {
        var score = getScoreFIB(quiz.answers);
        quiz.score = score.correctAnswers;
        msgStr = scoreMessage(score,quiz.questions.length);
        quiz.userAnswers = userAnswers;
        updateWithQuizAnswers(userAnswers,score);
        $("#fibModal").find('.score-container').show();
    }

    var scoreDiv = document.createElement('div');
    var answerDiv = document.createElement('div');
    var answerClass1="",answerClass2="",answerClass3="",answerClass4="";

    $("#fibModal").find('.questionsection').css("display","none");
    $("#fibModal").find('.answersection').css("display","block");
    $("#fibModal").find('.score-container').text("");
    if(!readMode) {
        scoreDiv.innerHTML = msgStr;
        $("#fibModal").find('#score-container')[0].appendChild(scoreDiv);
    }
	
    var displayQuestions = getDisplayFormat(quiz.originalQuestions,quiz.answers);
    var answerStr="";
    var correctAnswer="";
	
    for(var index = 0; index < quiz.questions.length; ++index) {
		if(siteId==4 && displayQuestions[index].ps.indexOf('value=""')!=-1) continue;
		
        if(siteId !==6) {
            answerStr += "<div class='row'><div style='margin-top: 30px;>"+
            "<div class='row'>"+
            "<div class='col-md-9'>"+
             "<span>"+(index+1)+".&nbsp;"+displayQuestions[index].ps+"</span>"+
            "</div>";
        }
        
        
		if(!readMode) {
            if(siteId ==6) {
                answerStr +="<div class='row' style='position:relative; margin-top: 30px; border-bottom: 1px solid #eeeeee;'>"+
                    "<div class='col-md-9' style='margin-bottom: 20px; margin-top: 10px; border-right: 2px solid #8a8988;'>"+
                    "<div class='col-md-1' style='width:4%;padding-right:0;padding-left:2px;'><b>"+(index+1)+"."+"</b>"+"</div>" +
                    "<span class='col-md-11' style='padding-left:0; margin-bottom: 20px;'>"+displayQuestions[index].ps+"</span>"+
                    "</div>"+
                    "<div class='col-md-3 text-center correct-answers' style='margin-top: 10px;'>" +
                    "<p style='font-weight: 700;'>Correct Answer</p>" +
                    "<p style='text-transform: capitalize;'>" + displayQuestions[index].as + "</p>" +
                    "</div>"+ 
                    "</div>";
            } else {
                answerStr +="<div class='col-md-3 left-border-vr  text-center correct-answers'>" +
                    "<p style='font-weight: 700;'>Correct Answer</p>" +
                    "<p>" +
                    "<span style='text-transform: capitalize'>" + displayQuestions[index].as + "</span>" +
                    "</p>" +
                    "</div>";
                }
        }
		 if(siteId !==6) {
            answerStr += "</div>"+"</div></div>";
            if(!readMode) answerStr+="<hr>";
        }
    }
	
    answerDiv.innerHTML=answerStr;
    $("#fibModal").find('.answer-holder').text(""); // to remove old ones
    $("#fibModal").find('#answer-holder')[0].appendChild(answerDiv);
    //initializing stuff..if the same quiz is loaded again
    quiz.currentIndex=0;
    quiz.mode="play";
    resetUserAnswers();
    MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
}

function getDisplayFormat(questions, answers) {
    var displayFormatted = [];
    var numberOfQuestions = questions.length;
	
    for(var i = 0; i < numberOfQuestions; ++i) {
        var qstn = questions[i].ps;
        qstn = qstn.split('__');
        var ps = '';
        var as = '';
        var style;
        var answer,userAnswer="";
        var tempAnswers = [answers[i]['ans1'],answers[i]['ans2'],answers[i]['ans3'],answers[i]['ans4']];
		
        for(var j = 0; j < qstn.length - 1; ++j){
            if(quiz.mode == "read"){
                var blank = '<input class="input-text-quiz text-center correct-answer-by-user" size="'+((answers[i]['ans' + (j + 1)].length+3))+'" value=' + answers[i]['ans' + (j + 1)]+ ' disabled>';
                ps += (qstn[j] + blank);

            } else {
                style = 'wrong-answer-by-user';
                as += answers[i]['ans' + (j + 1)] + '<br>';

                userAnswer = userAnswers[i]['ans' + (j + 1)].toLowerCase().replace(/\W+/g, '');
                answer = answers[i]['ans' + (j + 1)].toLowerCase().replace(/\W+/g, '');
                if(containsAnswer(tempAnswers,userAnswer)!=-1) {
                    style = 'correct-answer-by-user';
                }
				
                var blank = '<input class="input-text-quiz text-center '+style+'" size="'+((answers[i]['ans' + (j + 1)].length+3))+'" value="' + userAnswers[i]['ans' + (j + 1)] + '" disabled>';
                ps += (qstn[j] + blank);
            }
        }
		
        ps += qstn[j];
        displayFormatted.push({ps: ps,as :as});
    }
	
    return displayFormatted;
}

function resetTextInput(index){
    var ua = retrieveUserAnswerTextInput(index)

    if(ua=="") {
        if($('#fibans1').length) $('#fibans1').val('');
        if($('#fibans2').length) $('#fibans2').val('');
        if($('#fibans3').length) $('#fibans3').val('');
        if($('#fibans4').length) $('#fibans4').val('');
    } else {
        $.each(ua, function(key, value) {
            $('#fib'+key).val(value);

        });
    }
}