import mysql from 'mysql2/promise';

// MySQL connection configuration
const dbConfig = {
    host: process.env.DB_HOST || 'your-mysql-host',
    user: process.env.DB_USER || 'your-username',
    password: process.env.DB_PASSWORD || 'your-password',
    database: process.env.DB_NAME || 'your-database',
    port: process.env.DB_PORT || 3306,
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

export const handler = async (event, context, callback) => {
    const request = event.Records[0].cf.request;
    console.log(event.Records[0].cf);

    console.log("request====", request);

    const querystring = request.querystring;

    console.log('Lambda@Edge Request:', {
        uri: request.uri,
        querystring: querystring,
        requestId: context.awsRequestId,
        distributionId: event.Records[0].cf.config.distributionId
    });

    try {
        console.log('Lambda@Edge Request:', querystring);
        // Extract token from query parameters
        const token = extractToken(querystring);
        console.log('Extracted token:', token);

        if (!token) {
            console.log('No token provided in request');
            return callback(null, createErrorResponse(403, 'Access token required'));
        }

        // Validate and consume the token
        const isValidAndUnused = await validateAndConsumeToken(token);

        if (!isValidAndUnused) {
            console.log('Token validation failed:', token);
            return callback(null, createErrorResponse(403, 'Invalid or already used token'));
        }

        // Remove token from query string before forwarding to origin
        request.querystring = removeTokenFromQueryString(querystring);

        console.log('Token validated successfully, proceeding to origin');

        // Allow request to proceed to S3
        callback(null, request);

    } catch (error) {
        console.error('Error processing request:', error);
        callback(null, createErrorResponse(500, 'Internal server error'));
    }
};

function extractToken(querystring) {
    console.log("came");
    console.log("querystring to check", querystring);
    if (!querystring) return null;

    // fallback manual parse
    const parts = querystring.split("&");
    for (const part of parts) {
        const [key, val] = part.split("=");
        if (key === "token") {
            return decodeURIComponent(val || "");
        }
    }
    return null;
}

function removeTokenFromQueryString(querystring) {
    if (!querystring) return '';

    const params = querystring.split('&');
    const filteredParams = params.filter(param => !param.startsWith('token='));
    return filteredParams.join('&');
}

async function validateAndConsumeToken(token) {
    let connection;

    try {
        // Get connection from pool
        connection = await pool.getConnection();

        // Start transaction for atomic operation
        await connection.beginTransaction();

        // Check if token exists and is not used (keyValue = "false")
        const [rows] = await connection.execute(
            'SELECT key_name, key_value FROM key_value_mst WHERE key_name = ? FOR UPDATE',
            [token]
        );

        if (rows.length === 0) {
            console.log('Token not found:', token);
            await connection.rollback();
            return false;
        }

        const tokenData = rows[0];

        // Check if token is already used (keyValue = "true")
        if (tokenData.key_value === "true") {
            console.log('Token already used:', token);
            await connection.rollback();
            return false;
        }

        // Mark token as used by updating keyValue to "true"
        const [updateResult] = await connection.execute(
            'UPDATE key_value_mst SET key_value = ? WHERE key_name = ? AND key_value = ?',
            ["true", token, "false"]
        );

        if (updateResult.affectedRows === 0) {
            console.log('Token was already used (race condition):', token);
            await connection.rollback();
            return false;
        }

        // Commit transaction
        await connection.commit();
        console.log('Token consumed successfully:', token);
        return true;

    } catch (error) {
        console.error('MySQL error:', error);
        if (connection) {
            await connection.rollback();
        }
        throw error;
    } finally {
        if (connection) {
            connection.release();
        }
    }
}

function createErrorResponse(statusCode, message) {
    return {
        status: statusCode.toString(),
        statusDescription: message,
        headers: {
            'content-type': [{ key: 'Content-Type', value: 'text/plain' }],
            'cache-control': [{ key: 'Cache-Control', value: 'no-store, no-cache' }]
        },
        body: message
    };
}