<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<asset:javascript src="moment.min.js"/>
<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);">
    <div class='row'>
        <div class='col-md-9 main p-4' style=" margin: 40px auto; float: none;">
            <div id="content-books">
                <div class="form-group mb-4">
                    <label for="institutes" style="display: block;">Institute</label>
                    <div class="flex_st ad">
                        %{--                  <div class="col-md-4">--}%
                        <select name="institutes" id="institutes" class="form-control col-3" onchange="instituteChanged();">
                            <option value="" disabled="disabled" selected>Select Institute</option>
                            <g:each in="${institutes}" var="institute" status="i">
                                <option value="${institute.id}">${institute.name}</option>
                            </g:each>
                        </select>

                    </div>

                </div>
            </div>

            <div class="form-group ">
                <label for="batches" style="display: block;">Batch</label>
                <select name="batches" id="batches" class="form-control" style="">
                    <option value="">Select batch</option>
                    <option value="All">All batches</option>
                    <g:each in="${batchesList}" var="batch" status="i">
                        <option value="${batch.id}">${batch.name + (batch.endDate!=null?" - Completion date: "+(new SimpleDateFormat("dd-MM-yyyy")).format(batch.endDate):"")}</option>
                    </g:each>
                </select>

            </div>
            <div class="form-group">
                <label for="batches" style="display: block;">Report</label>
                <select name="report" id="report" class="form-control" style="">
                    <option value="">Select Report</option>
                    <option value="login">Login Report</option>
                    <option value="usagebreakup">Usage Report By Resources</option>
                    <option value="usageconsolidated">Consolidated Usage Report</option>
                </select>

                &nbsp;&nbsp;<button onclick="getReport()">Get Report</button>

            </div>
            <div id="reportDisplay" style="display: none"></div><br><br>
            <div id='chart_div' id="reportChart"></div>


        </div>
    </div>
</div>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>

<script>
    $(function(){
        $('*[name=endDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "dateOnly":true
        });

    });
    var instituteBatches={};
    var reportData = [];
    <%batchesList.each{batch->%>
    if(!instituteBatches[${batch.conductedFor}])
        instituteBatches[${batch.conductedFor}] = [];
    instituteBatches[${batch.conductedFor}].push(''+${batch.id});
    <%}%>
    google.charts.load('current', {packages: ['corechart', 'bar']});
    function getReport(){
        if(document.getElementById("batches").selectedIndex==0){
            alert("Please select the batch");
            document.getElementById("batches").focus();
        }else if(document.getElementById("report").selectedIndex==0){
            alert("Please select a report");
            document.getElementById("report").focus();
        }else if("All"==document.getElementById("batches")[document.getElementById("batches").selectedIndex].value&&document.getElementById("institutes").selectedIndex==0){
            alert("Please select a institute");
            document.getElementById("institutes").focus();
        }
        else{
            var batch = document.getElementById("batches")[document.getElementById("batches").selectedIndex].value;
            var report = document.getElementById("report")[document.getElementById("report").selectedIndex].value;
            if(report=="login") {
                if ("All" == batch) {
                    var institute = document.getElementById("institutes")[document.getElementById("institutes").selectedIndex].value;
                    <g:remoteFunction controller="reports" action="numberOfLoginsReport" onSuccess='showLoginReport(data);'
            params="'instituteId='+institute"/>
                } else {
                    <g:remoteFunction controller="reports" action="numberOfLoginsReport" onSuccess='showLoginReport(data);'
            params="'batchId='+batch"/>
                }
            }
            else if(report=="usagebreakup"){
                if ("All" == batch) {
                    var institute = document.getElementById("institutes")[document.getElementById("institutes").selectedIndex].value;
                    <g:remoteFunction controller="reports" action="resourcesUsedReportByResType" onSuccess='showResourceReport(data);'
            params="'instituteId='+institute"/>
                } else {
                    <g:remoteFunction controller="reports" action="resourcesUsedReportByResType" onSuccess='showResourceReport(data);'
            params="'batchId='+batch"/>
                }
            } else if(report=="usageconsolidated"){
                if ("All" == batch) {
                    var institute = document.getElementById("institutes")[document.getElementById("institutes").selectedIndex].value;
                    <g:remoteFunction controller="reports" action="resourcesUsedReport" onSuccess='showConsolidatedResourceReport(data);'
            params="'instituteId='+institute"/>
                } else {
                    <g:remoteFunction controller="reports" action="resourcesUsedReport" onSuccess='showConsolidatedResourceReport(data);'
            params="'batchId='+batch"/>
                }
            }
        }
    }

    function showLoginReport(data){
      var htmlStr = "No logins for this account so far.";
      reportData = [];
      if(data.logins.length>0){
          var logins = data.logins;
          htmlStr = "<table border='1' cellpadding='5'><tr><th>Month</th><th>Number of Logins</th></tr>";

          for(var i=0;i<logins.length;i++){
              htmlStr +="<tr><td>"+logins[i].month+"</td><td>"+logins[i].count+"</td></tr>";
              reportData.unshift([logins[i].month, logins[i].count]);
          }
          htmlStr +="</table>";
          reportData.unshift(['Month', 'Login Count']);
          drawChart();
      }
      document.getElementById("reportDisplay").innerHTML = htmlStr;
      $("#reportDisplay").show(500);

        $("#reportChart").show(500);

    }

    function showResourceReport(data){
        reportData = [];
        var htmlStr = "No resources used for this account so far.";

        if(data.resources!=null && data.resources.length>0){
            var resources = data.resources;
            htmlStr = "<table border='1' cellpadding='5'><tr><th>Month</th><th>Resource Type</th><th>Number of times used</th></tr>";

            for(var i=0;i<resources.length;i++){
                htmlStr +="<tr><td>"+resources[i].month+"</td><td>"+resources[i].resType+"</td><td>"+resources[i].count+"</td></tr>";
            }
            htmlStr +="</table>";
        }
        document.getElementById("reportDisplay").innerHTML = htmlStr;
        $("#reportDisplay").show(500);
        $("#reportChart").hide(500);
    }

    function showConsolidatedResourceReport(data){
        reportData = [];
        var htmlStr = "No resources used for this account so far.";

        if(data.resources.length>0){
            var resources = data.resources;
            htmlStr = "<table border='1' cellpadding='5'><tr><th>Month</th><th>Number of times used</th></tr>";

            for(var i=0;i<resources.length;i++){
                htmlStr +="<tr><td>"+resources[i].month+"</td><td>"+resources[i].count+"</td></tr>";
                reportData.unshift([resources[i].month, resources[i].count]);
            }
            htmlStr +="</table>";
            reportData.unshift(['Month', 'Resources Used']);
            drawChart();
        }
        document.getElementById("reportDisplay").innerHTML = htmlStr;
        $("#reportDisplay").show(500);

        $("#reportChart").show(500);
    }

    function drawChart() {

        var data = google.visualization.arrayToDataTable(reportData);
        var options = {
            bars: 'vertical',
            vAxis: {format: 'decimal'},
            bar: {groupWidth: "95%"},
            legend: {position: "none"},
            height: 200,
            width: 1000,
            colors: ['#1b9e77']
        };
        var chart = new google.charts.Bar(document.getElementById('chart_div'));
        chart.draw(data, google.charts.Bar.convertOptions(options));



    }
    function instituteChanged(){
        var batchesArray = instituteBatches[document.getElementById("institutes").value];
        var batches = document.getElementById('batches');

        for(var i=1; i < batches.length; i++)
        {
            if(batchesArray.includes(''+batches.options[i].value)){
                $("#batches option[value='"+batches.options[i].value+"']").show();
            }else{
                if(batches.options[i].value!="All") $("#batches option[value='"+batches.options[i].value+"']").hide();
            }
        }
    }




    $('#download-btn').on('click', function() {
        var batch = document.getElementById("batches");
        var batchId = batch.value;
        window.location.href = "/institute/downloadbooksreportData?batchId="+batchId;
    });

</script>

</body>
</html>
