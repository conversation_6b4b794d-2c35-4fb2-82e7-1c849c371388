<script>
    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }

    function getBooksCategories(){
        <g:remoteFunction controller="wonderpublish" action="getLevelBooksCategories"  onSuccess='processCategories(data);' />
    }
    var schoolCategories;
    var collegeCategories;
    var examCategories;

    var healthcare;
    function processCategories(data){

        schoolCategories = JSON.parse(data.School);
        collegeCategories = JSON.parse(data.College);
        examCategories = JSON.parse(data.CompetitiveExams);
        healthcare = JSON.parse(data.Healthcare);
        sessionStorage.setItem("allCategories",JSON.stringify(data));
        var allCategories =  JSON.parse(sessionStorage.getItem("allCategories"));
        var sc = JSON.parse(allCategories.School);
        //step1 create school content
        var htmlStr = "<div class='row'>\n" +
            "<div class='col-sm-12 col-md-12 mb-4 d-flex align-items-stretch'>";

        <% if(session["siteId"].intValue()==5){%>
            htmlStr += "<div class='card shadow border-0 w-100' style='background-color: #333333;'>\n";
        <%}else {%>
            htmlStr += "<div class='card shadow border-0 w-100' style='background-color: #2e86de;'>\n";
        <%}%>

        htmlStr += "<h5 class='card-header text-center text-white'><button id='goback-btn1' class='btn btn-sm btn-primary float-left'><< <span>Back</span></button><span id='titleOverwrite'>Select Syllabus</span></h5>\n"+
            // "<p>Select Syllabus</p>"+
            "<div class='card-body text-left'><div id='allSyllabus'>\n";

        for(var i=0;i<schoolCategories.length;i++){
            htmlStr +=  "<a href='javascript:syllabusSelected(\""+schoolCategories[i].syllabus+"\");'>"+schoolCategories[i].syllabus+"</a><span class='divider'>|</span>";
        }

        htmlStr +=  "</div><div id='schoolLevel'></div></div>\n" + "</div>"+
            "</div></div>";
        document.getElementById("pills-school").innerHTML = htmlStr;

        htmlStr = "<div class='row'>\n" +
            "<div class='col-sm-12 col-md-12 mb-4 d-flex align-items-stretch'>";

        <% if(session["siteId"].intValue()==5){%>
            htmlStr += "<div class='card shadow border-0 w-100' style='background-color: #333333;'>\n";
        <%}else {%>
            htmlStr += "<div class='card shadow border-0 w-100' style='background-color: #2e86de;'>\n";
        <%}%>

        htmlStr += "<h5 class='card-header text-center text-white'><button id='goback-btn1' class='btn btn-sm btn-primary float-left'><< <span>Back</span></button><span id='titleOverwrite'>Select Syllabus</span></h5>\n"+
            // "<p>Select Syllabus</p>"+
            "<div class='card-body text-left'><div id='healthcareSyllabus'>\n";

        // for(var i=0;i<healthcare.length;i++){
        //     htmlStr +=  "<a href='javascript:syllabusHealthcareSelected(\""+healthcare[i].syllabus+"\");'>"+healthcare[i].syllabus+"</a><span class='divider'>|</span>";
        //   /*  htmlStr +=  "<a href='/"+replaceAll(collegeCategories[i].syllabus,' ','-')+"/course'>"+collegeCategories[i].syllabus+"</a><span class='divider'>|</span>";*/
        // }
        // htmlStr +=  "</div><div id='healthcareLevel'></div></div>\n" + "</div>"+
        //     "</div></div>";
        document.getElementById("pills-healthcare").innerHTML = htmlStr;

        //step 2 create college content
        htmlStr ="<h5>Select Course</h5>\n"+
            "<div class='row'>\n" ;
        // "            <div class=\"col-sm-12 col-md-4 mb-4 text-center\">";
        var degreeType="";
        var backgroundColors=["#b71540","#079992","#4b7bec","#B33771","#a55eea","#45aaf2","#6ab04c"];
        var colorWise=0;
        for(var i=0;i<collegeCategories.length;i++){
            if(degreeType!=collegeCategories[i].degreeType){
                if(degreeType!=""){

                    htmlStr +=  "  </div>\n" +
                        "</div>"+"</div>";
                }
                degreeType = collegeCategories[i].degreeType;
                htmlStr +="<div class='col-sm-12 col-md-6 col-lg-4 mb-4 d-flex align-items-stretch'>"+
                    "<div class='card shadow border-0 w-100' style='background:"+backgroundColors[colorWise%(backgroundColors.length)]+"'>\n" +

                    "<h5 class='card-header text-center text-white'>"+degreeType+"</h5>"+
                    "  <div class='card-body text-left'>\n";
                colorWise++;
            }
            htmlStr +=  "<a href='/"+replaceAll(collegeCategories[i].syllabus,' ','-')+"/course'>"+collegeCategories[i].syllabus+"</a><span class='divider'>|</span>";
        }
        htmlStr +=  "  </div>\n" +
            "</div></div>";
        htmlStr += "</div>";

        document.getElementById("pills-college").innerHTML = htmlStr;

        //step 3 create competitive exams
        htmlStr ="<h5>Select Exam</h5>\n"+
            "<div class='row'>\n" ;

        // "            <div class=\"col-md-3 d-flex justify-content-center\">\n";
        var syllabus="";
        var backgroundColors=["#e58e26","#a55eea","#b71540","#B33771","#2e86de","#079992","#7158e2","#3867d6","#0a3d62","#fa8231"];
        var colorIndex=0;
        for(var i=0;i<examCategories.length;i++){
            if(examCategories[i].syllabus=="State Job Exams") continue;
            if(syllabus!=examCategories[i].syllabus){
                if(syllabus!=""){

                    htmlStr +=  "  </div>\n" +
                        "</div>"+"</div>";

                }
                syllabus = examCategories[i].syllabus;
                htmlStr += "<div class='col-sm-12 col-md-6 col-lg-4 mb-4 show_exams d-flex align-items-stretch'>"+
                    "<div class='card shadow border-0 w-100' style='background:"+backgroundColors[colorIndex%(backgroundColors.length)]+"'>\n" +
                    "<h5 class='card-header text-center text-white'>"+syllabus+"</h5>"+
                    "<div class='card-body text-left'>\n";
                colorIndex++;
            }
            htmlStr +=  "<a href='/"+replaceAll(examCategories[i].grade,' ','-')+"/exam'>"+examCategories[i].grade+"</a><span class='divider'>|</span>" ;
        }
        htmlStr +=  "</div>\n" +
            "</div></div>";

        htmlStr += "<div class='col-sm-12 col-md-6 col-lg-4 mb-4 show_exams state-job-exams d-flex align-items-stretch'>\n"+
            "<div class='card shadow border-0 w-100' style='background:"+backgroundColors[colorIndex%(backgroundColors.length)]+"'>\n"+
            "<h5 class='card-header text-center text-white'><button id='goback-btn2' class='btn btn-sm btn-primary float-left'><< <span>Back</spn></button><span id='titleOverwrite1'>State Job Exams</span></h5>\n"+
            "<div class='card-body text-left'><div id='allStates'>\n";

        var state = "";

        for(var i=0;i<examCategories.length;i++) {
            if (examCategories[i].syllabus == "State Job Exams") {
                if (state == ""||state != examCategories[i].state) {
                    state = examCategories[i].state;
                    htmlStr += "<a href='javascript:stateSelected(\"" + examCategories[i].state + "\");'>" + examCategories[i].state + "</a><span class='divider'>|</span>";
                }
            }

         }

        htmlStr +=  "</div><div id='stateExams'></div></div>\n" +
            "</div></div>";
        htmlStr +=     "</div>";
        document.getElementById("pills-exams").innerHTML = htmlStr;

        if(getCookie("level")) {
            console.log("level cookie is "+getCookie("level"));
            $('.nav-tabs a[href="#'+getCookie("level")+'"]').tab('show');
            if("pills-school"==getCookie("level")&&getCookie("syllabus")){
                syllabusSelected(getCookie("syllabus"));
            }
        }
        else {
            console.log("level not present");
            $('.nav-tabs a[href="#pills-school"]').tab('show');
        }

    }

    function syllabusSelected(syllabus){
        var gradesID = document.getElementById('schoolLevel');
        var syllabusID = document.getElementById('allSyllabus');

        if(gradesID.style.transform = "translateY(100%)") {
            // Grades Card Animation
            gradesID.style.transform = "translateY(0)";
            gradesID.style.opacity = "1";
            $('#allSyllabus').fadeOut();
            //gradesID.style.display = "block";

            // All Syllabus Card Animation
            syllabusID.style.transform = "scale(0)";
            syllabusID.style.opacity = "0";
            $('#schoolLevel').fadeIn("slow");
            $('#goback-btn1').fadeIn("slow");
            //syllabusID.style.display = "none";
        }
        setCookie("syllabus",syllabus);
        document.getElementById("titleOverwrite").innerHTML = syllabus;
        var htmlStr = "<div class='grades-list'>\n";

        //Only NIOS syllabus links
        if(syllabus == 'NIOS') {
            htmlStr += "<a href='/NIOS/school/Open-Basic-Education'>Open Basic Education</a><span class='divider'>|</span><a href='/NIOS/school/Secondary-Courses'>Secondary Courses</a><span class='divider'>|</span><a href='/NIOS/school/Sr.-Secondary-Courses'>Sr. Secondary Courses</a>";
        } else {
            for(var i=1;i<=12;i++){
                htmlStr +=  "<a href='/"+syllabus+"/school/"+i+"'>Class "+i+"</a><span class='divider'>|</span>" ;
            }
        }

        htmlStr +=  "</div>";


        document.getElementById("schoolLevel").innerHTML = htmlStr;

        $("#goback-btn1").click(function(){
            if(gradesID.style.transform = "translateY(0)") {
                // Grades Card Animation
                gradesID.style.transform = "translateY(100%)";
                gradesID.style.opacity = "0";
                $('#allSyllabus').fadeIn();
                //gradesID.style.display = "none";

                // All Syllabus Card Animation
                syllabusID.style.transform = "scale(1)";
                syllabusID.style.opacity = "1";
                //$('#schoolLevel').fadeOut();
                $('#goback-btn1').fadeOut();
                //syllabusID.style.display = "block";
            }
            document.getElementById("titleOverwrite").innerHTML = "Select Syllabus";
        });

    }

    function stateSelected(state){
        var examsID = document.getElementById('stateExams');
        var statesID = document.getElementById('allStates');

        if(examsID.style.transform = "translateY(100%)") {
            // State Exam Card Animation
            examsID.style.transform = "translateY(0)";
            examsID.style.opacity = "1";
            $('#allStates').fadeOut();

            // Other Exams Card Animation
            statesID.style.transform = "scale(0)";
            statesID.style.opacity = "0";
            $('#stateExams').fadeIn("slow");
            $('#goback-btn2').fadeIn("slow");
        }


        //$('.show_exams').toggle();
        setCookie("state",state);
        document.getElementById("titleOverwrite1").innerHTML = state + " Exams";
        var htmlStr = "  <div class='exams-list'>\n";

        for(var i=0;i<examCategories.length;i++) {
            if (examCategories[i].syllabus == "State Job Exams"&&examCategories[i].state==state) {
                htmlStr +=  "<a href='/"+replaceAll(examCategories[i].grade,' ','-')+"/exam'>"+examCategories[i].grade+"</a><span class='divider'>|</span>";
            }

        }
        htmlStr +=  "</div>";
        document.getElementById("stateExams").innerHTML = htmlStr;

        $("#goback-btn2").click(function(){
            if(examsID.style.transform = "translateY(0)") {
                // State Exam Card Animation
                examsID.style.transform = "translateY(100%)";
                examsID.style.opacity = "0";
                $('#allStates').fadeIn();

                // Other Exams Card Animation
                statesID.style.transform = "scale(1)";
                statesID.style.opacity = "1";
                $('#goback-btn2').fadeOut();
            }
            document.getElementById("titleOverwrite1").innerHTML = "State Job Exams";
        });
    }
    //tab clicked event
    $('#pills-tab a').on('shown.bs.tab', function (e) {
        var level = $(e.target).attr("href");
        level = level.substr(1);
        setCookie("level",level);
        $("#searchResults").hide();
        $("#pills-tabContent").show();
    });

    //step 1
    getBooksCategories();




    function replaceAll(str, find, replace) {
        if(str==undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }
    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }

</script>
<script>
    $(document).ready(function() {
        $('#testimonialSlider').slick({
            infinite: false,
            slidesToShow: 2,
            slidesToScroll: 2,
            dots: false,
            arrows: true,
            centerMode: false,
            autoplay: false,
            autoplaySpeed: 5000,
            responsive: [
                {
                    breakpoint: 992,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        arrows: false,
                        dots: true
                    }
                }
            ]
        });
    });
</script>
<script>
    var a = 0;
    $(window).scroll(function() {
        var oTop = $('.statistics').offset().top - window.innerHeight;
        if (a == 0 && $(window).scrollTop() > oTop) {

            $('.count-number').each(function () {
                $(this).prop('Counter',0).animate({
                    Counter: $(this).text()
                }, {
                    duration: 2000,
                    easing: 'swing',
                    step: function (now) {
                        $(this).text(Math.ceil(now));
                    }
                });
            });

            a = 1;
        }
    });
</script>
