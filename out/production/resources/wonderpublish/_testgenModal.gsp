<div class='modal fade test-gen-modal' id='test-gen-modal' data-backdrop='static' tabindex='-1' role='dialog' aria-labelledby='test-gen-modal-label'>
  <div class='modal-dialog modal-lg modal-dialog-centered' role='document'>
    <div class='modal-content'>
      <div class='modal-header'>
        <button type='button' class='close' data-dismiss='modal' aria-label='Close'><span aria-hidden='true'>&times;</span></button>
        <h4 class='modal-title text-center' id='test-gen-modal-label'>Test Generator</h4>
      </div>
      <div class='modal-body test-gen-modal-body'>
        <div class="test-background-image"></div>
        <div class='test-gen-books row on-top-overlay' id='divi2'>Hello Namaste</div>

        <div class='test-gen-chapters row on-top-overlay' id='divi3'></div>

        <div class="container test-type-container">
          <div class='test-gen-chapters row on-top-overlay' id='divi4' style='display: none'>
            <div class='col-md-12'>
              <p class='test-type'>Select test type1</p>
              <table class='table chapter-selection-table table-responsive table-bordered'>
                <tbody>
                <tr class='chapter-selection'>
                  <td class='chapter-name'>
                    <label class='not-active'>Fill in the blanks
                      <input type='radio' name='testSelection'>
                      <span class='checkmark checkmark-radio'></span>
                    </label>
                  </td>
                </tr>
                <tr class='chapter-selection'>
                  <td class='chapter-name'>
                    <label class='not-active'>Multiple Choice Questions
                      <input type='radio' name='testSelection'>
                      <span class='checkmark checkmark-radio'></span>
                    </label>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class='test-gen-chapters row on-top-overlay' id='divi5' style='display: none'>
            <div class='col-md-12'>
              <p class='test-type'>Select difficult level</p>
              <table class='table chapter-selection-table table-responsive table-bordered'>
                <tbody>
                <tr class='chapter-selection'>
                  <td class='chapter-name'>
                    <label class='not-active'>Easy
                      <input type='radio' name='testTypeSelection' disabled>
                      <span class='checkmark checkmark-radio'></span>
                    </label>
                  </td>
                </tr>
                <tr class='chapter-selection'>
                  <td class='chapter-name'>
                    <label class='not-active'>Medium
                      <input type='radio' name='testTypeSelection' disabled>
                      <span class='checkmark checkmark-radio'></span>
                    </label>
                  </td>
                </tr>
                <tr class='chapter-selection'>
                  <td class='chapter-name'>
                    <label class='not-active'>Hard
                      <input type='radio' name='testTypeSelection' disabled>
                      <span class='checkmark checkmark-radio'></span>
                    </label>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class='test-gen-chapters row on-top-overlay' id='divi6' style='display: none'>
            <div class='col-md-12'>
              <p class='test-type' id="noofquestionslabel">Number of questions </p>
              <input type='text' class='form-control' name='noOfQuestions' id='noOfQuestions' placeholder='Enter Number (200 maximum)' disabled size="4">
            </div>
          </div>
        </div>
        <div class='overlay'></div>
      </div>
      <div class='modal-footer'>
        <div>
          <a href="javascript:prev()" class="btn previous-btn pull-left">
            <i class="material-icons" aria-hidden="true">chevron_left</i> Previous
          </a>
          <div id="error_message" class="pull-left justify-content-center">
            Error messages comes here
          </div>
          <button type='button' class='btn next-btn ml-auto' id="next-button" onclick="javascript:next()">Next</button>
        </div>
      </div>
    </div>
  </div>
</div>
