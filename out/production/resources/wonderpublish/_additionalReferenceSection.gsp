<script>
    var additional = document.getElementById('additional-refs');

    function populateWebLinks(cData) {
        var cStr = "";
        additional.innerHTML="";
        for (var i = 0; i < cData.length; ++i) {
            var showShare = false;
            var data = cData[i];
            //assumption is the createdbyuser items will come of the items created by the instructor himself and not anybody else
            if (instructor && data.sharing == "createdbyuser") {
                showShare = true;
            }
            if (instructor && data.sharing == null && instructorControlledBook) {
                showShare = true;
            }
            imgSrc = "/assets/wonderslate/weblink.png";
            if(siteId == 9) {
                additional.innerHTML += "<div class='additional-ref-wrapper'>" +
                    "<a href='"+data.link.replace('#', ':')+"' target='_blank' class='additional-ref-link'>" +data.title+"</a>" +
                    "</div>";
            } else {
                if(data.resType=="QA"){
                    additional.innerHTML += "<div class='additional-ref-wrapper'>" +
                        "<a href='javascript:showQAndA(" + data.link + ")'>" +
                        "<div class='additional-ref-item'>" +
                        "<div class='additional-ref-img-wrapper'>" +
                        "<img src='" + imgSrc + "' class='additional-ref-img' alt=''/>" +
                        "</div>" +
                        "<div class='additional-ref-info'>" +
                        "<p class='additional-ref-name'>" + data.title + "</p>" +
                        "</div>" +
                        "</div>" +
                        "</a>" +
                        "</div>";
                }else {
                    additional.innerHTML += "<div class='additional-ref-wrapper'>" +
                        "<a href='javascript:openWebRef(" + data.id + ",\""+data.link+"\")'>" +
                        "<div class='additional-ref-item'>" +
                        "<div class='additional-ref-img-wrapper'>" +
                        "<img src='" + imgSrc + "' class='additional-ref-img' alt=''/>" +
                        "</div>" +
                        "<div class='additional-ref-info'>" +
                        "<p class='additional-ref-name'>" + data.title + "</p>" +
                        "</div>" +
                        "</div>" +
                        "</a>" +
                        "</div>";
                }
            }
        }
        $("#additional-refs").show();
        $("#addRefButton").show();
    }

    function openWebRef(id,link){
        initCallGoogle(id,"References");
        if(link.indexOf("http")==-1) link = "http://"+link;
        window.open(link, '_blank');
        if(loggedInUser){
            if(allTabMode)
                updateUserView(id,"all","weblinks");
            else
                updateUserView(id,"weblinks","weblinks");
        }
        else{
            if(allTabMode)
                updateView(id,"all","weblinks");
            else
                updateView(id,"weblinks","weblinks");
        }
    }

</script>