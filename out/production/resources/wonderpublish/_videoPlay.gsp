
<div class="videoPlays">
  <div id="videoDiv" class="row video-section"></div>
  <div id="content-data-relvideos" class="row"></div>
</div>
<div class="video-url">
<div class="modal fade" id="addVideo" role="dialog">
  <div class="modal-dialog modal-sm modal-dialog-centered justify-content-center">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Add Video</h4>
      </div>
      <div class="modal-body">
         <form id="videoForm">
             <input type="text" class="video-url" name="link" id="userYoutubeLink" placeholder="Enter Youtube URL here">
             <input type="text" class="video-url" name="resourceName" id="userYoutubeName" placeholder="Enter Video name">
         </form>
      </div>
        <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="userYoutubelinksUploadAlert">
            ** Enter valid Youtube link and the name.
        </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" onclick="closeAddVideo()">CANCEL</button>
          <button type="button" class="btn" onclick="addYoutubeVideo();">ADD</button>
      </div>
    </div>
  </div>
</div>
</div>
<script>
function backVideos() {

    if (!videoPresent) {
        $('#withnovideos').show();
        $('#searchVideos').hide();
    } else {
        $('#videoDiv').show();
        $('#searchVideos').hide();
    }
    if (allTabMode) {
        $('#chapter-details-tabs a[href="#all"]').tab('show');
    }
}

    function addVideoFromList(linkFromList,linkNameFromList){
        link = linkFromList;
        linkName = linkNameFromList;
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="resourceCreator" action="addlink" params="'from=app&chapterId='+previousChapterId+'&link='+link+'&resourceName='+linkName+'&resourceType=Reference Videos'" onSuccess='videoFromListAddCompleted(data);'/>

    }


function validateYouTubeUrl(url) {
    if (url != undefined || url != '') {
        var regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=|\?v=)([^#\&\?]*).*/;
        var match = url.match(regExp);

        if (match && match[2].length == 11) {
            // Do anything for being valid
            // if need to change the url to embed url then use below line
            //$('#ytplayerSide').attr('src', 'https://www.youtube.com/embed/' + match[2] + '?autoplay=0');
            return true;
        } else {
            // Do anything for not being valid
            return false;
        }
    }

    return false;
}


function getIdFromYouTubeURL(url) {
    if(url.match(/(youtu.be)/)){
        var split_c = "/";
        var split_n = 3;
    }

    if(url.match(/(youtube.com)/)){
        var split_c = "v=";
        var split_n = 1;
    }

    var getYouTubeVideoID = url.split(split_c)[split_n];
    return getYouTubeVideoID.replace(/(&)+(.*)/, "");
}



    function videoFromListAddCompleted(data){
        var playBtn = "/assets/play-btn.png";
        var imgSrc = "https://i.ytimg.com/vi/"+link+"/mqdefault.jpg";
        videoDiv.innerHTML = "<div class='video-wrapper'>" +
            "<a href='javascript:playVideo(\"" + link + "\"," + data.resId + ")'>" +
            "<div class='video-item'>" +
            "<div class='video-img-wrapper'>" +
            "<img src='"+imgSrc+"' class='video-img' alt=''/>" +
            "<div class='play-btn-wrapper'>" +
            "<i class='material-icons'>play_arrow</i> "+
            "</div>" +
            "</div>" +
            "<div class='video-info'>" +
                "<p class='video-name'>"+linkName+"</p>"+
            "</div>" +
            "</div>" +
            "</a>"+
            "</div>"+videoDiv.innerHTML;
        $('.loading-icon').addClass('hidden');
        $("#withnovideos").hide();
         $("#video"+link).find('.videoTOadd').hide();
        $("#video"+link).find('.dpr-none').show();
        videoPresent = true;
        $("#videoAddButton").show();

        //add to all tab
        addVideoToAllTab(linkName,link,data.resId);

    }

    function addVideoToAllTab(linkName,link,resId){
        var cStr = "<div class=\"container\">\n" +
            "<div class=\"all-container\">";
        cStr +="<div class=\"container-wrapper\">\n" +
            "<div class='d-flex justify-content-between align-items-center'>"+
            "<p class='quiz-number'></p>";
        cStr +="</div>";
        cStr += "        <div class=\"media\">\n" ;
        cStr +="<i class=\"align-self-center yt flaticon-youtube\"></i>";
        cStr += "            <div class=\"media-body\">\n" +
            "                <span class=\"date mt-0\">Added by you now</span>\n" +
            "                <p class=\"title\">"+linkName+"</p>\n" +
            "                <div class=\"d-flex align-items-center justify-content-between\">\n" ;
        cStr += "                    <a class=\"mb-0 readnow\" href='javascript:playVideo(\"" + link + "\"," + resId + ")'>Play</a>\n";

        cStr +=  "                </div>\n" +
            "            </div>\n" +
            "        </div>\n" +
            "    </div>\n";
        cStr +=     "        </div>\n" +
            "      </div>" ;
        document.getElementById('content-data-all').innerHTML += cStr;
    }

function videoUpdated(data){
    //add to all tab
    addVideoToAllTab(linkName,link,data.resId);
    alert("Video added");
    $('.loading-icon').addClass('hidden');
    document.getElementById("videoForm").reset();

}


function addYoutubeVideo(){
    $("#userYoutubelinksUploadAlert").hide(500);
    if(document.getElementById("userYoutubeName").value==""||document.getElementById("userYoutubeLink").value==""||!validateYouTubeUrl(document.getElementById("userYoutubeLink").value)){
        $("#userYoutubelinksUploadAlert").show(500);
    }
    else {
        linkName= document.getElementById("userYoutubeName").value;
        link = getIdFromYouTubeURL(document.getElementById("userYoutubeLink").value);
        $('.loading-icon').removeClass('hidden');
        $('#addVideo').modal('hide');
        <g:remoteFunction controller="resourceCreator" action="addlink" params="'from=app&chapterId='+previousChapterId+'&link='+link+'&resourceName='+linkName+'&resourceType=Reference Videos'" onSuccess='videoUpdated(data);'/>
    }
}

function addVideoFromList(linkFromList,linkNameFromList){
    link = linkFromList;
    linkName = linkNameFromList;
    $('.loading-icon').removeClass('hidden');
    <g:remoteFunction controller="resourceCreator" action="addlink" params="'from=app&chapterId='+previousChapterId+'&link='+link+'&resourceName='+linkName+'&resourceType=Reference Videos'" onSuccess='videoFromListAddCompleted(data);'/>

}

function videoFromListAddCompleted(data){
    var playBtn = "/assets/play-btn.png";
    var imgSrc = "https://i.ytimg.com/vi/"+link+"/mqdefault.jpg";
    videoDiv.innerHTML = "<div class='video-wrapper'>" +
        "<a href='javascript:playVideo(\"" + link + "\"," + data.resId + ")'>" +
        "<div class='video-item'>" +
        "<div class='video-img-wrapper'>" +
        "<img src='"+imgSrc+"' class='video-img' alt=''/>" +
        "<div class='play-btn-wrapper'>" +
        "<i class='material-icons'>play_arrow</i> "+
        "</div>" +
        "</div>" +
        "<div class='video-info'>" +
        "<p class='video-name'>"+linkName+"</p>"+
        "</div>" +
        "</div>" +
        "</a>"+
        "</div>"+videoDiv.innerHTML;
    $('.loading-icon').addClass('hidden');
    $("#withnovideos").hide();
    $("#video"+link).find('.videoTOadd').hide();
    $("#video"+link).find('.dpr-none').show();
    videoPresent = true;
    $("#videoAddButton").show();

    //add to all tab
    addVideoToAllTab(linkName,link,data.resId);

}

function addVideoToAllTab(linkName,link,resId){
    var cStr = "<div class=\"container\">\n" +
        "<div class=\"all-container\">";
    cStr +="<div class=\"container-wrapper\">\n" +
        "<div class='d-flex justify-content-between align-items-center'>"+
        "<p class='quiz-number'></p>";
    cStr +="</div>";
    cStr += "        <div class=\"media\">\n" ;
    cStr +="<i class=\"align-self-center yt flaticon-youtube\"></i>";
    cStr += "            <div class=\"media-body\">\n" +
        "                <span class=\"date mt-0\">Added by you now</span>\n" +
        "                <p class=\"title\">"+linkName+"</p>\n" +
        "                <div class=\"d-flex align-items-center justify-content-between\">\n" ;
    cStr += "                    <a class=\"mb-0 readnow\" href='javascript:playVideo(\"" + link + "\"," + resId + ")'>Play</a>\n";

    cStr +=  "                </div>\n" +
        "            </div>\n" +
        "        </div>\n" +
        "    </div>\n";
    cStr +=     "        </div>\n" +
        "      </div>" ;
    document.getElementById('content-data-all').innerHTML += cStr;
}

function createNewVideo() {
    $("#userYoutubelinksUploadAlert").hide(500);
    $("#content-data-videos").hide();
    $("#videoDiv").hide();
    $("#video-add").show();

}

// Your use of the YouTube API must comply with the Terms of Service:
// https://developers.google.com/youtube/terms
// Called automatically when JavaScript client library is loaded.
function onClientLoad() {
    gapi.client.load('youtube', 'v3', onYouTubeApiLoad);
}
// Called automatically when YouTube API interface is loaded (see line 9).
function onYouTubeApiLoad() {
    var apiKeys = '${apiKey}';
    var apiKey;
    if(apiKeys.indexOf(",")>0){
        var apiKeysArray = apiKeys.split(',');
        var randomIndex =  Math.floor(Math.random() * 5);
        apiKey = apiKeysArray[randomIndex];
    }else{
        apiKey = apiKeys;
    }
    gapi.client.setApiKey(apiKey);
}
function searchRelatedVideos(){

    openResourceScreen();
    var query = chapterName+" class "+chapterGrade+" of "+chapterSyllabus;
    if(chapterDesc!=null&&!chapterDesc=="") {
        query = chapterDesc;
    }
    // Use the JavaScript client library to create a search.list() API call.
    var request = gapi.client.youtube.search.list({
        part: 'snippet',
        q:query,
        maxResults:20
    });
    // Send the request to the API server, call the onSearchResponse function when the data is returned
    request.execute(displayRelatedVideos);
    if(loggedInUser){

        updateUserViewChapter(previousChapterId,"all","videos","videos");

    }
    else{

        updateViewChapter(previousChapterId,"all","videos","videos");

    }
}



function closeAddVideo(){
    $('#addVideo').modal('hide');
    if(allTabMode){
        $('#chapter-details-tabs a[href="#all"]').tab('show');
    }
}

function displayRelatedVideos(response){
    var responseString = JSON.stringify(response, '', 2);
    var videos =response.items;

    var cStr = "";
    cStr+="<div id='searchVideos' style='width: 100%;'>";
    cStr+="<div class='d-lfex flex-wrap'>"+"<div class='dflexPr'>"+
        setBackButton()+
        "</div>";

    for (var i = 0; i < videos.length; ++i) {
        var data = videos[i];
        imgSrc = "https://i.ytimg.com/vi/"+data.id.videoId+"/mqdefault.jpg";
        var playBtn = "/assets/playBtn.svg";
        var playBtn_sm = "/assets/playButn.svg";
        var addBtn="/assets/addVide.svg";
        var addBtn1="/assets/addedVid.svg";

        cStr +="<div class='test-pr'>"+
            "<div class='video-wrapper'>" +
            "<div class='d-flex'>"+
            "<a href='javascript:playVideo(\"" + data.id.videoId + "\"," + 1 + ")'>" +
            "<div class='video-item'>" +
            "<div class='video-img-wrapper'>" +
            "<img src='"+imgSrc+"' class='video-img' alt=''/>" +
            "<div class='overlay-pr'>"+
            "<div class='play-btn-wrapper'>" +
            "<img class='def-imgPr' src='"+playBtn+"' alt=''/>" +
            "</div>" +
            "</div>"+
            "</div>" +
            "</div>"+
            "</a>" +
            "</div>"+
            "<div style='margin-left:2rem;width: 300px;'>"+
            "<div class='video-info'>" +
            "<p class='video-name'>"+data.snippet.title+"</p>"+
            "</div>"+
            "<div id='video" +data.id.videoId+"'>" +
            "<div class='pr-flex'>"+
            "<a href='javascript:playVideo(\"" + data.id.videoId + "\"," + 1 + ")'>"+
            "<div class='play-btn-wrapper'>" +
            "<img class='sm-playImg' src='"+playBtn_sm+"' alt=''/>" +"<span>Play Video</span>"+
            "</div>"
            +"</a>"+
            "<a class='videoTOadd' href=\"javascript:addVideoFromList('"+data.id.videoId+"','"+data.snippet.title+"');\">" +
            "<img src='"+addBtn+"' class='addBtn-pr'> Add to Videos</a>" +
            "<a class='dpr-none'><img src='"+addBtn1+"' class='addBtn-pr'>Videos Added</a>" +
            "</div>"+
            "</div>"+
            "</div>"
            +"</div>"
            +"</div>";
    }
    cStr+="</div>";
    document.getElementById("htmlreadingcontent").innerHTML = cStr;
    $('.loading-icon').addClass('hidden');

}


</script>
<script>
    $(document).ready(function() {
        $(window).resize(function() {
            var width = $(window).width();
            if (width <= 1024){
                var elements = document.getElementsByTagName("*");
                for(var id = 0; id < elements.length; ++id) { elements[id].oncontextmenu = null; }
                console.log('right click enabled only on mobile devices..');
                /*$('#addVideo #videoForm input[type=text]').bind("copy paste", function(e) {
                    e.preventDefault();
                    alert("You cannot paste text into this textbox!");
                    /!*$('#text1').bind("contextmenu", function(e) {
                        e.preventDefault();
                    });*!/
                });*/
            }
        });
    });
</script>

<script src="https://apis.google.com/js/client.js?onload=onClientLoad" type="text/javascript"></script>
