<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>

<asset:stylesheet href="material-input.css"/>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<asset:stylesheet href="wonderslate/adminCommonStyle.css" async="true"/>

<style>
.sage-body .sage-banner {
    display: none;
}
.image-upload > input {
    display: none;
}

.cke_textarea_inline {
    height: 80px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

.form-group .cke_textarea_inline{
    height: 35px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}
</style>
<div class="notesCreator">
    <div class="container" >
            <div id="static-content" width="90%" class="rounded">
                <div class="row ">
                    <div class="col-md-12">
                        <h4>Notes</h4><br>
                    </div>
                </div>
            <div class="row">
                <div class="col-md-12">
                    <form class="form-horizontal" enctype="multipart/form-data" role="form" name="addhtml" id="addhtml" action="/resourceCreator/addHTML" method="post">
                        <input type="hidden" name="resourceType">
						<input type="hidden" name="bookId">
                        <input type="hidden" name="chapterId">
                        <input type="hidden" name="mode">
                        <input type="hidden" name="page">
                        <input type="hidden" name="gradeid">
                        <input type="hidden" name="resourceDtlId">
                        <input type="hidden" name="htmlId">
                        <input type="hidden" name="quizMode" value="${params.quizMode}">
                        <input type="hidden" name="folderId" value="${params.folderId}">

                        <div class="row mb-3 ">
                            <div class="col-md-12">
								<div class="form-group resourceName float-label-control">
                                    <div class="cktext">
                                        <input type="text" class="form-control" id="resourceName" name="resourceName" placeholder="Name of your notes"  maxlength="255" value="">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <% if(params.page=="currentaffairs"){%>
                        <div class="d-flex align-items-center mb-3">
                            <div class="form-group col-4">
                                <label for="testStartDate">Current Affairs Date:</label><br>
                                <input type="text" class="w-100 form-control" id="testStartDate" name="testStartDate" placeholder="Date" autocomplete="off" >
                            </div>
                        </div>
                        <%}%>

                        <%if("books".equals(session['entryController'])){%>
                        <div class="d-flex align-items-center">
                            <div class="form-group col-md-4 col-lg-4 col-12 mb-3 px-0">
                                <label>Resource Type </label>
                                <select name="subType" id="subType" class="form-control"><option value="">Select</option>
                                    <option value="lesson">Lesson</option>
                                    <option value="mindmap">Mind Map</option>
                                    <option value="modelquestionpaper">Model question paper</option>
                                    <option value="modelquestionpapersolved">Model question paper solved</option>
                                    <option value="notes">Notes</option>
                                    <option value="questionpaper">Previous year question paper</option>
                                    <option value="questionpapersolved">Previous year question paper solved</option>
                                    <option value="solution">Solution</option>
                                    <option value="trick">Trick to remember</option>
                                </select>
                            </div>
                        </div>
                        <%}%>

                        <div class="row">
                            <div class="col-md-12">
								<div class="form-group notes float-label-control">
									<div class="cktext">
										<textarea  rows="30" class="form-control" id="notes" name="notes" placeholder="Enter your notes here"></textarea>
									</div>
                                </div>
                            </div>						
                        </div>
                        <div class="row alertRow">
                        <div class="alert alert-danger col-sm-12" id="alertbox" style="display: none">
                            Please enter all the fields.
                        </div>
                        </div>
                        <div class="row notes mt-5">
%{--                        <div class="form-group">--}%
                            <div class=" col-sm-12 text-center">
                                <button type="button" onclick="javascript:formSubmit()" class="btn btn-primary"><%="${params.mode}"=="create"?"Add":"Update"%></button>
                            </div>
%{--                        </div>--}%
                        </div>
                    </form>
                </div>
            </div>
        </div>
       </div>
    </div>
<asset:javascript src="jquery-1.11.2.min.js"/>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<asset:javascript src="bootstrap.min.js"/>
<asset:javascript src="material-input.js"/>
<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>
<asset:javascript src="jquery.simple-dtpicker.js"/>
<script>
    function initializeQuizDataWithAnswers(data) {
        var htmlStr = "";
        var htmlAnswer = "";
        var htmlDescription = "";
        var checkInput = '';
        var appendAnswers = false;
        var cont = JSON.parse((data.results));
        for (var i = 0; i < cont.length; i++) {
            checkInput = cont[i].op5 ? ("<li><p>(e)</p>" + cont[i].op5) + "</li>" : "";
            htmlStr += "<div class='directions-parent'>";
            htmlStr += cont[i].directions ? cont[i].directions : '';
            htmlStr += "</div>"
            htmlStr += "<div class='questionBlock'>" + "<p>" + (i + 1) + "</p>" + "<h5>" + cont[i].ps + "</h5>" + "</div>" + "<ul>" + "<li><p>(a)</p>" + cont[i].op1 + "</li>" + "<li><p>(b)</p>" + cont[i].op2 + "</li>" + "<li><p>(c)</p>" + cont[i].op3 + "</li>" + "<li><p>(d)</p>" + cont[i].op4 + "</li>" + checkInput + "</ul>";


            var keys = Object.keys(cont[i]).filter(k => cont[i][k] === "Yes");
            console.log(keys)
            var answerOptions=[]
            for(var j=0;j<keys.length;j++) {
                if (keys[j] == 'ans1') {
                    htmlAnswer += "<li>" + "<p>" + (i + 1) + "</p>" + "<h6>(a)</h6>" + "</li>";
                    answerOptions.push("a")
                } if (keys[j] == 'ans2') {
                    htmlAnswer += "<li>" + "<p>" + (i + 1) + "</p>" + "<h6>(b)</h6>" + "</li>";
                    answerOptions.push("b")
                } if (keys[j] == 'ans3') {
                    htmlAnswer += "<li>" + "<p>" + (i + 1) + "</p>" + "<h6>(c)</h6>" + "</li>";
                    answerOptions.push("c")
                } if (keys[j] == 'ans4') {
                    htmlAnswer += "<li>" + "<p>" + (i + 1) + "</p>" + "<h6>(d)</h6>" + "</li>";
                    answerOptions.push("d")
                } if (keys[j] == 'ans5') {
                    htmlAnswer += "<li>" + "<p>" + (i + 1) + "</p>" + "<h6>(e)</h6>" + "</li>";
                    answerOptions.push("e")
                }
            }
            if (cont[i].answerDescription) {
                appendAnswers = true;
                htmlDescription += "<li>" + "<p>" + (i + 1) + "</p>" + "<h6>"
                answerOptions. forEach(function (item, index) {
                    htmlDescription+= "("+item+")"
                })
                htmlDescription+="</h6>" + "<p>" + cont[i].answerDescription + "</p>" + "</li>";
            }



        }
        htmlStr += "</div>";
        console.log(htmlStr)
        z0 = document.createElement('div');
        z0.classList.add("maincontainer");
        var z = document.createElement('div');
        z.classList.add("container");
        z.classList.add("mcqRead");
        z.id = "parent";
        z0.appendChild(z)
        var z1 = document.createElement('div');
        z1.classList.add("row");
        z.appendChild(z1);
        var z2 = document.createElement('div');
        z2.classList.add("col-12");
        z2.classList.add("mt-2")
        z1.appendChild(z2)
        z2.innerHTML += "<h4>Read Quiz</h4>"
        var z3 = document.createElement('div');
        z3.classList.add("inner-ques");
        z3.id = "getdata"
        z2.appendChild(z3)
        var z4 = document.createElement('div');
        z4.innerHTML = htmlStr;
        z3.appendChild(z4);
        var z5 = document.createElement('div');
        z5.classList.add("col-12");
        z5.classList.add("mt-4")
        z5.classList.add("answers-liat")
        z5.innerHTML += "<h4>Quiz Answers</h4>"
        z1.appendChild(z5)
        var z6 = document.createElement('div');
        z6.classList.add("answer-block");
        z6.id = "answer-list"
        var ans = document.createElement('ul');
        ans.innerHTML = htmlAnswer;
        z6.appendChild(ans);
        z5.appendChild(z6);
        if (appendAnswers == true) {
            var z7 = document.createElement('div');
            z7.classList.add("col-12");
            z7.classList.add("answer-description-block")
            z7.innerHTML += "<h4>Answer with explanation</h4>"
            z1.appendChild(z7)
            var z8 = document.createElement('div');
            z8.classList.add("description");
            z8.id = "description"
            z7.appendChild(z8)
            var dis = document.createElement('ul');
            dis.innerHTML = htmlDescription;
            z8.appendChild(dis);
        }

        optionVAlidation()
    }
    function optionVAlidation(){
        var listItem = document.querySelectorAll("#getdata li");
        for(var i=0 ; i<listItem.length ; i++){
            if(listItem[i] == "" || listItem[i] == "undefined" || listItem[i] == null ){
                listItem[i].style.display = "none";
            }
        }
        formSubmit();
    }
    var z0;
    var checkDirection="";
    var quizMode='${params.quizMode}';
    var quizNameRead= '${params.quizName}';
    $.noConflict();
    jQuery(document).ready(function ($) {
        $("#testStartDate").appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "minuteInterval": 5,
            "closeButton": true
        });

         if( "${params.quizMode}"=="read") {
             var quizId= "${params.quizId}"
             var resId= "${params.resId}"
            $("#subType").val("lesson").change();
             getQuestionAnswers(quizId,resId);
         }

    });
    function getQuestionAnswers(quizId,resId){
        $('.loading-icon').removeClass('hidden');
        $('.que-side-menu .tab').hide();
        <g:remoteFunction controller="funlearn" action="newQuizQA" params="'quizId='+quizId+'&resId='+resId" onSuccess = "initializeQuizDataWithAnswers(data);"/>
    }
</script>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
</div>




<script>
    var serverPath = "${request.contextPath}";
    var mode = "${params.mode}";
    var gradeid = "${params.gradeid}";
    var page = "${params.page}";
    var resourceType = "${params.resourceType}";
    var chapterId = "${params.chapterId}";
	var bookId = "${params.bookId}";

    if(quizMode!="read") {
        CKEDITOR.replace('notes', {
            height: 600,
            customConfig: '/assets/ckeditor/customConfig.js',
            extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog,autolink',
            mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',

            // Upload images to a CKFinder connector (note that the response type is set to JSON).
            uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode=' + mode + "&chapterId=" + chapterId + '&bookId=' + bookId + '&resourceType=' + resourceType + '&page=' + page,

            // Configure your file manager integration. This example uses CKFinder 3 for PHP.
            filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode=' + mode + "&chapterId=" + chapterId + '&bookId=' + bookId + '&resourceType=' + resourceType + '&page=' + page,
            filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode=' + mode + "&chapterId=" + chapterId + '&bookId=' + bookId + '&resourceType=' + resourceType + '&page=' + page,

            // The following options are not necessary and are used here for presentation purposes only.
            // They configure the Styles drop-down list and widgets to use classes.

            stylesSet: [
                {name: 'Narrow image', type: 'widget', widget: 'image', attributes: {'class': 'image-narrow'}},
                {name: 'Wide image', type: 'widget', widget: 'image', attributes: {'class': 'image-wide'}}
            ],

            // Load the default contents.css file plus customizations for this sample.
            contentsCss: [CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css', 'http://fonts.googleapis.com/css?family=Merriweather'],
            //contentsCss: [ CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css','http://fonts.googleapis.com/css?family=Merriweather','http://fonts.googleapis.com/css?family=MerriweatherSans'],


            // Configure the Enhanced Image plugin to use classes instead of styles and to disable the
            // resizer (because image size is controlled by widget styles or the image takes maximum
            // 100% of the editor width).
            image2_alignClasses: ['image-align-left', 'image-align-center', 'image-align-right'],
        });
    }
    if ( CKEDITOR.env.ie && CKEDITOR.env.version == 8 ) {
        document.getElementById( 'ie8-warning' ).className = 'tip alert';
    }

    CKEDITOR.on( 'dialogDefinition', function( ev ) {
        var dialogName = ev.data.name;
        var dialogDefinition = ev.data.definition;
        if ( dialogName == 'link' ) {
            var targetTab = dialogDefinition.getContents( 'target' );
            var targetField = targetTab.get( 'linkTargetType' );
            targetField[ 'default' ] = '_blank';
        }
    });

    var resourceDtlId,htmlId;
<%  if("edit".equals(params.mode)){ %>
	getHtmlsData(${params.id});
	resourceDtlId = ${params.id};
<%  } %>


        
    function formSubmit() {


        if( quizMode!="read" ) {
            document.addhtml.notes.value=CKEDITOR.instances.notes.getData();
            if(validate()){
            document.addhtml.mode.value=mode;
            document.addhtml.resourceType.value=resourceType;
            document.addhtml.page.value=page;
            document.addhtml.resourceDtlId.value=resourceDtlId;
            document.addhtml.htmlId.value='<%="edit".equals(params.mode)?params.id:session['htmlId']%>';


            <%  if("notes".equals(params.page)){ %>

            document.addhtml.chapterId.value=chapterId;
            document.addhtml.bookId.value=bookId;
            <%  } else { %>
            document.addhtml.gradeid.value=gradeid;

            <%  } %>
            document.addhtml.submit();
            }
        }
        else if(quizMode=="read"){
            document.addhtml.resourceName.value=quizNameRead?quizNameRead+"-read":'';
            document.addhtml.notes.value=z0.innerHTML?z0.innerHTML:'';
            document.addhtml.mode.value=mode;
            document.addhtml.resourceType.value=resourceType;
            document.addhtml.page.value=page;
            document.addhtml.resourceDtlId.value=resourceDtlId;
            document.addhtml.htmlId.value='<%="edit".equals(params.mode)?params.id:session['htmlId']%>';


            <%  if("notes".equals(params.page)){ %>

            document.addhtml.chapterId.value=chapterId;
            document.addhtml.bookId.value=bookId;
            <%  } else { %>
            document.addhtml.gradeid.value=gradeid;

            <%  } %>
            if(validate()) {
                document.addhtml.submit();
            }
        }
    }
       
    var flds =  new Array (
        'resourceName',
        'notes'
    );
    
    function validate(){
        var allFilled=true;
        $('.alert').hide();

        for (i=0; i<flds.length; i++) {
			//console.log($("#"+flds[i]).val()+"<--"+$("#"+flds[i]).attr('name'));
            if(!$("#"+flds[i]).val()) {
                $("#"+flds[i]).addClass('has-error');
                $("#"+flds[i]).closest('.form-group').addClass('has-error');
                allFilled = false;
            } else {
                $("#"+flds[i]).removeClass('has-error');
                $("#"+flds[i]).closest('.form-group').removeClass('has-error');
            }
        }
        
        if(!allFilled){
            $('.alert').show();
        }
    
        return allFilled;
    }
    
    var resSubFileNm="";    
	
	function getHtmlsData(resId){
        <g:remoteFunction controller="funlearn" action="getHtmlsFile"  onSuccess="displayHtmls(data);" params="'resId='+resId+'&mode=edit'" />
    }




	function displayHtmls(data){
	    var resLink="${resLink}";
	    var filename="${filename}";
	      var extraPath = "/OEBPS";
        var isZipBook = false;

        var replaceStr = "/funlearn/downloadEpubImage"+
            "?source="+resLink.substring(0,resLink.lastIndexOf('/')+ 1)+"extract"+
            (isZipBook?"":extraPath)+"/"+resSubFileNm.substring(0,resSubFileNm.lastIndexOf("/")+1);
        console.log(replaceStr);
    	var htmls = data;
        htmls =  replaceAll(htmls,"src=\"", "src=\"" +replaceStr);
        htmls =  replaceAll(htmls,"../Styles/", replaceStr+"../Styles/");
        //htmls = htmls.replace("</head>"," <link rel=\"stylesheet\" href=\"/assets/wonderslate-material-design.css\" /></head>");
        if(isZipBook) htmls = replaceAll(htmls,"data=\"", "data=\"" +replaceStr );
        htmls = replaceAll(htmls,"src: url(\"", "src: url(\"" +replaceStr);
        htmls = replaceAll(htmls,"background-image: url('", "background-image: url('" +replaceStr);
        htmls = htmls.replace(/\\\\/g , '\\');
        htmls = htmls.replace(/\\mathring\ *\{\ *([A-Za-z])\ *\}\ */g , '\\overset\{\\ \\ \\circ\}\{$1\}');
        document.addhtml.resourceName.value=htmlDecode("${resourceName}");
        <% if(params.page=="currentaffairs"){%>
        document.addhtml.testStartDate.value="${testStartDate}";
        <%}%>
        CKEDITOR.instances.notes.setData(htmls);
        <%if(subType!=null&&!"".equals(subType)&&"books".equals(session['entryController'])){%>
            var selectObj = document.getElementById("subType");

            for (var i = 0; i < selectObj.options.length; i++) {
                if (selectObj.options[i].value== "${subType}") {
                    selectObj.options[i].selected = true;
                    break;
                }
            }

        <%}%>
	}	
</script>
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1){ %>
<asset:javascript src="analytics.js"/>
<% } %>

<script>
    $('.navbar-wonderslate').css({
        'padding' : '0 0 0 10px'
    });
    $('.navbar-right').css({
        'padding-right' : '10px'
    });
    $('.user-profile-dropdown .login-signup-dropdown').css({
        'right' : '-40px'
    });
    $('.search-book').detach();

    function replaceAll(str, find, replace) {
        return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }
    function htmlDecode( html ) {
        var a = document.createElement( 'a' ); a.innerHTML = html;
        return a.textContent;
    };
</script>
</body>
</html>
