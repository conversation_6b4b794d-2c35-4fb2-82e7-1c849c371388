<%
    String syllabusType="";
    String country="";

    if(request.getRequestURL().toString().indexOf("success")>-1) syllabusType="success";
    else syllabusType="school";


    country=session.getAttribute("country")==null?"":session.getAttribute("country");%>
<%if(session.getAttribute("pl")==null||"wonderslate".equals(""+session.getAttribute("pl"))){%>
<footer>
    <div class="container" id="footerid">


        <div class="row">
            <div class="col-md-12 text-center">
                <p class="white">&copy; Wonderslate 2016 - All Rights Reserved</p>
            </div>
        </div>
    </div>
    <div class="lmodal" style="display: none">
        <div class="lcenter">
            <img alt="" src="${assetPath(src: 'loading.gif')}" />
        </div>
    </div>


    <%}else{%>
    <!-- Footer -->
    <!--<footer>-->
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center">

                Powered by <span class="brand"><a href="#"><img alt="brand" src="${assetPath(src: 'logo-ws.png')}">&nbsp;&nbsp; WONDERSLATE</a></span>


            </div>
        </div>
    </div>

</footer>


<%}%>
<asset:javascript src="jquery-1.11.2.min.js"/>
<asset:javascript src="bootstrap.min.js"/>
<sec:ifNotLoggedIn>
    <g:render   template="/creation/register"></g:render>
</sec:ifNotLoggedIn>




<script>
    var serverPath= "${request.contextPath}";

    function showregister(registerType){
        $("#registerModal").modal("show");
        document.getElementById('forgotPassword').style.display = 'none';
        document.getElementById('tandc').style.display = 'none';

        if("login"==registerType) {
            document.getElementById('signup').style.display = 'none';
            document.getElementById('login').style.display = 'block';
            document.getElementById('loginFailed').style.display = 'none';
        }
        else{
            document.getElementById('login').style.display = 'none';
            document.getElementById('signup').style.display = 'block';
        }

    }


    <%if("true".equals(params.loginFailed)){%>
    showregister('login');
    document.getElementById('loginFailed').style.display = 'block';
    <%}%>

    var syllabusType="${syllabusType}";

    var country ="${country}";
    var noOfNotifications=0;
    var messagetype="message";
    function getTopicsMap(mode){
        console.log("coming here and what is happening");
        <g:remoteFunction controller="funlearn" action="topicsMap"  onSuccess='initializeDataIndex(data);'
                params="'country='+country+'&mode='+mode" />
    }
    <%if("true".equals(showDiscover)||"true".equals(homeDiscover)){%>
    getTopicsMap('topic');
    <%}%>

    <sec:ifLoggedIn>
    function checkFriendsRequest(){
        <g:remoteFunction controller="friends" action="getFriendRequests"  onSuccess='updateNotification(data);'
                 />
    }

    var friendsHtml = "";

    function updateNotification(data){

        var friends = data.results;
        var imageStr;
        if(friends.length>0) {

            friendsHtml=" <a href='#'><i class='fa fa-bell-o fa-x'><span class='badge' style='background-color: red' id='notificationCount'>" + data.results.length + "</span></i></a>" +
                    "<div class='dropdown-menu' role='menu'>" ;
            noOfNotifications = friends.length;
            for(var i = 0; i < friends.length; ++i) {
                if("null"==""+friends[i].profilepic) imageStr="&nbsp;<i class='fa fa-user fa-3x'></i>";
                else imageStr = "&nbsp;<img src='/funlearn/showProfileImage?id="+friends[i].friendId+"&fileName="+friends[i].profilepic+"&type=user&imgType=icon' width='50'>";

                friendsHtml +=  "<div class='row'>"+
                        "<div class='col-md-2 vcenter'>"+imageStr+"</div>"+
                        "<div class='col-md-5 vcenter '><b><span class='smallerText'>"+friends[i].name+"</span></b></div>"+
                        "<div class='col-md-5 vcenter ' id='friendoption"+friends[i].friendId+"'><a class='btn btn-success' type='button' href='javascript:friendAccept(1,"+friends[i].friendId+")' style='color: white'>Accept</a>&nbsp;&nbsp;<a class='btn btn-default' type='button' href='javascript:friendAccept(2,"+friends[i].friendId+")'>Ignore</a></div>"+
                        "<hr class='myhrline'>"+
                        "</div>";
            }

            friendsHtml += "</div>";
            document.getElementById("notification").className = "show-on-hover";
            document.getElementById("notification").innerHTML =friendsHtml;

        }
    }

    function friendAccept(accept,friendid){
        <g:remoteFunction controller="friends" action="acceptFriend"  onSuccess='friendStatusChanged(data);'
                params="'friendid='+friendid+'&status='+accept" />
        if("1"==accept)document.getElementById("friendoption"+friendid).innerHTML ="<span class='green'><b>Accepted</b></span>";
        else document.getElementById("friendoption"+friendid).innerHTML ="<span class='green'><b>Ignored</b></span>";
    }

    function friendAcceptMain(accept,friendid){
        <g:remoteFunction controller="friends" action="acceptFriend"  onSuccess='friendStatusChanged(data);'
                params="'friendid='+friendid+'&status='+accept" />
        if("1"==accept)document.getElementById("friendoptionmain"+friendid).innerHTML ="<span class='green'><b>Accepted</b></span>";
        else document.getElementById("friendoptionmain"+friendid).innerHTML ="<span class='green'><b>Ignored</b></span>";
    }

    function friendStatusChanged(data){
        noOfNotifications = noOfNotifications -1;
        if(noOfNotifications==0)
            document.getElementById("notificationCount").innerHTML="";
        else
            document.getElementById("notificationCount").innerHTML=noOfNotifications;
    }

    checkFriendsRequest();

    function getTotalUnreadMessageCount(){
        <g:remoteFunction controller="messaging" action="getTotalUnreadMessageCount"  onSuccess='updateMessageAlert(data);'
             params="'messagetype='+messagetype"  />
    }

    function updateMessageAlert(data){
        var htmlStr="";
        if (document.getElementById('messagenotificationCount') != null) {
            var element = document.getElementById('messageid');
            var notificationCount = document.getElementById('messagenotificationCount');
            element.removeChild(notificationCount);
        }
        if("OK"==data.status) {

            document.getElementById('messageid').innerHTML = document.getElementById('messageid').innerHTML + " " + "<span class='badge' style='background-color: red' id='messagenotificationCount'>" + data.results[0].unreadCount + "</span>";

        }
    }

    getTotalUnreadMessageCount();
    setInterval(getTotalUnreadMessageCount, 20000);
    </sec:ifLoggedIn>

    $(document).ready(function(){
        $('[data-toggle="popover"]').popover();
    });

    function getLocalDate(dateStr){
        var newDateStr = dateStr.substr(0,dateStr.length-18)+" "+dateStr.substr(dateStr.length-5);
        return newDateStr;
    }

    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }

    function setSelectedValue(selectObj, valueToSet) {
        for (var i = 0; i < selectObj.options.length; i++) {
            if (selectObj.options[i].text.replace(/\W+/g, '') == valueToSet.replace(/\W+/g, '')) {
                selectObj.options[i].selected = true;
                return;
            }
        }
    }

    function updateQuizPoints(quizId,correctAnswers) {
        <g:remoteFunction controller="funlearn" action="addQuizPoints"
			params="'quizId='+quizId+'&correctAnswers='+correctAnswers"></g:remoteFunction>
    }

    // checkNewMessages();
</script>

    <%if(showDrift) { %>
    <script>
    !function() {
        var t;
        if (t = window.driftt = window.drift = window.driftt || [], !t.init) return t.invoked ? void (window.console && console.error && console.error("Drift snippet included twice.")) : (t.invoked = !0,
            t.methods = [ "identify", "config", "track", "reset", "debug", "show", "ping", "page", "hide", "off", "on" ],
            t.factory = function(e) {
                return function() {
                    var n;
                    return n = Array.prototype.slice.call(arguments), n.unshift(e), t.push(n), t;
                };
            }, t.methods.forEach(function(e) {
            t[e] = t.factory(e);
        }), t.load = function(t) {
            var e, n, o, i;
            e = 3e5, i = Math.ceil(new Date() / e) * e, o = document.createElement("script"),
                o.type = "text/javascript", o.async = !0, o.crossorigin = "anonymous", o.src = "https://js.driftt.com/include/" + i + "/" + t + ".js",
                n = document.getElementsByTagName("script")[0], n.parentNode.insertBefore(o, n);
        });
    }();
    drift.SNIPPET_VERSION = '0.3.1';
    drift.load('wr8d6bs3a6ce');
</script>
<%}%>
<!-- End of Async Drift Code -->
