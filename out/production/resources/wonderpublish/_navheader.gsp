<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>

<!doctype html>
<html lang="en" >
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>${title}</title>
    <meta name="keywords" content="${keywords}">
    <meta name="description" content="Wonderslate is an effort to bring syllabus specific educational content to all.Engaging parents, teachers and students to create and share content. Be it mind maps, videos , quiz, solved question paper etc. CBSE,ICSE,state boards etc.">
    <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1, minimum-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <link rel="icon" sizes="192x192" href="${assetPath(src: 'app-icon-4x.png')}">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" href="${assetPath(src: 'favicon.ico')}" type="image/x-icon" />

    <link href='https://fonts.googleapis.com/css?family=Exo' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Roboto Slab:300,400,700' rel='stylesheet' type='text/css'>
    <link href="https://fonts.googleapis.com/css?family=Merriweather:700|Rubik:400,500" rel="stylesheet" type='text/css'>
    <asset:stylesheet href="hopscotch-0.1.1.css"/>
    <asset:stylesheet href="/bootstrap3.3.7/bootstrap.css"/>
    <asset:stylesheet href="flat-ui.css"/>
    <asset:stylesheet href="demo.css"/>
    <asset:stylesheet href="style.css"/>
    <asset:stylesheet href="font-awesome.min.css"/>
    <asset:stylesheet href="clock.css"/>
    <ckeditor:resources />
</head>
<%
    String url = (request.getRequestURL()).toString();
    String serverUrl = url.substring(0,url.indexOf('/',9));

%>

<style>
.modal.modal-wide .modal-dialog {
    width: 90%;

}
.show-on-hover:hover > div.dropdown-menu {
    display: block;
    width: 500px !important;
}

</style>
<body>

<nav class="navbar navbar-default" style="background-color: white"><br>
    <div class="container-fluid navbarfirstline">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#myNavbar">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <sec:ifNotLoggedIn>
                <span class="brand">&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/books/index"><img alt="brand" src="${assetPath(src: 'logo-ws.png')}" id="addingcontent2">PUBLISHER NAME</a></span>
            </sec:ifNotLoggedIn>
            <sec:ifLoggedIn>
                <%if(session.getAttribute("pl")==null||"wonderslate".equals(""+session.getAttribute("pl"))){%>
                <span class="brand">&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/wonderpublish/demoHome"><img alt="brand" src="${assetPath(src: 'logo-ws.png')}">PUBLISHER NAME</a></span>
                <%}else{%>
                <span>&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/books/index"><img alt="brand" src="${assetPath(src: 'privatelabels/logo.png')}" height="40"></a></span>
                <%}%>

            </sec:ifLoggedIn>

        </div>
        <div class="collapse navbar-collapse" id="myNavbar">
            <div class="row row-right">

                <ul class="nav navbar-nav navbar-right top-nav">
                    <sec:ifNotLoggedIn>
                        <li><a href="javascript:showregister('signup');">&nbsp;REGISTER&nbsp;</a></li>
                        <li><a href="javascript:showregister('login');">&nbsp;LOGIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</a></li>
                    </sec:ifNotLoggedIn>
                    <sec:ifLoggedIn>

                        <li><a href="/books/books">MY LIBRARY</a></li>
                        <li id="notification" class="dropdown"><a href="#"><i class="fa fa-bell-o fa-x"></i></a></li>
                        <li><g:link uri="/logoff">&nbsp;LOGOUT&nbsp;&nbsp;&nbsp;&nbsp;</g:link>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</li>
                    </sec:ifLoggedIn>
                </ul>

            </div>
        </div>
    </div>
    <div class="popoverhome" style="display:none"></div>

    <!--<a href='#' id='example2' rel='popover' data-placement='left' data-content='Discover your subject by choosing these options' data-original-title='Find your subject'></a>-->


</nav>
<body>


<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>



