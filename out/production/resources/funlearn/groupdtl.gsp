
<g:render template="navheader"></g:render>
<style>
.image-upload > input
{
    display: none;
}
</style>
<asset:stylesheet href="imageoverlay.css"/>
<%
  def imageStr="${assetPath(src: 'bg-bottom.jpg')}";
    if(!"null".equals(""+group.profilepic)) imageStr="/funlearn/showProfileImage?id=${group.id}&fileName=${group.profilepic}&type=group&imgType=thumbnail";
 %>
<div class="wrapper">
    <div class="container-fluid">
        <div class='row maincontent'>
            <div class='col-md-2 col-md-offset-1 sidebar'>

            </div>
            <div class='col-md-7 col-md-offset-1 main'>
                <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadprofile" id="uploadprofile" action="/creation/uploadprofile" method="post">
                    <input type="hidden" name="type" value="group">
                    <input type="hidden" name="groupId" value="${group.id}">
                    <div class="row">

                    <div class="col-md-3 col-md-offset-1">
                        <div class="row"><div class="col-md-12 image-wrapper overlay-fade-in">
                        <img src="${imageStr}" width="150">
                            <div class="image-overlay-content image-upload">
                                <p><br></p>
                                <div id="filelabel1"><label for="fileoption1"><span class="smallText" style="cursor: pointer;color:white">Update Group Picture</span></label></div>
                                <input id="fileoption1" name="file" type="file"  accept="image/png, image/jpeg, image/gif" onchange="updateProfile();"/>

                            </div></div></div>
                    </div>

                    <div class="col-md-7 smallerText" id="createButton"><h3>${group.name}</h3>
                        Created on <g:formatDate format="dd-MM-yyyy" date="${group.dateCreated}"/><br><br>
                      <%if(addMember){%>  <a class='btn btn-primary' type='button' href="javascript:getNonMembers();">+ Add Members</a><%}%>
                    </div>

                </div>
                </form>
                <ul class="nav nav-tabs">
                    <li class="active"><a data-toggle="tab" href="#home">Activity</a></li>
                    <li><a data-toggle="tab" href="#menu1">Members</a></li>

                </ul>
                <div class="tab-content">
                    <div id="home" class="tab-pane fade in active">
                      <div id="groupActivity"></div>
                    </div>
                    <div id="menu1" class="tab-pane fade">
                        <div class='row'><div class='col-md-offset-1 col-md-11' id='nonmembers' style="display: none;">

                        </div> </div>
                        <div class='row'><div class='col-md-offset-1 col-md-3'>
                            <h4>Members</h4>
                        </div></div>
                        <div class='row'><div class='col-md-offset-1 col-md-11' id='members'>

                        </div></div>
                    </div>
                </div>


                <br>
            </div>
            <div class='col-md-2 col-md-offset-1 sidebar'>

            </div>
        </div>
    </div>
    
    <div class="push"></div> 
 </div>   
<g:render template="footer"></g:render>
<asset:javascript src="moment.min.js"/>
<script>
	//var friendsData;
	var groupId = ${group.id};
    
	function getNonMembers(){
        <g:remoteFunction controller="friends" action="nonMembers"  onSuccess='displayNonMembers(data);' params="'groupId='+groupId"/>
    }

	function getMembers(){
		<g:remoteFunction controller="friends" action="members"  onSuccess='displayMembers(data);' onFailure='gotoGroupPage();' params="'groupId='+groupId"/>
	}
	
	function gotoGroupPage() {
		window.open("/funlearn/groups","_self");
	}

    function displayNonMembers(data){
        var friends = data.results;
        var imageStr;
        var htmlStr="<br> <span class='smallerText'>** Add members by clicking on <a href='#'><i class='fa fa-plus-circle fa-x'></i></span>";
        if(friends.length ==0)  htmlStr="<div class='row'><div class=' col-md-12 '><b><span class='smallerText'>All your friends are already added to the group.</span></b></div></div>";
        for(var i = 0; i < friends.length; ++i) {
            if("null"==""+friends[i].profilepic) imageStr="<a href='/funlearn/profile?id="+friends[i].friendId+"'> <i class='fa fa-user fa-3x'></i></a>";
            else imageStr = "<a href='/funlearn/profile?id="+friends[i].friendId+"'><img src='/funlearn/showProfileImage?id="+friends[i].friendId+"&fileName="+friends[i].profilepic+"&type=user&imgType=icon' width='40'></a>";

            if(i%3 == 0) htmlStr+="<div class='row'><div class='col-md-4'>";
            else htmlStr+="<div class='col-md-4'>";

            htmlStr+= "<div class='row'>" +
                    "<div class='col-md-3 vcenter'>"+imageStr+"</div>"+
                    "<div class='col-md-9 vcenter'><b><span class='smallerText'><a href='/funlearn/profile?id="+friends[i].friendId+"'> "+friends[i].name+"</a></span></b>"+
                    "&nbsp;&nbsp;<span id='friendoption" + friends[i].friendId + "'><a href='javascript:addMember("+friends[i].friendId+")'><i class='fa fa-plus-circle fa-1x'></i></a></span></div>"+
                    "</div></div>";
            if(i%3 == 2) htmlStr+="</div><hr class='myhrline'>";
        }
		
        //if the no items is not divisible by 3
        if(friends.length%3 != 0) htmlStr+="</div><hr class='myhrline'>";
        $('.nav-tabs a[href="#menu1"]').tab('show');
        document.getElementById('nonmembers').innerHTML= htmlStr;
        $("#nonmembers").toggle(1000);
    }

	function displayMembers(data){
		var friends = data.results;
		var imageStr;
		var htmlStr="";
		if(friends.length ==0)  htmlStr="<div class='row'><div class=' col-md-12 '><b><span class='smallerText'>All your friends are already added to the group.</span></b></div></div>";     
	 
		for(var i = 0; i < friends.length; ++i) {	
			if("null"==""+friends[i].profilepic) imageStr="<a href='/funlearn/profile?id="+friends[i].friendId+"'> <i class='fa fa-user fa-3x'></i></a>";
			else imageStr = "<a href='/funlearn/profile?id="+friends[i].friendId+"'><img src='/funlearn/showProfileImage?id="+friends[i].friendId+"&fileName="+friends[i].profilepic+"&type=user&imgType=icon' width='40'></a>";

			if(i%3 == 0) htmlStr+="<div class='row'><div class='col-md-4'>";
			else htmlStr+="<div class='col-md-4'>";

			htmlStr+="<div class='row'>" +
					"<div class='col-md-3 vcenter'>"+imageStr+"</div>"+
					"<div class='col-md-9 vcenter'><span class='smallerText'> <a href='/funlearn/profile?id="+friends[i].friendId+"'>"+friends[i].name+(friends[i].friendRole==0?" (C)":(friends[i].friendRole==1?" (A)":""))+"</a>"
					
			<% if(addMember) { %> 
			htmlStr+="&nbsp;&nbsp;<span class='dropdown'> <a href='#' alt='Settings' class='dropdown-toggle greytext' id='dropdownMenu1' data-toggle='dropdown' aria-haspopup='true' aria-expanded='true'> <i class='fa fa-cog fa-x'></i></a><ul class='dropdown-menu smallerText greytext' aria-labelledby='dropdownMenu1'><li class='"+(friends.length>1 && friends[i].friendRole==0?'disabled':'')+"'><a href='javascript:removeMember("+(friends.length>1 && friends[i].friendRole==0?-99:friends[i].friendId)+");'>"+(friends[i].friendId!='<%=session['userdetails'].id%>'?"Remove from group":"Leave the group")+"</a></li><li class='"+(friends[i].friendRole!=2?'disabled':'')+"'><a href='javascript:makeAdmin("+(friends[i].friendRole!=2?-99:friends[i].friendId)+");'>"+(friends[i].friendId!='<%=session['userdetails'].id%>'?"Make group admin":"Become group admin")+"</a></li></ul></span>"
			<% } else { %> 
			htmlStr+="&nbsp;&nbsp;<span class='dropdown'> <a href='#' alt='Settings' class='dropdown-toggle greytext' id='dropdownMenu1' data-toggle='dropdown' aria-haspopup='true' aria-expanded='true'> <i class='fa fa-cog fa-x'></i></a><ul class='dropdown-menu smallerText greytext' aria-labelledby='dropdownMenu1'><li class='"+(friends[i].friendId!='<%=session['userdetails'].id%>'?'disabled':'')+"'><a href='javascript:removeMember("+(friends[i].friendId!='<%=session['userdetails'].id%>'?-99:friends[i].friendId)+");'>"+(friends[i].friendId!='<%=session['userdetails'].id%>'?"Remove from group":"Leave the group")+"</a></li></ul></span>"			
			<% } %> 			
			
			htmlStr+="</span></div></div></div>";
			if(i%3 == 2) htmlStr+="</div><hr class='myhrline'>";
		}
     
		//if the no items is not divisible by 3
		if(friends.length%3!= 0) htmlStr+="</div><hr class='myhrline'>";

		document.getElementById('members').innerHTML= htmlStr;
	}

	function addMember(friendId){
		<g:remoteFunction controller="friends" action="addMember"  onSuccess='memberAdded(data);' params="'groupId='+groupId+'&friendId='+friendId"/>
	}

	function memberAdded(data){
		document.getElementById("friendoption"+data.friendId).innerHTML = "<i class='fa fa-check fa-x'></i>";
	}
	
	function removeMember(friendId) {
		if(friendId!=-99) 
			<g:remoteFunction controller="friends" action="removeMember"  onSuccess='getMembers();' params="'groupId='+groupId+'&friendId='+friendId"/>
	}
	
	function makeAdmin(friendId) {
		if(friendId!=-99)
			<g:remoteFunction controller="friends" action="makeAdmin"  onSuccess='getMembers();' params="'groupId='+groupId+'&friendId='+friendId"/>
	}	
	
	getMembers();

	function  getGroupActivity(){
		<g:remoteFunction controller="friends" action="groupActivity"  onSuccess='displayGroupActivity(data);' params="'id='+groupId"></g:remoteFunction>
	}

	function displayGroupActivity(data){
		if(data.results.length ==0)  document.getElementById('groupActivity').innerHTML="<div class='row'><div class='col-md-offset-1  col-md-11 '><b><span class='smallerText'>No activities to display</span></b></div></div>";
		else{
			var groups = data.results;
			var imageStr;
			var htmlStr="";
			for(var i = 0; i < groups.length; ++i) {
				var date = new Date(moment(groups[i].dateCreated));
				if("null"==""+groups[i].profilepic) imageStr=" <i class='fa fa-user fa-3x'></i>";
				else imageStr = "<img src='/funlearn/showProfileImage?id="+groups[i].userId+"&fileName="+groups[i].profilepic+"&type=user&imgType=icon' width='40'></a>";
				htmlStr+= "<div class='row'>" +
						"<div class='col-md-1 vcenter'>"+imageStr+"</div>"+
						"<div class='col-md-9 vcenter'><span class='smallestText greytext'> "+getLocalDate(date.toString())+"</span><br>" +
						"<b><span class='smallerText'><a href='/funlearn/profile?id="+groups[i].userId+"'> "+groups[i].name+"</a> shared</span></b>"+
						"&nbsp;&nbsp;"+
						"</div></div><br>" +
						"<div class='row'><div class='col-md-10 col-md-offset-1 smallerText'><a href='/funlearn/topic/"+groups[i].topicId+"/"+groups[i].topicName+"/"+groups[i].resourceId+"'>"+groups[i].resourceName+"</a> ("+groups[i].resType+")  of "+ groups[i].topicName
						+"</div></div><hr class='myhrline'><br> ";
			}
         
			document.getElementById('groupActivity').innerHTML= htmlStr;
		}
	}

	getGroupActivity();

    function updateProfile(){
        document.uploadprofile.submit();
    }
	

</script>
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>
<asset:javascript src="analytics.js"/>
<% }%>
</body>
</html>