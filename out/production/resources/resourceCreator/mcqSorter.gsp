<!DOCTYPE html>
<g:render template="/wonderpublish/loginChecker"></g:render>

<g:render template="/${session['entryController']}/navheader_new"></g:render>


<asset:stylesheet href="imageoverlay.css"/>
<asset:stylesheet href="bootstrap-select.css"/>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>

<link href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css" rel="stylesheet">
<div class="row">
    <div class="col-md-12 text-center">
        <h3>${resourceDtl.resourceName}</h3>
    </div>
</div>

    <div class="container lightblue_bg border rounded my-5">

<div class="list-chapters" id="chaptersList">
<%for(int i=0;i<mcqs.size();i++){%>
    <div id="${mcqs[i].id}" class="d-flex justify-content-between p-2">
        ${(i+1)}. ${mcqs[i].question}


    </div>
    <%}%>

</div>

        <div class="d-flex my-2 justify-content-center">
           
            <button class="btn btn-sm btn-primary" onclick="updateSortOrder();">Update Sort Order</button>
        
        </div>
</div>

<div class="loading-icon hidden">
   <div class="loader-wrapper">
       <div class="loader">Loading</div>
   </div>
</div>

<script src="/assets/katex.min.js"></script>
<script src="/assets/auto-render.min.js"></script>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<asset:javascript src="bootstrap-select.js"/>
<asset:javascript src="generic.js"/>
<asset:javascript src="topic.js"/>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    $(function() {
        $( "#chaptersList" ).sortable();
        $( "#chaptersList" ).disableSelection();
    });
    function updateSortOrder(){
        var itemOrder = $('#chaptersList').sortable("toArray");
        var mcqSortIds="";
        for (var i = 0; i < itemOrder.length; i++) {
            mcqSortIds += itemOrder[i]+",";
        }
        mcqSortIds=mcqSortIds.substring(0,(mcqSortIds.length-1));
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="resourceCreator" action="updateMCQsSortId" params="'mcqSortIds='+mcqSortIds+'&resId=${params.resId}&bookId=${params.bookId}'" onSuccess="uploadMCQsSort(data)"></g:remoteFunction>
    }

    function uploadMCQsSort(data){
        if(data.status=="success") window.location.reload();
    }

    $(document).ready(function(){

        renderMathInElement(document.body);
    });
</script>
