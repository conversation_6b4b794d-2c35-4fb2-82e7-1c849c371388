<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<style>


.userProfileContent{
    background: transparent !important;
    box-shadow: 0 3px 10px 0 rgba( 31, 38, 135, 0.37 ) !important;
    backdrop-filter: blur( 2px ) !important;
    -webkit-backdrop-filter: blur( 2px ) !important;
    border-radius: 10px !important;
    border: 1px solid rgba( 255, 255, 255, 0.18) !important;
    max-width: 500px;
}

label{
    color: #fff;
}
.userProfile{
    min-height: 70vh;
}
.updateBtn{
    background: #E83500;
    color: #fff;
}

.profile-wrapper{
    width: fit-content;
}
.profile-wrapper img{
    object-fit: cover;
}
.profile-wrapper:hover{
    position: relative;
}
.edit-img:hover{
    cursor: pointer;
}

.profile-wrapper:hover .edit-img{
    background: rgba(255,255,255,0.5);
    opacity: 0.4;
    transition: all 0.5s ease;
}
.camicon{
    display: none;
}
.profile-wrapper:hover .camicon{
    display: flex;
    position: absolute;
    top: 60px;
    left: 72px !important;
    transition: all 0.5s ease;
    font-size: 2rem;
    z-index: 999999;
}

@media only screen and (min-device-width: 320px) and (max-device-width: 480px) {
    .user_profile {
        margin-top: 0;
    }
}
</style>
<section class="userProfile p-1 p-md-4" id="editProfile">
    <div class="container d-flex justify-content-center">
        <div class="row userProfileContent p-3 mx-auto">
            <div class="profileImage col col-12 col-md-12 d-flex justify-content-center">
                <form class="update-user" enctype="multipart/form-data" role="form" name="uploadProfileImage" id="uploadProfileImage" action="/creation/uploadProfileImage" method="post">
                    <div class="media d-block d-sm-flex">
                        <input type="hidden" name="type" value="user">
                        <input type="hidden" name="source" value="userProfile">
                        <input type="hidden" name="sourceController" value="creation">
                        <div class="profile-wrapper text-center">
                            <%if(session['userdetails'].profilepic!=null){%>
                            <label for="fileoption1">
                                <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" style="width: 140px;height:140px;border: 2px solid #fff" class="img-responsive align-self-start mr-1 rounded-circle edit-img">
                            </label>
                            <%}else {%>
                            <label for="fileoption1">
                                <img src="${assetPath(src: 'wonderslate/puser.svg')}" class="img-responsive align-self-start mr-1 rounded-circle edit-img" alt="" style="width: 140px;border: 2px solid #fff">
                            </label>
                            <%}%>
                            <input id="fileoption1" class="d-none" name="file" type="file" accept="image/png, image/jpeg, image/gif ,image/svg" onchange="updateProfile();"/>
                            <a href="javascript:updateImage();" class="rounded-circle edit-btn" style="bottom:75px;color: #fff"><i class="fa-solid fa-camera camicon"></i></a>
                            <p id="file-error" style="font-size: 12px;
                            margin-top: 0.5rem;
                            text-align: center;">Please Upload Image below 2mb</p>
                        </div>
                    </div>
                </form>
            </div>
            <div class="profileInformation col col-12 col-md-12">
                <g:form name="userProfile" url="[action:'updateUserProfile',controller:'creation']" method="post">
                    <div class="col">
                        <label for="name">Name</label>
                        <input class="form-control" type="text" id="name" maxlength="30" name="name" onkeypress="return alpha(event)"  onkeydown=" return valid(event)" value="<%= session["userdetails"]!=null?session["userdetails"].name:"" %>">
                    </div>
                    <div class="col mt-4">
                        <label for="mobile">Mobile Number</label>
                        <input class="input-field input-field-login mobile-input form-control" type="number" name="mobile" oninput="numberOnly(this.id);" id="mobile"
                               value="<%= session["userdetails"]!=null?session["userdetails"].mobile:""%>" required maxlength="10" minlength="10" disabled
                            <%=(grailsApplication.config.grails.appServer.siteName=="e-Utkarsh" && session["userdetails"]!=null && session["userdetails"].mobile!=null)?"disabled":""%>>
                    </div>

                    <div class="col mt-4">
                        <label for="stateSelect">State</label>
                        <select name="state" id="stateSelect" class="form-control" onchange="stateValidate()">
                            <option value="" selected="selected">Select State</option>
                        </select>
                    </div>
                    <div class="col mt-4">
                        <label for="districtSelect">District</label>
                        <select name="district" id="districtSelect" class="form-control">
                            <option value="" selected="selected">Select District</option>
                        </select>
                    </div>
                    <div class="col mt-4">
                        <button id="updateButton" onclick="javascript:formSubmit();" class="btn btn-lg btn-block continue mt-2 updateBtn" disabled>Update</button>
                        <input type="hidden" name="mode" value="update">
                    </div>
                </g:form>
            </div>
        </div>
    </div>
</section>



%{--<g:render template="/creation/changePasswordModal"></g:render>--}%
<g:render template="/wonderpublish/bookReviewModal"></g:render>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<asset:javascript src="moment.min.js"/>
<asset:javascript src="moment-timezone.js"/>
<asset:javascript src="moment-timezone-utils.js"/>

<script>
    function alpha(e) {
        var k;
        document.all ? k = e.keyCode : k = e.which;
        return ((k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || k == 32 || (k >= 48 && k <= 57));
    }

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }
    function valid(e){
        var length = e.target.value.length;
       if (length>0){
           $('#updateButton').removeAttr('disabled');
       }
    }

</script>
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script>
    var stateObject = {
        "Andhra Pradesh": [
            "Anantapur",
            "Chittoor",
            "East Godavari",
            "Guntur",
            "Krishna",
            "Kurnool",
            "Nellore",
            "Prakasam",
            "Srikakulam",
            "Visakhapatnam",
            "Vizianagaram",
            "West Godavari",
            "YSR Kadapa"
        ],
        "Arunachal Pradesh": [
            "Tawang",
            "West Kameng",
            "East Kameng",
            "Papum Pare",
            "Kurung Kumey",
            "Kra Daadi",
            "Lower Subansiri",
            "Upper Subansiri",
            "West Siang",
            "East Siang",
            "Siang",
            "Upper Siang",
            "Lower Siang",
            "Lower Dibang Valley",
            "Dibang Valley",
            "Anjaw",
            "Lohit",
            "Namsai",
            "Changlang",
            "Tirap",
            "Longding"
        ],
        "Assam": [
            "Baksa",
            "Barpeta",
            "Biswanath",
            "Bongaigaon",
            "Cachar",
            "Charaideo",
            "Chirang",
            "Darrang",
            "Dhemaji",
            "Dhubri",
            "Dibrugarh",
            "Goalpara",
            "Golaghat",
            "Hailakandi",
            "Hojai",
            "Jorhat",
            "Kamrup Metropolitan",
            "Kamrup",
            "Karbi Anglong",
            "Karimganj",
            "Kokrajhar",
            "Lakhimpur",
            "Majuli",
            "Morigaon",
            "Nagaon",
            "Nalbari",
            "Dima Hasao",
            "Sivasagar",
            "Sonitpur",
            "South Salmara-Mankachar",
            "Tinsukia",
            "Udalguri",
            "West Karbi Anglong"
        ],
        "Bihar": [
            "Araria",
            "Arwal",
            "Aurangabad",
            "Banka",
            "Begusarai",
            "Bhagalpur",
            "Bhojpur",
            "Buxar",
            "Darbhanga",
            "East Champaran (Motihari)",
            "Gaya",
            "Gopalganj",
            "Jamui",
            "Jehanabad",
            "Kaimur (Bhabua)",
            "Katihar",
            "Khagaria",
            "Kishanganj",
            "Lakhisarai",
            "Madhepura",
            "Madhubani",
            "Munger (Monghyr)",
            "Muzaffarpur",
            "Nalanda",
            "Nawada",
            "Patna",
            "Purnia (Purnea)",
            "Rohtas",
            "Saharsa",
            "Samastipur",
            "Saran",
            "Sheikhpura",
            "Sheohar",
            "Sitamarhi",
            "Siwan",
            "Supaul",
            "Vaishali",
            "West Champaran"
        ],
        "Chandigarh (UT)": [
            "Chandigarh"
        ],
        "Chhattisgarh": [
            "Balod",
            "Baloda Bazar",
            "Balrampur",
            "Bastar",
            "Bemetara",
            "Bijapur",
            "Bilaspur",
            "Dantewada (South Bastar)",
            "Dhamtari",
            "Durg",
            "Gariyaband",
            "Janjgir-Champa",
            "Jashpur",
            "Kabirdham (Kawardha)",
            "Kanker (North Bastar)",
            "Kondagaon",
            "Korba",
            "Korea (Koriya)",
            "Mahasamund",
            "Mungeli",
            "Narayanpur",
            "Raigarh",
            "Raipur",
            "Rajnandgaon",
            "Sukma",
            "Surajpur  ",
            "Surguja"
        ],
        "Dadra and Nagar Haveli (UT)": [
            "Dadra & Nagar Haveli"
        ],
        "Daman and Diu (UT)": [
            "Daman",
            "Diu"
        ],
        "Delhi (NCT)": [
            "Central Delhi",
            "East Delhi",
            "New Delhi",
            "North Delhi",
            "North East  Delhi",
            "North West  Delhi",
            "Shahdara",
            "South Delhi",
            "South East Delhi",
            "South West  Delhi",
            "West Delhi"
        ],
        "Goa": [
            "North Goa",
            "South Goa"
        ],
        "Gujarat": [
            "Ahmedabad",
            "Amreli",
            "Anand",
            "Aravalli",
            "Banaskantha (Palanpur)",
            "Bharuch",
            "Bhavnagar",
            "Botad",
            "Chhota Udepur",
            "Dahod",
            "Dangs (Ahwa)",
            "Devbhoomi Dwarka",
            "Gandhinagar",
            "Gir Somnath",
            "Jamnagar",
            "Junagadh",
            "Kachchh",
            "Kheda (Nadiad)",
            "Mahisagar",
            "Mehsana",
            "Morbi",
            "Narmada (Rajpipla)",
            "Navsari",
            "Panchmahal (Godhra)",
            "Patan",
            "Porbandar",
            "Rajkot",
            "Sabarkantha (Himmatnagar)",
            "Surat",
            "Surendranagar",
            "Tapi (Vyara)",
            "Vadodara",
            "Valsad"
        ],
        "Haryana": [
            "Ambala",
            "Bhiwani",
            "Charkhi Dadri",
            "Faridabad",
            "Fatehabad",
            "Gurgaon",
            "Hisar",
            "Jhajjar",
            "Jind",
            "Kaithal",
            "Karnal",
            "Kurukshetra",
            "Mahendragarh",
            "Mewat",
            "Palwal",
            "Panchkula",
            "Panipat",
            "Rewari",
            "Rohtak",
            "Sirsa",
            "Sonipat",
            "Yamunanagar"
        ],
        "Himachal Pradesh": [
            "Bilaspur",
            "Chamba",
            "Hamirpur",
            "Kangra",
            "Kinnaur",
            "Kullu",
            "Lahaul &amp; Spiti",
            "Mandi",
            "Shimla",
            "Sirmaur (Sirmour)",
            "Solan",
            "Una"
        ],
        "Jammu and Kashmir": [
            "Anantnag",
            "Bandipore",
            "Baramulla",
            "Budgam",
            "Doda",
            "Ganderbal",
            "Jammu",
            "Kargil",
            "Kathua",
            "Kishtwar",
            "Kulgam",
            "Kupwara",
            "Leh",
            "Poonch",
            "Pulwama",
            "Rajouri",
            "Ramban",
            "Reasi",
            "Samba",
            "Shopian",
            "Srinagar",
            "Udhampur"
        ],
        "Jharkhand": [
            "Bokaro",
            "Chatra",
            "Deoghar",
            "Dhanbad",
            "Dumka",
            "East Singhbhum",
            "Garhwa",
            "Giridih",
            "Godda",
            "Gumla",
            "Hazaribag",
            "Jamtara",
            "Khunti",
            "Koderma",
            "Latehar",
            "Lohardaga",
            "Pakur",
            "Palamu",
            "Ramgarh",
            "Ranchi",
            "Sahibganj",
            "Seraikela-Kharsawan",
            "Simdega",
            "West Singhbhum"
        ],
        "Karnataka": [
            "Bagalkot",
            "Ballari (Bellary)",
            "Belagavi (Belgaum)",
            "Bengaluru (Bangalore) Rural",
            "Bengaluru (Bangalore) Urban",
            "Bidar",
            "Chamarajanagar",
            "Chikballapur",
            "Chikkamagaluru (Chikmagalur)",
            "Chitradurga",
            "Dakshina Kannada",
            "Davangere",
            "Dharwad",
            "Gadag",
            "Hassan",
            "Haveri",
            "Kalaburagi (Gulbarga)",
            "Kodagu",
            "Kolar",
            "Koppal",
            "Mandya",
            "Mysuru (Mysore)",
            "Raichur",
            "Ramanagara",
            "Shivamogga (Shimoga)",
            "Tumakuru (Tumkur)",
            "Udupi",
            "Uttara Kannada (Karwar)",
            "Vijayapura (Bijapur)",
            "Yadgir"
        ],
        "Kerala": [
            "Alappuzha",
            "Ernakulam",
            "Idukki",
            "Kannur",
            "Kasaragod",
            "Kollam",
            "Kottayam",
            "Kozhikode",
            "Malappuram",
            "Palakkad",
            "Pathanamthitta",
            "Thiruvananthapuram",
            "Thrissur",
            "Wayanad"
        ],
        "Lakshadweep (UT)": [
            "Agatti",
            "Amini",
            "Androth",
            "Bithra",
            "Chethlath",
            "Kavaratti",
            "Kadmath",
            "Kalpeni",
            "Kilthan",
            "Minicoy"
        ],
        "Madhya Pradesh": [
            "Agar Malwa",
            "Alirajpur",
            "Anuppur",
            "Ashoknagar",
            "Balaghat",
            "Barwani",
            "Betul",
            "Bhind",
            "Bhopal",
            "Burhanpur",
            "Chhatarpur",
            "Chhindwara",
            "Damoh",
            "Datia",
            "Dewas",
            "Dhar",
            "Dindori",
            "Guna",
            "Gwalior",
            "Harda",
            "Hoshangabad",
            "Indore",
            "Jabalpur",
            "Jhabua",
            "Katni",
            "Khandwa",
            "Khargone",
            "Mandla",
            "Mandsaur",
            "Morena",
            "Narsinghpur",
            "Neemuch",
            "Panna",
            "Raisen",
            "Rajgarh",
            "Ratlam",
            "Rewa",
            "Sagar",
            "Satna",
            "Sehore",
            "Seoni",
            "Shahdol",
            "Shajapur",
            "Sheopur",
            "Shivpuri",
            "Sidhi",
            "Singrauli",
            "Tikamgarh",
            "Ujjain",
            "Umaria",
            "Vidisha"
        ],
        "Maharashtra": [
            "Ahmednagar",
            "Akola",
            "Amravati",
            "Aurangabad",
            "Beed",
            "Bhandara",
            "Buldhana",
            "Chandrapur",
            "Dhule",
            "Gadchiroli",
            "Gondia",
            "Hingoli",
            "Jalgaon",
            "Jalna",
            "Kolhapur",
            "Latur",
            "Mumbai City",
            "Mumbai Suburban",
            "Nagpur",
            "Nanded",
            "Nandurbar",
            "Nashik",
            "Osmanabad",
            "Palghar",
            "Parbhani",
            "Pune",
            "Raigad",
            "Ratnagiri",
            "Sangli",
            "Satara",
            "Sindhudurg",
            "Solapur",
            "Thane",
            "Wardha",
            "Washim",
            "Yavatmal"
        ],
        "Manipur": [
            "Bishnupur",
            "Chandel",
            "Churachandpur",
            "Imphal East",
            "Imphal West",
            "Jiribam",
            "Kakching",
            "Kamjong",
            "Kangpokpi",
            "Noney",
            "Pherzawl",
            "Senapati",
            "Tamenglong",
            "Tengnoupal",
            "Thoubal",
            "Ukhrul"
        ],
        "Meghalaya": [
            "East Garo Hills",
            "East Jaintia Hills",
            "East Khasi Hills",
            "North Garo Hills",
            "Ri Bhoi",
            "South Garo Hills",
            "South West Garo Hills ",
            "South West Khasi Hills",
            "West Garo Hills",
            "West Jaintia Hills",
            "West Khasi Hills"
        ],
        "Mizoram": [
            "Aizawl",
            "Champhai",
            "Kolasib",
            "Lawngtlai",
            "Lunglei",
            "Mamit",
            "Saiha",
            "Serchhip"
        ],
        "Nagaland": [
            "Dimapur",
            "Kiphire",
            "Kohima",
            "Longleng",
            "Mokokchung",
            "Mon",
            "Peren",
            "Phek",
            "Tuensang",
            "Wokha",
            "Zunheboto"
        ],
        "Odisha": [
            "Angul",
            "Balangir",
            "Balasore",
            "Bargarh",
            "Bhadrak",
            "Boudh",
            "Cuttack",
            "Deogarh",
            "Dhenkanal",
            "Gajapati",
            "Ganjam",
            "Jagatsinghapur",
            "Jajpur",
            "Jharsuguda",
            "Kalahandi",
            "Kandhamal",
            "Kendrapara",
            "Kendujhar (Keonjhar)",
            "Khordha",
            "Koraput",
            "Malkangiri",
            "Mayurbhanj",
            "Nabarangpur",
            "Nayagarh",
            "Nuapada",
            "Puri",
            "Rayagada",
            "Sambalpur",
            "Sonepur",
            "Sundargarh"
        ],
        "Puducherry (UT)": [
            "Karaikal",
            "Mahe",
            "Pondicherry",
            "Yanam"
        ],
        "Punjab": [
            "Amritsar",
            "Barnala",
            "Bathinda",
            "Faridkot",
            "Fatehgarh Sahib",
            "Fazilka",
            "Ferozepur",
            "Gurdaspur",
            "Hoshiarpur",
            "Jalandhar",
            "Kapurthala",
            "Ludhiana",
            "Mansa",
            "Moga",
            "Muktsar",
            "Nawanshahr (Shahid Bhagat Singh Nagar)",
            "Pathankot",
            "Patiala",
            "Rupnagar",
            "Sahibzada Ajit Singh Nagar (Mohali)",
            "Sangrur",
            "Tarn Taran"
        ],
        "Rajasthan": [
            "Ajmer",
            "Alwar",
            "Banswara",
            "Baran",
            "Barmer",
            "Bharatpur",
            "Bhilwara",
            "Bikaner",
            "Bundi",
            "Chittorgarh",
            "Churu",
            "Dausa",
            "Dholpur",
            "Dungarpur",
            "Hanumangarh",
            "Jaipur",
            "Jaisalmer",
            "Jalore",
            "Jhalawar",
            "Jhunjhunu",
            "Jodhpur",
            "Karauli",
            "Kota",
            "Nagaur",
            "Pali",
            "Pratapgarh",
            "Rajsamand",
            "Sawai Madhopur",
            "Sikar",
            "Sirohi",
            "Sri Ganganagar",
            "Tonk",
            "Udaipur"
        ],
        "Sikkim": [
            "East Sikkim",
            "North Sikkim",
            "South Sikkim",
            "West Sikkim"
        ],
        "Tamil Nadu": [
            "Ariyalur",
            "Chennai",
            "Coimbatore",
            "Cuddalore",
            "Dharmapuri",
            "Dindigul",
            "Erode",
            "Kanchipuram",
            "Kanyakumari",
            "Karur",
            "Krishnagiri",
            "Madurai",
            "Nagapattinam",
            "Namakkal",
            "Nilgiris",
            "Perambalur",
            "Pudukkottai",
            "Ramanathapuram",
            "Salem",
            "Sivaganga",
            "Thanjavur",
            "Theni",
            "Thoothukudi (Tuticorin)",
            "Tiruchirappalli",
            "Tirunelveli",
            "Tiruppur",
            "Tiruvallur",
            "Tiruvannamalai",
            "Tiruvarur",
            "Vellore",
            "Viluppuram",
            "Virudhunagar"
        ],
        "Telangana": [
            "Adilabad",
            "Bhadradri Kothagudem",
            "Hyderabad",
            "Jagtial",
            "Jangaon",
            "Jayashankar Bhoopalpally",
            "Jogulamba Gadwal",
            "Kamareddy",
            "Karimnagar",
            "Khammam",
            "Komaram Bheem Asifabad",
            "Mahabubabad",
            "Mahabubnagar",
            "Mancherial",
            "Medak",
            "Medchal",
            "Nagarkurnool",
            "Nalgonda",
            "Nirmal",
            "Nizamabad",
            "Peddapalli",
            "Rajanna Sircilla",
            "Rangareddy",
            "Sangareddy",
            "Siddipet",
            "Suryapet",
            "Vikarabad",
            "Wanaparthy",
            "Warangal (Rural)",
            "Warangal (Urban)",
            "Yadadri Bhuvanagiri"
        ],
        "Tripura": [
            "Dhalai",
            "Gomati",
            "Khowai",
            "North Tripura",
            "Sepahijala",
            "South Tripura",
            "Unakoti",
            "West Tripura"
        ],
        "Uttarakhand": [
            "Almora",
            "Bageshwar",
            "Chamoli",
            "Champawat",
            "Dehradun",
            "Haridwar",
            "Nainital",
            "Pauri Garhwal",
            "Pithoragarh",
            "Rudraprayag",
            "Tehri Garhwal",
            "Udham Singh Nagar",
            "Uttarkashi"
        ],
        "Uttar Pradesh": [
            "Agra",
            "Aligarh",
            "Allahabad",
            "Ambedkar Nagar",
            "Amethi (Chatrapati Sahuji Mahraj Nagar)",
            "Amroha (J.P. Nagar)",
            "Auraiya",
            "Azamgarh",
            "Baghpat",
            "Bahraich",
            "Ballia",
            "Balrampur",
            "Banda",
            "Barabanki",
            "Bareilly",
            "Basti",
            "Bhadohi",
            "Bijnor",
            "Budaun",
            "Bulandshahr",
            "Chandauli",
            "Chitrakoot",
            "Deoria",
            "Etah",
            "Etawah",
            "Faizabad",
            "Farrukhabad",
            "Fatehpur",
            "Firozabad",
            "Gautam Buddha Nagar",
            "Ghaziabad",
            "Ghazipur",
            "Gonda",
            "Gorakhpur",
            "Hamirpur",
            "Hapur (Panchsheel Nagar)",
            "Hardoi",
            "Hathras",
            "Jalaun",
            "Jaunpur",
            "Jhansi",
            "Kannauj",
            "Kanpur Dehat",
            "Kanpur Nagar",
            "Kanshiram Nagar (Kasganj)",
            "Kaushambi",
            "Kushinagar (Padrauna)",
            "Lakhimpur - Kheri",
            "Lalitpur",
            "Lucknow",
            "Maharajganj",
            "Mahoba",
            "Mainpuri",
            "Mathura",
            "Mau",
            "Meerut",
            "Mirzapur",
            "Moradabad",
            "Muzaffarnagar",
            "Pilibhit",
            "Pratapgarh",
            "RaeBareli",
            "Rampur",
            "Saharanpur",
            "Sambhal (Bhim Nagar)",
            "Sant Kabir Nagar",
            "Shahjahanpur",
            "Shamali (Prabuddh Nagar)",
            "Shravasti",
            "Siddharth Nagar",
            "Sitapur",
            "Sonbhadra",
            "Sultanpur",
            "Unnao",
            "Varanasi"
        ],
        "West Bengal": [
            "Alipurduar",
            "Bankura",
            "Birbhum",
            "Burdwan (Bardhaman)",
            "Cooch Behar",
            "Dakshin Dinajpur (South Dinajpur)",
            "Darjeeling",
            "Hooghly",
            "Howrah",
            "Jalpaiguri",
            "Kalimpong",
            "Kolkata",
            "Malda",
            "Murshidabad",
            "Nadia",
            "North 24 Parganas",
            "Paschim Medinipur (West Medinipur)",
            "Purba Medinipur (East Medinipur)",
            "Purulia",
            "South 24 Parganas",
            "Uttar Dinajpur (North Dinajpur)"
        ]

    }

    // Change State and District Function


    var stateSel = document.getElementById("stateSelect"),
        districtSel = document.getElementById("districtSelect");
    for (var state in stateObject) {
        stateSel.options[stateSel.options.length] = new Option(state, state);
    }
    stateSel.onchange = function () {
        var stateValLength = stateSel.value.length;
        var districtValLength = districtSel.value.length;
        if (stateValLength>0 ){
            $('#updateButton').removeAttr('disabled');
        }else{
            $('#updateButton').attr('disabled','disabled')
        }

        if (districtValLength==0){
            $('#updateButton').attr('disabled','disabled');
        }else if (districtValLength>0){
            $('#updateButton').removeAttr('disabled');
        }
        districtSel.length = 1; // remove all options bar first
        if (this.selectedIndex < 1) return; // done
        var district = stateObject[stateSel.value];
        for (var i = 0; i < district.length; i++) {
            districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
        }
    }
    // stateSel.onchange(); // reset in case page is reloaded
    stateSel.onload = function () {
        districtSel.length = 1; // remove all options bar first
        if (this.selectedIndex < 1) return; // done
        var district = stateObject[stateSel.value];
        for (var i = 0; i < district.length; i++) {
            districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
        }
    }
    stateSel.onload(); // reset in case page is reloaded
    districtSel.onchange = function (){
        if (districtSel.value.length>0){
            $('#updateButton').removeAttr('disabled');
        }
    }

    $('#stateSelect').val('<%= session["userdetails"].state %>').trigger('change');
    $('#districtSelect').val('<%= session["userdetails"].district %>').trigger('change');

    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
    var siteId="${session["siteId"]}";
    var name="";
    var image="";
    <%com.wonderslate.data.SiteMst siteMst = com.wonderslate.data.SiteMst.findById(session["siteId"])%>

    if(siteId==1) {
        name = "${grailsApplication.config.grails.appServer.siteName}";
        image="https://www.wonderslate.com/assets/wonderslate/logo.svg";
    }else if (siteId==5){
        name = "Blackspine";
        image="/assets/landingpageImages/blackspine_logo.png";
    }else if(siteId==3){
        name = "Arihant Publications";
        image="/assets/arihant/footer-logo.png";
    }


    function openRazorPay(price,title,bookId,type,chaptersId) {
        var options = {
            "key": "${siteMst.razorPayKeyId}",
            "amount": price*100, // 2000 paise = INR 20
            "name": name,
            "description": title+" (Course "+bookId+")",
            "image": image,
            "handler": function (response) {
                $('.loading-icon').removeClass('hidden');
                $(".lmodal").show();
                $('body').removeClass('loaded');
                window.location = "/wonderpublish/purchase?razorpay_payment_id=" + response.razorpay_payment_id + "&bookId="+bookId+"&type="+type+"&chaptersId="+chaptersId;
            },
            "prefill": {
                "name": "<%=(session.getAttribute("userdetails")!=null && session['userdetails'].name!=null)?"${session['userdetails'].name}":""%>",
                "email": "<%=(session.getAttribute("userdetails")!=null && session['userdetails'].email!=null)?"${session['userdetails'].email}":""%>",
                "contact": "<%=(session.getAttribute("userdetails")!=null && session['userdetails'].mobile!=null)?"${session['userdetails'].mobile}":""%>"
            },
            "notes": {
                "bookId":bookId,
                "username":"<%=(session.getAttribute("userdetails")!=null && session['userdetails'].username!=null)?"${session['userdetails'].username}":""%>"
            },
            "theme": {
                "color": "#F37254"
            },
            "readonly": {
                "email": 1,
                "contact": 1
            }
        };
        var rzp1 = new Razorpay(options);
        rzp1.open();
    }


</script>

<script>
    function updateImage() {
        $('#fileoption1').trigger('click');
    }
    function formSubmit() {
        var flds = new Array ('name','syllabus');
        if (genericValidate(flds)) {
            document.userProfile.submit();
        }
    }
    function getFile(filePath) {
        return filePath.substr(filePath.lastIndexOf('\\') + 1).split('.')[0];

    }

    function getoutput() {

    }
    function updateProfile(){
        var oFile = document.getElementById("fileoption1").files[0]; // <input type="file" id="fileUpload" accept=".jpg,.png,.gif,.jpeg"/>

        if(oFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1){
            alert("File Name must not contain any space and special characters \n < > { } # / \\ ? : ! & $ ^ * % ` | + ")
            return
        }

        if (oFile.size > 2097152) // 2 mb for bytes.
        {
            $('#file-error').css('color','red');
            return;
        }
        else {
            document.uploadProfileImage.submit();
        }
    }
    function changeSubmitAction() {
        submitPasswordChange();
    }
    function submitPasswordChange() {
        var password = $("#newpassword").val();
        var oldPassword  ="<%= session['userdetails']!=null?session['userdetails'].password:"" %>";
        if($('#newpassword').val() != $('#cnf-password').val()) {
            $('#cnf-password').addClass('has-error');
            $('.password-error-tooltip').show().addClass('new-password-error').removeClass('empty-input-error');
            $('.password-match-error').html("Password does not match the new password");
            $('.password-match-error').css({'display': 'block'});
        } else {
            $('.password-match-error').css({'display': 'none'});
            $('.password-error-tooltip').hide();
            <g:remoteFunction controller="creation" action="updateMigrateUserPassword"  onSuccess='profileUpdateSuccess(data);'
    params="'oldPassword='+oldPassword+'&password='+password" />
        }
    }

    function profileUpdateSuccess(data) {
        if(data.status=='failed'){
            alert(data.message)
        }
        if(data.status=="success") {
            $('#change-password-modal').modal('show');
            $('#change-password-modal .modal-header .login').hide();
            $('#change-password-modal .modal-content ').attr('data-content', 'success');
            $('.password-success').css({'display' : 'block'});
            setTimeout(function(){
                $('#change-password-modal').modal('hide');
            },1000);
            $('.password-reset-input').val("");
            setTimeout(function(){
                $('#password-change-form').css({
                    'display' : 'block'
                });
                $('.password-success').css({
                    'display' : 'none'
                });
                $('.password-reset-input').val("");
            },1500);
        }
        else {
            $('.password-error-tooltip').show().addClass('empty-input-error');
            $('.password-match-error').html("Old password did not match");
            $('#cnf-password').addClass('has-error').removeClass('is-correct');
        }
    }
    // Reset form data
    function resetPasswordData() {
        $('.password-reset-input').val("");
        $('.password-success').css({'display' : 'none'});
        $('.password-match-error').css({'display' : 'none'});
    }

    var topElement = document.querySelector('.header');
    var myDivHeight= topElement.clientHeight || topElement.offsetHeight  || (topElement.getBoundingClientRect()).height;
    var editProfile = document.querySelector('.userProfile');
    editProfile.style.setProperty('margin-top',myDivHeight+'px');

</script>
<%if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>
<asset:javascript src="analytics.js"/>
<% }%>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.1.1/jquery.min.js"></script>