<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>

<!doctype html>
<html lang="en" >
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>${title}</title>
    <meta name="keywords" content="${keywords}">
    <meta name="description" content="Wonderslate is an effort to bring syllabus specific educational content to all.Engaging parents, teachers and students to create and share content. Be it mind maps, videos , quiz, solved question paper etc. CBSE,ICSE,state boards etc.">
    <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1, minimum-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <link rel="icon" sizes="192x192" href="${assetPath(src: 'app-icon-4x.png')}">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" href="${assetPath(src: 'favicon.ico')}" type="image/x-icon" />
    <link href='https://fonts.googleapis.com/css?family=Roboto:300,700,900' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Exo' rel='stylesheet' type='text/css'>
    <asset:stylesheet href="hopscotch-0.1.1.css"/>
    <asset:stylesheet href="bootstrap.css"/>
    <asset:stylesheet href="flat-ui.css"/>
    <asset:stylesheet href="demo.css"/>
    <asset:stylesheet href="style.css"/>
    <asset:stylesheet href="font-awesome.min.css"/>
    <asset:stylesheet href="clock.css"/>

</head>
<%
    String url = (request.getRequestURL()).toString();
    String serverUrl = url.substring(0,url.indexOf('/',9));

%>

<body>

<nav class="navbar navbar-default navbar-fixed-top" style="background-color: white">
    <div class="container-fluid navbarfirstline">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#myNavbar">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <sec:ifNotLoggedIn>
                <span class="brand">&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/"><img alt="brand" src="${assetPath(src: 'logo-ws.png')}" id="addingcontent2">WONDERSLATE <span class="smallerText greytext">(BETA)</span></a></span>
            </sec:ifNotLoggedIn>
            <sec:ifLoggedIn>
                <%if(session.getAttribute("pl")==null||"wonderslate".equals(""+session.getAttribute("pl"))){%>
                <span class="brand">&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/funlearn/home"><img alt="brand" src="${assetPath(src: 'logo-ws.png')}">WONDERSLATE <span class="smallerText greytext">(BETA)</span></a></span>
                <%}else{%>
                <span>&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/funlearn/home"><img alt="brand" src="${assetPath(src: 'privatelabels/logo.png')}" height="40"></a></span>
                <%}%>

            </sec:ifLoggedIn>

        </div>

    </div>


    <!--<a href='#' id='example2' rel='popover' data-placement='left' data-content='Discover your subject by choosing these options' data-original-title='Find your subject'></a>-->


</nav>
<body>
<div class="container-fluid  top-container">
    <div  class='row maincontent' style="height: 600px;">
        <div class='hidden-xs col-md-2  col-md-offset-1 sidebar'>

        </div>
        <div class='col-md-7 col-md-offset-1 main'>
                <div>


                    <g:form name="welcomeForm" url="[action:'basicProfileUpdate',controller:'creation']"  method="post">
                        <input type="hidden" name="actionType">
                        <div class="row">
                            <div class="col-md-2"></div>
                            <div class="col-md-9">

                                <h3>${session['userdetails'].name}, welcome to Wonderslate.</h3>
                                <p>We are almost there. Kindly provide us the following information to serve you better.</p>
                                <p><b>You are </b></p>
                                <div class="red" style="display: none" id="sex">** Please select Female or Male.</div>
                                <div class="form-group">
                                    <input type="radio" name="sex" value="female">&nbsp;Female&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" name="sex" value="male">&nbsp;Male
                                </div>
                                    <div class="red" style="display: none" id="usertype">** Please select a user type</div>
                                    <div class="form-group">
                                        <input type="checkbox" name="student" >&nbsp;Student&nbsp;&nbsp;&nbsp;&nbsp;<input type="checkbox" name="teacher">&nbsp;Teacher&nbsp;&nbsp;&nbsp;&nbsp;<input type="checkbox" name="parent">&nbsp;Parent

                                    </div>
                                <div class="red" style="display: none" id="level">** Please select the level.</div>
                                <div class="form-group">
                                    <g:select name="syllabusType" from="${com.wonderslate.data.LevelsMst.listOrderBySortBy()}" optionKey="name" optionValue="name"
                                              noSelection="['':'Choose Level']" onchange="javascript:getBoardsForSyllabusType(this.value)" />
                                </div>
                                <div class="red" style="display: none" id="syllabus">** Please select the syllabus.</div>
                                <div class="red" style="display: none" id="grade" >** Please enter the grade.</div>
                                <div class="form-group">
                                   <select name="syllabus" id="syllabusselect"><option>Choose Syllabus</option></select>
                                                &nbsp;<input type="text" name="grade"  size="20" placeholder="Grade/Class">
                                </div>

                                <div class="red" style="display: none" id="qualification" >** Please enter your qualification.</div>
                                <div class="form-group"><input type="text" name="qualification"  size="20" placeholder="Qualification"></div>
                                <div class="red" style="display: none" id="city" >** Please enter your place of residence.</div>
                                <div class="form-group"><input type="text" name="city"  size="30" placeholder="Place(City/Town/Village)"></div>



                                    <br>
                                   <button class="btn  btn-primary " type="button" onclick="javascript:formSubmitWelcome('start')">Let me start using Wonderslate</button><br><br>
                           </div>

                        </div>
                        </g:form>

                </div>
        </div>
        <div class='col-md-2 col-md-offset-1 sidebar'>
        </div>
    </div>
</div>

<g:render template="/funlearn/footer"></g:render>
<script>
    function formSubmitWelcome(actionType){
        var allValid =  true;
        document.getElementById('usertype').style.display = 'none';
        document.getElementById('sex').style.display = 'none';
        document.getElementById('syllabus').style.display = 'none';
        document.getElementById('grade').style.display = 'none';
        document.getElementById('qualification').style.display = 'none';
        document.getElementById('city').style.display = 'none';
        document.getElementById('level').style.display = 'none';
        if(!(document.welcomeForm.student.checked||document.welcomeForm.teacher.checked||document.welcomeForm.parent.checked)){
            document.getElementById('usertype').style.display = 'block';
            allValid =  false;

        }
        if(!(document.welcomeForm.sex[0].checked||document.welcomeForm.sex[1].checked)){
            document.getElementById('sex').style.display = 'block';
            allValid =  false;

        }

        if(document.welcomeForm.student.checked || document.welcomeForm.teacher.checked) {
            if (document.welcomeForm.syllabusType.selectedIndex == 0) {
                document.getElementById('level').style.display = 'block';
                allValid =  false;
            }
                if (document.welcomeForm.syllabus.selectedIndex == 0) {
                    document.getElementById('syllabus').style.display = 'block';
                    allValid =  false;
                }
                if (document.welcomeForm.grade.value == '') {
                    document.getElementById('grade').style.display = 'block';
                    allValid =  false;
                }
        }
        if(document.welcomeForm.parent.checked && document.welcomeForm.qualification.value == ''){
                document.getElementById('qualification').style.display = 'block';
                allValid =  false;
        }
        if (document.welcomeForm.city.value == '') {
            document.getElementById('city').style.display = 'block';
            allValid =  false;
        }



    if(allValid) {
            document.welcomeForm.actionType.value = actionType;
            document.welcomeForm.submit();
        }
    }

    function getBoardsForSyllabusType(level){
        <g:remoteFunction controller="funlearn" action="getBoardsForSyllabusType"  onSuccess='displaySyllabus(data);'
        params="'syllabusType='+level"/>
    }

    function displaySyllabus(data){
        var select = document.getElementById("syllabusselect");
        var tempboards = data.boards;
        var boardsLength = tempboards.length;
        select.options.length = 1;
        // if("selectedSubject"==fieldName) select.options.length = 2;

        for(var i=0; i<boardsLength; i++) {
            var opt = tempboards[i].board;
            var el = document.createElement("option");
            el.textContent = opt;
            el.value = opt;
            select.appendChild(el);
        }

        select.focus();
    }
</script>
