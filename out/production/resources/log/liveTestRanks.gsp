<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<style>

input[type="number"]::-webkit-outer-spin-button, input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}
</style>
<script>
    var loggedIn=false;
</script>

<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main p-4' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <label for="resId" style="display: block;">Resource Id:</label>
                    <div class="form-inline">
                        <input type="hidden" class="form-control" name="duration" id="duration">
                        <input type="hidden" class="form-control" name="resultscore" id="resultscore">
                        <input type="hidden" class="form-control" name="submittime" id="submittime">
                        <input type="number" class="form-control mt-0 mr-3" name="resId" id="resId"  placeholder="Resource Id">
                        <button class="btn btn-primary btn-lg col-2"  onclick="javascript:getUserRankDetails();">Show Test Ranks</button>
                    </div>
                </div>
                <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                <div id="usersrank" class="my-4" style="display: none"></div>
                <div id="successmsg" style="display: none"></div>
                <div style="display: none;" id="download">
                    <div class="form-group">
                        <button type="button" id="download-btn" class="btn btn-primary btn-lg col-2">Download</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="moment.min.js"/>


<script>
    function getUserRankDetails(){
        if(document.getElementById("resId").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the Resource Id"
            $("#usersrank").hide();
            $("#errormsg").show();
            $("#successmsg").hide();
            $('#download').hide();
        }
        else {
            $("#errormsg").hide();
            $("#successmsg").hide();
            $("#usersrank").hide();
            $('.loading-icon').removeClass('hidden');
            var resId = document.getElementById("resId").value;
            <g:remoteFunction controller="wonderpublish" action="getLiveTestRanks" params="'resId='+resId" onSuccess = "showUserRankData(data);"/>
        }

    }

    function showUserRankData(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr= "                    <table class='table table-responsive table-striped table-bordered'>\n" +
            "                        <tr>\n" +
            "                            <th>Username</th>\n" +
            "                            <th>Mobile</th>\n" +
            "                            <th>Rank</th>\n" +
            "                            <th>Score</th>\n" +
            "                            <th>Total Questions</th>\n" +
            "                            <th>Wrong Answers</th>\n" +
            "                            <th>Correct Answers</th>\n" +
            "                            <th>Skipped Questions</th>\n" +
            "                            <th>Test Duration</th>\n" +
            "                            <th>Test Submit Time</th>\n" +
            "                            <th>State</th>\n" +
            "                            <th>District</th>\n" +
            "                        </tr>\n" ;
        if(data.testRanks!=null && data.testRanks!=""){
            $("#errormsg").hide();
            var test = data.testRanks;
            var duration;
            var score;
            var submittime;
            var district;
            for(var i=0;i<test.length;i++) {
                if(test[i].district==null ){
                    district="";
                }else{
                    district=test[i].district;
                }
                htmlStr += "<tr>"+
                    "<td>" + test[i].name + "</td>"+
                    "<td>" + test[i].mobile + "</td>"+
                    "<td>" + test[i].rank + "</td>"+
                    "<td>" +test[i].marks+ "</td>"+
                    "<td>" +test[i].totalquestions+ "</td>"+
                    "<td>" +test[i].wronganswers+ "</td>"+
                    "<td>" +test[i].correctanswers+ "</td>"+
                    "<td>" +test[i].skipped+ "</td>"+
                    "<td>" +test[i].timetaken+ "</td>"+
                "<td>"  +test[i].submittime+"</td>";
                "<td>"  +test[i].state+"</td>";
                "<td>"  +district+"</td>";

                htmlStr +=    "</tr>";
            }
            htmlStr += "</table>";
            document.getElementById("usersrank").innerHTML= htmlStr
            document.getElementById("duration").value= duration
            document.getElementById("resultscore").value= resultscore
            document.getElementById("submittime").value= submittime
            $("#usersrank").show();
            $('#download').show();
        }else if(data.testRanksend!=undefined && data.testRanksend!=""){
            $("#usersrank").hide();
            $('#download').hide();
            document.getElementById("errormsg").innerHTML= "The end date for this test ends at "+ moment.utc(data.testRanksend).local().format("MMM Do YYYY h:mm a")+".Please check after that!";
            $("#errormsg").show();
            $('#download').hide();

        }
        else{
            $('#download').hide();
            $("#errormsg").hide();
            $("#usersrank").hide();
            document.getElementById("errormsg").innerHTML= "No Test found with this Resource Id.";
            $("#errormsg").show();
        }
    }


    function millisecondsToTime(millisec) {
        var seconds = (millisec / 1000).toFixed(0);
        var minutes = Math.floor(seconds / 60);
        var hours = "";
        if (minutes > 59) {
            hours = Math.floor(minutes / 60);
            hours = (hours >= 10) ? hours : "0" + hours;
            minutes = minutes - (hours * 60);
            minutes = (minutes >= 10) ? minutes : "0" + minutes;
        }

        seconds = Math.floor(seconds % 60);
        seconds = (seconds >= 10) ? seconds : "0" + seconds;
        if (hours != "") {
            return hours + ":" + minutes + ":" + seconds+ " (Hours : Minutes : Seconds)";
        }
        return minutes + ":" + seconds + " (Minutes : Seconds)";
    }

    $('#download-btn').on('click', function() {
        var resId = document.getElementById("resId");

        var resId = resId.value;
        window.location.href = "/wonderpublish/downloadLiveTestRanks?resId="+resId;
    });
</script>

</body>
</html>
