<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">AutoGPT for ${booksMst.title}</h3>
                    <div class="form-group" id="intrst-area">
                    </div>
                </div>
                <div class="form-group">
                    <h5 style="color: #0b7cc5">Chapters List</h5>
                    <input type="checkbox" id="selectAll" name="selectAll" value="all">
                    <label for="selectAll">Select All</label>
                    <div id="chaptersList" style="display: flex; flex-wrap: wrap;">
                        <g:each in="${chaptersList}" var="chapter">
                            <div style="margin-right: 10px;">
                                <input type="checkbox" id="${chapter.id}" name="chapter" value="${chapter.id}">
                                <label for="${chapter.id}">${chapter.name}</label>
                            </div>
                        </g:each>
                    </div>
                </div>
                <div class="form-group col-6">
                    <h5 style="color: #0b7cc5">Select Template</h5>
                    <g:select  class="form-control" id="templateId" name="templateId" from="${promptTemplates}" optionKey="id" optionValue="name"
                               title="Select Template"  noSelection="['':'Select']" onChange="getTemplateDetails()"/>
                </div>
                <div class="form-group">
                    <h5 style="color: #0b7cc5">Prompts List</h5>
                    <input type="checkbox" id="selectAllPrompts" name="selectAllPrompts" value="all">
                    <label for="selectAllPrompts">Select All</label>
                    <div id="promptsList" style="display: flex; flex-wrap: wrap;">
                        <g:each in="${prompts}" var="prompt">
                            <div style="margin-right: 10px;">
                                <input type="checkbox" id="${prompt.promptType}" name="prompt" value="${prompt.promptType}">
                                <label for="${prompt.promptType}">${prompt.promptLabel}&nbsp;${prompt.parentPromptType!=null?"("+prompt.parentPromptType+")":""}</label>
                            </div>
                        </g:each>
                    </div>
                </div>
                <button id="createContent" class="btn btn-primary" style="margin-top: 20px;" onclick="javascript:createContent()">Create Content</button> </div>
        </div>
    </div>
</div>

<div class="container-fluid adminForm" >
    <div class="row" id="">
        <div class='col-md-9 main' style=" margin: 10px auto; float: none; padding: 15px;">
            <div>
                <div class="form-group" id="selectedChaptersList">
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
    document.getElementById('selectAll').addEventListener('change', function() {
        var checkboxes = document.getElementsByName('chapter');
        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = this.checked;
        }
    });

    document.getElementById('selectAllPrompts').addEventListener('change', function() {
        var checkboxes = document.getElementsByName('prompt');
        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = this.checked;
        }
    });

    async function createContent() {

        var chapters = [];
        var prompts = [];
        var chapterCheckboxes = document.getElementsByName('chapter');
        for (var i = 0; i < chapterCheckboxes.length; i++) {
            if (chapterCheckboxes[i].checked) {
                chapters.push(chapterCheckboxes[i].value);
            }
        }
        var promptCheckboxes = document.getElementsByName('prompt');
        for (var i = 0; i < promptCheckboxes.length; i++) {
            if (promptCheckboxes[i].checked) {
                prompts.push(promptCheckboxes[i].value);
            }
        }
        if (chapters.length == 0) {
            alert('Please select at least one chapter');
            return;
        }
        if (prompts.length == 0) {
            alert('Please select at least one prompt');
            return;
        }
        var chapterList = document.getElementById('selectedChaptersList');
        chapterList.innerHTML = '';
        var selectedChapters = document.createElement('div');
        selectedChapters.innerHTML = '<h5 style="color: #0b7cc5">Processing Chapters</h5>';
        chapterList.appendChild(selectedChapters);
        var resourceCreated = false;
        for (var i = 0; i < chapters.length; i++) {
            var chapter = document.getElementById(chapters[i]).nextElementSibling.textContent;
            var chapterDiv = document.createElement('div');
            chapterDiv.innerHTML = "<b>"+chapter+"</b>";

            // Add a loader and a response div next to each selected chapter name
            var loader = document.createElement('div');
            loader.id = 'loader' + chapters[i];
            loader.className = 'loader';
            loader.style.display = 'none';
            loader.textContent = 'Loading...';
            chapterDiv.appendChild(loader);

            var responseDiv = document.createElement('div');
            responseDiv.id = 'response' + chapters[i];
            responseDiv.style.display = 'inline-block'; // Display in the same line
            responseDiv.style.color = 'green'; // Change the color of the text
            chapterDiv.appendChild(responseDiv);

            // Append the chapterDiv to the selectedChapters div

            chapterList.appendChild(chapterDiv);

            var url = '/autogpt/setUpChapter';
            var formData = new FormData();
            formData.append('chapterId', chapters[i]);

            // Show the loader
            loader.style.display = 'block';

            await fetch(url, {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(async data => {

                    // Hide the loader and display the response
                    loader.style.display = 'none';
                    if(data.status=="Resource not found"){
                        responseDiv.style.color = 'red';
                        responseDiv.innerHTML = "&nbsp;<i class='fa-regular fa-circle-xmark' style='color: #d3143a;'></i>";
                    }
                    else
                    {
                        responseDiv.style.color = 'green';
                        //add a green tick mark using fontawesome
                        responseDiv.innerHTML = "&nbsp;<i class='fa-solid fa-check' style='color: #63E6BE;'></i>";

                    }

                    if (data.readingMaterialResId != null) {
                        // create a table with all the selected prompts
                        var table = document.createElement('table');
                        table.style.width = '100%';
                        table.style.borderCollapse = 'collapse';
                        table.style.marginTop = '10px';
                        var promptLabel
                        chapterList.appendChild(table);
                        for (var j = 0; j < prompts.length; j++) {
                            var tr = document.createElement('tr');
                            var td1 = document.createElement('td');
                            td1.textContent = document.getElementById(prompts[j]).nextElementSibling.textContent;
                            promptLabel= td1.textContent;
                            if(promptLabel.indexOf('(')!=-1)
                                promptLabel = promptLabel.substring(0,(promptLabel.indexOf('(')-1))
                                console.log("************"+promptLabel);
                            var td2 = document.createElement('td');
                            td2.innerHTML = '<div class="loader" style="display: inline-block;"></div>';
                            tr.appendChild(td1);
                            tr.appendChild(td2);
                            chapterList.appendChild(tr);

                            resourceCreated = false;
                            //check if resource is already created
                            if (data.createdResources != null) {
                                for (var k = 0; k < data.createdResources.length; k++) {
                                    console.log("promptType:" + prompts[j] + " data.createdResouces[k].promptType:" + data.createdResources[k].promptType);
                                    if (data.createdResources[k].promptType == prompts[j]) {
                                        resourceCreated = true;
                                        td2.textContent = 'Created';
                                        break;
                                    }
                                }
                                if (!resourceCreated) {
                                    //convert this request to json request

                                    var url1 = '/autogpt/createAndUpdateGPTContent';
                                    var formData1 = new FormData();
                                    formData1.append('namespace', data.namespace);
                                    formData1.append('promptType', prompts[j]);
                                    formData1.append('readingMaterialResId', data.readingMaterialResId);
                                    formData1.append('chapterId', chapters[i]);
                                    //make the ajax call and wait for the response
                                    await fetch(url1, {
                                        method: 'POST',
                                        body: formData1
                                    })
                                        .then(response => response.json())
                                        .then( data1 => {
                                            td2.textContent = data1.response;

                                        })
                                        .catch((error) => {
                                            console.error('Error:', error);
                                            // Hide the loader in case of error
                                            loader.style.display = 'none';
                                        });
                                }
                            }

                        }

                    }
                })
                .catch((error) => {
                    console.error('Error:', error);
                    // Hide the loader in case of error
                    loader.style.display = 'none';
                });

            //add a new line after each chapter
            var br = document.createElement('br');
            chapterList.appendChild(br);
        }

    }

    function getTemplateDetails(){
        var templateId = $('#templateId').val();
        $.ajax({
            url: '/autogpt/getTemplateDetails',
            type: 'POST',
            data: {
                templateId: templateId
            },
            success: function(response){
                if(response){
                    $('#promptsList input[type=checkbox]').each(function() {
                        this.checked = false;
                    });
                    var prompts = response.prompts.split(",");
                    for(var i=0; i<prompts.length; i++){
                        $('#'+prompts[i]).prop('checked', true);
                    }

                }else{
                    alert("Error in getting template details");
                }
            }
        });
    }


</script>
</body>
</html>
