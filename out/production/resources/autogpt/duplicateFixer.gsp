<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Duplicate fixer</h3>
                    <div class="form-group" id="intrst-area"><br>
                        <textarea class="form-control" id="chapterIds" rows="5" placeholder="Enter the Chapter Ids separated by comma"></textarea><br>
                        <btn class="btn btn-primary" id="fix-duplicates" onclick="fixDuplicates()">Fix Duplicates</btn><br>
                        <div id="results"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script>
    async function fixDuplicates() {

        var chapterIds = $('#chapterIds').val();
        if (chapterIds == '') {
            alert('Please enter the chapter ids');
            return;
        }
        let chapterIdsArr = chapterIds.split(',');
        //start the loader
        $('.loading-icon').removeClass('hidden');
        //loop through the chapter ids and call the remote function one by one
        for (var i = 0; i < chapterIdsArr.length; i++) {
            var chapterId = chapterIdsArr[i];
            if (chapterId != '') {

                //make an ajax call and wait for the results before going to the next chapter id

                //wait for the response and then go to the next chapter id

                await $.ajax({
                    url: '/autogpt/createAndUpdateGPTContent',
                    type: 'POST',
                    data: {
                        chapterId: chapterId,
                        promptType: "chapter_snapshot"
                    },
                    success: function (response) {
                        if (response) {
                            document.getElementById('results').innerHTML += '<p>' + (i + 1) + '. Chapter Id: ' + chapterId + ' - ' + response.response + '</p>';
                        } else {
                            alert("Error in deleting template");
                        }
                    }
                });
            }
        }
        $('.loading-icon').addClass('hidden');
    }
</script>

</body>
</html>
