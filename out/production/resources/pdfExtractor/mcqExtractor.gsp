<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCQs Extractor</title>
    <script src="https://code.jquery.com/jquery-1.12.4.min.js" crossorigin="anonymous"></script>
    <script src="/assets/wonderslate/vendors.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js" defer></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="/assets/bookgpt/mcqExtractor.css">
</head>
<body>
<!-- Loader overlay -->
<div class="loader-container">
    <div class="spinner"></div>
    <p class="loader-text">Loading chapter data...</p>
</div>

<g:render template="/pdfExtractor/api_modal"></g:render>

<header>
    <div class="container">
        <div class="header-content">
            <div class="logo">
                <span>MCQs Extractor</span>
            </div>
            <div class="timer-container">
                <div class="timer-label">Extraction Processed in:</div>
                <div class="timer" id="api-timer">0.00s</div>
            </div>
        </div>
    </div>
</header>

<main>
    <div class="container">
        <div class="page-title-container">
            <h1 class="page-title"></h1>
            <p class="page-subtitle"></p>
        </div>

        <div class="data-table">
            <div class="table-header">
                <div class="table-title"></div>
            </div>
            <div id="quiz-container" class="quiz-container"></div>
        </div>
    </div>
</main>

<script>
    const hasAnyError = "${error}"
    const hasAnyErrorMessage = "${message}"
    const resLink = "${resLink}"
    const bookTitle = "${bookTitle}"
    const chapterName = "${chapterName}"
    const resName = "${resName}"
    const bookId = "${params.bookId}"
    const chapterId = "${params.chapterId}"
    const resId = "${resId}"

    const apiTimer = document.getElementById('api-timer');
    const pageTitle = document.querySelector('.page-title')
    const pageSubTitle = document.querySelector('.page-subtitle')
    const resourceTitle = document.querySelector('.table-title')
    const loaderText = document.querySelector('.loader-text')

    let startTime, endTime, timerInterval;

    function startTimer() {
        startTime = new Date();
        timerInterval = setInterval(updateTimer, 1000);
    }

    function updateTimer() {
        const currentTime = new Date();
        const elapsedTimeInSeconds = Math.floor((currentTime - startTime) / 1000);
        const minutes = Math.floor(elapsedTimeInSeconds / 60);
        const seconds = elapsedTimeInSeconds % 60;
        // Format as MM:SS
        apiTimer.textContent = minutes.toString().padStart(2, '0') +":"+seconds.toString().padStart(2, '0');
    }

    function stopTimer() {
        clearInterval(timerInterval);
        endTime = new Date();
        const totalTimeInSeconds = Math.floor((endTime - startTime) / 1000);
        const minutes = Math.floor(totalTimeInSeconds / 60);
        const seconds = totalTimeInSeconds % 60;
        // Format as MM:SS
        apiTimer.textContent = minutes.toString().padStart(2, '0') +":"+seconds.toString().padStart(2, '0')
    }

     function fetchChapterData() {
        document.body.classList.add('loaded');
        if(hasAnyError == "true"){
            showModal(hasAnyErrorMessage, true);
            return false;
        }
        showModal("Fetched chapter details successfully. Starting MCQs extraction...", false);
        closeModalDialogAfterTimeout(3000)
        pageTitle.innerHTML = chapterName
        pageSubTitle.innerHTML = "Book: "+bookTitle;
        resourceTitle.innerHTML = resName
        extractMCQs();
    }

    async function extractMCQs(){
        const requestObj = {
            bookId: bookId,
            chapterId: chapterId,
            resId: resId,
            filePath:resLink
        }
        try {
            loaderText.innerHTML = "Extracting MCQs this might take few minutes... "
            document.body.classList.remove('loaded');
            startTimer()
            const response = await fetch('/pdfExtractor/taskHandler', {
                method: 'POST',
                body: JSON.stringify(requestObj)
            });
            const result = await response.json()
            console.log(result)

            document.body.classList.add('loaded');
            stopTimer()

            if(result.status == 500){
                showModal(result.message, true);
                return false;
            }
            closeModalDialogAfterTimeout(3000)
            showModal("MCQs Extracted Successfully!", false);
            processContent(result.data)
        }catch (error) {
            stopTimer()
            showModal(error, true);
            console.log(error)
            document.body.classList.add('loaded');
            loaderText.innerHTML = "Loading chapter data..."
            closeModalDialogAfterTimeout(3000)
        }
    }

    function closeModalDialogAfterTimeout(timeInSecs){
        setTimeout(()=>{
            hideModal();
        },timeInSecs)
    }
    document.addEventListener('DOMContentLoaded', function() {
        fetchChapterData();
    });

</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/13.0.1/marked.min.js"></script>
<script src="/assets/bookGPTScripts/extractor.js"></script>
</body>
</html>