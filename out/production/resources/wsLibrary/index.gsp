<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>

<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css" />

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<style>
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
/* Firefox */
input[type=number] {
    -moz-appearance: textfield;
}
</style>

<div class="digital-library">

<section class="main-section page-main-wrapper mdl-js pt-0">
    <div class="banner py-4 py-md-5  mt-3 mt-md-0">
    <div class="container banner-section">
        <div class="row align-items-center">
            <div class="col-12 col-lg-6">
                <h1 data-aos="fade-down"
                    data-aos-easing="linear"
                    data-aos-duration="600">Digital Library <span> > Library </span></h1>
                <p class="mt-4 mt-lg-2">A comprehensive online library for your Institution</p>
                <div class="d-flex mt-3" data-aos="fade-right">
<sec:ifNotLoggedIn>
                    <button class="btn btn-login btn-shadow mr-3 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect" type="button" onclick="javascript:loginOpen();">Register or Login</button>
</sec:ifNotLoggedIn>


                </div>
                <div class="d-flex mt-3 align-items-center">


                    <p class="available mt-1">Available on</p>
                    <a target="_blank" href="https://apps.apple.com/in/app/wonderslate/id1438381878"><img src="${assetPath(src: 'wslibrary/ios-grey.svg')}" class="ml-2"></a>
                    <a target="_blank" href="https://play.google.com/store/apps/details?id=com.wonderslate.wonderpublish&utm_source=signup%20mailer&utm_medium=email&utm_campaign=Wonderslate_App&utm_term=IITJEE"> <img src="${assetPath(src: 'wslibrary/android-grey.svg')}" class="ml-2"></a>
                </div>
            </div>
            <div class="col-12 col-lg-6 text-center">
                <img src="${assetPath(src: 'wslibrary/banner-image.svg')}" class="banner-img">
            </div>
        </div>
    </div>
    </div>
</section>
<section class="ebook-banner">
<div class="container">
    <div class="row">
        <div class="col-12 col-md-4 text-center">
            <img src="${assetPath(src: 'wslibrary/ebookaudio.svg')}" data-aos="fade-right">
        </div>
        <div class="col-12 col-md-4 text-center">
            <img src="${assetPath(src: 'wslibrary/webandroid.svg')}" data-aos="fade-down">
        </div>
        <div class="col-12 col-md-4 text-center">
            <img src="${assetPath(src: 'wslibrary/analytics.svg')}" data-aos="fade-left">
        </div>
    </div>
</div>
</section>
<section class="features">
    <div class="container">
        <h2 class="text-center offer" data-aos="fade-up"
            data-aos-easing="linear"
            data-aos-duration="300">Features</h2>
        <div class="row">
            <div class="col-12 col-md-6 col-lg-4" data-aos="fade-right">
                <div class="card">
                    <img src="${assetPath(src: 'wslibrary/access.svg')}" data-aos="zoom-in">
                    <div class="card-body">
                        <h4 class="card-title">Anytime,
                        Anywhere Access</h4>
                        <p class="card-text">Download your titles and study on the go with 100% offline access</p>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-4" data-aos="fade-down">
                <div class="card">
                    <img src="${assetPath(src: 'wslibrary/flashcard.svg')}">
                    <div class="card-body">
                        <h4 class="card-title">Flashcards & Review Mode</h4>
                        <p class="card-text">Study smarter with a targeted review of course content</p>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-4" data-aos="fade-left">
                <div class="card">
                    <img src="${assetPath(src: 'wslibrary/report.svg')}">
                    <div class="card-body">
                        <h4 class="card-title">Detailed Usage Reports</h4>
                        <p class="card-text">Understand and analyse your students at a glance</p>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-4" data-aos="fade-right">
                <div class="card">
                    <img src="${assetPath(src: 'wslibrary/audiovideo.svg')}">
                    <div class="card-body">
                        <h4 class="card-title">Audio & Video eBooks</h4>
                        <p class="card-text">Study eBooks with your preferred mode</p>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-4" data-aos="fade-up">
                <div class="card">
                    <img src="${assetPath(src: 'wslibrary/notes.svg')}">
                    <div class="card-body">
                        <h4 class="card-title">Notes & Highlights</h4>
                        <p class="card-text">Color code your annotations and sync across all your devices</p>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-4" data-aos="fade-left">
                <div class="card">
                    <img src="${assetPath(src: 'wslibrary/askdoubts.svg')}">
                    <div class="card-body">
                        <h4 class="card-title">Ask your Doubts</h4>
                        <p class="card-text">Ask your doubts and get them answered by subject matter experts</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>
<section class="setup">
    <h1 class="setup-simple" data-aos="fade-down"
        data-aos-easing="linear"
        data-aos-duration="300">Setup in just 3 simple steps!</h1>
    <div class="container d-flex align-items-center pb-4" style="min-height: 300px;">

        <div class="row">
            <div class="col-12 col-lg-4">
                <div class="card" data-aos="zoom-in">
                    <div class="card-body">
                        <div class="circle bg-blue">
                         <h2>1</h2>
                        </div>
                        <h4 class="card-title">Register</h4>
                        <p class="card-text">Register Your Institution (& Create Your Library)</p>
                    </div>
                </div>
            </div>
            <div class="col-12 col-lg-4">
                <div class="card" data-aos="zoom-in-up">
                    <div class="card-body">
                        <div class="circle bg-thickBlue">
                            <h2>2</h2>
                        </div>
                        <h4 class="card-title">Customise</h4>
                        <p class="card-text">As Per Your Needs (Put your school logo, colours, eBooks you need from the standard package)</p>
                    </div>
                </div>
            </div>
            <div class="col-12 col-lg-4">
                <div class="card" data-aos="zoom-in-down">
                    <div class="card-body">
                        <div class="circle bg-pink">
                            <h2>3</h2>
                        </div>
                        <h4 class="card-title">Add Students</h4>
                        <p class="card-text">Start Adding Students (Multiple students at the same time, via file, SSO etc.)</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="price-layer">
<div class="container">
    <div class="row">
        <div class="col-12 col-lg-6 p-0">
            <div class="card whiteBg" data-aos="zoom-in">
                <h1>Why you will
                <span>love our</span>
                    <span>Digital Library ?</span></h1>
                <img src="${assetPath(src: 'wslibrary/digi-lib.svg')}">
                <button class="btn btn-green request mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect">Request For Demo</button>
            </div>
        </div>
        <div class="col-12 col-lg-6 p-0">
           <div class="card pink-bg d-flex align-items-center" data-aos="zoom-in">
             <ul>
                 <li>Simplicity <span>– it’s so easy to use</span></li>
                 <li> Low cost</li>
                 <li>  Customise <span>for your school's branding</span></li>
                 <li>  eBook shopping <span>& smart selection tools</span></li>
                 <li>   <span>Detailed</span> usage reports</li>
                 <li>  Easy Administration</li>
                 <li> Full technical support</li>
                 <li> Compatible with <span>your LMS</span></li>
                 <li> Easily understood <span>by students and staff</span></li>
                 <li> Intuitive<span> mobile apps (iOS & Android)</span></li>
                 <li> <span>Thousands of </span> free eBooks</li>
             </ul>
           </div>
        </div>
    </div>
</div>
</section>


<section class="priceList d-flex align-items-center">
    <div class="container">
        <div class="row">
        <div class="col-12 col-lg-6 price-img d-none d-lg-flex align-items-center">

            <div class="price-image1"></div>
            <div class="pricing-text ml-lg-4" data-aos="flip-left">
                <p class="text-left">@ Just</p>
                <h1><sub>₹</sub>39,000</h1>
                <p class="text-right">per year</p>
                <button class="btn btn-demo mt-4 request mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect">Request for Demo</button>
            </div>
        </div>
            <div class="cover-price d-lg-none">
                <img src="${assetPath(src: 'wslibrary/coverprice1.svg')}" class="coverprice1">
                <div class="price-wrappers">
                    <div data-aos="flip-left">
                        <p class="text-left">@ Just</p>
                        <h1><sub>₹</sub>39,000</h1>
                        <p class="text-right">per year</p>
                    </div>
                    <div>
                         <button class="btn btn-demo mt-4 request">Demo</button>
                    </div>
                </div>
            </div>
        <div class="col-12 col-lg-6 d-flex align-items-center justify-content-lg-start">

            <ul class="price-agenda" data-aos="fade-down-left">
                <li>100 Volumes of Publisher eBooks + 500 Free eBooks</li>
                <li>Highlight and Note taking</li>
                <li>Customised landing page - Web</li>
                <li>Android & iOS app</li>
                <li>Add institutional eBooks/ Teacher Notes/ Videos</li>
                <li>Institutional Labelling</li>
                <li>IP Based Access</li>
                <li>Discussion board and doubt solving</li>
            </ul>
        </div>
        </div>
    </div>
</section>

<section class="banner_wrap container mt-0 mt-md-4 mb-4" id="demo-request">

    <div class="row mt-md-5">
        <div class="banner_info col-12 col-md-6"  data-aos="zoom-out">
            <h2>Yes I want a Demo</h2>
            <p>Personalised Cloud-Based Digital Library <br>for every Institution.</p>
            <div class="icon-lists d-flex flex-wrap align-items-center justify-content-around mt-5 mb-4 mb-md-5">
                <div class="icon-list">
                    <img src="${assetPath(src: 'ws/icon-cloud-based.svg')}">
                    <p>Cloud<br>Based</p>
                </div>
                <div class="icon-list">
                    <img src="${assetPath(src: 'ws/icon-institutional-repository.svg')}">
                    <p>Institutional<br>Repository</p>
                </div>
                <div class="icon-list">
                    <img src="${assetPath(src: 'ws/icon-remote-access.svg')}">
                    <p>Remote<br>Access</p>
                </div>
                <div class="icon-list">
                    <img src="${assetPath(src: 'ws/icon-drm-secured.svg')}">
                    <p>DRM<br>Secured</p>
                </div>
                <div class="icon-list">
                    <img src="${assetPath(src: 'ws/icon-publisher-resources.svg')}">
                    <p>Other Publisher<br>Resources</p>
                </div>
            </div>
            <div class="d-flex bg-white mb-5 pb-4 mb-md-0 pb-md-0">
                <p class="p-2">and Other exciting features..</p>
            </div>
        </div>
        <div class="banner_register_form col-12 col-md-6">
            <div class="form-info col-12 col-md-9 col-lg-7 mx-auto text-center p-3 p-md-4"  data-aos="zoom-in">
                <g:form class="form-horizontal" name="demorequest" id="demorequest" method="post" autocomplete="off">
                    <div class="form-group p-3 p-md-4 pb-4">
                        <h3 class="py-3"><strong>Yes, I want a demo!</strong></h3>
                        <input type="text" class="form-control mt-2" id="requestername" name="requestername" placeholder="Name" required>
                        <input type="text" class="form-control mt-2" id="schoolName" name="schoolName" placeholder="Institution's Name" required>
                        <input type="number" class="form-control mt-2" name="requestermobile" id="requestermobile" placeholder="Mobile Number" required>
                        <div class="alert-mobile border-0 mb-0 mt-2 p-0" style="display: none;font-size: 15px;color:red">Please enter a valid Mobile Number!</div>
                        <input type="email" id="requesteremail" class="form-control mt-2" name="requesteremail" placeholder="Email Address" required>
                        <div class="alert-email border-0 mb-0 mt-2 p-0" style="display: none;font-size: 15px;color:red">Please enter all Fields!</div>
                    </div>
                    <button type="button" class="btn btn-primary submit-btn mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect" onclick="formSubmits()"> Submit</button>
                </g:form>
                <div class="bottom-image pt-4">
                    <img src="${assetPath(src: 'ws/form-bottom-img.svg')}">
                </div>
            </div>
        </div>
    </div>
</section>

</div>
<footer class="digital-library-footer">

    <div class="container pt-8"> <!--mt-4-->

        <div class="row align-items-center">
        <div class="col-12 col-lg-6 d-flex justify-content-center justify-content-lg-end">
         <div class="wsborder">
            <img class="footer-logo" src="${assetPath(src: 'wslibrary/digilibrary.svg')}" alt="wonderslate">
            <br>
             <a href="/books/index">
            <img class="footer-logo" src="${assetPath(src: 'wslibrary/wspoweredby.svg')}" alt="wonderslate">
             </a>
         </div>
        </div>
            <div class="col-12 col-lg-6 d-flex justify-content-center justify-content-lg-start">
                <div>
                <p class="contactus">Contact us</p>
                <ul class="social-icons text-center text-md-left">
                    <li><a href="https://www.facebook.com/wonderslate" target="_blank"><i class="fb flaticon-facebook-logo"></i> </a> </li>
                    <li><a href="https://twitter.com/wonderslate" target="_blank"><i class="tw flaticon-twitter-black-shape"></i></a> </li>
                    <li><a href="https://www.linkedin.com/company/wonderslate/" target="_blank"><i class="ln flaticon-linkedin-logo"></i> </a> </li>
                    <li><a href="https://www.youtube.com/channel/UCMJv3HwAgBCwqWsZ63wIWwA" target="_blank"><i class="yt flaticon-youtube"></i> </a> </li>
                </ul>
                    <div class="d-flex">
                        <div>
                            <p class="mobile-no mt-3">Phone</p>
                            <p class="mobile-no">+91-8088443860</p>
                        </div>
                        <div class="ml-3">
                            <p class="mobile-no mt-3">Email</p>
                            <p class="mobile-no"><EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="row justify-content-center mt-4 pt-4 app-store">
            <a href="https://apps.apple.com/in/app/wonderslate/id1438381878" target="_blank">
                <img class="footer-logo" src="${assetPath(src: 'ws/appstore.svg')}" alt="wonderslate">
            </a>
            <a href="https://play.google.com/store/apps/details?id=com.wonderslate.wonderpublish&utm_source=signup%20mailer&utm_medium=email&utm_campaign=Wonderslate_App&utm_term=IITJEE" target="_blank">
                <img class="footer-logo ml-3" src="${assetPath(src: 'ws/playstore.svg')}" alt="wonderslate">
            </a>
        </div>

    </div>
</footer>
<script>
    var flds = new Array (
        'requestername',
        'requesteremail',
        'schoolName',
        'requestermobile'
    );

    function formSubmits() {
        if (validate()) {
            var requestername = document.demorequest.requestername.value;
            var requesteremail = document.demorequest.requesteremail.value
            var schoolName = document.demorequest.schoolName.value;
            var requestermobile = document.demorequest.requestermobile.value;

            if(requestermobile.length === 10) {
                $("#requestermobile").css('border', '2px solid green');
                $(".alert-mobile").hide();
                swal({
                    title: "Success!",
                    text: "Your information submitted successfully.",
                    type: "success",
                    allowOutsideClick: false,
                    showConfirmButton: true,
                    showCancelButton: false,
                    confirmButtonColor: "#27AE60",
                    confirmButtonText: "Ok",
                    cancelButtonText: "Cancel",
                    closeOnConfirm: true,
                    closeOnCancel: false,
                    allowEscapeKey: false,
                    customClass: '',
                }, function () {
                    <g:remoteFunction controller="log" action="addEnquiryFormRecord"  onSuccess='enquired(data);'
              params="'name='+requestername+'&email='+requesteremail+'&schoolName='+schoolName+'&mobile='+requestermobile"/>
                });
            }
            else{
                $("#requestermobile").css('border', '2px solid rgba(240, 90, 40, 0.5)');
                $(".alert-mobile").show();
            }
        }
    }
    function enquired(data){
        window.location = "/books/index";
        $("#demorequest").trigger("reset");
    }
    function validate(){
        var allFilled=true
        for (i=0; i<flds.length; i++) {
            if( !$("#"+flds[i]).val() ) {
                //actual code to check all fields needs to be entered. use the array of fields
                $("#"+flds[i]).css('border', '2px solid rgba(240, 90, 40, 0.5)');
                $("#"+flds[i]).css('border', '2px solid rgba(240, 90, 40, 0.5)');
                allFilled = false;
            } else {
                $('.alert-email').hide();
                $("#"+flds[i]).css('border', '2px solid green');
                $("#"+flds[i]).css('border', '2px solid green');
            }
        }
        if(!allFilled) {
            $('.alert-email').show();
        } else {
            var email = $("#requesteremail").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");
            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                document.getElementById('email').style.border = 'rgba(240, 90, 40, 0.5)';
                $('.alert-email').html('Please enter a valid email address.').show();
                return false;
            }
        }
        return allFilled;
    }

</script>

<div class="hidden-footer">
<g:render template="/books/footer_new"></g:render>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.6/highlight.min.js"></script>

<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
    AOS.init({
        easing: 'ease-out-back',
        duration: 1000
    });
</script>

<script>
    $(".request").click(function() {
        $('html, body').animate({
            scrollTop: $("#demo-request").offset().top - 200
        }, 1000);
    });
</script>
</body>
</html>