<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    <title><%= title!=null?title:"Next-gen Learning Platform - UPSC | JEE | NEET - Wonderslate"%></title>
    <meta name="description" content="Next-gen learning platform which offers Smart eBooks for competitive exams such as JEE, NEET, UPSC, Banking and much more. Learn, practice and analyze your preparation now!">

    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <link rel="android-touch-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}"/>
    <link rel="windows-touch-icon" href="icon.png" />
    <meta name="facebook-domain-verification" content="b1ndgtx0vj7jgzgmkhl612wuekczgd" />
    <meta name="theme-color" content="#6F58D8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0,maximum-scale=1.0, user-scalable=0" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&family=Rubik:wght@300;400;500;600;700;800;900&family=DM+Sans:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" async>
    <asset:stylesheet href="wonderslate/material.css" async="true"/>
    <asset:stylesheet href="landingpage/bootstrap.min.css" async="true"/>
    <asset:stylesheet href="wonderslate/headerws.css" async="true"/>
    <asset:stylesheet href="landingpage/slick.min.css" async="true"/>
    <asset:stylesheet href="landingpage/slick.theme.css" async="true"/>
    <asset:stylesheet href="landingpage/homepageStyle.css" async="true" data-role="baseline" />
    <asset:stylesheet href="landingpage/fonts/flaticon.css" async="true"/>

    <asset:stylesheet href="wonderslate/appInAppTemplate.css" async="true"/>
    <asset:stylesheet href="wonderslate/appinappStyles.css" async="true"/>

    <link rel="stylesheet" href="/assets/katex.min.css">

    <!-- General styles for admin & user pages -->
    <asset:stylesheet href="wonderslate/ws_general.css" async="true"/>

    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-MH384WF');</script>
    <!-- End Google Tag Manager -->
    <script>
        function encodeString(String){
            return(btoa(String))
        }
        function decodeString(String){
            return(atob(String))
        }
        function unicodeToChar(text) {
            return text.replace(/\\u[\dA-F]{4}/gi,
                function (match) {
                    return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
                });
        }
        function htmlDecode( html ) {
            var a = document.createElement( 'a' ); a.innerHTML = html;
            return a.textContent;
        };
    </script>
    <style>
    .mobile-back-button{
        display: none;
    }
    @media only screen and (max-width: 767px) {
        #mobile-chat-container {
            right: 30px !important;
            bottom: 120px !important;
        }
    }
    #mobile-chat-container {
        display: none;
        z-index: 998;
    }


    </style>
</head>

<body class="app_in_app">

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MH384WF"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->




<g:render template="/books/signIn"></g:render>


<!-- Header -->

<div class="mdl-js-layout">

    <header class="mdl-layout__header mdl-layout__header--transparent d-flex">
        <button class="mobile-back-button d-lg-none" onclick="javascript:history.back();">
            <img src="${assetPath(src: 'ws/icon-back-arrow-white.svg')}" class="d-lg-none"> Back
        </button>

        <div class="mdl-layout__header-row px-3 px-xl-5">


            <div class="mdl-layout-spacer px-3 ebooks">
                <div class="d-flex justify-content-center global-search w-100">
                    <form class="form-inline rounded rounded-modifier col-12 p-0">
                        <button type="button" onclick="hideMobileSearch()" class="btn bg-transparent text-white d-sm-none search-close-btn"><i class="material-icons">close</i></button>
                        <input type="text" class="form-control form-control-modifier border-0 typeahead w-100" name="search" id="search-book-header" autocomplete="off" placeholder="Search title, subject, author, ISBN, language etc.">
                        <button type="button" class="btn bg-transparent text-primary text-primary-modifier" onclick="submitSearchHeader()" id="search-btn-header"><i class="material-icons">search</i></button>
                    </form>
                </div>
            </div>

            <nav class="mdl-navigation" id="headerTab">
               <%if(session["defaultLevel"]==null){%>
                <a href="/appinapp/store" class="mdl-button mdl-js-button " id="ebooksAppInApp" >
                Explore eBooks
                </a>
                <%}else{%>
                <a href="/appinapp/store" class="mdl-button mdl-js-button " id="ebooksAppInApp" >
                Explore eBooks
                </a>
                <%}%>
                <a href="/wsLibrary/myLibrary" class="mdl-button mdl-js-button" id="myLibAppInApp" >
                    My eBooks
                </a>
                <a href="javascript:showMobileSearch();" class="mdl-button mdl-js-button d-sm-none search-btn-mobile">
                    <i class="material-icons">search</i>
                </a>
            </nav>

        </div>

    </header>


</div>



<script>
    $("#search-book-header").keyup(function(){
        if($(".dropdown-menu").is(":visible")) {
            var current = $(window).scrollTop();
            $(window).scroll(function () {
                $(window).scrollTop(current);
            });
        }
    });

    var activeCategories = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));

    function showMobileSearch() {
        $(".app_in_app .mdl-layout__header-row").addClass("showing-mobile-search");
        $(".app_in_app .mdl-layout__header-row .mdl-navigation").addClass("d-none");
    }
    function hideMobileSearch() {
        $(".app_in_app .mdl-layout__header-row").removeClass("showing-mobile-search");
        $(".app_in_app .mdl-layout__header-row .mdl-navigation").removeClass("d-none");
        $("#search-book-header").val('');
        $(window).off('scroll');
    }

</script>
