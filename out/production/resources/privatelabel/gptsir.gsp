<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <title>GPT Sir</title>
    <meta name="keywords" content="" />
    <meta name="description" content="" />
    <meta name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=5"/>
    <link rel="shortcut icon" href="/assets/gptsir/favicon.png" type="image/x-icon" />
    <link rel="icon" href="/assets/gptsir/favicon.png" type="image/x-icon" />
    <link rel="android-touch-icon" href="/assets/gptsir/favicon.png" />
    <link rel="windows-touch-icon" href="/assets/gptsir/favicon.png" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css" />
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" async/>
    <link rel="stylesheet" type="text/css" href="/assets/bookgpt/gptsirMob.css" media="screen"/>
    <link rel="stylesheet" type="text/css" href="/assets/bookgpt/gptsirDesktop.css" media="only screen and (min-width: 1020px)"/>

    <style>
    body {
        background-color: #0f0839 !important;
    }
    /* .mod h3,
    .animate-text,
    .pillar-title {
      color: #000 !important;
    } */
    .word-content,
    .two-words {
        opacity: 0.2 !important;
        filter: brightness(0.5);
    }
    .how-to-use {
        background: #f2f0fe;
        color: #000 !important;
        border-color: #f2f0fe !important;
    }
    .contact-wrapper {
        background: #f4f4f4;
        margin-top: 30px;
        border-radius: 10px;
    }
    .input-group input,
    .contact-form textarea {
        border: 1px solid #3e2b5e;
        color: #000;
    }
    .contact-form button {
        background: #e83500 !important;
    }
    .contact-info h2,
    .contact-info p,
    .contact-info ul li {
        color: #000;
    }
    .mod.prep_footer {
        background: #0c0051 !important;
    }
    .headerItem {
        width: 100%;
        background: #0f0839 !important;
        position: sticky;
        z-index: 9999999;
        top: 0;
    }
    .headerNav{
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 70px;
        width: calc(100% - 20%);
        margin: 0 auto;
    }
    .headerNav ul{
        list-style: none;
    }
    .headerNav ul li a{
        text-decoration: none;
        color: #fff !important;
        font-size: 17px;
    }
    .btnLogin{
        background: #e83500;
        color: #fff;
        width: 90px;
        padding: 8px 10px;
        font-size: 16px;
        border-radius: 5px;
        cursor: pointer;
        text-align: center;
        text-decoration: none;
    }
    </style>
</head>

<body class="prep_capsule">
<header class="headerItem">
    <nav class="headerNav">
        <a href='/sp/${session["siteName"]}/store'>
            <img src="/assets/gptsir/gptsirwhite.svg" style="width: 100px;opacity:1 !important;" id="lgNv" alt="GPT Sir Logo"/>
        </a>
        <div style="display: flex;align-items: center;gap: 20px">
            <ul>
                <li>
                    <a href='/sp/${session["siteName"]}/store' target="_blank">Store</a>
                </li>
            </ul>
            <a href='/sp/${session["siteName"]}/store?mode=loginform' class="btnLogin">Login</a>
        </div>
    </nav>
</header>
<div class="container">
    <section class="hero" data-bgcolor="#F2F0FE">
        <div class="hero-content" data-scroll>
            <img class="logo_image" src="/assets/gptsir/gptsirwhite.svg" data-src="/assets/gptsir/gptsirwhite.svg" alt="GPT Sir Logo"/>
            <h3 class="pillar-title" data-scroll style="margin-bottom: 0 !important;">
                AI Tutor in all books
            </h3>
        </div>
    </section>
    <section class="mod prep_content" id="content" data-bgcolor="#D7D2F2">
        <div class="wrap">
            <div class="col feature" data-scroll>
                <h3 class="animate-title">
                    <br />
                    iBookGPT
                </h3>
                <p class="">
                    <span style="color: #fff !important;">
                        iBookGPT = Book +
                        <span style="color: #ff790b;">AI Tutor.</span>
                    </span>
                    <br />
                    Experience learning with AI-powered interactive books that
                    personalize your study experience.
                </p>
                <div class="button-container">
                    <a href="/sp/${session["siteName"]}/store" class="try-for-free" target="_blank">Store</a>
                    <a href="/prompt/bookgpt?siteName=${session["siteName"]}&bookId=70350" class="how-to-use" target="_blank">Try for Free</a>
                    <a href="#howTo" class="store">How to use?</a>
                </div>
            </div>
            <div class="col phone" data-scroll>
                <img src="/assets/gptsir/ipad.png" data-src="/assets/gptsir/ipad.png" alt="iBookGPT"/>
            </div>
        </div>
        <div class="word single-word word-content" data-scroll data-scroll-direction="horizontal" data-scroll-speed="-6" data-scroll-target="#content"></div>
    </section>

    <section class="mod prep_coach" id="coach" data-bgcolor="#F2F0FE" style="margin-top: 200px;">
        <div class="wrap" style="flex-direction: row-reverse;">
            <div class="col feature" data-scroll>
                <h3 class="animate-title" style="line-height: normal !important;">
                    <br />
                    Instant Doubt Solving
                </h3>
                <p class="animate-text">
                    Get immediate answers to your doubts with detailed explanations,
                    making complex concepts easier to understand.
                </p>
            </div>
            <div class="col phone" data-scroll>
                <img src="/assets/gptsir/desk.png" data-src="/assets/gptsir/desk.png" alt="Instant Doubt Solving"/>
            </div>
        </div>

        <div class="two-words" id="personal-coaching">
            <div class="word word-personal in-view" data-scroll="" data-scroll-direction="horizontal" data-scroll-speed="-6" data-scroll-target="#coach"></div>
            <div class="word word-coaching in-view" data-scroll="" data-scroll-direction="horizontal" data-scroll-speed="6" data-scroll-target="#coach"></div>
        </div>
    </section>

    <section class="mod prep_immerse" id="immerse" data-bgcolor="#D7D2F2">
        <div class="wrap" style="flex-direction: row-reverse;">
            <div class="col feature" data-scroll>
                <h3 class="animate-title">
                    <br />
                    Unlimited Content
                </h3>
                <p class="animate-text">
                    Create Q&A, MCQs,Flashcards,Tricks,Summary etc
                </p>
            </div>
            <div class="col phone" data-scroll>
                <img src="/assets/gptsir/gpt.png" data-src="/assets/gptsir/gpt.png" alt="Unlimited Content"/>
            </div>
        </div>
        <div class="two-words" id="personal-immerse" style="margin-top: 300px;">
            <div class="word word-unlimited in-view" data-scroll="" data-scroll-direction="horizontal" data-scroll-speed="-6" data-scroll-target="#immerse"></div>
            <div class="word word-ucontent in-view" data-scroll="" data-scroll-direction="horizontal" data-scroll-speed="6" data-scroll-target="#immerse"></div>
        </div>
    </section>

    <section class="mod prep_revision" id="revision" data-bgcolor="#F2F0FE">
        <div class="wrap" style="flex-direction: row-reverse;">
            <div class="col feature" data-scroll>
                <h3 class="animate-title">
                    <br />
                    Gamified Practice
                </h3>
                <p class="animate-text">
                    Transform your learning experience with interactive, game-like
                    practice sessions. Stay motivated and sharpen your skills with
                    engaging challenges
                </p>
            </div>
            <div class="col phone" data-scroll>
                <img src="/assets/gptsir/game.png" data-src="/assets/gptsir/game.png" alt="Gamified Practice"/>
            </div>
        </div>
        <div class="two-words" id="personal-revision">
            <div class="word word-game in-view" data-scroll="" data-scroll-direction="horizontal" data-scroll-speed="-6" data-scroll-target="#revision"></div>
            <div class="word word-practice in-view" data-scroll="" data-scroll-direction="horizontal" data-scroll-speed="6" data-scroll-target="#revision"></div>
        </div>
    </section>

    <section class="mod prep_check" id="check" data-bgcolor="#D7D2F2">
        <div class="wrap" style="flex-direction: row-reverse;">
            <div class="col feature" data-scroll>
                <h3 class="animate-title">
                    <br />
                    School Ready
                </h3>
                <p class="animate-text">
                    Comes ready to deploy across your school with NCERT and all State board books.
                </p>
            </div>
            <div class="col phone" data-scroll>
                <img src="/assets/gptsir/school-ready.png" data-src="/assets/gptsir/school-ready.png" alt="SchoolReady"/>
            </div>
        </div>
        <div class="two-words" id="personal-check" style="top: 200px">
            <div class="word word-school in-view" data-scroll="" data-scroll-direction="horizontal" data-scroll-speed="-6" data-scroll-target="#check"></div>
            <div class="word word-ready in-view" data-scroll="" data-scroll-direction="horizontal" data-scroll-speed="6" data-scroll-target="#check"></div>
        </div>
    </section>

    <section class="prep_partners" data-bgcolor="##F2F0FE" id="howTo">
        <div class="wrap" data-scroll>
            <h3 class="pillar-title animate-title" data-scroll>
                How to Use?
            </h3>
            <div class="embed-responsive embed-responsive-16by9">
                <iframe
                        style="border-radius: 30px; width: 100%; height: 400px;"
                        class="embed-responsive-item"
                        src="https://www.youtube.com/embed/WDU3isa1hyw?si=dZB0xrgbxYyCdYG1"
                        allowfullscreen=""
                ></iframe>
            </div>
        </div>
    </section>

    <section class="prep_partners" data-bgcolor="#F2F0FE">
        <div class="wrap" data-scroll>
            <h3 class="pillar-title animate-title" data-scroll>
                Our Popular
                <br />
                Categories
            </h3>
        </div>
        <div class="wrap logos">

        </div>
    </section>
    <hr class="horizontal-separator" />

    <section class="mod prep_footer" data-bgcolor="#D7D2F2">
        <div class="wrap">
            <div class="col copyright" data-scroll>
                <p>
                    &copy;
                    <span id="copyrightYear"></span>
                    GPT Sir
                </p>
            </div>
            <div class="col links" data-scroll>
                <p>
                    Phone :
                    <a href="tel:+916363371085">+916363371085</a>
                </p>
                <p>
                    Email :
                    <a href="mailto:<EMAIL>">
                        <EMAIL>
                    </a>
                </p>
            </div>
        </div>
    </section>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript" src="/assets/bookGPTScripts/lettering.min.js"></script>
<script>
    $(document).ready(function () {
        // DEFER IMAGE LOAD
        var imgDefer = document.getElementsByTagName('img')
        for (var i = 0; i < imgDefer.length; i++) {
            if (imgDefer[i].getAttribute('data-src')) {
                imgDefer[i].setAttribute(
                    'src',
                    imgDefer[i].getAttribute('data-src'),
                )
            }
        }
        // STYLE INDIVIDUAL LINES & LETTERS
        $('.button').lettering('words')
        $('.animate-title, .animate-text').lettering('lines')
    })
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.1/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.1/ScrollTrigger.min.js"></script>
<script type="text/javascript" src="/assets/bookGPTScripts/locomotive-scroll.min.js"></script>
<script>
    // CURRENT YEAR
    var currentDate = new Date()
    $('#copyrightYear').html(currentDate.getFullYear())

    if("${session["activeCategoriesSyllabus_"+session["siteId"]]}"){
        document.querySelector('.prep_partners').removeAttribute('style')
        var activeCategoriesSyllabus = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
        let catHtml = "";
            activeCategoriesSyllabus.forEach(item=>{
                catHtml +="<a href='/sp/${session["siteName"]}/store?level="+encodeURIComponent(item.level)+"&syllabus="+encodeURIComponent(item.syllabus)+"' class='col' data-scroll style='text-decoration:none;'>"+
                    "<span>"+item.syllabus+"</span>"+
                    "</a>";
            })
        document.querySelector('.logos').innerHTML = catHtml

    }

    window.addEventListener('load', function () {
        gsap.registerPlugin(ScrollTrigger)

        const pageContainer = document.querySelector('.container')
        pageContainer.setAttribute('data-scroll-container', '')

        const scroller = new LocomotiveScroll({
            el: pageContainer,
            smooth: true,
            getSpeed: true,
            getDirection: true,
            repeat: false,
            smoothMobile: false,
            class: 'in-view',
            lerp: 0.1,
        })

        scroller.on('scroll', function (e) {
            document.documentElement.setAttribute('data-direction', e.direction)
        })

        scroller.on('scroll', ScrollTrigger.update)

        ScrollTrigger.scrollerProxy(pageContainer, {
            scrollTop(value) {
                return arguments.length
                    ? scroller.scrollTo(value, 0, 0)
                    : scroller.scroll.instance.scroll.y
            },
            getBoundingClientRect() {
                return {
                    left: 0,
                    top: 0,
                    width: window.innerWidth,
                    height: window.innerHeight,
                }
            },
            pinType: pageContainer.style.transform ? 'transform' : 'fixed',
        })

        if ($(window).width() > 1080) {
            // HORIZONTAL SCROLLING
            let horizontalSections = document.querySelectorAll(
                '.horizontal-scroll',
            )

            horizontalSections.forEach((horizontalSection) => {
                let pinWrap = horizontalSection.querySelector('.pin-wrap')
                let pinWrapWidth = pinWrap.offsetWidth
                let horizontalScrollLength = pinWrapWidth - window.innerWidth
                gsap.to(pinWrap, {
                    scrollTrigger: {
                        scroller: '[data-scroll-container]',
                        scrub: true,
                        trigger: horizontalSection,
                        pin: true,
                        start: 'top top',
                        end: () => `+=${pinWrapWidth}`,
                        invalidateOnRefresh: true,
                    },

                    x: -horizontalScrollLength,
                    ease: 'none',
                })
            })
        }
    })


</script>
</body>
</html>
