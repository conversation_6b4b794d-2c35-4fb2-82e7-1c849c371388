
<div class="faqWrapper">
<g:render template="/sage/navheader"></g:render>
</div>
%{--<%--}%
    %{--def notLoggedIn=false--}%
    %{--if(session.getAttribute("userdetails")==null) notLoggedIn=true--}%
%{--%>--}%
<style>
    .faqWrapper .sage-banner{
        display: none;
    }
.sageFaq ul{
    padding-left:2rem;
}
.sageFaq ul li{
    padding: 1rem;
}
.mt2{
    margin-top: 2rem;
}
.sageFaq p{
    line-height: 1.5;
}
.sageFaq h4,h3{
    font-weight: bold !important;
    margin-top: 2rem;
}
    .sageFaq a{
        color:#5ec8d7;
        text-decoration: underline;
        line-height: 2;
    }
</style>
<section class="sageFaq">

    <div class="container">
           <h3>Frequently Asked Questions (FAQ)
           </h3>
        <p>Our FAQ information helps you navigate through the <i>SAGE Texts</i> online resources.
        </p>
        <h4>How can I access resources available on SAGE Texts Resource site?
        </h4>
        <p>You can access the resources by creating an account on SAGE Texts website. For creating your profile account, click on the  <sec:ifNotLoggedIn><a href="javascript:showRegistration();">Signup</a> </sec:ifNotLoggedIn> <sec:ifLoggedIn><a href="#">Signup</a></sec:ifLoggedIn> button. You will be asked to identify yourself as either a Student or Instructor. After selecting your role, fill in your details on the page. A verification would be sent to you for verifying your account. In case of both students and Instructor, the verification OTP code would be sent via email.
        </p>
    <h4>I am a Student and want to access the SAGE Texts Student Resource site.
    </h4>
        <p>Please  <sec:ifNotLoggedIn><a href="javascript:showRegistration();">Signup</a> </sec:ifNotLoggedIn><sec:ifLoggedIn><a href="#">Signup</a></sec:ifLoggedIn>to create an account on the resource site, if you have not already done so. On submission of Signup form, you would get an OTP verification code on your email. Fill in the OTP number in the required field. You would be able to access our resources now. If you are unable to access your account, please <a href="mailto:<EMAIL>">contact us</a> directly.
    </p>
        <h4>I am an Instructor and want to access the SAGE Texts Resource site.</h4>
        <p>Please  <sec:ifNotLoggedIn><a href="javascript:showRegistration();">Signup</a> </sec:ifNotLoggedIn><sec:ifLoggedIn><a href="#">Signup</a></sec:ifLoggedIn> to create an account, if you have not already done so. On submission of create account form you would get an email with OTP pin in your inbox. Copy the OTP provided in the email and fill it in the required field. Once verified you would be able to access our resources now. If you are unable to access your account, please <a href="mailto:<EMAIL>">contact us</a> directly.
  </p>
        <h4>I have an account on the SAGE corporate site, can I login to SAGE Texts resource site using the same account credentials?
        </h4>
        <p>The account credentials used for your SAGE corporate site cannot be used to login on this resource site. You have to create an account on SAGE Texts resource site to access the resources. Please see the steps required to create an account on SAGE Texts resource site above according to your profile type.
        </p>
        <h4>I have not yet created a profile account on the SAGE Texts resource site. How do I register?
        </h4>
        <p>If you have not yet created a SAGE Texts resources profile account,  <sec:ifNotLoggedIn><a href="javascript:showRegistration();">register for your account</a> </sec:ifNotLoggedIn><sec:ifLoggedIn><a href="#">register for your account</a></sec:ifLoggedIn> now. Your account will be activated once your account has been verified.
        </p>
        <h4>
            I have an account on this resource site but the email address or username I entered was not found when I tried to log in.
        </h4>
        <p>Please <a href="mailto:<EMAIL>">contact us</a> directly. We might ask you for more information like, if you’ve made recent updates to your email account information.</p>
    <h4>The password I entered when I tried to log in is incorrect.</h4>
        <p>If you’ve forgotten your password, it’s easy, go to <sec:ifLoggedIn><a href="javascript:showForgotPasswordFaq();">reset password</a></sec:ifLoggedIn><sec:ifNotLoggedIn><a href="javascript:showForgotPasswordFaq();">reset password</a></sec:ifNotLoggedIn> page.
    </p>
        <h4>I’ve reset my password but I’m still unable to login.
        </h4>
        <p>If you reset your password and you are unable to access your account, please  <a href="mailto:<EMAIL>"> contact us</a> directly.
        </p>
        <h4>
            The OTP verification received is not working, what should I do?
        </h4>
        <p>In case the OTP provided shows up an error, please contact <a href="mailto:<EMAIL>"><EMAIL></a></p>
        <h4>I have other questions regarding SAGE offerings? Where can I find more information?</h4>
        <p>Please contact us at <a href="mailto:<EMAIL>"><EMAIL></a> . We are happy to answer any questions you might have. </p>
        <h4>I would like to write a feedback for the SAGE Texts Resource site. Who can I contact regarding this?</h4>
        <p>We appreciate comments and feedback from our customers. If you want to write to us about with any of our sites, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a> . Make sure to add the URL you visited with detailed information of the issues you found.
        </p>
        <h4>I have other questions regarding SAGE? Where can I find more information?
        </h4>
        <p>Please <a href="mailto:<EMAIL> ">contact us</a> for additional information about SAGE.</p>
    </div>
</section>

<g:render template="/sage/footer"></g:render>
<script>

</script>