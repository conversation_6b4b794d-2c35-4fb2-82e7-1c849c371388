<g:render template="/wonderpublish/commonfooter"></g:render>
<% if(!hideFooter) {%>
<footer class="main-footer">

    <div class="container-fluid footer-container">
        <div class="row">
            <div class="col-md-4 col-sm-4 col-xs-12">
                <p class="footer-credits">&copy; 2021 SAGE Publications India Pvt. Ltd.</p>
            </div>
            <div class="col-md-4 col-sm-4 col-xs-12 wonderslate-mobile-app">
                <p class="mobile-app-legend">Take your smart eBooks with you.</p>

            </div>
            <div class="col-md-4 col-sm-4 col-xs-12">
                <ul class="footer-links">
                    %{-- <li>
                      <a href="#">Contact Us</a>
                    </li> --}%

                    <li>
                        <a href="" target="_blank">Facebook</a>
                    </li>
                    <li>
                        <a href="" target="_blank">• Twitter</a>
                    </li>
                    <li>
                        <a href="" target="_blank">• Youtube</a>
                    </li>

                    <li>
                        <a href="/evidya/privacy">• Privacy Policy</a>
                    </li>
                    <li>
                        <a href="/evidya/terms">• Terms of Service</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</footer>
<%}%>
<script>

    $(function(){
        var current = location.pathname;
        $('.header-menu-item > a').each(function(){
            var $this = $(this);
            if($this.attr('href').indexOf(current) !== -1) {
                $this.addClass('active');
            }
        });
    });

    $(function(){
        var current = location.pathname;
        $('.mobile-bottom-menu-wrapper > a').each(function(){
            var $this = $(this);
            if($this.attr('href').indexOf(current) !== -1) {
                $this.addClass('active');
            }
        });
    });

    $('#sales-link').mouseenter(function() {
        $(this).parents('.header-menu-item').addClass('open');
    });
    $('#logout').click(function() {
        sessionStorage.clear();
    });


    $('#digital-link').mouseenter(function() {
        $(this).parents('.header-menu-item').addClass('open');
    });
    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }
</script>
<!-- Start of Async Drift Code -->
<%if(showDrift) { %>
<script>
    !function() {
        var t;
        if (t = window.driftt = window.drift = window.driftt || [], !t.init) return t.invoked ? void (window.console && console.error && console.error("Drift snippet included twice.")) : (t.invoked = !0,
            t.methods = [ "identify", "config", "track", "reset", "debug", "show", "ping", "page", "hide", "off", "on" ],
            t.factory = function(e) {
                return function() {
                    var n;
                    return n = Array.prototype.slice.call(arguments), n.unshift(e), t.push(n), t;
                };
            }, t.methods.forEach(function(e) {
            t[e] = t.factory(e);
        }), t.load = function(t) {
            var e, n, o, i;
            e = 3e5, i = Math.ceil(new Date() / e) * e, o = document.createElement("script"),
                o.type = "text/javascript", o.async = !0, o.crossorigin = "anonymous", o.src = "https://js.driftt.com/include/" + i + "/" + t + ".js",
                n = document.getElementsByTagName("script")[0], n.parentNode.insertBefore(o, n);
        });
    }();
    drift.SNIPPET_VERSION = '0.3.1';
    drift.load('et3w6ne98zbb');
</script>
<%}%>
<!-- End of Async Drift Code -->

<script>
    $( document ).ready( function () {
        $( '.dropdown-menu a.dropdown-toggle' ).on( 'click', function ( e ) {
            var $el = $( this );
            var $parent = $( this ).offsetParent( ".dropdown-menu" );
            if ( !$( this ).next().hasClass( 'show' ) ) {
                $( this ).parents( '.dropdown-menu' ).first().find( '.show' ).removeClass( "show" );
            }
            var $subMenu = $( this ).next( ".dropdown-menu" );
            $subMenu.toggleClass( 'show' );

            $( this ).parent( "li" ).toggleClass( 'show' );

            $( this ).parents( 'li.nav-item.dropdown.show' ).on( 'hidden.bs.dropdown', function ( e ) {
                $( '.dropdown-menu .show' ).removeClass( "show" );
            } );

            if ( !$parent.parent().hasClass( 'navbar-nav' ) ) {
                $el.next().css( { "top": $el[0].offsetTop, "left": $parent.outerWidth() - 4 } );
            }

            return false;
        } );
    } );
</script>
