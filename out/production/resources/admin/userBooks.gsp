<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css" type="text/css" rel="stylesheet">
<style>
#booksdata .dataTables_wrapper {
    padding-top: 0;
}
table.dataTable thead th:first-child {
    width:10% !important;
}
table.dataTable thead th:first-child:after,
table.dataTable thead th:first-child:before {
    display: none;
}
table.dataTable thead th:nth-child(2) {
    width:25% !important;
}
table.dataTable thead th:nth-child(3) {
    width:15% !important;
}
table.dataTable thead th:nth-child(4) {
    width:10% !important;
}
table.dataTable thead th:nth-child(5) {
    width:10% !important;
}
table.dataTable thead th:nth-child(6) {
    width:20% !important;
}
table.dataTable thead th:last-child {
    width:10% !important;
}
table.dataTable.dtr-inline.collapsed>tbody>tr>td.dtr-control:before,
table.dataTable.dtr-inline.collapsed>tbody>tr>th.dtr-control:before {
    top: 10px;
    margin-top: 0;
    background-color: #A81175;
}
</style>

<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section class="page-main-wrapper mdl-js self_service">
<div class="container">
    <div class='row mx-0'>
        <div class='col-md-12 main p-4' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <label for="userValue" style="display: block;">Get User Books</label>
                <div class="form-group form-inline">
                    <input type="text" class="form-control col-12 col-md-7 col-lg-4 mr-4 mb-3 mb-md-0" name="userValue" id="userValue"  placeholder="Email or mobile number">
                <button class="btn btn-primary col-6 col-md-3 col-lg-2" onclick="getUser();">Get Details</button>
            </div>
             <div id="errormsg" class="alert alert-danger has-error mt-4 mr-0 p-3" role="alert" style="display: none; background: none;"></div>
                <div id="successmsg" style="display: none"></div>
                <div id="booksdata" class="mt-4" style="display: none">
                    <table class='table table-hover table-bordered dt-responsive nowrap w-100'>
                        <thead>
                            <tr class='bg-primary text-white'>
                                <th data-orderable="false">Book Id</th>
                                <th>Book Title</th>
                                <th>Date Purchased</th>
                                <th>Payment Id</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Mobile</th>
                            </tr>
                        </thead>
                        <tbody id="purchasedBooksData">

                        </tbody>
                    </table>
                </div>
                <div id="noRecordsMsg" style="display:none;"></div>
            </div>

        </div>
    </div>
</div>
</section>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap4.min.js"></script>
<script>
    function getUser(){
        $('#booksdata table').DataTable().clear().destroy();
        if(document.getElementById("userValue").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the user email id or mobile number."
            $("#errormsg").show();
            $("#booksdata,#noRecordsMsg").hide();
        } else {
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            $("#successmsg").hide();
            $("#booksdata").show();
            var userValue = document.getElementById("userValue").value;
            <g:remoteFunction controller="admin" action="userBooks" params="'userName='+userValue+'&filter=true'" onSuccess="showUsers(data)"/>
        }
    }

    function showUsers(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr= "" ;
        if(data.status=="OK"){
            var users = data.userBooksList;
            for(var i=0;i<users.length;i++){
                htmlStr +="<tr><td>"+users[i].bookId+"</td>"+
                    "<td>"+users[i].title+"</td>" +
                    "<td>"+users[i].dateCreated+"</td>" +
                    "<td>"+users[i].paymentId+"</td>"+
                    "<td>"+users[i].name+"</td>" +
                    "<td>"+users[i].email+"</td>" +
                    "<td>"+users[i].mobile+"</td>" +
                    "</tr>";
            }
            document.getElementById("purchasedBooksData").innerHTML= htmlStr;
            $('#noRecordsMsg').hide();
            $('#booksdata table').DataTable({
                "ordering": false,
                "responsive": true,
                "searching": false,
                "info": false,
                "paging": false
            });
        }else{
            document.getElementById("noRecordsMsg").innerHTML= "<p>No records found with this email id or mobile number.</p>";
            $('#noRecordsMsg').show();
            $("#booksdata").hide();
        }
    }

</script>
</body>
</html>