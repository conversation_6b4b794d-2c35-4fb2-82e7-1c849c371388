<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<script>
    var loggedIn=false;
</script>

<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<style>
.external-order-title {
    font-size: 18px;
    margin-bottom: 10px;
}
.modal-content {
    width: 80%;
}
.hidden{
    display: none;
}
.modal-footer, .modal-header {
    border: none;
}
.modal-body i {
    color: #4BB543;
    font-size: 30px;
}
.order-section {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    border-radius: 4px;
}
</style>

<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>


%{--</div>--}%
<div class="d-flex justify-content-center container flex-column col-10 col-md-5 mt-4 order-section"><h2 class="external-order-title pt-4">Add order details</h2>
    <div class="alert alert-warning mt-3 hidden" role="alert" id="add-order-msg">

    </div>
    <div class=" d-flex justify-content-center flex-column">
        <div class="d-flex w-100 mb-2 mt-2">Order from &nbsp;&nbsp;
            <select id="order-from" class="btn btn-light btn-lg">
                <option value="">Select</option>
                <option value="amazon">Amazon</option>
                <option value="flipkart">Flipkart</option>
            </select>
        </div>
        <div class="d-flex w-100 mb-2 mt-2">
            <input type="text" class="w-100 p-2" id="add-order-id" placeholder="Order ID*" required>
        </div>
        <div class="d-flex w-100 mb-2 mt-2">
            <input type="text" class="w-100 p-2" id="item-code" placeholder="Item Code*" required>
        </div>
        <div class="d-flex w-100 mb-2 mt-2">
            <input type="text" class="w-100 p-2" id="order-value" placeholder="Order value">
        </div>

        <div class="d-flex w-100 mb-2 mt-2">
            <input type="text" class="w-100 p-2" id="access-code" placeholder="Access code">
        </div>
    </div>
    <div class="d-flex mb-5">
        <button class="btn btn-primary btn-lg" type="submit" onclick="javascript:handleAddOrder();">Add Order</button>
    </div>
</div>

<div class="d-flex justify-content-center container flex-column col-10 col-md-5 mt-4 order-section"> <h2 class="external-order-title pt-4">Cancel order</h2>
    <div class="alert alert-warning mt-3 hidden" role="alert" id="delete-order-msg">

    </div>
    <div class=" d-flex justify-content-center flex-column">
        <div class="d-flex w-100 mb-2 mt-2">
            <input type="text" class="w-100 p-2" id="delete-order-id" placeholder="Order ID*">
        </div>
    <div class="d-flex mb-5">
        <button class="btn btn-primary btn-lg" type="submit" onclick="javascript:handleDeleteOrder();">Cancel Order</button>
    </div>
</div>
</div>

<div class="d-flex justify-content-center container flex-column col-10 col-md-5 mt-4 order-section"><h2 class="external-order-title pt-4">Order Status</h2>
    <div class="alert alert-warning mt-3 hidden" role="alert" id="status-order-msg">

    </div>
    <div class=" d-flex justify-content-center flex-column">
        <div class="d-flex w-100 mb-2 mt-2">
            <input type="text" class="w-100 p-2" id="status-order-id" placeholder="Order ID*">
        </div>
        <div class="d-flex mb-5">
            <button class="btn btn-primary btn-lg" type="submit" onclick="javascript:handleOrderStatus();">Order Status</button>
        </div>
    </div>
</div>


<!-- Modal -->
<div class="modal fade" id="successModalOrders" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>

      <div class="modal-body d-flex justify-content-center align-items-center flex-column" id="modalMessage">
      </div>
      <div class="modal-footer d-flex justify-content-center">
        <button type="button" class="btn btn-primary btn-lg" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>



<g:render template="/${session['entryController']}/footer_new"></g:render>


<script>
    function handleAddOrder(){
        $('#add-order-msg').addClass('hidden');
        document.getElementById('add-order-msg').innerText = "";
        var orderId = $('#add-order-id').val();
        var itemCode = $('#item-code').val();
        var orderValue = $('#order-value').val();
        var accessCode = $('#access-code').val();
        var orderFrom = $('#order-from').val();

        if(!orderId || !itemCode || !orderFrom){
            $('#add-order-msg').removeClass('hidden');
            document.getElementById('add-order-msg').innerText = "Order From, Order Id and Item Code are mandatory.";
            return;
        }
        <g:remoteFunction controller="wsshop" action="addExternalOrder"  onSuccess="orderAdded(data);" params="'orderFrom='+orderFrom+'&orderId='+orderId+'&itemCode='+itemCode+'&poValue='+orderValue+'&accessCode='+accessCode" />


    }

    function orderAdded(data){
        $('#successModalOrders').modal('show');
        var message = '<i class="material-icons">check_circle_outline</i> <p>The order is submitted successfully!</p>';
        if("-1"==data.orderAckNo) message ='<i class="material-icons">highlight_off</i> <p>This order already exists on the system!</p>';
        else message += '<p>The order url is <b>'+getBaseUrl()+'?orderId='+data.orderId+'</b></p>';
        document.getElementById('modalMessage').innerHTML  = message;
    }

    function handleDeleteOrder(){
        $('#delete-order-msg').addClass('hidden');
        document.getElementById('delete-order-msg').innerText = "";
        var deleteOrderId = $('#delete-order-id').val();
        if(!deleteOrderId){
            $('#delete-order-msg').removeClass('hidden');
            document.getElementById('delete-order-msg').innerText = "Order Id is mandatory.";
            return;
        }
        <g:remoteFunction controller="wsshop" action="externalOrderCancelled"  onSuccess="orderDeleted(data);" params="'orderId='+deleteOrderId" />


    }

    function orderDeleted(data){
        var message = '<i class="material-icons">check_circle_outline</i> <p>The order is cancelled successfully!</p>';
        if("NotFound"==data.status) message ='<i class="material-icons">highlight_off</i> <p>Order not found</p>';

        $('#successModalOrders').modal('show');
        document.getElementById('modalMessage').innerHTML  = message;
    }

    function handleOrderStatus(){
        $('#status-order-msg').addClass('hidden');
        document.getElementById('status-order-msg').innerText = "";
        var statusOrderId = $('#status-order-id').val();
        if(!statusOrderId){
            $('#status-order-msg').removeClass('hidden');
            document.getElementById('status-order-msg').innerText = "Order Id is mandatory.";
            return;
        }
        <g:remoteFunction controller="wsshop" action="orderStatus"  onSuccess="orderStatus(data);" params="'orderId='+statusOrderId" />
    }

    function orderStatus(data){
        var message = '<i class="material-icons">check_circle_outline</i> <p>Order is created. Book is not yet added</p>';
        if("NotFound"==data.status) message ='<i class="material-icons">highlight_off</i> <p>Order not found</p>';
        else if("bookAdded"==data.status) message ='<i class="material-icons">check_circle_outline</i> <p>Book is added to </p>'+data.username;

        $('#successModalOrders').modal('show');
        document.getElementById('modalMessage').innerHTML  = message;
    }

   function getBaseUrl() {
       var url = window.location.href;
       var arr = url.split("/");
       var result = arr[0] + "//" + arr[2];
       return result;
   }
</script>
</body>
</html>
