<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);">
    <div class='row'>
        <div class='col-md-6 main text-center' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <h4><strong>CART PURCHASE DETAILS</strong></h4>
                <div class="form-inline p-4">
                    <div class="flex_st1 col-12 p-0">
                        <div class="form-group">
                            <input type="texts" class="form-control col-5" name="cartId" id="cartId"  placeholder="Enter cart Id" style="width: 300px;"/>

                            <button class="btn btn-primary col-2 ml-3"  onclick="cartDetails();" style="margin:10px auto;">Submit</button>
                        </div>
                        <div id="cartData"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>

    function cartDetails(){
        var cartId = document.getElementById("cartId").value;
        if(document.getElementById("cartId").value==""){
            document.getElementById("cartData").innerHTML= "<h6 style='text-align: left'>Please enter the cart Id</h6>";
        }else {
            <g:remoteFunction controller="admin" action="cartPurchase"  params="'cartMstId='+cartId"    onSuccess = "cartDetailsData(data)"/>
        }
    }

    function cartDetailsData(data){
            if(data.status=="OK") {
                var htmlStr = "<div><table class='table table-bordered'>" +
                    "           <thead class='bg-primary text-white text-center'><tr>\n" +
                    "                            <th>Username</th>\n" +
                    "                            <th>Book Id</th>\n" +
                    "                            <th>Title</th>\n" +
                    "                            <th>Paid Amount </th>\n" +
                    "                            <th>Book Price</th>\n" +
                    "                        </tr> </thead>\n";


                var books = data.cartDetails;
                for (i = 0; i < books.length; i++) {
                    htmlStr += "<tr>" +
                        "<td>" + books[i].userName + "</td>" +
                        "<td>" + books[i].bookId + "</td>" +
                        "<td>" + books[i].title + "</td>" +
                        "<td>" + books[i].paidAmount + "</td>" +
                        "<td>" + books[i].bookPrice + "</td>" +
                        "</tr>";
                }
                htmlStr += "                        \n" +
                    "                    </table></div>";
                document.getElementById("cartData").innerHTML = htmlStr;
            }else{
                document.getElementById("cartData").innerHTML= "<h4 style='text-align: left'>No Records</h4>";
            }

    }

</script>


</body>
</html>