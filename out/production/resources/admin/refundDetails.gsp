<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<script>
    var loggedIn=false;
</script>
<style>

.page-item.active .page-link {
    background-color: #007bff !important;
    color: #ffffff;
}
table td a {
    color: #007bff;
}
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>th:first-child:before {
    top: 16px !important;
}
table.dataTable>tbody>tr.child ul.dtr-details>li {
    border-bottom: none !important;
    padding: 0.2em 0 !important;
}
table.dataTable>tbody>tr.child span.dtr-title {
    min-width: 180px !important;
}
@media screen and (max-width: 767px){
    div.dataTables_wrapper div.dataTables_paginate ul.pagination {
        margin: 10px 0 30px !important;
        justify-content: center !important;
    }
    table.dataTable.nowrap th, table.dataTable.nowrap td {
        white-space: normal !important;
    }
    table.dataTable>tbody>tr.child span.dtr-title {
        min-width: 100% !important;
    }
    .form-group.col-md-6.mt-2 {
        display:flex;
    }
    .form-group.col-md-6.mt-2 button{
        max-width:100%;
    }
}

table.dataTable {
    width: 100% !important;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<div>
    <div class="container-fluid my-5 px-5 publishing_sales">
        <div  class='px-0' id="bookdtl" style="min-height: calc(100vh - 156px);">
            <div class='col-md-12 main mx-auto p-4'>
                <h3 class="text-center mb-4">Refund Report</h3><br>
                <div id="content-books" class="sale">
                    <div class="form-inline align-items-end">
                        <div class="form-group col-md-3">
                            <label for="poStartDate"><strong>From date</strong></label>
                            <input type="text" class="form-control" id="poStartDate" placeholder="Any" value="<%=poStartDate!=null?poStartDate:""%>" autocomplete="off">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="poEndDate"><strong>To date</strong></label>
                            <input type="text" class="form-control" id="poEndDate" placeholder="Any" value="<%=poEndDate!=null?poEndDate:""%>" autocomplete="off">
                        </div>

                    </div>
                        <% if(session["userdetails"].publisherId==null&&"1".equals(""+session["siteId"])) {%>
                        <div class="form-group col-md-3">
                            <label for="siteId"><strong>Sites</strong></label>
                            <g:select id="siteId" class="form-control w-100" optionKey="id" optionValue="clientName"
                                      value="" name="siteId" from="${sitesList}" noSelection="['':'All']"/>
                        </div>
                        <%  } else{%>
                        <input type="hidden" name="siteId" id="siteId" value="${session["siteId"]}">
                        <%}%>
                    </div>
                    <div class="form-group col-md-6 mt-2">
                        <button type="button" id="search-btn" onclick="saleSearch()" class="btn btn-lg btn-primary col-3">Search</button>
                    </div>
                    <div class="col-md-12 mt-5">
                        <table id="salesData" class="table table-striped table-bordered dt-reponsive nowrap" style="display: none;">
                            <thead>
                            <tr class="bg-primary text-white">
                                <th>Invoice/Order number</th>
                                <th>Book name</th>
                                <th>ISBN</th>
                                <th>Publisher</th>
                                <th>Site</th>
                                <th>Price</th>
                                <th>Refund date/time</th>
                                <th>Purchased date/time</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Mobile number</th>
                                <th>Payment Id</th>
                                <th>Book price</th>
                                <th>Discount Price</th>
                                <th>Discount Type</th>
                                <th>State</th>
                                <th>District</th>
                            </tr>
                            </thead>
                        </table>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<div class="push"></div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<sec:ifLoggedIn>
    <script>

        $('#saleSelect').change(function() {
            localStorage.removeItem('SalesReport');
            var salesVal = $(this).val();
            localStorage.setItem("SalesReport", salesVal);
        });

        window.onload = function() {
            var SalesReport = localStorage.getItem("SalesReport");
            if(SalesReport==null){
                $('#saleSelect').val('paymentId');
            }
            else {
                $('#saleSelect').val(SalesReport);
            }
        }


    </script>

</sec:ifLoggedIn>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script>
    var isResponsive = false;

    var mq = window.matchMedia( "(max-width: 570px)" );
    if (mq.matches) {
        isResponsive = true;
    }
    else isResponsive = false;


    $('#salesData').hide();
    function saleSearch() {

        if ($.fn.dataTable.isDataTable('#salesData')) {
            $('#salesData').DataTable().destroy();
        }

        $('#salesData').show();
        $('#salesData').DataTable({
            'responsive': isResponsive ? true : false,
            "scrollX": true,
            'destroy': true,
            //'processing': true,
            'serverSide': true,
            'searching': false,
            'ordering': false,
            'retrieve': true,
            'ajax': {
                'url': '/admin/refundDetails',
                'type': 'GET',
                'data': function (outData) {
                    //console.log(outData);
                    outData.poStartDate = $('#poStartDate').val();
                    outData.poEndDate = $('#poEndDate').val();
                    outData.select = $('#saleSelect').val();
                    outData.paymentId = $('#paymentId').val();
                    outData.publisherId = $('#publisherId').val();
                    outData.mode="submit";
                    outData.salesSiteId=$('#siteId').val();
                    return outData;
                },
                dataFilter: function (inData) {
                    //console.log(inData);
                    return inData;
                },
                error: function (err, status) {
                    console.log(err);
                },
            },
            'columns': [
                {
                    'data': 'poNo',
                },
                {
                    'data': 'title'
                },
                {
                    'data':'isbn'
                },
                {
                    'data': 'publisher'
                },
                {
                    'data': 'siteName'
                },
                {
                    'data': 'price'
                },
                {
                    'data': 'salesDate'
                },
                {
                    'data': 'purchasedDate'
                },
                {
                    'data': 'name'
                },
                {
                    'data': 'email'
                },
                {
                    'data': 'mobile'
                },
                {
                    'data': 'paymentId'
                },
                {
                    'data': 'bookPrice'
                },
                {
                    'data': 'discountAmount'
                },
                {
                    'data': 'discountType'
                },
                {
                    'data': 'state'
                },
                {
                    'data': 'district'
                }
            ],

        });
    }

    $('#poStartDate, #poEndDate').datepicker({
        format: 'dd-mm-yyyy',
        startView: 1,
        todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });
</script>
</body>
</html>
