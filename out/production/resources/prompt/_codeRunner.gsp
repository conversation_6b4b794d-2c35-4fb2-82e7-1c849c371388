<div id="editorModal" class="compiler_modal">
    <div class="compiler_modal-content">
        <div class="compiler_modal-header">
            <h2>Code Editor</h2>
        </div>
        <div class="compiler_close-btn"></div>
        <div class="compiler_iframe-container compiler_with-header">
            <iframe
                    frameBorder="0"
                    height="100%"
                    src="https://onecompiler.com/embed/python?listenToEvents=true&codeChangeEvent=true"
                    width="100%"
                    id="oneCompilerFrame"
            ></iframe>
        </div>
    </div>
</div>

<script>
    const editorModal = document.getElementById("editorModal");
    const editorModalBtn = document.querySelector(".compiler_open-modal-btn");
    const editorCloseBtn = document.querySelector(".compiler_close-btn");
    const oneCompilerFrame = document.getElementById("oneCompilerFrame");
    let isIframeLoaded = false;
    editorModalBtn.onclick = function() {
        editorModal.style.display = "block";
        setTimeout(() => {
            editorModal.classList.add("show");
            document.body.style.overflow = "hidden";
        }, 10);
    }
    editorCloseBtn.onclick = function() {
        editorModal.classList.remove("show");
        setTimeout(() => {
            editorModal.style.display = "none";
            document.body.style.overflow = "";
        }, 400);
    }
    oneCompilerFrame.onload = function() {
        isIframeLoaded = true;
        console.log("OneCompiler iframe loaded");
    };

    if (!isIframeLoaded) {
        console.log("Waiting for iframe to load...");
    }
    function injectCodeToEditor(temp_code) {
        editorModal.style.display = "block";
        setTimeout(() => {
            editorModal.classList.add("show");
            document.body.style.overflow = "hidden";
        }, 10);

        const message = {
            eventType: 'populateCode',
            language: 'python',
            files: [
                {
                    "name": "main.py",
                    "content": temp_code
                }
            ]
        };
        oneCompilerFrame.contentWindow.postMessage(message, '*');
    }
</script>