<style>
* {
    scrollbar-width: thin;
    scrollbar-color: #333 transparent !important;
}
.study-tools {
    width: 80%;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin: 0 auto;
    background: #fff;
    transition: height 0.3s ease;
}
.study-tools[open] {
    max-height: 300px;
    overflow-y: auto;
}

.study-tools:not([open]) {
    height: auto;
}
.dt_text{
    font-size: 14px;
    font-weight: bold;
    margin-left: 5px;

}
.study-tools hr{
    margin-top: 0 !important;
    margin-bottom: 5px !important;
}
.study-tools-header {
    padding: 10px;
    cursor: pointer;
    list-style: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 99;
}

.study-tools-header::-webkit-details-marker {
    display: none;
}

.study-tools-content {
    padding: 0 10px 10px 10px;
}

.study-tool-item {
    display: flex;
    align-items: center;
    padding: 8px;
    cursor: pointer;
}

.study-tool-item:hover {
    background-color: #f5f5f5;
}

.study-tool-item img {
    margin-right: 8px;
    filter: brightness(0.1);
    width: 20px;
}

.lockedContent {
    opacity: 0.5;
    pointer-events: none;
}
.scroll-indicator {
    position: sticky;
    bottom: 0;
    width: 100%;
    height: 28px;
    background: linear-gradient(transparent, white);
    display: none;
    align-items: center;
    justify-content: center;
}

.scroll-indicator i {
    cursor: pointer;
    background: #fff;
}
.study-tools.is-scrollable .scroll-indicator {
    display: flex;
}
@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}
</style>

<div class="chatViewer mobileChatViewer" id="mobileChatViewer">
    <div id="gpt_loader" class="gpt_loader">
        <div class="spinner"></div>
        <div class="introText">
            <%if("Yes".equals(gptcustomloader) && gptloaderpath !=null){%>
            <h3 id="loaderSub" style="margin-top: 12px;">
                <img src="/privatelabel/showPrivatelabelImage?siteId=${""+session["siteId"]}&fileName=${gptloaderpath}"style="width: 90px;margin-top: -8px;"  />
            </h3>
            <%}else {%>
            <h3 id="loaderSub" style="margin-top: 12px;">
                <img src="/assets/resource/ibookgpt-logo-light.svg" style="width: 120px;margin-top: -8px;"/>
            </h3>
            <%}%>
        </div>
    </div>
    <div class="conversation">
        <div id="defaultPromptPanel">
            <div class="tokensSec" style="display:none;">
                <div class="tokensSec-counts">
                    <p>Doubts: </p>
                    <p>
                        <span>Free: </span>
                        <span id="freeTokenCountItem"></span>
                    </p>
                    <p>
                        <span>Paid: </span>
                        <span id="paidTokenCountItem"></span>
                    </p>
                </div>
                <a href="/whitelabel/recharge" class="superChargeBtn">Recharge <i class="fa-solid fa-bolt"></i></a>
            </div>

            <p class="teacherTitle" style="display: none">Teaching <span>Assistant</span></p>
            <div class="chatOptions">
                <details class="study-tools" open>
                    <summary class="study-tools-header">
                        <span style="display: flex;align-items: center;gap: 10px">
                            <i class="st_icon"></i> <span class="dt_text">Study tools</span>
                        </span>
                        <i class="fa-solid fa-angle-up"></i>
                    </summary>
                    <div class="study-tools-content">
                        <hr>
                        <div id="studyToolsList"></div>
                        <div class="scroll-indicator">
                            <i class="fa-solid fa-chevron-down"></i>
                        </div>
                    </div>
                </details>
                <div class="prompts_dropdown-highlights" style="display:none;" onclick="getHighlightedData()">
                    <div class="prompts_dropdown-header">
                        <span class="prompts_dropdown-title">Show Highlights & Notes</span>
                    </div>
                </div>

                <span class="testseriesDisclaimer">All interactions are specific to selected MCQ</span>
            </div>
            <div class="bookwithAIWrap">
                <div class="purchaseMsgWrap">
                    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="512" height="512" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M256 0c-76.544.094-138.573 62.122-138.667 138.667V224c0 5.891 4.776 10.667 10.667 10.667h42.667c5.891 0 10.667-4.776 10.667-10.667v-85.333C181.333 97.429 214.763 64 256 64s74.667 33.429 74.667 74.667V224c0 5.891 4.776 10.667 10.667 10.667H384c5.891 0 10.667-4.776 10.667-10.667v-85.333C394.573 62.122 332.544.094 256 0z" style="" fill="#455a64" data-original="#455a64" class=""></path><path d="M128 213.333h256c29.455 0 53.333 23.878 53.333 53.333v192C437.333 488.122 413.455 512 384 512H128c-29.455 0-53.333-23.878-53.333-53.333v-192c0-29.456 23.878-53.334 53.333-53.334z" style="" fill="#ffc107" data-original="#ffc107" class=""></path><path d="M309.333 330.667c.124-29.455-23.653-53.434-53.108-53.558-29.455-.124-53.434 23.653-53.558 53.108a53.331 53.331 0 0 0 29.674 48.023l-8.235 57.6c-.825 5.833 3.235 11.23 9.068 12.055.494.07.993.105 1.492.105h42.667c5.891.06 10.715-4.667 10.774-10.558.005-.543-.03-1.086-.108-1.623l-8.235-57.6a53.522 53.522 0 0 0 29.569-47.552z" style="" fill="#455a64" data-original="#455a64" class=""></path></g></svg>
                    <%if("71".equals(""+session["siteId"])){%>
                        <p>
                            Upgrade to AI to access full <span>AI-powered</span> features</span>
                        </p>
                    <%} else{%>
                        <p>
                            Upgrade to iBookGPT to access full <span>AI-powered</span> features</span>
                        </p>
                    <%}%>
                </div>
                <div class="gptBuyNowBtnWrap" style="display: flex;justify-content: center;margin-top: 20px;margin-bottom: 8px;">
                    <button class="gptBuyNowBtn" id="gptBuyNowBtn" onclick="openBookDtlPage()">Upgrade Now</button>
                </div>
            </div>
        </div>
        <div class="messages">
            <p id="prevChat" style="display:none;text-align: center">Loading previous chat...</p>
            <div id="messages" style="display:flex;flex-direction: column"></div>
            <div class="is-typing" style="display:none;">
                <div class="jump1"></div>
                <div class="jump2"></div>
                <div class="jump3"></div>
                <p id="storepdfState" style="display:none;color: #d0d0d0;">Updating memory...</p>
            </div>
        </div>
        <div class="clearChat" style="display: none">
            <button onclick="showClearChatBtn()" id="showClearChatBtn"><i class="fa-solid fa-chevron-left"></i></button>
            <button onclick="clearChat()" id="clearChatBtn">Clear Chat <i class="fa-solid fa-delete-left"></i></button>
        </div>
    </div>
    <div class="legalText">
        <div class="buyPopup" id="buyPopup">
            <i class="fa-solid fa-xmark closeBuyPopup" id="closeBuyPopup" onclick="hideBuyPopup()"></i>
            <h4 id="buyTitle">Unlock Full Access!</h4>
            <div class="popupOpt"></div>
        </div>
        <div class="chat" id="customChatInput">
            <div class='chatInputWrapper'>
                <div id="capture-result-wrapper" style="width: 72px;">
                    <span class="snipCancel" style="display: none">
                        <i class="fa-solid fa-xmark"></i>
                    </span>
                    <div id="capture-result"></div>
                </div>
                <textarea type='text' id='chatInput' class='chatInputField' placeholder='Ask any question about the chapter...' oninput='auto_grow(this)'></textarea>

            </div>
            <div class='chatInputOptions'>
                <div>
                    <button class='formulaBtn'>
                        <i class='fa-solid fa-square-root-variable'></i>
                    </button>
                    <button id="start-capture-btn" class="inptOtpBtn"><i class="fa-solid fa-crop-simple"></i></button>
                    <button id="snip-btn" class="inptOtpBtn">Snip</button>
                </div>
                <button class='sendIcon' id='sendBtn'>
                    <i class='fa-solid fa-paper-plane'></i>
                </button>
            </div>
        </div>
        <%if("71".equals(""+session["siteId"])){%>
            <p>Answers are based on the provided book and might have errors.</p>
        <%} else{%>
            <p>iBookGPT's answers are based on the provided book and might have errors.</p>
        <%}%>
    </div>
    <div class="notesAndHighlights">
        <div class="apiLoader">
            <div class="spinner"></div>
        </div>
        <div class="hnHeader">
            <p>Highlights and Notes <i class="fa-solid fa-arrows-rotate refresHN" id="refresHN"></i></p>
            <i class="fa-solid fa-xmark" id="closeHNSlider"></i>
        </div>
        <div class="hnContent" id="hnContent">
        </div>
    </div>
</div>
<div id="feedbackModal" class="feedbackModal">
    <div class="modal-content">
        <div id="feedbackContent"></div>
    </div>
</div>
<div class="modal" id="videoModal">
    <div class="video-modal-content">
        <button class="close-button" id="closeModal">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z" />
            </svg>
        </button>
        <div class="video-container">
            <div class="video-wrapper">
                <div id="youtube-player"></div>
            </div>
            <div class="custom-controls">
                <button class="control-btn play-pause" title="Play/Pause">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z" />
                    </svg>
                </button>
                <div class="progress-bar">
                    <div class="progress"></div>
                </div>
                <span class="time-display">0:00 / 0:00</span>
                <button class="control-btn volume" title="Volume">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z" />
                    </svg>
                </button>
            </div>
        </div>
    </div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/13.0.1/marked.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/highlight.min.js"></script>
<script src="/assets/bookGPTScripts/bookGPTChatViewer.js"></script>