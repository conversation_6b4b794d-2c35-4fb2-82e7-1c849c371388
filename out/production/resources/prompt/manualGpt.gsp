<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Manual GPT</h3>
                    <div class="form-group" id="intrst-area"><form>
    <div class="form-group">
        <label for="resTypeSelector">Prompt Type</label>
        <select class="form-control" id="resTypeSelector" name="resTypeSelector">

        </select>
    </div>

    <div class="form-group">
        <label for="prompt">Prompt</label>
        <textarea class="form-control" id="prompt" name="prompt" rows="6" style="width: 90%"></textarea>
    </div>

    <div class="form-group">
        <label for="response">Response</label>
        <textarea class="form-control" id="response" name="response" rows="20" style="width: 90%"></textarea>
    </div>

    <button type="button" class="btn btn-primary" onclick="addResource()">Submit</button>
</form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
<script>
    const resTypeSelector = document.getElementById('resTypeSelector')
    const basePromptArray = '${basePromptArray}'
    const basePrompts = JSON.parse(basePromptArray.replace(/&quot;/g,'"'))

    let basePromptsHTML = "<option value=''>Select Label</option>"

    basePrompts.forEach((prompt,index)=>{
        basePromptsHTML +="<option value='"+prompt.promptType+"' data-index='"+index+"'>"+prompt.promptLabel+"</option>"
    })
    resTypeSelector.innerHTML = basePromptsHTML

function addResource() {
        let prompt = document.getElementById('prompt').value
        var answer = document.getElementById('response').value
        let promptType = resTypeSelector.value
        let promptLabel = resTypeSelector.options[resTypeSelector.selectedIndex].text
        if(promptType === 'mcqs'||promptType === 'mcq'){
            answer = fixMCQIssueIfAny()
        }else if(promptType === 'qna'){
            answer = cleanupQnA()
        }
        else if(promptType === 'flashcards'){
            answer = cleanupFlashcards()
        }
        let resId = '${params.resId}';
        if(promptType === ''){
            alert('Please select a prompt type')
            return
        }
        if(prompt === ''){
            alert('Please enter a prompt')
            return
        }
        if(response === ''){
            alert('Please enter a response')
            return
        }
        let data = {
            promptType,
            prompt,
            answer,
            resId,
            promptLabel

        }
        console.log(data)
        fetch('/prompt/createGPTResource',{
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        }).then(res=>res.json())
        .then(data=>{
            console.log(data)
            if(data.status=="OK"){
                alert('Resource added successfully')
                document.getElementById('prompt').value = ''
                document.getElementById('response').value = ''
                resTypeSelector.value = ''
            }else{
                alert('Failed to add resource')
            }
        })
    }

    function cleanupQnA(){
        let answers = JSON.parse(document.getElementById('response').value)
        var parsedContent
        let formattedAnswer = []
        for (let a = 0; a < answers.length; a++) {
            parsedContent = answers[a]
            if (typeof parsedContent === 'string') {
                parsedContent = JSON.parse(parsedContent.replace(/\n\s*/g, ''));
            }
        }
        formattedAnswer.push(JSON.stringify({ qna: answers }))

        return formattedAnswer;
    }

    function cleanupFlashcards(){
        let answers = JSON.parse(document.getElementById('response').value)
        var parsedContent
        let formattedAnswer = []
        for (let a = 0; a < answers.length; a++) {
            parsedContent = answers[a]
            if (typeof parsedContent === 'string') {
                parsedContent = JSON.parse(parsedContent.replace(/\n\s*/g, ''));
            }
        }
        answers
        formattedAnswer.push(JSON.stringify({ flashcards: answers }))

        return formattedAnswer;
    }
    function fixMCQIssueIfAny() {
        let answers = JSON.parse(document.getElementById('response').value)
        var parsedContent
        let formattedAnswer = []
        for (let a = 0; a < answers.length; a++) {
            parsedContent = answers[a]
            if (typeof parsedContent === 'string') {
                parsedContent = JSON.parse(parsedContent.replace(/\n\s*/g, ''));
            }
        }
            const questions = answers
            for (var q = 0; q < questions.length; q++) {
                if (
                    questions[q].answer == questions[q].op1 &&
                    !questions[q].answer.startsWith('op')
                ) {
                    questions[q].answer = 'op1'
                } else if (
                    questions[q].answer == questions[q].op2 &&
                    !questions[q].answer.startsWith('op')
                ) {
                    questions[q].answer = 'op2'
                } else if (
                    questions[q].answer == questions[q].op3 &&
                    !questions[q].answer.startsWith('op')
                ) {
                    questions[q].answer = 'op3'
                } else if (
                    questions[q].answer == questions[q].op4 &&
                    !questions[q].answer.startsWith('op')
                ) {
                    questions[q].answer = 'op4'
                }
            }
            formattedAnswer.push(JSON.stringify({ questions: questions }))

        return formattedAnswer;
    }

    document.getElementById('resTypeSelector').addEventListener('change', function() {
        var selectedPromptType = this.value;
        var selectedBasePrompt = basePrompts.find(function(prompt) {
            return prompt.promptType === selectedPromptType;
        });
        if (selectedBasePrompt) {
            document.getElementById('prompt').value = selectedBasePrompt.basePrompt;
        } else {
            document.getElementById('prompt').value = '';
        }
    });
</script>
</html>
