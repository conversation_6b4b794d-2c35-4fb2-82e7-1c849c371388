<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<section class="page-main-wrapper mdl-js pb-5 pt-0 my-activity">
    <div class="container pt-4 pt-md-5 mt-3 mt-md-0">
        <div class="d-flex justify-content-start align-items-center page_title col-12 col-md-10 mx-auto pb-2 px-0">
            <button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button>
            <div class="mdl-tooltip" data-mdl-for="goBack">Back</div>
            <h3><strong>My Learning History</strong></h3>
        </div>
<div class="row col-12 col-md-10 mx-auto px-0">
    <div class="history-records col-12 mt-3 px-0">
        <div id="emptyActivity"></div>
        <div class="card card-modifier card-shadow border-0">
    <div class="all-container" id="content-data-all">
    </div>

<div id="showMore" class="text-center" style="display: none;">
    <a href="javascript:showMore();" class="d-flex align-items-center justify-content-center">
        <span class="material-icons">
            expand_more
        </span>
        Show More
    </a>
</div>
        </div>
        </div>
</div>
</div>
</section>



<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<div class="books-list">
    <g:render template="/resources/relatedBooks"></g:render>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>
    var batchIndex = 0;


    function getUserActivity() {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="log" action="getResViewByUser" params="'batchIndex='+batchIndex" onSuccess="showUserActivity(data)"/>
    }

    function showUserActivity(data){
        var cData = data.userLog;
        var cStr = "";
        if(cData.length>0) {
            var resMap = new Map();
            resMap.set('Notes',{color:'lightgreen',text:'Notes'});
            resMap.set('Reference Videos',{color:'pink',text:'Videos'});
            resMap.set('Videos',{color:'pink',text:'Videos'});
            resMap.set('Reference Web Links',{color:'green',text:'Link'});
            resMap.set('QA',{color:'violet',text:'Q & A'});
            resMap.set('KeyValues',{color:'yellow',text:'Flash Card'});
            resMap.set('Multiple Choice Questions',{color:'violet',text:'MCQ'});
            resMap.set('Uploaded Media',{color:'pink',text:'Media'});


            for (var i = 0; i < cData.length; ++i) {
                var data = cData[i];
                // if (typeof data.bookId !== 'undefined') console.log("bookId=" + data.bookId);
                cStr += "<div class=\"container-wrapper px-3 pt-2\" id='res_" + i + "'>\n" +
                    "<div class='d-flex justify-content-between align-items-center'>";

                cStr += "        <div class=\"media\">\n";
                if(data.bookName && data.bookName != "undefined") {
                    if (data.testStartDate != "" && data.testStartDate != null) {
                        cStr += "<a class=\"mb-0 readnow d-flex align-items-start\" href='/funlearn/quiz?quizId=" + data.quizId + "&resId=" + data.resourceDtlId + "&quizMode=practice&source=web' target='_blank'>";
                    } else if (data.fromTab == "_") {
                        cStr += "<a class=\"mb-0 readnow d-flex align-items-start\" href='/prepjoy/prepJoyGame?quizId=" + data.quizId + "&resId=" + data.resourceDtlId + "&quizType=&source=web&learn=false&pubDesk=false&dailyTest=false' target='_blank'>";
                    }else if (data.fromTab == "testSeries_") {
                        cStr += "<a class=\"mb-0 readnow d-flex align-items-start\" href='/prepjoy/prepJoyGame?quizId=" + data.quizId + "&resId=" + data.resourceDtlId + "&quizType=testSeries&source=web&learn=false&pubDesk=false&dailyTest=false' target='_blank'>";
                    } else if (data.fromTab == "practice_") {
                        cStr += "<a class=\"mb-0 readnow d-flex align-items-start\" href='/prepjoy/prepJoyGame?quizId=" + data.quizId + "&resId=" + data.resourceDtlId + "&quizType=practice&source=web&learn=false&pubDesk=false&dailyTest=false' target='_blank'>";
                    } else {
                        cStr += "<a class=\"mb-0 readnow d-flex align-items-start\" href='/resources/ebook?resId=" + data.resourceDtlId + "&clickSource=myactivity&myActivity=true&newActivity=true&viewedFrom="+data.viewedFrom+"' target='_blank'>";
                    }
                    cStr += "   <div class='box " + resMap.get(data.resType).color + "'>" +
                        "<div>" +
                        " <i class=\"align-self-center\">\n" +
                        "                \n" +
                        "            </i>\n" +
                        "<p>" + resMap.get(data.resType).text + "</p>" +
                        "</div>" +
                        "</div>";
                    cStr += "            <div class=\"media-body pl-3 pt-2 pb-0\">\n" +
                        "                <h5 class=\"book-name pl-0 mb-1\">" + data.bookName + "</h5>\n" +
                        "                <h5 class=\"title pl-0 mb-1\"><span>Chapter : </span>" + data.chapterName + "</h5>\n";
                    if (typeof data.bookName !== 'undefined') {
                        cStr += "                <h5 class='title pl-0 mb-1'><span>" + resMap.get(data.resType).text + " : </span>" + data.resName + "</h5>\n";
                    }
                    cStr += "                <p class='updated_info'><small><em>Last used " + moment.utc(data.dateCreated).local().format("D MMM YYYY h:mm a") + "</em></small></p>\n";
                    cStr += "            </div>\n" +
                        "</a>";
                } else {
                    cStr += "<div class='readnow'><div class='box " + resMap.get(data.resType).color + "'>" +
                        "<div>" +
                        " <i class=\"align-self-center\">\n" +
                        "                \n" +
                        "            </i>\n" +
                        "<p>" + resMap.get(data.resType).text + "</p>" +
                        "</div>" +
                        "</div>";
                    cStr += "            <div class=\"media-body pl-3 pt-2 pb-0\">\n" +
                        "                <h5 class=\"book-name pl-0 mb-1 text-danger\">This resource is no longer available.</h5>\n";
                    cStr += "            </div></div>\n";
                }
                cStr += "        </div>\n" +
                    "    </div>\n" + "</div>";
            }


            document.getElementById('content-data-all').innerHTML += cStr;
            $("#emptyActivity").hide();
            $("#showMore").show();
        }else{
            var emptyStr = "<div class='empty_activity text-center pt-4 pb-5 mt-4 mb-5' style=\"display: block;\">\n" +
                "                <img src='${assetPath(src: 'ws/todo-empty.svg')}'>\n" +
                "                <p>Welcome Aboard!</br>You can see your activity here.</p>\n" +
                "            <p class='mt-3'><strong>Start using now!</strong></p>\n" +
                "            </div>";
            document.getElementById('emptyActivity').innerHTML += emptyStr;
            $("#showMore").hide();
        }
        $('.loading-icon').addClass('hidden');
    }

    function showMore(){
        batchIndex++;
        getUserActivity();
        $("#emptyActivity").hide();
    }
    //call for the first time
    getUserActivity();



</script>

</body>
</html>
