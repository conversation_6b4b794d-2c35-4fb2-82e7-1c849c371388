<g:render template="/books/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/mobile.css" media="screen"/>
<asset:stylesheet href="wonderslate/desktop.css" media="only screen and (min-width: 1020px)"/>
<link href="https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" async>

<style>
.contact form button, .contact form input, .contact form textarea, .contact form select{
    padding: 4px !important;
    font-size: 16px !important;
}
#contactForm label{
    font-size: 16px !important;
}
.wrap.logos .col.in-view img{
    height: 133px !important;
}
.teachers_contact{
    width: 75%;
    margin: 0 auto;
}
.requiredInd{
    color: red;
}
.contact form input{
    height: 42px;
}
@media (max-width: 768px) {
    .wrap.logos .col.in-view img{
        height: 88px !important;
    }
    .teachers_contact{
        width: 100%;
    }
}
@media (max-width: 1020px) {
    .wrap{
       max-width: 100% !important;
    }
}
</style>
<body class="prep_capsule">

<div class="container">
    <div class="wrap">
        <h1 class="pagetitle" style="margin-bottom: 0 !important;">Free Specimen Books For Teachers</h1>
        <p class="school_products-title_sub">We would be delighted to provide you with specimen Books for the subject of your choice. Free access to our books for 60 days.</p>
    </div>

    <section class="mod prep_contact p-0">
        <div class="wrap" style="flex-direction: column">
            <div class="title_wrapper">
                <p style="text-align: center">Fill this simple form to request the books</p>
            </div>
            <div class="wrapper animate__animated animate__fadeInLeft teachers_contact" style="box-shadow: 0 1px 5px; !important;background: transparent !important;">
                <div class="contact">
                    <form id="contactForm">
                        <p>
                            <label>Name <span class="requiredInd">*</span></label>
                            <input type="text" name="name" id="uname" required placeholder="Full Name">
                        </p>
                        <p>
                            <label>Email address <span class="requiredInd">*</span></label>
                            <input type="email" name="email" id="qemail" required placeholder="Email">
                        </p>
                        <p>
                            <label>Phone Number <span class="requiredInd">*</span></label>
                            <input type="text" name="phone" id="phone" required placeholder="Mobile Number">
                        </p>
                        <p>
                            <label>Date of birth <span class="requiredInd">*</span></label>
                            <input type="date" name="dob" id="dob" required placeholder="Date of Birth">
                        </p>
                        <p class="full">
                            <label>School / College / Institution <span class="requiredInd">*</span></label>
                            <input type="text" name="company" id="company" placeholder="School / College / Institution" required>
                        </p>
                        <p class="full">
                            <label>Full Address <span class="requiredInd">*</span></label>
                            <input type="text" name="address" id="address" placeholder="Full Address" required>
                        </p>
                        <p>
                            <label>Pin Code <span class="requiredInd">*</span></label>
                            <input type="number" name="pincode" id="pincode" placeholder="Pin Code" required>
                        </p>
                        <p>
                            <label>Country <span class="requiredInd">*</span></label>
                            <select name="country" id="country" required>
                                <option value="india">India</option>
                            </select>
                        </p>

                        <p>
                            <label>State <span class="requiredInd">*</span></label>
                            <select name="state" id="state" required>
                                <option value="">Select State</option>
                            </select>
                        </p>

                        <p>
                            <label>District <span class="requiredInd">*</span></label>
                            <select name="district" id="district" required>
                                <option value="">Select District</option>
                            </select>
                        </p>

                        <p>
                            <label>Publisher <span class="requiredInd">*</span></label>
                            <select name="publisher" id="publisher" required>
                                <option value="">Select Publisher</option>
                            </select>
                        </p>
                        <p>
                            <label>Level <span class="requiredInd">*</span></label>
                            <select name="level" id="level" required>
                                <option value="">Select Level</option>
                            </select>
                        </p>
                        <p>
                            <label>Board <span class="requiredInd">*</span></label>
                            <select name="board" id="syllabus" required>
                                <option value="">Select Syllabus</option>
                            </select>
                        </p>
                        <p>
                            <label>Class/Grade <span class="requiredInd">*</span></label>
                            <select name="grade" id="grade">
                                <option value="">Select Grade</option>
                            </select>
                        </p>
                        <p class="full">
                            <label>Subject <span class="requiredInd">*</span></label>
                            <select name="subject" id="subject" required>
                                <option value="">Select Subject</option>
                            </select>
                        </p>

                        <p class="full">
                            <label>Upload your School / Coaching ID Card / Principal's Authorization / Visiting Card / Proof <span class="requiredInd">*</span></label>
                            <input type="file" accept="image/*" id="photoId" required>
                        </p>

                        <p class="full">
                            <button type="submit" id="askQuery" class="d-flex w-50 justify-content-center mx-auto">Request Now!</button>
                        </p>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <section class="mod prep_footer" data-bgcolor="#263238" style="visibility: hidden">
        <div class="wrap">
            <div class="col copyright" data-scroll>
                <p>&copy; <span id="copyrightYear"></span> PrepCapsule - <a href="https://www.wonderslate.com/" target="_blank">Wonderslate</a></p>
            </div>
            <div class="col links" data-scroll>
                <p>Phone : <a href="tel:+91-8088443860">+91-8088443860</a></p>
                <p>Email : <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
        </div>
    </section>
</div>


<!--------  REPORT SUCCESS MODAL --------->
<div class="modal fade report-success__modal modal-modifier" id="report-success">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close mr-2" data-dismiss="modal" aria-label="Close" style="text-align: end">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center">
                <div id="addedIconAnimation" class="scaleAnimation">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <h5 class="mt-3">Request Sent Successfully.</h5>
                <p>We will get back to you within 24 hours</p>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-6 col-md-5 mr-2" data-dismiss="modal" aria-label="Close">OK</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<!--------  REPORT SUCCESS MODAL --------->
<div class="modal fade report-success__modal modal-modifier" id="report-success">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close mr-2" data-dismiss="modal" aria-label="Close" style="text-align: end">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center">
                <div id="addedIconAnimation" class="scaleAnimation">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <h5 class="mt-3">Message Sent Successfully.</h5>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-6 col-md-5 mr-2" data-dismiss="modal" aria-label="Close">OK</button>
                </div>
            </div>

        </div>
    </div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<asset:javascript src="wonderslate/vendors.min.js"/>
<script>
    $(document).ready(function() {
        // DEFER IMAGE LOAD
        var imgDefer = document.getElementsByTagName('img');
        for (var i=0; i<imgDefer.length; i++) {
            if(imgDefer[i].getAttribute('data-src')) {
                imgDefer[i].setAttribute('src',imgDefer[i].getAttribute('data-src'));
            }
        }
        // STYLE INDIVIDUAL LINES & LETTERS
        $(".button").lettering('words');
        $(".animate-title, .animate-text").lettering('lines');
    });
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.1/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.1/ScrollTrigger.min.js"></script>
<asset:javascript src="locomotive-scroll.min.js"/>
<script>

    // CURRENT YEAR
    var currentDate = new Date();
    $("#copyrightYear").html(currentDate.getFullYear());

    window.addEventListener("load", function () {
        gsap.registerPlugin(ScrollTrigger);

        const pageContainer = document.querySelector(".container");
        pageContainer.setAttribute("data-scroll-container", "");

        const scroller = new LocomotiveScroll({
            el: pageContainer,
            smooth: true,
            getSpeed: true,
            getDirection: true,
            repeat: false,
            smoothMobile: false,
            class: 'in-view',
            // lerp: .08
        });

        scroller.on("scroll", function (e) {
            document.documentElement.setAttribute("data-direction", e.direction);
        });

        scroller.on("scroll", ScrollTrigger.update);

        ScrollTrigger.scrollerProxy(pageContainer, {
            scrollTop(value) {
                return arguments.length ? scroller.scrollTo(value, 0, 0) : scroller.scroll.instance.scroll.y;
            },
            getBoundingClientRect() {
                return {
                    left: 0,
                    top: 0,
                    width: window.innerWidth,
                    height: window.innerHeight
                };
            },
            pinType: pageContainer.style.transform ? "transform" : "fixed"
        });

        if($(window).width() > 1080) {

            // HORIZONTAL SCROLLING
            let horizontalSections = document.querySelectorAll(".horizontal-scroll");

            horizontalSections.forEach(horizontalSection => {
                let pinWrap = horizontalSection.querySelector(".pin-wrap");
                let pinWrapWidth = pinWrap.offsetWidth;
                let horizontalScrollLength = pinWrapWidth - window.innerWidth;
                gsap.to(pinWrap, {
                    scrollTrigger: {
                        scroller: "[data-scroll-container]",
                        scrub: true,
                        trigger: horizontalSection,
                        pin: true,
                        start: "top top",
                        end: () => `+=${pinWrapWidth}`,
                        invalidateOnRefresh: true
                    },

                    x: -horizontalScrollLength,
                    ease: "none"
                });

            });
        }

        // COLOR CHANGER
        const scrollColorElems = document.querySelectorAll("[data-bgcolor]");
        scrollColorElems.forEach((colorSection, i) => {
            const prevBg = i === 0 ? "" : scrollColorElems[i - 1].dataset.bgcolor;
            const prevText = i === 0 ? "" : scrollColorElems[i - 1].dataset.textcolor;

            ScrollTrigger.create({
                trigger: colorSection,
                scroller: "[data-scroll-container]",
                start: "top 50%",
                onEnter: () => gsap.to("body", {
                    backgroundColor: colorSection.dataset.bgcolor,
                    color: colorSection.dataset.textcolor,
                    overwrite: "auto"
                }),

                onLeaveBack: () => gsap.to("body", {
                    backgroundColor: prevBg,
                    color: prevText,
                    overwrite: "auto"
                })
            });

        });

        ScrollTrigger.addEventListener("refresh", () => scroller.update());

        ScrollTrigger.refresh();
    });



    var stateSel = document.getElementById("state"),
        districtSel = document.getElementById("district");

    //STATES & DIST OBJ
    var stateObjectNew = {
        "Andhra Pradesh": [
            "Anantapur",
            "Chittoor",
            "East Godavari",
            "Guntur",
            "Krishna",
            "Kurnool",
            "Nellore",
            "Prakasam",
            "Srikakulam",
            "Visakhapatnam",
            "Vizianagaram",
            "West Godavari",
            "YSR Kadapa"
        ],
        "Arunachal Pradesh": [
            "Tawang",
            "West Kameng",
            "East Kameng",
            "Papum Pare",
            "Kurung Kumey",
            "Kra Daadi",
            "Lower Subansiri",
            "Upper Subansiri",
            "West Siang",
            "East Siang",
            "Siang",
            "Upper Siang",
            "Lower Siang",
            "Lower Dibang Valley",
            "Dibang Valley",
            "Anjaw",
            "Lohit",
            "Namsai",
            "Changlang",
            "Tirap",
            "Longding"
        ],
        "Assam": [
            "Baksa",
            "Barpeta",
            "Biswanath",
            "Bongaigaon",
            "Cachar",
            "Charaideo",
            "Chirang",
            "Darrang",
            "Dhemaji",
            "Dhubri",
            "Dibrugarh",
            "Goalpara",
            "Golaghat",
            "Hailakandi",
            "Hojai",
            "Jorhat",
            "Kamrup Metropolitan",
            "Kamrup",
            "Karbi Anglong",
            "Karimganj",
            "Kokrajhar",
            "Lakhimpur",
            "Majuli",
            "Morigaon",
            "Nagaon",
            "Nalbari",
            "Dima Hasao",
            "Sivasagar",
            "Sonitpur",
            "South Salmara-Mankachar",
            "Tinsukia",
            "Udalguri",
            "West Karbi Anglong"
        ],
        "Bihar": [
            "Araria",
            "Arwal",
            "Aurangabad",
            "Banka",
            "Begusarai",
            "Bhagalpur",
            "Bhojpur",
            "Buxar",
            "Darbhanga",
            "East Champaran (Motihari)",
            "Gaya",
            "Gopalganj",
            "Jamui",
            "Jehanabad",
            "Kaimur (Bhabua)",
            "Katihar",
            "Khagaria",
            "Kishanganj",
            "Lakhisarai",
            "Madhepura",
            "Madhubani",
            "Munger (Monghyr)",
            "Muzaffarpur",
            "Nalanda",
            "Nawada",
            "Patna",
            "Purnia (Purnea)",
            "Rohtas",
            "Saharsa",
            "Samastipur",
            "Saran",
            "Sheikhpura",
            "Sheohar",
            "Sitamarhi",
            "Siwan",
            "Supaul",
            "Vaishali",
            "West Champaran"
        ],
        "Chandigarh (UT)": [
            "Chandigarh"
        ],
        "Chhattisgarh": [
            "Balod",
            "Baloda Bazar",
            "Balrampur",
            "Bastar",
            "Bemetara",
            "Bijapur",
            "Bilaspur",
            "Dantewada (South Bastar)",
            "Dhamtari",
            "Durg",
            "Gariyaband",
            "Janjgir-Champa",
            "Jashpur",
            "Kabirdham (Kawardha)",
            "Kanker (North Bastar)",
            "Kondagaon",
            "Korba",
            "Korea (Koriya)",
            "Mahasamund",
            "Mungeli",
            "Narayanpur",
            "Raigarh",
            "Raipur",
            "Rajnandgaon",
            "Sukma",
            "Surajpur  ",
            "Surguja"
        ],
        "Dadra and Nagar Haveli (UT)": [
            "Dadra & Nagar Haveli"
        ],
        "Daman and Diu (UT)": [
            "Daman",
            "Diu"
        ],
        "Delhi (NCT)": [
            "Central Delhi",
            "East Delhi",
            "New Delhi",
            "North Delhi",
            "North East  Delhi",
            "North West  Delhi",
            "Shahdara",
            "South Delhi",
            "South East Delhi",
            "South West  Delhi",
            "West Delhi"
        ],
        "Goa": [
            "North Goa",
            "South Goa"
        ],
        "Gujarat": [
            "Ahmedabad",
            "Amreli",
            "Anand",
            "Aravalli",
            "Banaskantha (Palanpur)",
            "Bharuch",
            "Bhavnagar",
            "Botad",
            "Chhota Udepur",
            "Dahod",
            "Dangs (Ahwa)",
            "Devbhoomi Dwarka",
            "Gandhinagar",
            "Gir Somnath",
            "Jamnagar",
            "Junagadh",
            "Kachchh",
            "Kheda (Nadiad)",
            "Mahisagar",
            "Mehsana",
            "Morbi",
            "Narmada (Rajpipla)",
            "Navsari",
            "Panchmahal (Godhra)",
            "Patan",
            "Porbandar",
            "Rajkot",
            "Sabarkantha (Himmatnagar)",
            "Surat",
            "Surendranagar",
            "Tapi (Vyara)",
            "Vadodara",
            "Valsad"
        ],
        "Haryana": [
            "Ambala",
            "Bhiwani",
            "Charkhi Dadri",
            "Faridabad",
            "Fatehabad",
            "Gurgaon",
            "Hisar",
            "Jhajjar",
            "Jind",
            "Kaithal",
            "Karnal",
            "Kurukshetra",
            "Mahendragarh",
            "Mewat",
            "Palwal",
            "Panchkula",
            "Panipat",
            "Rewari",
            "Rohtak",
            "Sirsa",
            "Sonipat",
            "Yamunanagar"
        ],
        "Himachal Pradesh": [
            "Bilaspur",
            "Chamba",
            "Hamirpur",
            "Kangra",
            "Kinnaur",
            "Kullu",
            "Lahaul &amp; Spiti",
            "Mandi",
            "Shimla",
            "Sirmaur (Sirmour)",
            "Solan",
            "Una"
        ],
        "Jammu and Kashmir": [
            "Anantnag",
            "Bandipore",
            "Baramulla",
            "Budgam",
            "Doda",
            "Ganderbal",
            "Jammu",
            "Kargil",
            "Kathua",
            "Kishtwar",
            "Kulgam",
            "Kupwara",
            "Leh",
            "Poonch",
            "Pulwama",
            "Rajouri",
            "Ramban",
            "Reasi",
            "Samba",
            "Shopian",
            "Srinagar",
            "Udhampur"
        ],
        "Jharkhand": [
            "Bokaro",
            "Chatra",
            "Deoghar",
            "Dhanbad",
            "Dumka",
            "East Singhbhum",
            "Garhwa",
            "Giridih",
            "Godda",
            "Gumla",
            "Hazaribag",
            "Jamtara",
            "Khunti",
            "Koderma",
            "Latehar",
            "Lohardaga",
            "Pakur",
            "Palamu",
            "Ramgarh",
            "Ranchi",
            "Sahibganj",
            "Seraikela-Kharsawan",
            "Simdega",
            "West Singhbhum"
        ],
        "Karnataka": [
            "Bagalkot",
            "Ballari (Bellary)",
            "Belagavi (Belgaum)",
            "Bengaluru (Bangalore) Rural",
            "Bengaluru (Bangalore) Urban",
            "Bidar",
            "Chamarajanagar",
            "Chikballapur",
            "Chikkamagaluru (Chikmagalur)",
            "Chitradurga",
            "Dakshina Kannada",
            "Davangere",
            "Dharwad",
            "Gadag",
            "Hassan",
            "Haveri",
            "Kalaburagi (Gulbarga)",
            "Kodagu",
            "Kolar",
            "Koppal",
            "Mandya",
            "Mysuru (Mysore)",
            "Raichur",
            "Ramanagara",
            "Shivamogga (Shimoga)",
            "Tumakuru (Tumkur)",
            "Udupi",
            "Uttara Kannada (Karwar)",
            "Vijayapura (Bijapur)",
            "Yadgir"
        ],
        "Kerala": [
            "Alappuzha",
            "Ernakulam",
            "Idukki",
            "Kannur",
            "Kasaragod",
            "Kollam",
            "Kottayam",
            "Kozhikode",
            "Malappuram",
            "Palakkad",
            "Pathanamthitta",
            "Thiruvananthapuram",
            "Thrissur",
            "Wayanad"
        ],
        "Lakshadweep (UT)": [
            "Agatti",
            "Amini",
            "Androth",
            "Bithra",
            "Chethlath",
            "Kavaratti",
            "Kadmath",
            "Kalpeni",
            "Kilthan",
            "Minicoy"
        ],
        "Madhya Pradesh": [
            "Agar Malwa",
            "Alirajpur",
            "Anuppur",
            "Ashoknagar",
            "Balaghat",
            "Barwani",
            "Betul",
            "Bhind",
            "Bhopal",
            "Burhanpur",
            "Chhatarpur",
            "Chhindwara",
            "Damoh",
            "Datia",
            "Dewas",
            "Dhar",
            "Dindori",
            "Guna",
            "Gwalior",
            "Harda",
            "Hoshangabad",
            "Indore",
            "Jabalpur",
            "Jhabua",
            "Katni",
            "Khandwa",
            "Khargone",
            "Mandla",
            "Mandsaur",
            "Morena",
            "Narsinghpur",
            "Neemuch",
            "Panna",
            "Raisen",
            "Rajgarh",
            "Ratlam",
            "Rewa",
            "Sagar",
            "Satna",
            "Sehore",
            "Seoni",
            "Shahdol",
            "Shajapur",
            "Sheopur",
            "Shivpuri",
            "Sidhi",
            "Singrauli",
            "Tikamgarh",
            "Ujjain",
            "Umaria",
            "Vidisha"
        ],
        "Maharashtra": [
            "Ahmednagar",
            "Akola",
            "Amravati",
            "Aurangabad",
            "Beed",
            "Bhandara",
            "Buldhana",
            "Chandrapur",
            "Dhule",
            "Gadchiroli",
            "Gondia",
            "Hingoli",
            "Jalgaon",
            "Jalna",
            "Kolhapur",
            "Latur",
            "Mumbai City",
            "Mumbai Suburban",
            "Nagpur",
            "Nanded",
            "Nandurbar",
            "Nashik",
            "Osmanabad",
            "Palghar",
            "Parbhani",
            "Pune",
            "Raigad",
            "Ratnagiri",
            "Sangli",
            "Satara",
            "Sindhudurg",
            "Solapur",
            "Thane",
            "Wardha",
            "Washim",
            "Yavatmal"
        ],
        "Manipur": [
            "Bishnupur",
            "Chandel",
            "Churachandpur",
            "Imphal East",
            "Imphal West",
            "Jiribam",
            "Kakching",
            "Kamjong",
            "Kangpokpi",
            "Noney",
            "Pherzawl",
            "Senapati",
            "Tamenglong",
            "Tengnoupal",
            "Thoubal",
            "Ukhrul"
        ],
        "Meghalaya": [
            "East Garo Hills",
            "East Jaintia Hills",
            "East Khasi Hills",
            "North Garo Hills",
            "Ri Bhoi",
            "South Garo Hills",
            "South West Garo Hills ",
            "South West Khasi Hills",
            "West Garo Hills",
            "West Jaintia Hills",
            "West Khasi Hills"
        ],
        "Mizoram": [
            "Aizawl",
            "Champhai",
            "Kolasib",
            "Lawngtlai",
            "Lunglei",
            "Mamit",
            "Saiha",
            "Serchhip"
        ],
        "Nagaland": [
            "Dimapur",
            "Kiphire",
            "Kohima",
            "Longleng",
            "Mokokchung",
            "Mon",
            "Peren",
            "Phek",
            "Tuensang",
            "Wokha",
            "Zunheboto"
        ],
        "Odisha": [
            "Angul",
            "Balangir",
            "Balasore",
            "Bargarh",
            "Bhadrak",
            "Boudh",
            "Cuttack",
            "Deogarh",
            "Dhenkanal",
            "Gajapati",
            "Ganjam",
            "Jagatsinghapur",
            "Jajpur",
            "Jharsuguda",
            "Kalahandi",
            "Kandhamal",
            "Kendrapara",
            "Kendujhar (Keonjhar)",
            "Khordha",
            "Koraput",
            "Malkangiri",
            "Mayurbhanj",
            "Nabarangpur",
            "Nayagarh",
            "Nuapada",
            "Puri",
            "Rayagada",
            "Sambalpur",
            "Sonepur",
            "Sundargarh"
        ],
        "Puducherry (UT)": [
            "Karaikal",
            "Mahe",
            "Pondicherry",
            "Yanam"
        ],
        "Punjab": [
            "Amritsar",
            "Barnala",
            "Bathinda",
            "Faridkot",
            "Fatehgarh Sahib",
            "Fazilka",
            "Ferozepur",
            "Gurdaspur",
            "Hoshiarpur",
            "Jalandhar",
            "Kapurthala",
            "Ludhiana",
            "Mansa",
            "Moga",
            "Muktsar",
            "Nawanshahr (Shahid Bhagat Singh Nagar)",
            "Pathankot",
            "Patiala",
            "Rupnagar",
            "Sahibzada Ajit Singh Nagar (Mohali)",
            "Sangrur",
            "Tarn Taran"
        ],
        "Rajasthan": [
            "Ajmer",
            "Alwar",
            "Banswara",
            "Baran",
            "Barmer",
            "Bharatpur",
            "Bhilwara",
            "Bikaner",
            "Bundi",
            "Chittorgarh",
            "Churu",
            "Dausa",
            "Dholpur",
            "Dungarpur",
            "Hanumangarh",
            "Jaipur",
            "Jaisalmer",
            "Jalore",
            "Jhalawar",
            "Jhunjhunu",
            "Jodhpur",
            "Karauli",
            "Kota",
            "Nagaur",
            "Pali",
            "Pratapgarh",
            "Rajsamand",
            "Sawai Madhopur",
            "Sikar",
            "Sirohi",
            "Sri Ganganagar",
            "Tonk",
            "Udaipur"
        ],
        "Sikkim": [
            "East Sikkim",
            "North Sikkim",
            "South Sikkim",
            "West Sikkim"
        ],
        "Tamil Nadu": [
            "Ariyalur",
            "Chennai",
            "Coimbatore",
            "Cuddalore",
            "Dharmapuri",
            "Dindigul",
            "Erode",
            "Kanchipuram",
            "Kanyakumari",
            "Karur",
            "Krishnagiri",
            "Madurai",
            "Nagapattinam",
            "Namakkal",
            "Nilgiris",
            "Perambalur",
            "Pudukkottai",
            "Ramanathapuram",
            "Salem",
            "Sivaganga",
            "Thanjavur",
            "Theni",
            "Thoothukudi (Tuticorin)",
            "Tiruchirappalli",
            "Tirunelveli",
            "Tiruppur",
            "Tiruvallur",
            "Tiruvannamalai",
            "Tiruvarur",
            "Vellore",
            "Viluppuram",
            "Virudhunagar"
        ],
        "Telangana": [
            "Adilabad",
            "Bhadradri Kothagudem",
            "Hyderabad",
            "Jagtial",
            "Jangaon",
            "Jayashankar Bhoopalpally",
            "Jogulamba Gadwal",
            "Kamareddy",
            "Karimnagar",
            "Khammam",
            "Komaram Bheem Asifabad",
            "Mahabubabad",
            "Mahabubnagar",
            "Mancherial",
            "Medak",
            "Medchal",
            "Nagarkurnool",
            "Nalgonda",
            "Nirmal",
            "Nizamabad",
            "Peddapalli",
            "Rajanna Sircilla",
            "Rangareddy",
            "Sangareddy",
            "Siddipet",
            "Suryapet",
            "Vikarabad",
            "Wanaparthy",
            "Warangal (Rural)",
            "Warangal (Urban)",
            "Yadadri Bhuvanagiri"
        ],
        "Tripura": [
            "Dhalai",
            "Gomati",
            "Khowai",
            "North Tripura",
            "Sepahijala",
            "South Tripura",
            "Unakoti",
            "West Tripura"
        ],
        "Uttarakhand": [
            "Almora",
            "Bageshwar",
            "Chamoli",
            "Champawat",
            "Dehradun",
            "Haridwar",
            "Nainital",
            "Pauri Garhwal",
            "Pithoragarh",
            "Rudraprayag",
            "Tehri Garhwal",
            "Udham Singh Nagar",
            "Uttarkashi"
        ],
        "Uttar Pradesh": [
            "Agra",
            "Aligarh",
            "Allahabad",
            "Ambedkar Nagar",
            "Amethi (Chatrapati Sahuji Mahraj Nagar)",
            "Amroha (J.P. Nagar)",
            "Auraiya",
            "Azamgarh",
            "Baghpat",
            "Bahraich",
            "Ballia",
            "Balrampur",
            "Banda",
            "Barabanki",
            "Bareilly",
            "Basti",
            "Bhadohi",
            "Bijnor",
            "Budaun",
            "Bulandshahr",
            "Chandauli",
            "Chitrakoot",
            "Deoria",
            "Etah",
            "Etawah",
            "Faizabad",
            "Farrukhabad",
            "Fatehpur",
            "Firozabad",
            "Gautam Buddha Nagar",
            "Ghaziabad",
            "Ghazipur",
            "Gonda",
            "Gorakhpur",
            "Hamirpur",
            "Hapur (Panchsheel Nagar)",
            "Hardoi",
            "Hathras",
            "Jalaun",
            "Jaunpur",
            "Jhansi",
            "Kannauj",
            "Kanpur Dehat",
            "Kanpur Nagar",
            "Kanshiram Nagar (Kasganj)",
            "Kaushambi",
            "Kushinagar (Padrauna)",
            "Lakhimpur - Kheri",
            "Lalitpur",
            "Lucknow",
            "Maharajganj",
            "Mahoba",
            "Mainpuri",
            "Mathura",
            "Mau",
            "Meerut",
            "Mirzapur",
            "Moradabad",
            "Muzaffarnagar",
            "Pilibhit",
            "Pratapgarh",
            "RaeBareli",
            "Rampur",
            "Saharanpur",
            "Sambhal (Bhim Nagar)",
            "Sant Kabir Nagar",
            "Shahjahanpur",
            "Shamali (Prabuddh Nagar)",
            "Shravasti",
            "Siddharth Nagar",
            "Sitapur",
            "Sonbhadra",
            "Sultanpur",
            "Unnao",
            "Varanasi"
        ],
        "West Bengal": [
            "Alipurduar",
            "Bankura",
            "Birbhum",
            "Burdwan (Bardhaman)",
            "Cooch Behar",
            "Dakshin Dinajpur (South Dinajpur)",
            "Darjeeling",
            "Hooghly",
            "Howrah",
            "Jalpaiguri",
            "Kalimpong",
            "Kolkata",
            "Malda",
            "Murshidabad",
            "Nadia",
            "North 24 Parganas",
            "Paschim Medinipur (West Medinipur)",
            "Purba Medinipur (East Medinipur)",
            "Purulia",
            "South 24 Parganas",
            "Uttar Dinajpur (North Dinajpur)"
        ]

    }

    // Change State and District Function
    for (var state in stateObjectNew) {
        stateSel.options[stateSel.options.length] = new Option(state, state);
    }
    stateSel.onchange = function () {
        districtSel.length = 1; // remove all options bar first
        if (this.selectedIndex < 1) return; // done
        var district = stateObjectNew[stateSel.value];
        for (var i = 0; i < district.length; i++) {
            districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
        }
    }
    // stateSel.onchange(); // reset in case page is reloaded
    stateSel.onload = function () {
        districtSel.length = 1; // remove all options bar first
        if (this.selectedIndex < 1) return; // done
        var district = stateObjectNew[stateSel.value];
        for (var i = 0; i < district.length; i++) {
            districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
        }
    }
    stateSel.onload();

    var levelSelect = document.getElementById("level");
    var syllabusSelect = document.getElementById("syllabus");
    var gradeSelect = document.getElementById("grade");
    var subjectSelect = document.getElementById("subject");
    var publisherSelect = document.getElementById("publisher");

    var levelTags = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
    var syllabusTags = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
    var gradeTags = JSON.parse("${session["activeGrades_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
    var subjectTags = JSON.parse("${session["activeSubjects_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
    var publisherTags = JSON.parse("${publishers}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));


    function updateFilters(){

        levelTags.map(level=>{
            levelSelect.innerHTML += "<option value='"+level.level+"'>"+level.level+"</option>";
        })
        publisherTags.map(publisher=>{
            publisherSelect.innerHTML +="<option value='"+publisher.publisher+"'>"+publisher.publisher+"</option>";
        })
    }
    updateFilters();
    levelSelect.addEventListener('change',function (e){
        var syllabusTemp = [];
        var gradeTemp = [];
        var subjectTemp = [];
        syllabusSelect.innerHTML = "";
        gradeSelect.innerHTML = "";
        subjectSelect.innerHTML = "";

        syllabusTags.map(syllabus=>{
            if (e.target.value == syllabus.level){
                syllabusTemp.push(syllabus)
            }else if (e.target.value==""){
                syllabusTemp.push(syllabus)
            }
        })

        syllabusTemp.map(syllabus=>{
            syllabusSelect.innerHTML +="<option value='"+syllabus.syllabus+"'>"+syllabus.syllabus+"</option>";
        })

        gradeTags.map(grade=>{
            if (e.target.value == grade.level && syllabusSelect.value == grade.syllabus){
                gradeTemp.push(grade)
            }else if (e.target.value==""){
                gradeTemp.push(grade)
            }
        })

        gradeTemp.map(grade=>{
            gradeSelect.innerHTML +="<option value='"+grade.grade+"'>"+grade.grade+"</option>";
        })

        subjectTags.map(subject=>{
            if (e.target.value == subject.level && syllabusSelect.value == subject.syllabus && gradeSelect.value == subject.grade){
                subjectTemp.push(subject)
            }else if (e.target.value==""){
                subjectTemp.push(subject)
            }
        })


        subjectTemp.map(subject=>{
            subjectSelect.innerHTML +="<option value='"+subject.subject+"'>"+subject.subject+"</option>";
        })
    })

    syllabusSelect.addEventListener('change',function (e){
        var gradeTemp = [];
        var subjectTemp = [];
        gradeSelect.innerHTML = "";
        subjectSelect.innerHTML = "";

        gradeTags.map(grade=>{
            if (e.target.value == grade.syllabus){
                gradeTemp.push(grade)
            }else if (e.target.value==""){
                gradeTemp.push(grade)
            }
        })

        subjectTags.map(subject=>{
            if (e.target.value == subject.syllabus){
                subjectTemp.push(subject)
            }else if (e.target.value==""){
                subjectTemp.push(subject)
            }
        })
        gradeTemp.map(grade=>{
            gradeSelect.innerHTML +="<option value='"+grade.grade+"'>"+grade.grade+"</option>";
        })
        subjectTemp.map(subject=>{
            subjectSelect.innerHTML +="<option value='"+subject.subject+"'>"+subject.subject+"</option>";
        })
    })

    gradeSelect.addEventListener('change',function (e){
        var subjectTemp = [];
        subjectSelect.innerHTML = "";
        subjectTags.map(subject=>{
            if (e.target.value == subject.grade){
                subjectTemp.push(subject)
            }else if (e.target.value==""){
                subjectTemp.push(subject)
            }
        })
        subjectTemp.map(subject=>{
            subjectSelect.innerHTML +="<option value='"+subject.subject+"'>"+subject.subject+"</option>";
        })
    })

    var teachName = document.getElementById('uname');
    var teachEmail = document.getElementById('qemail');
    var teachPhone = document.getElementById('phone');
    var teachDOB = document.getElementById('dob');
    var teachInst = document.getElementById('company');
    var teachAddress = document.getElementById('address');
    var teachPincode = document.getElementById('pincode');
    var teachCountry = document.getElementById('country');
    var teachState = document.getElementById('state');
    var teachDist = document.getElementById('district');
    var teachPub = document.getElementById('publisher');
    var teachLevel = document.getElementById('level');
    var teachSyllabus = document.getElementById('syllabus');
    var teachGrade = document.getElementById('grade');
    var teachSubject = document.getElementById('subject');
    var teachPhotoID = document.getElementById('photoId');

    var contactForm = document.getElementById('contactForm');


    contactForm.addEventListener('submit',function (e){
        $(".loading-icon").removeClass("hidden");
        e.preventDefault();
        var formData = new FormData();
        var files = $("#photoId")[0].files;

        var currentDate = new Date();
        var options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        };

        var currentDateTimeString = currentDate.toLocaleString('en-US', options);
        currentDateTimeString = currentDateTimeString.replaceAll('/','-').replace(',','');
        var teacherDetails = {
            teachName: teachName.value,
            teachEmail : teachEmail.value,
            teachPhone : teachPhone.value,
            teachDOB : teachDOB.value,
            teachInst : teachInst.value,
            teachAddress : teachAddress.value,
            teachPincode : teachPincode.value,
            teachCountry : teachCountry.value,
            teachState : teachState.value,
            teachDist : teachDist.value,
            teachPub : teachPub.value,
            teachLevel : teachLevel.value,
            teachSyllabus : teachSyllabus.value,
            teachGrade : teachGrade.value,
            teachSubject : teachSubject.value,
            dateCreated:currentDateTimeString
        }

        teacherDetails = JSON.stringify(teacherDetails);

        $.ajax({
            type: "POST",
            url: '/usermanagement/submitSpecimenCopyReq',
            dataType: 'json',
            contentType : "application/json",
            data:  teacherDetails,
            success: function(data) {
                if(files.length > 0){
                    addTeacherImage(data)
                }
            }
        });

        function addTeacherImage(data){
            if(files.length > 0) {
                formData.append('file',files[0]);
                formData.append('id', data.id);
                $.ajax({
                    type: "POST",
                    url: '/usermanagement/addTeacherID',
                    type: 'POST',
                    cache: false,
                    contentType: false,
                    processData: false,
                    data: formData,
                    success: function(data) {
                        $(".loading-icon").addClass("hidden");
                        $('#report-success').modal('show');
                        teachName.value = ""
                        teachEmail.value = ""
                        teachPhone.value = ""
                        teachDOB.value = ""
                        teachInst.value = ""
                        teachAddress.value = ""
                        teachPincode.value = ""
                        teachCountry.value = ""
                        teachState.value = ""
                        teachDist.value = ""
                        teachPub.value = ""
                        teachLevel.value = ""
                        teachPhotoID.value = ""
                        gradeSelect.innerHTML = " <option value=''>Select Grade</option>";
                        subjectSelect.innerHTML = "<option value=''>Select Subject</option>";
                        syllabusSelect.innerHTML = "<option value=''>Select Syllabus</option>";

                    }
                });
            }
        }
    })
</script>















