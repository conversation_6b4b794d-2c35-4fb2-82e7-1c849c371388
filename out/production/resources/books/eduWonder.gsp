<head>
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <link rel="android-touch-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}"/>
    <link rel="windows-touch-icon" href="icon.png" />
</head>
<g:render template="/wonderpublish/loginChecker"></g:render>
<div class="d-none">
    <g:render template="/books/navheader_new"></g:render>
</div>

<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css" />


<asset:stylesheet href="landingpage/eduWonder.css" async="true"/>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<style>
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
/* Firefox */
input[type=number] {
    -moz-appearance: textfield;
}
</style>

<div class="digital-library">

    <header class="edw-header">
        <div class="container">
            <div class="logo text-center">
                <a href="eduWonder">
                    <img src="${assetPath(src: 'eduwonder/eduWonder-logo.svg')}" class="logo-img">
                </a>
            </div>
        </div>
    </header>

    <section class="main-section page-main-wrapper mdl-js pt-0">
        <div class="py-4 py-md-5  mt-3 mt-md-0">
            <div class="container ">
                <div class="row align-items-center">
                    <div class="col-12 col-lg-6">
                        <h1 data-aos="fade-down"
                            class="edw-banner__header offer">Ed-Tech platform for Institutions</h1>
                        <div class="edw-form__section mt-4" data-aos="zoom-in-up">
                            <h5>Access 7-Days Free Trial for School Solutions</h5>
                            <form id="schoolForm">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Enter Name" id="name">
                                </div>
                                <div class="form-group">
                                    <input type="number" class="form-control" placeholder="Mobile Number" id="mobNumber" maxlength="10" oninput="numberOnly(this.id)">
                                </div>
                                <div class="form-group">
                                    <input type="email" class="form-control" placeholder="Email" id="emailId">
                                </div>
                                <div class="form-group">
                                    <select class="form-control" id="role">
                                        <option value="">Role at school</option>
                                        <option value="owner">School Owner</option>
                                        <option value="principal">Principal</option>
                                        <option value="teacher">Teacher</option>
                                        <option value="trustee">Trustee</option>
                                        <option value="director">Director</option>
                                        <option value="admin">Admin</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="School Name" id="schlName">
                                </div>
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="City" id="city">
                                </div>
                                <div class="alert-email border-0 mb-0 mt-2 p-0" style="display: none;font-size: 15px;color:red">Please enter all Fields!</div>
                                <button class="btn btn-join btn-block" id="joinBtn">Join</button>
                            </form>
                        </div>
                    </div>
                    <div class="col-12 col-lg-6 text-center">
                        <img src="${assetPath(src: 'wslibrary/banner-image.svg')}" class="banner-img">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="edw-descriptive__banners">
        <div class="container">
            <div class="edw-descriptive__banners-header">
                <h2>360 degree solutions for your Institution</h2>
                <p>EduWonder offers end-to-end learning solutions for schools.</p>
            </div>
            <div class="edw-all__banners edw-banner__one mb-3 even mt-3" data-aos="fade-down">
                <div class="d-flex justify-content-around align-items-center edw-banner-wrapper">
                    <div class="banner-content__wrapper">
                        <h2>Curriculum based Smart Content</h2>
                        <p>Study Guides, Textbooks, Resource & Reference Books from Bestselling Publishers</p>
                    </div>
                    <div class="">
                        <img src="${assetPath(src: 'eduwonder/img-1.png')}">
                    </div>
                </div>
            </div>
            <div class="edw-all__banners edw-banner__two mb-3 odd" data-aos="fade-up">
                <div class="d-flex justify-content-around align-items-center edw-banner-wrapper">
                    <div class="banner-content__wrapper">
                        <h2>Integrated Learning</h2>
                        <p>Offline teaching and online practice coming together for best results.</p>
                    </div>
                    <div class="">
                        <img src="${assetPath(src: 'eduwonder/img-2.png')}">
                    </div>
                </div>
            </div>
            <div class="edw-all__banners edw-banner__three mb-3 even" data-aos="fade-down">
                <div class="d-flex justify-content-around align-items-center edw-banner-wrapper">
                    <div class="banner-content__wrapper">
                        <h2>Super charging Teachers</h2>
                        <p>Teacher's autonomy & flexibility. Increase in knowledge depth.</p>
                    </div>
                    <div class="">
                        <img src="${assetPath(src: 'eduwonder/img-3.png')}">
                    </div>
                </div>
            </div>
            <div class="edw-all__banners edw-banner__four mb-3 odd" data-aos="fade-up">
                <div class="d-flex justify-content-around align-items-center edw-banner-wrapper">
                    <div class="banner-content__wrapper">
                        <h2>Institution branding</h2>
                        <p>Access to the platform via your website branded with your Institution name and logo.</p>
                    </div>
                    <div class="">
                        <img src="${assetPath(src: 'eduwonder/img-4.png')}">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="features">
        <div class="container">
            <h2 class="text-center title-name-1" data-aos="fade-up"
                data-aos-easing="linear"
                data-aos-duration="300">Features</h2>
            <div class="row">
                <div class="col-12 col-md-6 col-lg-4" data-aos="fade-right">
                    <div class="card">
                        <img src="${assetPath(src: 'wslibrary/access.svg')}" data-aos="zoom-in">
                        <div class="card-body">
                            <h4 class="card-title">Organized classroom</h4>
                            <p class="card-text">Manage and facilitate two-way communication during classes effectively and efficiently</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-4" data-aos="fade-down">
                    <div class="card">
                        <img src="${assetPath(src: 'wslibrary/flashcard.svg')}">
                        <div class="card-body">
                            <h4 class="card-title">Immersive learning resources</h4>
                            <p class="card-text">Reading materials, Videos, Flashcards, MCQs, Audio books and more</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-4" data-aos="fade-left">
                    <div class="card">
                        <img src="${assetPath(src: 'wslibrary/askdoubts.svg')}">
                        <div class="card-body">
                            <h4 class="card-title">Doubts Center</h4>
                            <p class="card-text">Place for students to ask all their questions and get solved by teachers and fellow students.</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-4" data-aos="fade-right">
                    <div class="card">
                        <img src="${assetPath(src: 'wslibrary/audiovideo.svg')}">
                        <div class="card-body">
                            <h4 class="card-title">External and internal content repository</h4>
                            <p class="card-text">Along with the smart content from publishers create and use your own smart content</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-4" data-aos="fade-up">
                    <div class="card">
                        <img src="${assetPath(src: 'wslibrary/notes.svg')}">
                        <div class="card-body">
                            <h4 class="card-title">AI based quiz and test generator</h4>
                            <p class="card-text">Create as many tests using our proprietary AI enabled test generator.</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-4" data-aos="fade-left">
                    <div class="card">
                        <img src="${assetPath(src: 'wslibrary/report.svg')}">
                        <div class="card-body">
                            <h4 class="card-title">Detailed usage reports</h4>
                            <p class="card-text">Understand and analyse your students at a glance.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </section>
    <section class="setup">
        <h1 class="setup-simple" data-aos="fade-down"
            data-aos-easing="linear"
            data-aos-duration="300">Setup in just 3 simple steps!</h1>
        <div class="container d-flex align-items-center pb-4" style="min-height: 300px;">

            <div class="row">
                <div class="col-12 col-lg-4">
                    <div class="card" data-aos="zoom-in">
                        <div class="card-body">
                            <div class="circle bg-blue">
                                <h2>1</h2>
                            </div>
                            <h4 class="card-title">Register</h4>
                            <p class="card-text">Register Your Institution (& Create Your Library)</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-4">
                    <div class="card" data-aos="zoom-in-up">
                        <div class="card-body">
                            <div class="circle bg-thickBlue">
                                <h2>2</h2>
                            </div>
                            <h4 class="card-title">Customise</h4>
                            <p class="card-text">As Per Your Needs (Put your school logo, colours, eBooks you need from the standard package)</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-4">
                    <div class="card" data-aos="zoom-in-down">
                        <div class="card-body">
                            <div class="circle bg-pink">
                                <h2>3</h2>
                            </div>
                            <h4 class="card-title">Add Students</h4>
                            <p class="card-text">Start Adding Students (Multiple students at the same time, via file, SSO etc.)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="price-layer">
        <div class="container">
            <div class="row">
                <div class="col-12 col-lg-6 p-0">
                    <div class="card whiteBg" data-aos="zoom-in">
                        <h1>Why you will love
                            <span>EduWonder ?</span></h1>
                        <img src="${assetPath(src: 'wslibrary/digi-lib.svg')}">
                    </div>
                </div>
                <div class="col-12 col-lg-6 p-0">
                    <div class="card pink-bg d-flex align-items-center" data-aos="zoom-in">
                        <ul>
                            <li>Simplicity <span>– it’s so easy to use</span></li>
                            <li> Low cost</li>
                            <li>  Customise <span>for your school's branding</span></li>
                            <li>  eBook shopping <span>& smart selection tools</span></li>
                            <li>   <span>Detailed</span> usage reports</li>
                            <li>  Easy Administration</li>
                            <li> Full technical support</li>
                            <li> Compatible with <span>your LMS</span></li>
                            <li> Easily understood <span>by students and staff</span></li>
                            <li> Intuitive<span> mobile apps (iOS & Android)</span></li>
                            <li> <span>Thousands of </span> free eBooks</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="brands">
        <div class="container">
            <h2 class="text-center title-name">Our Content Partners</h2>
            <div class="brandlogos">
                <img src="${assetPath(src: 'eduwonder/arihantlogo.png')}">
                <img src="${assetPath(src: 'eduwonder/oswaallogo.png')}">
                <img src="${assetPath(src: 'eduwonder/univlogo.png')}">
                <img src="${assetPath(src: 'eduwonder/gplogo.png')}" style="width: 250px">
                <img src="${assetPath(src: 'eduwonder/nirali.png')}">
            </div>
            <div class="brandList-mob">
                <img src="${assetPath(src: 'eduwonder/pub-list-mob.svg')}">
            </div>
        </div>
    </section>
    <section class="brands">
        <div class="container">
            <h2 class="text-center title-name">Our Customers</h2>
            <div class="brandlogos mt-4 mb-4">
                <img src="${assetPath(src: 'eduwonder/bvdulogo.png')}" style="width: 150px !important;">
                <img src="${assetPath(src: 'eduwonder/RNTULogo.png')}" style="width: 260px !important;">
                <img src="${assetPath(src: 'eduwonder/sliet-logo.png')}" style="width: 100px !important;">
                <img src="${assetPath(src: 'eduwonder/siiet-logo.png')}" style="width: 250px">
                <img src="${assetPath(src: 'eduwonder/mpclg-logo.png')}" style="width: 100px;">
            </div>
            <div class="brandList-mob">
                <img src="${assetPath(src: 'eduwonder/cus-mob-logo.svg')}">
            </div>
        </div>
    </section>
</div>

<footer class="digital-library-footer">

    <div class="container pt-8">

        <div class="row align-items-center">
            <div class="col-12 col-lg-6 d-flex justify-content-center justify-content-lg-end">
                <div class="wsborder d-flex align-items-center">
                    <a href="/books/index">
                        <img class="footer-logo" src="${assetPath(src: 'eduwonder/logo.svg')}" style="width: 190px;">
                    </a>
                </div>
            </div>
            <div class="col-12 col-lg-6 d-flex justify-content-center justify-content-lg-start">
                <div class="contact-wrapper">
                    <h5 class="contactus">Contact us</h5>
                    <div class="d-flex flex-column">
                        <div class="d-flex align-items-center">
                            <p class="mobile-no">Phone :</p>
                            <p class="mobile-no">+91-6363371085</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <p class="mobile-no">Email :</p>
                            <p class="mobile-no">&nbsp;<EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</footer>

<div class="hidden-footer">
    <g:render template="/books/footer_new"></g:render>
</div>


<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.6/highlight.min.js"></script>

<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
    AOS.init({
        easing: 'ease-out-back',
        duration: 1400
    });
</script>

<script>



    $("#joinBtn").on("click",function (e){
        e.preventDefault();
        var name = document.getElementById("name").value;
        var mobNumber = document.getElementById("mobNumber").value;
        var role = document.getElementById("role").value;
        var schoolName = document.getElementById("schlName").value;
        var city =  document.getElementById("city").value;
        var email =  document.getElementById("emailId").value;
        var comment="EduWonder Registration"

        if (name !="" && mobNumber !="" && role !="" && schoolName!="" && city!="" && emailId!=""){
            console.log("Valid")
            swal({
                title: "Success!",
                text: "Your information submitted successfully.",
                type: "success",
                allowOutsideClick: false,
                showConfirmButton: true,
                showCancelButton: false,
                confirmButtonColor: "#27AE60",
                confirmButtonText: "Ok",
                cancelButtonText: "Cancel",
                closeOnConfirm: true,
                closeOnCancel: false,
                allowEscapeKey: false,
                customClass: '',
            },function (){
                <g:remoteFunction controller="log" action="addEnquiryFormRecord"  onSuccess='enquired(data);'
              params="'name='+name+'&email='+email+'&schoolName='+schoolName+'&mobile='+mobNumber+'&comment='+comment"/>
            });

        }else{
            $('.alert-email').show();
        }

    })
    function enquired(data){
        $("#schoolForm").trigger("reset");
    }

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }
    $(document).on('keypress','#mobNumber,#name,#role,schlName,#city',function(e){
        $('.alert-email').hide();
    })
    $(document).on('keypress','#mobNumber',function(e){
        if($(e.target).prop('value').length>=10){
            if(e.keyCode!=32) {
                return false
            }
        }
    })
</script>
</body>
</html>
