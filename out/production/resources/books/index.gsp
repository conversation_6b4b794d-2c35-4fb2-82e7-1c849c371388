<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/landingPageUI.css"/>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<section class="landingPage">
    <div class="landingPage_container ">
        <!--- SECTION - 1 SEARCH ---->
        <div class="lp_hero">

            <div class="lp_hero-search">
                <div class="titleDiv">
                    <h2 class="text-start">
                        Let's find <span>your book</span>
                    </h2>
                </div>
                <div class="lp_hero-search_wrapper">
                    <div class="lp_hero-search_inputs">
                        <div class="lp_hero-search_input">
                            <div>
                                <input type="text" class="typeahead" name="search" id="search-book-page" autocomplete="off" placeholder="Search title, subject, author, ISBN, language etc.">
                                <button class="lp_hero-search_input-closeBtn d-none" id="closeSearchBtn"><i class="material-icons-round search-close-icon">close</i></button>
                            </div>

                        </div>
                        <button onclick="submitSearchLP();">Search</button>
                    </div>
                    <p class="text-center" style="margin-top: 8px;color: rgba(0, 0, 0, 0.3);">
                        <a href="/ebooks?linkSource=landingpage&level=School" style="color: rgba(0, 0, 0, 0.3);">School</a>
                        |
                        <a href="/ebooks?linkSource=landingpage&level=College" style="color: rgba(0, 0, 0, 0.3);">College</a>
                        |
                        <a href="/ebooks?linkSource=landingpage&level=Medical-Entrances&syllabus=NEET" style="color: rgba(0, 0, 0, 0.3);">NEET</a>
                        |
                        <a href="/ebooks?linkSource=landingpage&level=Engineering-Entrances&syllabus=JEE&" style="color: rgba(0, 0, 0, 0.3);">JEE</a>
                        |
                        <a href="/ebooks?linkSource=landingpage&level=Competitive-Exams" style="color: rgba(0, 0, 0, 0.3);">Entrance Exams etc...</a>
                    </p>
                </div>
            </div>
            <!---- SECTION - 2 STORE, BOOKS ---->
            <div class="lp_store mt-4">
                <div class="lp_store-cards">
                    <a class="lp_store-cards_card" href="/books/store">
                        <img src="${assetPath(src: 'products/cart-2.png')}" >
                        <p>Book Store</p>
                    </a>

                    <sec:ifLoggedIn>
                        <a class="lp_store-cards_card" href="/wsLibrary/myLibrary">
                            <img src="${assetPath(src: 'products/librarry-1.png')}">
                            <p>My Books <br> <span>(${noOfBooksInLibrary} Test series & Books)</span></p>
                        </a>
                    </sec:ifLoggedIn>
                    <sec:ifNotLoggedIn>
                        <a class="lp_store-cards_card" href="javascript:loginOpen();">
                            <img src="${assetPath(src: 'products/librarry-1.png')}">
                            <p>My Books <br> <span>Login to Access</span></p>
                        </a>
                    </sec:ifNotLoggedIn>
                </div>
            </div>
        </div>

        <!---- SECTION - 3 PRODUCTS ---->
        <div class="lp_products">
            <h2 class="text-center">Products for</h2>
            <div class="lp_products-cards">
                <a class="lp_products-cards_card" href="/books/publishersProduct">
                    <img src="${assetPath(src: 'products/publisher-1.png')}">
                    <p class="text-center">Publishers</p>
                </a>
                <a class="lp_products-cards_card" href="/books/teachersProduct">
                    <img src="${assetPath(src: 'products/teacher-1.png')}">
                    <p class="text-center">Teachers</p>
                </a>
                <a class="lp_products-cards_card" href="/books/schoolProducts">
                    <img src="${assetPath(src: 'products/college-1.png')}">
                    <p class="text-center">Schools & Colleges</p>
                </a>
            </div>
        </div>
    </div>
</section>




<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<g:render template="/books/footer_new"></g:render>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<script>
    var searchCloseBtn = document.getElementById('closeSearchBtn');
    var searchInputField = document.querySelector('#search-book-page');

    $(document).on("keypress", "#search-book-page", function(e) {
        if( e.which === 32 && this.value === '' ) {
            return false;
        } else if (e.which == 13) {
            if (e.keyCode == 13) {
                // to prevent submitting of the page
                submitSearchLP();
            }
        }
    });

    $('#search-book-page').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearchLP();
        }
    });

    function submitSearchLP(){
        var searchString = searchInputField.value
        var requestList = "";

        if(searchString.length == 0) {

        } else {
            $('ul.typeahead').css('display','none');
            <%if("true".equals(session["prepjoySite"])){%>
            $('#loading').show();
            <%}else{%>
            $('.loading-icon').removeClass('hidden');
            <%}%>
            if (typeof submitSearchTop === 'function') {
                submitSearchTop();
                $('html, body').animate({scrollTop: $('.searching-book-store').offset().top - 77 }, 'slow');
            }else{
                if(searchStringValues!=null&&searchStringValues!=""){
                    //use the filters
                    for(var i=0;i<searchStringValues.length;i++){
                        if(searchStringValues[i]==searchString){
                            var searchData = searchDataValues[i];
                            //callRemoteSearch = populateFromFilters(searchDataValues[i]);

                            if(searchData.hasOwnProperty("publisherId")){
                                requestList += "&publisherId="+searchData.publisherId;
                            }
                            if(searchData.hasOwnProperty("level")){
                                requestList += "&level="+searchData.level;
                            }
                            if(searchData.hasOwnProperty("syllabus")){
                                requestList += "&syllabus="+searchData.syllabus;
                            }
                            if(searchData.hasOwnProperty("grade")){
                                requestList += "&grade="+searchData.grade;
                            }
                            if(searchData.hasOwnProperty("subject")){
                                requestList += "&subject="+searchData.subject;
                            }
                            break
                        }
                    }
                }
                <%if("true".equals(session["prepjoySite"])){%>
                if (siteNamesPrep == 'prepjoycurrentaffairs'){
                    siteNamesPrep = 'currentaffairs'
                }
                window.location.href="/"+siteNamesPrep+"/eBooks?searchString="+encodeURIComponent(searchString)+requestList;
                <%}else if("true".equals(session["commonWhiteLabel"])){%>
                window.location.href="/sp/<%= session["siteName"] %>/store?searchString="+encodeURIComponent(searchString)+requestList;
                <%}else{%>
                window.location.href="/<%= session["entryController"] %>/store?searchString="+encodeURIComponent(searchString)+requestList;
                <%}%>
            }
        }
    }

    searchCloseBtn.addEventListener('click',function (){
        searchInputField.value = "";
        searchCloseBtn.classList.add('d-none');
    })

    searchInputField.addEventListener('input',function (e){
        if (searchInputField.value.length >= 1){
            searchCloseBtn.classList.remove('d-none');
        }else{
            searchCloseBtn.classList.add('d-none');
        }
    })
</script>
</body>
</html>