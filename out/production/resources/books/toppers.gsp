<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/toppers.css" async="true"/>
<div class="loading-icon">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section id="student-problems-section" class="topper">
    <div class="container hero-text">
        <h1>Toppers’ Secrets</h1>
        <p class="text-center text-md-left">Do you want to learn how to improve your scores and learning outcomes?
        You have come to the right place.
        Topper’s secrets give you information about different studying techniques, tricks to remember important
        points and secrets of the toppers success from their experience.</p>
    </div>
    <div class="container features">
        <ul class="row d-flex justify-content-start justify-content-xs-start flex-md-nowrap pr-2">
            <a href='https://blog.wonderslate.com/category/toppers-secrets${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
                <img src="${assetPath(src: 'ws/study-tech.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">Study Techniques</h3>
                </span>
            </li></a>
            <a href='https://blog.wonderslate.com/category/toppers-secrets${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
                <img src="${assetPath(src: 'ws/study-trick.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">Study Tricks</h3>
                </span>
            </li></a>
            <a href='https://blog.wonderslate.com/category/toppers-secrets${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
                <img src="${assetPath(src: 'ws/success-story.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">Success Stories</h3>
                </span>
            </li></a>
        </ul>
    </div>
    <g:render template="/discussion/discussionBoardTemplate" model="[parentPageFilter:'page_toppers']"></g:render>
</section>
<asset:javascript src="sharer.min.js"/>
<asset:javascript src="landingpage/slick.js"/>
<script>
    function slickInitialize() {
        if (window.matchMedia("(min-width:768px)").matches) {
            $('.posts-scroller').slick({
                slidesToShow: 3,
                slidesToScroll: 3,
                arrows: true,
                infinite: false,
                prevArrow: '.posts-arrows .left-arrow',
                nextArrow: '.posts-arrows .right-arrow'

            });

        }
        window.addEventListener('resize', () => {
            if (window.matchMedia("(min-width:768px)").matches) {

                $(".posts-scroller").not('.slick-initialized').slick({
                    slidesToShow: 3,
                    slidesToScroll: 3,
                    arrows: true,
                    infinite: false,
                    prevArrow: '.posts-arrows .left-arrow',
                    nextArrow: '.posts-arrows .right-arrow'

                });

            } else {
                if($('.posts-scroller').hasClass('slick-initialized')) {
                    $('.posts-scroller').slick('unslick');
                }
            }
        })
    }

    $(document).ready(function(){
        slickInitialize()

        // When document is loaded this will based on the size of the device remove the d-none class from back button [ used for history ]
        if($(window).width() < 767){
            $('.backMenu').removeClass('d-none');
        }
    });
</script>

