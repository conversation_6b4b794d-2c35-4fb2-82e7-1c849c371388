<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>

<!doctype html>
<html lang="en" >
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>${title}</title>
    <meta name="keywords" content="${keywords}">
    <meta name="description" content="Wonderslate is an effort to bring syllabus specific educational content to all.Engaging parents, teachers and students to create and share content. Be it mind maps, videos , quiz, solved question paper etc. CBSE,ICSE,state boards etc.">
    <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1, minimum-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <link rel="icon" sizes="192x192" href="${assetPath(src: 'app-icon-4x.png')}">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" href="${assetPath(src: 'favicon.ico')}" type="image/x-icon" />

    <link href='https://fonts.googleapis.com/css?family=Exo' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Roboto Slab:300,400,700' rel='stylesheet' type='text/css'>

    <asset:stylesheet href="hopscotch-0.1.1.css"/>
    <asset:stylesheet href="bootstrap.css"/>
    <asset:stylesheet href="flat-ui.css"/>
    <asset:stylesheet href="demo.css"/>
    <asset:stylesheet href="style.css"/>
    <asset:stylesheet href="font-awesome.min.css"/>
    <asset:stylesheet href="clock.css"/>
    <ckeditor:resources />
</head>
<%
    String url = (request.getRequestURL()).toString();
    String serverUrl = url.substring(0,url.indexOf('/',9));

%>

<style>
.modal.modal-wide .modal-dialog {
    width: 90%;

}
.show-on-hover:hover > div.dropdown-menu {
    display: block;
    width: 500px !important;
}
.wrapper {
    min-height: 70%;
    height: auto !important;
    height: 100%;
    margin: 0 auto -3em;
}
.footer, .push {
    height: 3em;
}
</style>
<body>

<nav class="navbar navbar-default navbar-fixed-top" style="background-color: white">
    <div class="container-fluid navbarfirstline">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#myNavbar">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <sec:ifNotLoggedIn>
                <span class="brand">&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/books/index"><img alt="brand" src="${assetPath(src: 'vtu.jpeg')}" id="addingcontent2">WONDERPUBLISH</a></span>
            </sec:ifNotLoggedIn>
            <sec:ifLoggedIn>
                <span class="brand">&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/books/index"><img alt="brand" src="${assetPath(src: 'vtu.jpeg')}">Visvesvaraya Technological University</a></span>



            </sec:ifLoggedIn>

        </div>
        <div class="collapse navbar-collapse" id="myNavbar">
            <div class="row row-right">
                <ul class="nav navbar-nav navbar-right top-nav">
                    <sec:ifNotLoggedIn>
                        <li><a href="javascript:showregister('login');">&nbsp;Sign in&nbsp;</a></li>
                        <li><a href="javascript:showregister('signup');">&nbsp;Register&nbsp;</a></li>
                    </sec:ifNotLoggedIn>
                    <sec:ifLoggedIn>

                        <li><a href="/books/books">My Library</a></li>
                        <li id="notification" class="dropdown"><a href="#"><i class="fa fa-bell-o fa-x"></i></a></li>
                        <li><g:link uri="/logoff">&nbsp;Logout&nbsp;&nbsp;&nbsp;&nbsp;</g:link></li>
                    </sec:ifLoggedIn>
                </ul>

            </div>
        </div>
    </div>
    <div class="popoverhome" style="display:none"></div>

    <!--<a href='#' id='example2' rel='popover' data-placement='left' data-content='Discover your subject by choosing these options' data-original-title='Find your subject'></a>-->


</nav>
<body>


<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>

<!--<div>-->
<div >
    <div class="container-fluid" >
        <div class="row wpbluebackground"><br><br>
            <div class="col-md-2"><h4 ><br><a href="/books/vtulibrary" class="whitetext"><i class="fa fa-arrow-left fa-x"></i> MY LIBRARY</a></h4></div>
            <div class="col-md-2 col-md-offset-3"><h3 class="whitetext">INSIGHTS</h3></div>
        </div>



        <br>
    </div>
    <div class="container-fluid" >
        <div  class='row' id="bookdtl">
            <div class="col-md-1 ">


            </div>

            <div class='col-md-10 main'>
                <div class="row">
                    <div class="col-md-12 text-center"><h4 class="greytext">TIME SPENT ON INDIVIDUAL SUBJECTS (IN HOURS)</h4> </div>
                </div>
               <div class="row">
                    <div class="col-md-10 col-md-offset-2">
                        <div id="chart_div"></div>
                    </div>


                </div>


            </div>

        </div>
        <div  class='row'>
            <div class="col-md-1 ">


            </div>

            <div class='col-md-10 main'>
                <div class="row">
                    <div class="col-md-12 text-center"><h4 class="greytext">TIME SPENT ON ALL SUBJECTS EACH DAY (IN MINUTES)</h4> </div>
                </div>
                <div class="row">
                    <div class="col-md-10 col-md-offset-2">
                        <div id="chart_div1"></div>
                    </div>


                </div>


            </div>

        </div>

    </div>

    <div class="push"></div>
</div>



<g:render template="/funlearn/wpfooter"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js" async></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<script type="text/javascript">

    google.charts.load('current', {'packages':['corechart', 'bar']});
    google.charts.setOnLoadCallback(drawChart);

    function drawChart() {
        var data = google.visualization.arrayToDataTable([
            ['Subjects', 'Computer Organisation', 'Design and Analysis of Algorithms','Engineering Mathematics - IV','Graph Theory','Microprocessor','Unix and Shell Programming'],
            ['Hours', 10, 17,35,15,9,21]
        ]);

        var options = {
            chart: {
                title: '',
            },

            bars: 'vertical',
            vAxis: {format: 'decimal'},
            height: 400,


        };

        var chart = new google.charts.Bar(document.getElementById('chart_div'));

        chart.draw(data, google.charts.Bar.convertOptions(options));

        var data = google.visualization.arrayToDataTable([
            ['Date', 'Time Spent'],
            ['1', 32],
            ['2', 96],
            ['3', 10],
            ['4', 0 ],
            ['5', 25],
            ['6', 40],
            ['7', 10],
            ['8', 56 ],
            ['9', 10],
            ['10', 0],
            ['11', 0],
            ['12', 15 ],
            ['13', 110],
            ['14', 150 ],
            ['15', 25],
            ['16', 50],
            ['17', 70],
            ['18', 2 ],
            ['19', 10],
            ['20', 45],
            ['21', 35],
            ['22', 60 ],
            ['23', 10],
            ['24', 14 ],
            ['25', 65],
            ['26', 40],
            ['27', 80],
            ['28', 56 ],
            ['29', 10],
            ['30', 25]
        ]);




        options = {
            chart: {
                title: '',
                subtitle: 'JUNE 2016'
            },
            axes: {
                x: {
                    0: {side: 'bottom'}
                }
            },

        };

        var material = new google.charts.Bar(document.getElementById('chart_div1'));
        material.draw(data, options);




    }
</script>

<%
    if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>

<asset:javascript src="analytics.js"/>
<% }%>


</body>
</html>