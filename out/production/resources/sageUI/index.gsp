<%@ page import="javax.servlet.http.Cookie" %>
<%String requestURL = request.getRequestURL().toString();
String servletPath = request.getServletPath();
String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));


session.setAttribute("servername", appURL);
def newCookie = new javax.servlet.http.Cookie( "siteName", "sageUI");
newCookie.path = "/"
response.addCookie newCookie;
%>
<g:render template="/sageUI/navheader"></g:render>
<section class="main-content">
  <div class="container main-content-container">
    <div class="row">
      <div class="col-md-12">
        <div class="book-banner" style="background: url('/assets/sage/bg-bottom.jpg'); background-size: cover; background-repeat: no-repeat; background-position: center; min-height: 260px; margin-top: 30px;"></div>  
      </div>
      <div class="col-md-3 col-sm-3" style="margin-top: 0;">
        <div class="sage-filters">
          <p class="discipline">Discipline</p>
          <div class="subjects">
            <ul>
              <li>
                <a href="#">Business & Management (24)</a>
              </li>
              <li>
                <a href="#">Political Science & International Relations (16)</a>
              </li>
              <li>
                <a href="#">Psychology (12)</a>
              </li>
              <li>
                <a href="#">Economics & Development Studies (11)</a>
              </li>
              <li>
                <a href="#">Sociology (10)</a>
              </li>
              <li style="margin-bottom: 0;">
                <a href="#" class="view-more">View more</a>
              </li>
            </ul>
          </div>
        </div>
        %{-- <label>Level</label>
        <ul class="level-selector">  
          <li class="level-selector-link">
            <a href="#">School</a>
          </li>
          <li class="level-selector-link">
            <a href="#">College</a>
          </li>
          <li class="level-selector-link">
            <a href="#">Competetive</a>
          </li>
        </ul>
        

        <label>Board/university</label>
        <div class="dropdown">
          <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
            Dropdown
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
            <li><a href="#">Action</a></li>
            <li><a href="#">Another action</a></li>
            <li><a href="#">Something else here</a></li>
            <li role="separator" class="divider"></li>
            <li><a href="#">Separated link</a></li>
          </ul>
        </div>

        <label>Class/grade</label>
        <div class="dropdown">
          <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
            Dropdown
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" aria-labelledby="dropdownMenu2">
            <li><a href="#">Action</a></li>
            <li><a href="#">Another action</a></li>
            <li><a href="#">Something else here</a></li>
            <li role="separator" class="divider"></li>
            <li><a href="#">Separated link</a></li>
          </ul>
        </div>
        <label>Subject</label>
        <div class="dropdown">
          <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu3" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
            Dropdown
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" aria-labelledby="dropdownMenu3">
            <li><a href="#">Action</a></li>
            <li><a href="#">Another action</a></li>
            <li><a href="#">Something else here</a></li>
            <li role="separator" class="divider"></li>
            <li><a href="#">Separated link</a></li>
          </ul>
        </div> --}%

        %{-- <label>Level</label>
        <select required id="selectedLevel" name="selectedLevel" class="form-control level-list" onchange="javascript:changeBoard(this.value)">
          <option value=""  selected disabled="disabled">Select</option>
        </select>

        <label>Board/Uninversity</label>
        <select disabled="disabled" required id="selectedBoard" name="selectedBoard" class="form-control disabled" onchange="javascript:changeGrade(this.value)">
          <option value=""  selected>Select</option>
        </select>

        <label>Class</label>
        <select disabled="disabled"  required id="selectedGrade" name="selectedGrade"  class="form-control" onchange="javascript:changeSubject(this.value)" >
          <option value="" selected>Select</option>
        </select>

        <label>Subject</label>
        <select disabled="disabled" required id="selectedSubject" name="selectedSubject" class="form-control" onchange="javascript:changeTopic(this.value)" >
          <option value=""  selected>Select</option>
        </select> --}%
      </div>

      <div class="col-md-9 col-sm-9 books-wrapper">
          <div class="book-banner-text content" style="width: 100%; max-height: 100%; background: #fff; padding-left: 15px; margin-top: 0;">
              <p class="welcome" style="margin: 0; line-height: normal;"> 
                <span class="sage-text">SAGE Texts</span>
                <br />
                <br />
                Quality needn&#39;t always be expensive.
                <br />
                <br />
              SAGE&#39;s commitment to furthering education takes another quantum leap forward with SAGE Texts. This fledgling list is designed to provide teacher and student resources without compromising on SAGE&#39;s globally recognised standards of excellence. The current focus is Business and Management, Organizational Behaviour, Politics and International Studies, Economics and the Social Sciences. The books are designed keeping UGC recommendations in mind thus providing an affordable option to teachers and students alike.
              <br />
              <br />
              For students: Quality at affordable prices that includes study resources such as sample quizzes.
              <br />
              </p>
            %{-- <p class="banner-book-description">${description}</p> --}%
          </div>
        <div id="search-error" class='no-books'><i class='fa fa-frown-o' aria-hidden='true'></i> Sorry! There are no results for your search.</div>
        
        %{-- <button type="button" class="btn show-more-btn" id="show-more">SHOW MORE BOOKS</button> --}%
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        %{-- <ul class="books sage-books" id="content-data-books" style="margin-top: 0;"></ul> --}%
        <div class="sage-books-container">
          <div class="col-md-3">
            <div class="book-img">
              <img src="/assets/sage/1.png" alt="" class="img-responsive" />
              
            </div>
            <div class="sage-book-text">TEXTBOOK</div>
            <div class="sage-book-name">Total Quality Management</div>
            <div class="sage-book-desc">Concepts, Strategy and Implementation for Operational Excellence</div>
            <div class="sage-book-writer">Sunil Sharma</div>
            <div class="sage-book-publish">Published: December 2017</div>
            <div class="col-md-6">Paperback
              <br />
              <i class="fa fa-inr"></i>495
            </div>
            <div class="col-md-6" style="margin-top: 20px;">
              <a href="#" class="sage-buy">Buy Now</a>
            </div>
          </div>
          
          <div class="col-md-3">
            <div class="book-img">
              <img src="/assets/sage/2.png" alt="" class="img-responsive" />
              
            </div>
            <div class="sage-book-text">TEXTBOOK</div>
            <div class="sage-book-name">
              <a href="/wonderpublish/bookdtl?bookId=147">Microeconomics</a>
            </div>
            <div class="sage-book-desc">Theories and Applications for Emerging Economies</div>
            <div class="sage-book-writer">Sreejata Banerjee, P. N. Warrier</div>
            <div class="sage-book-publish">Published: December 2017</div>
            <div class="col-md-6">Paperback
              <br />
              <i class="fa fa-inr"></i>495
            </div>
            <div class="col-md-6" style="margin-top: 20px;">
              <a href="#" class="sage-buy">Buy Now</a>
            </div>
          </div>

          <div class="col-md-3">
            <div class="book-img">
              <img src="/assets/sage/3.png" alt="" class="img-responsive" />
              
            </div>
            <div class="sage-book-text">TEXTBOOK</div>
            <div class="sage-book-name">Ethics, Integrity, and Aptitude in Governance</div>
            <div class="sage-book-desc"></div>
            <div class="sage-book-writer">Ranvijay Upadhyaya</div>
            <div class="sage-book-publish">Published: November 2017</div>
            <div class="col-md-6">Paperback
              <br />
              <i class="fa fa-inr"></i>550
            </div>
            <div class="col-md-6" style="margin-top: 20px;">
              <a href="#" class="sage-buy">Buy Now</a>
            </div>
          </div>

          <div class="col-md-3">
            <div class="book-img">
              <img src="/assets/sage/4.png" alt="" class="img-responsive" />
              
            </div>
            <div class="sage-book-text">TEXTBOOK</div>
            <div class="sage-book-name">An Introduction to Social Psychology</div>
            <div class="sage-book-desc"></div>
            <div class="sage-book-writer">Suhas Shetgovekar</div>
            <div class="sage-book-publish">Published: November 2017</div>
            <div class="col-md-6">Paperback
              <br />
              <i class="fa fa-inr"></i>350
            </div>
            <div class="col-md-6" style="margin-top: 20px;">
              <a href="#" class="sage-buy">Buy Now</a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row" style="margin-bottom: 20px;">
      <div class="col-md-12">
        %{-- <ul class="books sage-books" id="content-data-books" style="margin-top: 0;"></ul> --}%
        <div class="sage-books-container">
          <div class="col-md-3">
            <div class="book-img">
              <img src="/assets/sage/5.jpg" alt="" class="img-responsive" />
            </div>
            <div class="sage-book-text">TEXTBOOK</div>
            <div class="sage-book-name">International Politics</div>
            <div class="sage-book-desc"></div>
            <div class="sage-book-writer">Rumki Basu</div>
            <div class="sage-book-publish">Published: November 2017</div>
            <div class="col-md-6">Paperback
              <br />
              <i class="fa fa-inr"></i>425
            </div>
            <div class="col-md-6" style="margin-top: 20px;">
              <a href="#" class="sage-buy">Buy Now</a>
            </div>
          </div>

          <div class="col-md-3">
            <div class="book-img">
              <img src="/assets/sage/6.jpg" alt="" class="img-responsive" />
            </div>
            <div class="sage-book-text">TEXTBOOK</div>
            <div class="sage-book-name">Financial Management</div>
            <div class="sage-book-desc">A Strategic Perspective</div>
            <div class="sage-book-writer">Nikhil Chandra Shil, Bhagaban Das</div>
            <div class="sage-book-publish">Published: December 2016</div>
            <div class="col-md-6">Paperback
              <br />
              <i class="fa fa-inr"></i>650
            </div>
            <div class="col-md-6" style="margin-top: 20px;">
              <a href="#" class="sage-buy">Buy Now</a>
            </div>
          </div>

          <div class="col-md-3">
            <div class="book-img">
              <img src="/assets/sage/7.png" alt="" class="img-responsive" />
            </div>
            <div class="sage-book-text">TEXTBOOK</div>
            <div class="sage-book-name">
              <a href="/wonderpublish/bookdtl?bookId=146">Rural Development</a>
            </div>
            <div class="sage-book-desc">Principles, Policies, and Management, Fourth Edition</div>
            <div class="sage-book-writer">Katar Singh, Anil Shishodia</div>
            <div class="sage-book-publish">Published: October 2016</div>
            <div class="col-md-6">Paperback
              <br />
              <i class="fa fa-inr"></i>425
            </div>
            <div class="col-md-6" style="margin-top: 20px;">
              <a href="#" class="sage-buy">Buy Now</a>
            </div>
          </div>

          <div class="col-md-3">
            <div class="book-img">
              <img src="/assets/sage/8.png" alt="" class="img-responsive" />
            </div>
            <div class="sage-book-text">TEXTBOOK</div>
            <div class="sage-book-name">
              <a href="/wonderpublish/bookdtl?bookId=147">A Textbook of Microeconomic Theory</a>
            </div>
            <div class="sage-book-desc"></div>
            <div class="sage-book-writer">Pankaj Tandon</div>
            <div class="sage-book-publish">Published: July 2015</div>
            <div class="col-md-6">Paperback
              <br />
              <i class="fa fa-inr"></i>495
            </div>
            <div class="col-md-6" style="margin-top: 20px;">
              <a href="#" class="sage-buy">Buy Now</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  %{-- <div class="loading-icon">
    <img class="loading-img" src="${assetPath(src: 'loading-books.gif')}" alt="">
  </div> --}%
</section>
<g:render template="/${session['entryController']}/footer"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-3-typeahead/4.0.2/bootstrap3-typeahead.js"></script>
<script>
dropdownMode = "books";
function getBooksList(getBooksList) {
  <g:remoteFunction controller="wonderpublish" action="getBooksList"  onSuccess='displayBooks(data);'/>
}

function getBookDetails(bookId, getBookDetails) {
  <g:remoteFunction controller="wonderpublish" action="getBookDetails"  onSuccess='displayBookData(data, bookId);' params="'bookId='+bookId"/>
}

function displayBookData(data, bookId) {
  var author = document.getElementById('author-name-'+bookId);
  if (author) {
    author.innerHTML = data.authors;
  }
}


// function displayBooks(data,booksType) {
//   var books = data.books;
//   var htmlStr = "";
//   var offerPrice = "";
//   var listPrice = "";
//   var imgSrc = "";
//   var ratingCount = data.books.rating;
//   for (var i = 0; i < books.length; ++i) {
//     if (books[i].offerPrice ==0 || books[i].offerPrice ==0.0 || books[i].offerPrice == null) {
//       offerPrice = "";
//     } else {
//       offerPrice = "<i class='fa fa-inr'></i> " + books[i].offerPrice;
//     }

//     if (books[i].listPrice ==0 || books[i].listPrice ==0.0 || books[i].listPrice==null || books[i].listPrice == books[i].offerPrice){
//       listPrice = "";
//     } else {
//       listPrice = books[i].listPrice;
//     }

//     if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
//       imgSrc = "/assets/booksmojo/img_cover_placeholder.png";
//     } else {
//       imgSrc = "/funlearn/showProfileImage?id="+books[i].id+"&fileName="+books[i].coverImage+"&type=books&imgType=passport";
//     }

//     if (books[i].rating ==0 || books[i].rating ==0.0 || books[i].rating==null){
//       ratingCount="";
//     } else {
//       ratingCount="(" + books[i].rating + ")";
//     }

//     htmlStr+= "<li class='col-xs-6 col-sm-6 col-md-3 book-content-wrapper'>" + 
//     "<div class='book-item'>" +
//     "<a class='book-item-link' href='/wonderpublish/bookdtl?bookId="+books[i].id+"'>" +
//     "<div class='book-image-wrapper'>" +
//     "<img class='book-image' src='"+imgSrc+"' alt=''/>" +
//     "</div>" +
//     "<div class='book-info'>" +
//     "<div class='book-preview hidden'>"+
//     "<p class='preview-links'>" + "Preview" + "</p>" +
//     "<p class='preview-links'>" + "Buy Now" + "</p>" +
//     "</div>" +
//     "<div class='book-name-author'>" +
//     "<p class='book-name'>"+books[i].title+"</p>" +
//     "<p id='author-name-"+books[i].id+"' class='author-name'></p>" +
//     "</div>" +
//     "<div class='price-rating'>" +
//     "<p class='rating'>"+getStars(books[i].rating)+" <span class='rating-count'>"+ratingCount+"</span></p>" +
//     "<div class='book-price'>" +
//     "<p class='offer-price'>"+offerPrice+"</p>" +
//     "<p class='original-price'>"+listPrice+"</p>"+
//     "</div>" +
//     "</div>" +
//     "</div>" +
//     "</a>" +
//     "</div>" +
//     "</li>"
//   };

//   for(i=0;i<books.length;i++) {
//     getBookDetails(books[i].id);
//   }

//   if(books.length > 0) {
//     document.getElementById("content-data-books").innerHTML=htmlStr;
//     $('.loading-icon').addClass('hidden');
//   } else {
//     document.getElementById("content-data-books").innerHTML="<div class='no-books-available'>" +
//     "<div class='no-book-wrapper'>" + "<img class='book-image' src='${assetPath(src: 'desert_plants.png')}'>" + "</div>" + "<p>Currently no books available.</p>" + "</div>";
//     $('.loading-icon').addClass('hidden');
//     $('#show-more').hide();
//     $('body').css({
//       'position' : 'relative'
//     });
//     $('footer').css({
//       'width' : '100%',
//       'position' : 'static',
//       'bottom' : '0'
//     });
//     $('.search-filters').hide();
//     $('.books-wrapper').removeClass('books-wrapper col-md-9 col-sm-9').addClass('col-md-12 col-sm-12');
//     $('.main-content-container').css({
//       'position' : 'relative',
//       'top' : '50%',
//       'transform' : 'translateY(-50%)'
//     });
//   }
//   if(books.length <= 8) {
//     $('#show-more').hide();
//   }
// }

function getBookCategories(){
  <g:remoteFunction controller="wonderpublish" action="getBookCategories"  onSuccess='initializeDataIndex(data);'/>
}
getBooksList();
getBookCategories();

function getBooksForSelectedCategories(fieldSelected,fieldValue) {
  $('.loading-icon').removeClass('hidden');
  var level = document.getElementById('selectedLevel').value;
  var syllabus = null;
  var grade = null;
  var subject = null;
  if("grade"==fieldSelected){
    syllabus = document.getElementById('selectedBoard').value;
  }
  else if("subject"==fieldSelected){
    syllabus = document.getElementById('selectedBoard').value;
    grade = document.getElementById('selectedGrade').value;

    if("All"!=fieldValue) subject=fieldValue;
  }
  jQuery('#booksloading').show(500);
  <g:remoteFunction controller="wonderpublish" action="getBooksList"  onSuccess='displayBooks(data);'
  params="'categories=true&level='+level+'&syllabus='+syllabus+'&grade='+grade+'&subject='+subject" />
}

$('#content-data-books').on('mouseenter', '.book-item', function() {
  $(this).find('.book-name-author').addClass('hidden');
  $(this).find('.book-preview').removeClass('hidden');
}).on('mouseleave', '.book-item', function() {
  $(this).find('.book-name-author').removeClass('hidden');
  $(this).find('.book-preview').addClass('hidden');
});
$('#search-error').addClass('hidden');

$(document).ready(function() {
  $('#show-more').click(function() {
    $('#content-data-books li').slice(0, 12).show();
  });
});

function searchBooks() {
  var filterBooksList = document.getElementById('search-book').value.toLowerCase();
  var bookSearchInput = document.getElementById('search-book');
  var bookData = document.getElementById('content-data-books');
  var bookItem = bookData.querySelectorAll('li.col-md-3');
  for (i = 0; i < bookItem.length; i++) {
    var bookName = bookItem[i].getElementsByClassName('book-name')[0];
    if (bookName.innerHTML.toLowerCase().indexOf(filterBooksList) > -1) {
        bookItem[i].style.display = 'block';
        document.getElementById('search-error').classList.add = 'hidden';
    } else {
      bookItem[i].style.display = 'none';
      document.getElementById('search-error').classList.remove = 'hidden';
    }
  }
}

$('#search-book').typeahead({
  source: function(query, process) {
    $.ajax({
      url: '/wonderpublish/getBooksList',
      prefetch: '/wonderpublish/getBooksList',
      method: 'POST',
      data: {query:query},
      dataType: 'JSON',
      success: function fetchBooks(data) {
        process($.map(data.books, function(item) {
          if(query === '') {
            return(item.title);
          } else {
            return item.title;
          }
        }));
      }
    })
  }
});

function triggerClickOnSearchList(e) {
  if (e.keyCode === 13 || e.type=='click'){
    searchBooks();
  }
}

  $('#search-button').click(function() {
    if($('#search-book').val() == '') {
      $('#search-book').attr('placeholder', "Please type something to search").css({
        "box-shadow" : "0 0 10px #F05A2A"
      });
    } else {
      $('#search-book').css({
        "box-shadow" : ""
      });
      searchBooks();
    }
    
  });
$('.search-book').on('click', '.dropdown-item', triggerClickOnSearchList).on('keyup', triggerClickOnSearchList);

$('#book-search-type li a').click(function(e) {
  e.preventDefault();
  var a = $(this).html();
  $('#book-search-type-btn').html(a + " <span class='caret'></span>");
  $('#search-book').attr('placeholder', "Search using " + a +"...");
  setCssOfInputOnCategorySelection();
});
function setCssOfInputOnCategorySelection() {
  $('#search-book').css({
    "padding-left" : $('#book-search-type-btn').width() + 30
  }).focus();
}
</script>
</body>
</html>