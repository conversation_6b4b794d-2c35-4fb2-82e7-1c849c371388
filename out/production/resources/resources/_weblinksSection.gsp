<div class="web-url">
    <div class="modal fade" id="addWeburl"  data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content">
                <div class="modal-header">
%{--                    <h4 class="modal-title">Add Reference</h4>--}%
                </div>
                <div class="modal-body">
                    <form id='webForm'>
                        <div class="mdl-textfield mdl-js-textfield">
                            <input type="text" class="mdl-textfield__input" name="link" id="userWebLink" placeholder="Paste link">
                            <label class="mdl-textfield__label" for="userWebLink">Paste link</label>
                        </div>
                        <div class="mdl-textfield mdl-js-textfield">
                            <input type="text" class="mdl-textfield__input" name="resourceName" id="userWeblinkName" placeholder="Enter name of the Link">
                            <label class="mdl-textfield__label" for="userWeblinkName">Name</label>
                        </div>
                    </form>
                    <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="userWeblinkUploadAlert">
                        ** Enter both link and the name
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" onclick="closeAddLink()">CANCEL</button>
                    <button type="button" class="btn saveLink" onclick="addWebLink();">SAVE</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>

    function addWebLink(){
        $("#userWeblinkUploadAlert").hide(500);
        if(document.getElementById("userWeblinkName").value==""||document.getElementById("userWebLink").value==""){
            $("#userWeblinkUploadAlert").show(500);
        }
        else {
            linkName= document.getElementById("userWeblinkName").value;
            var encodedlinkName=encodeURIComponent(document.getElementById("userWeblinkName").value);
            link = document.getElementById("userWebLink").value;
            $('.loading-icon').removeClass('hidden');

            <g:remoteFunction controller="resourceCreator" action="addlink" params="'from=app&chapterId='+previousChapterId+'&link='+link+'&resourceName='+encodedlinkName+'&resourceType=Reference Web Links&folderId=${params.folderId}'" onSuccess='weblinkUpdated(data);'/>
        }
    }

    function weblinkUpdated(data){
        if(previousChapterId==''){
            location.reload();
        }
        else {
            $('.loading-icon').addClass('hidden');
            $('#webForm')[0].reset();
            $('#addWeburl').modal('hide');
            updateAllTabWithWebLink(data.resId, link, linkName,data.createdBy)
        }

    }


    function updateAllTabWithWebLink(id,link,linkName){
        var cStr = "<div class=\"container\">\n" +
            "<div class=\"all-container\">"+
            "<div class=\"container-wrapper\">\n" +
            "<div class='d-flex justify-content-between align-items-center'>"+
            "        <div class=\"media\">\n" +
            "                    <a class=\"mb-0 readnow\" href='javascript:openWebRef(" + id + ",\""+link+"\")'>"+
            " <div class='box green'>" +
            "<div>"+
            "<i class=\"align-self-center\"></i>"+"<p>LINK</p>"+
            "</div>"+"</div>"+
            "  <div class=\"media-body\">\n" +
            "                <p class=\"title\">"+linkName+"</p>\n" +
            "            </div>\n" +
            "</a>";
        cStr +=  "</div>"+
            "    </div>\n"+
            "</div>\n" +
            "</div>" ;
        document.getElementById('content-data-all').innerHTML += cStr;
        $('.nav-tabs a[href="#all"]').tab('show');
        $('.loading-icon').addClass('hidden');
    }


    function googleSearch(tag){
        var searchString;
        if("School"==chapterLevel) searchString = " https://www.google.com/search?q=Class "+chapterGrade+" "+chapterSubject+" "+chapterName+" "+chapterSyllabus+" "+tag;
        else searchString = " https://www.google.com/search?q="+chapterGrade+" "+chapterSubject+" "+chapterName+" "+chapterSyllabus+" "+tag;
        if(chapterDesc!=null&&!chapterDesc=="") {
            searchString = " https://www.google.com/search?q="+chapterDesc;
        }
        window.open(searchString);
        if(loggedInUser){

                updateUserViewChapter(previousChapterId,"all","weblinks",tag);

        }
        else{
                 updateViewChapter(previousChapterId,"all","weblinks",tag);

        }

    }


    function openWebRef(id,link){
       if(previousChapterId!='') initCallGoogle(id,"References");
        if(link.indexOf("http")==-1) link = "http://"+link;
        window.open(link, '_blank');
        if(loggedInUser){

            updateUserView(id,"all","weblinks");

        }
        else{

            updateView(id,"all","weblinks");

        }
    }
</script>

