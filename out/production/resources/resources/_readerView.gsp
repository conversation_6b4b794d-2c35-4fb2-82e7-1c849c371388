<asset:stylesheet href="wonderslate/readSection.css"></asset:stylesheet>

<style>

@media all and (max-width: 770px){
    #reader{
        height: 98vh;
    }
    #reader iframe {
        height: 100% !important;
    }
    .mobile-footer-nav{
        display: none;
    }
}
</style>

<div id="reader" style="z-index: 9999"></div>
<script>
    var bookId = "${params.bookId}";
    var resId;
    var readId;
    var resSubFileNm="";
    var defaultZoom;
    var zoomLevel;
    var prepjoySite = "${session['prepjoySite']}";
    var urlBookId= bookId;
    var serverPath = "${serverURL}";
    var bookLang = "${params.bookLang}";
    var bookTitle = "";
    var mobileView=false;
    var previewMode=${previewMode};
    var genericReader= ${genericReader};

    function getHtmlsData(id){
        resId = id;
        readId=id
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="funlearn" action="getHtmlsFileNm"  onSuccess='setResSubFileNm(data);' params="'resId='+id+'&bookId='+bookId" />
    }

    function getHtmlsDataByChapter(id){
        var rId = id;
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="funlearn" action="getHtmlsFileNm"  onSuccess='setResSubFileNm(data);' params="'chapterId='+rId+'&bookId='+bookId" />
    }

    function setResSubFileNm(data){
        resId = data.resId;
        readId = data.resId;

        if(data && data.fileNm){
            filename = data.fileNm
        }
        if(data && data.resourceName){
            resourceName = data.resourceName
        }
        if($(window).width()<768){
            $('.mobile-footer-resource').removeClass('d-flex').addClass('d-none');
            $('.mobile-back-button').attr('onclick','closeResourceScreen()');
            mobileView=true;
        }
        if(data.isEpub == false && data.notesType == "pdf"){
            $('header,.headerCategoriesMenu,.prepjoy_cta,.footer-menus,.related-book-wrapper,.prepjoy-footer').hide();
            $('.add-tabs').removeClass('d-lg-flex').addClass('d-none');
            document.getElementById('reader').classList.add('readerAnimation');
            document.getElementById('reader').classList.remove('readerAnimationClose');
            resSubFileNm = data.fileNm;
            zoomLevel = data.zoomLevel;

            //Setting default zoom value
            if (zoomLevel !=null || zoomLevel !=""){
                if (zoomLevel == "1"){
                    defaultZoom = 130;
                }else if (zoomLevel == "2"){
                    defaultZoom = 160;
                }else if (zoomLevel == "3"){
                    defaultZoom = 190;
                }

                if($(window).width()<768){
                    defaultZoom = 48;
                }
            }else {
                defaultZoom = 100;
            }

            var pdfKey = "${encryptPdfKey}";
            var bookId=0;
            var chapterId=chapterIdForPDF;

            bookTitle = data.resourceName;
            <% if(params.bookId == null || params.bookId == '') { %>
                bookId=urlBookId;
            <% } else {%>
                bookId="${params.bookId}";
            <%}%>
            var knimbusInstUser = false
            <%if (session["userdetails"]!=null){%>
            var checkInstUseName = '${session["userdetails"].username}'
            if(checkInstUseName !="" && checkInstUseName.indexOf("_institute")>0){
                knimbusInstUser = true
            }
            <%}%>
            $("#reader").html("<iframe id='readerFrameOne' src=\"" + serverPath + "/resources/pdfReader?bookLang=" + bookLang + "&resId=" + resId + "&bookId=" + bookId + "&knimbusInstUser="+knimbusInstUser+"&loggedInUser="+loggedInUser+"&bookUrl=/funlearn/getPdfFile?resId=" + resId + "__encryptedKey=" + pdfKey + "&chapterId="+data.chapterId+"&title="+bookTitle+"&mobileView="+mobileView+"&bookGPT=false#zoom="+defaultZoom+"\" width='100%' style='height:100vh;border: none;' class='tes'></iframe>");
            localStorage.setItem('lastReadPDF',JSON.stringify({resId:data.resId,pdfOpen:true,chapterId:data.chapterId,bookId:bookId}))
            document.getElementById('reader').classList.add('readerAnimation');
            document.querySelector('body').removeAttribute('style');
            document.querySelector('body').setAttribute('style','position: relative;min-height: 100%;top: 0px;overflow: hidden;');
            if (!prepjoySite){
                $('.loading-icon').addClass('hidden');
            }else{
                $('#loading').hide();
            }

            if (bookLang != "English") {
                $('#htmlreadingcontent iframe').on('load', function () {
                    var getIframe = $("#htmlreadingcontent iframe").contents().find(".annotator-wrapper .annotator-adder .google-search-btn");
                    getIframe.css("display", "none");
                    $("#htmlreadingcontent iframe").contents().find(".annotator-wrapper .annotator-adder .highlight-btn").css("margin-right", "0px");
                    $("#htmlreadingcontent iframe").contents().find(".annotator-wrapper .annotator-adder .highlight-btn").css("padding-right", "30px");

                });
            }
            $("#notesLoading").hide();
        }else if(data.isEpub == true){
            displayEpub(data,readId,"${params.bookId}",masterChapterID,data.resId);
        }else if(data.notesType == "notes" && data.ebupChapterLink && data.ebupChapterLink!="" && data.ebupChapterLink!=null){
            document.getElementById('reader').innerHTML=''
            displaySplittedEpubs(data,resId)
        }else if(data.notesType == "notes" && data.ebupChapterLink  =="" || data.ebupChapterLink==null){
            $("body").append('<script type="text/javascript" async src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full"/>')
            $("body").append('<script type="text/javascript" src="/assets/katex.min.js"/>')
            $("body").append('<script type="text/javascript" src="/assets/auto-render.min.js"/>')
            $("body").append("<script type='text/x-mathjax-config'>MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}})<" + "/script>");
            if ($(window).width() < 768){
                $('header,.related-book-wrapper').show();
                $('.add-tabs').addClass('d-lg-flex').removeClass('d-none');
                $('.mobile-back-button').attr('onclick','mobileBackToChapters()');
            }else{
                $('header,.headerCategoriesMenu,.prepjoy_cta,.footer-menus,.related-book-wrapper').show();
                $('.add-tabs').addClass('d-lg-flex').removeClass('d-none');
            }

            document.getElementById('reader').classList.remove('readerAnimation');
            document.getElementById("htmlreadingcontent").height=screen.availHeight;
            if(!readId){readId = resId}
            <g:remoteFunction controller="funlearn" action="getHtmlsFile"  onSuccess="displayHtmls(data);" params="'resId='+readId" />
        }
    }
    document.addEventListener('keydown', event => {
        if (event.ctrlKey || event.metaKey && event.key === 's') {
            event.preventDefault();
            event.stopPropagation();
           return false;
        }
    },false);
</script>
</body>
</html>
