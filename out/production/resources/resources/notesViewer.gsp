<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/mobileFooter.css" async="true"/>
<asset:javascript src="sharer.min.js"/>
<asset:stylesheet href="wonderslate/flashcardHome.css" async="true"/>

<section class="flashcard-home">
    <div class="container">
        <div id="htmlreadingcontent" class="row quiz-section"></div>
        </div>
</section>
<div class="loading-icon">
    <div class="loading-icon">
        <div class="loader-wrapper">
            <div class="loader">Loading</div>
        </div>
    </div>
</div>

<script src="/assets/katex.min.js"></script>
<script src="/assets/auto-render.min.js"></script>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>
    function getReadingMaterial(){
        <g:remoteFunction controller="funlearn" action="getHtmlsFile"  onSuccess="displayHtmls(data);" params="'resId=${params.resId}'" />
    }

    function displayHtmls(data){
        if($(window).width()<768){
            $('.bookTemplate .content-wrapper .price-wrapper').css('bottom','0');
            $('.mobile-footer-resource').removeClass('d-flex').addClass('d-none');
        }

        if($(window).width()<767) {
            $('#chapter-actions').removeClass('d-flex').addClass('d-none');
            $('.generateTest').addClass('d-none');
        }

        var resourceName='${resourceName}',resLink='${resLink}';
         var extraPath = "/OEBPS";

        var replaceStr = "/funlearn/downloadEpubImage"+
            "?source="+resLink.substring(0,resLink.lastIndexOf('/')+ 1)+"extract"+
            extraPath+"/";


        var  htmls = "<div class=\"row pb-4 justify-content-between container notes-head p-1\">";
        htmls +="<i class='material-icons' onclick='javascript:backToResource();' style='cursor: pointer;'>keyboard_backspace</i>";
        htmls += "<h4>" + resourceName + "</h4>";

        htmls +=  "        <div class=\"text-right\"></div>\n" +
            "    </div>"+data;

        $('.loading-icon').addClass('hidden');

        var $iframe = $('#htmlreadingcontent');

        $iframe.ready(function() {
            htmls =  replaceAll(htmls,"src=\"", "src=\""+replaceStr);

            htmls =  replaceAll(htmls,"\"../Styles/", "\""+replaceStr+"../Styles/");
            htmls = htmls.replace(/\\\\/g , '\\');
            htmls = htmls.replace(/\\mathring\ *\{\ *([A-Za-z])\ *\}\ */g , '\\overset\{\\ \\ \\circ\}\{$1\}');

            $('#htmlreadingcontent').css({
                    'justify-content':'center'
                });
                $('.text-format').show();

            document.getElementById('htmlreadingcontent').innerHTML = '<div id="htmlContent" style="margin: 0 auto;">'+htmls+'</div>';
            renderMathInElement(document.body);

            $('#htmlreadingcontent').find('table').each(function () {
                $('table').wrap('<div class="table-responsive">');
            });

        });


        $("#htmlreadingcontent").show();

        $(".contentEdit #formatMenu, .contentEdit #notesMenu").show();

        var anchors = document.getElementById('htmlreadingcontent').getElementsByTagName('a');
        for (var i=0; i<anchors.length; i++){
            anchors[i].setAttribute('target', '_blank');
        }

    }

    getReadingMaterial();
    <sec:ifNotLoggedIn>

    updateView("${params.resId}","Notes","Notes");
    </sec:ifNotLoggedIn>
    <sec:ifLoggedIn>
    updateUserView("${params.resId}","Notes","Notes");
    </sec:ifLoggedIn>
    function backToResource(){
        <%if("android".equals(session["appType"])){%>
        JSInterface.showNativeHeader();
        <%}else if("ios".equals(session["appType"])) {%>
        webkit.messageHandlers.showNativeHeader.postMessage('');
        <%}%>
        window.history.back();
    }
</script>
