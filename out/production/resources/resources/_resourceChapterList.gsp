<style>
.mdl-tabs.side-column .sidebar-background{
    height: 70vh !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px){
    .mdl-tabs.side-column .mdl-card li:last-child {
        margin-bottom: 150px !important;
    }
    .mdl-tabs.side-column .sidebar-background{
        height: 100vh !important;
    }
}
.mdl-tabs.side-column{
    top: 200px !important;
}
</style>
<script>
    var bookPrice="${eBookPrice}";
    var previews;
    var tempChapterId = "${params.chapterId}";
    var nextExam = "${params.nextexam}";
    var myResId="${params.resId}";
    var myResource="${params.resourceId}";
    var myResourceName="${params.resourceName}";
    var flashcardPresent="${params.flashcard}";
    var chapterId="${params.chapterId}";
    var showPubSlide=false;
    var selectedchapterName;
    var fromNotes="${params.fromNotes}";
    if(!nextExam){
        var myActivity = "${params.myActivity}";
        var newActivity="${params.newActivity}";
        if(myActivity){
            <%if(!hasBookAccess){%>
            if(bookPrice=='' || bookPrice =='0'){
                bookPrice = 1;
            }
            else{
                bookPrice='${eBookPrice}';
            }
            var shareBookId = "${book.id}";
            var shareBookName = "${bookName}";
            var myresId = "${resId}";
            if(!newActivity) {
                location.href = location.href.replace('ebook?resId=' + myresId + '&passUrl&myActivity=true', 'ebook/' + shareBookName + '?siteName=books&bookId=' + shareBookId + '&preview=true');
            }
            else {
                location.href = location.href.replace('ebook?resId=' + myresId + '&clickSource=myactivity&myActivity=true&newActivity=true', 'ebook/' + shareBookName + '?siteName=books&bookId=' + shareBookId + '&preview=true');
            }
            <%}%>
        }
    }

</script>
<div class="side-wrapper mt-3">
    <div class="mdl-tabs side-column mdl-js-tabs mdl-js-ripple-effect">
        <%
            String encodedBookTitle = URLEncoder.encode(book.title,"UTF-8");
            %>
        <div class="sidebar-background" style="overflow: hidden;">

            <div class="book-main-detials" style="margin-top: 55px;">
            <p class="book-name pb-lg-2" style="font-size: 14px;">${book.title}</p>
            <p class="book-name mt-0" id="expiryDate" style="display: none;font-size: 12px;font-weight: normal;"></p>

                <ul class="nav nav-tabs">
                    <li class="nav-item">
                        <%if("published".equals(book.status)){%>
                        <%if("true".equals(session["commonWhiteLabel"])){%>
                        <a class="nav-link mdl-tabs__tab mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect" href='/${encodedBookTitle.replace("'","&#39;")}/ebook-details?siteName=${session['siteName']}&bookId=${book.id}'>About this Book</a>
                        <%}else{%>
                        <a class="nav-link mdl-tabs__tab mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect" href='/${encodedBookTitle.replace("'","&#39;")}/ebook-details?siteName=${session['entryController']}&bookId=${book.id}'>About this Book</a>
                        <%}%>
                      <%}%>
                    </li>
                    <li class="nav-item" style="display: none">
                        <a class="nav-link mdl-tabs__tab mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect is-active active" data-toggle="tab" href="#allchapters">About this Book</a>
                    </li>
                    <li class="nav-item">
                        <sec:ifLoggedIn>
                            <a class="nav-link mdl-tabs__tab mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect" data-toggle="tab" href="#allnotes"><img src="${assetPath(src: 'resource/notes.svg')}" class="mr-2" > All Highlights</a>
                        </sec:ifLoggedIn>
                        <sec:ifNotLoggedIn>
                            <a class="nav-link mdl-tabs__tab mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"  href="javascript:loginOpen()"><img src="${assetPath(src: 'resource/notes.svg')}" class="mr-2" > All Notes</a>
                        </sec:ifNotLoggedIn>
                    </li>
                </ul>
            </div>

            <%if(previewMode||!hasBookAccess){%>
            <div class="preview-book-btns">
                <div class="card-wrapper">
                    <%if(!session["wileySite"]){%>
                    <% if(!("0".equals(""+eBookPrice)||"0.0".equals(""+eBookPrice))) {%>
                    <div class="buy">
                    <%if("true".equals(session["commonWhiteLabel"])){%>
                    <a class="btn-book-buy mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect" href='/${encodedBookTitle.replace("'","&#39;")}/ebook-details?siteName=${session['siteName']}&bookId=${book.id}'>Buy now</a>
                    <%}else{%>
                    <a class="btn-book-buy mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect" href='/${encodedBookTitle.replace("'","&#39;")}/ebook-details?siteName=${session['entryController']}&bookId=${book.id}'>Buy now</a>
                    <%}%>
                </div>
                    <%}else {%>
                    <div class="rprice-tag">
                        <div class="complte-book">
                            <p> Free</p>
                        </div>
                    </div>
                    <div>
                        <a class="btn-book-buy mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect" href="javascript:addToMyLibrary('${book.id}','${eBookPrice}','${book.title}');">Add to Library</a>
                    </div>
                    <%}}%>
                </div>



            </div>
            <%}%>

            <div class="d-flex h-75 mt-lg-3 chaptersListings">
                <div class="tab-content side-column h-100">
                    <div id="allchapters" class="tab-pane active">
                        <div class="demo-card-square mdl-card mdl-shadow--2dp">

                            <ul class="read-book-chapters-wrapper demo-list-icon mdl-list" style="overflow: scroll">
                                <g:each in="${topicMst}" var="chapter" status="i">
                                    <%lockedChapter=true;%>

                                    <%if(!previewMode||(previewMode&&(""+topicId).equals(""+chapter.id))||"0".equals(""+eBookPrice)||"0.0".equals(""+eBookPrice)&&(hasBookAccess)){
                                    String url = "/"+encodedBookTitle+"/ebook?siteName=${session['entryController']}&bookId="+book.id+"&chapterId="+chapter.id
                                    %>

                                    <li class="mdl-list__item" id="chapterName${chapter.id}">
                                 <!--       <i class="material-icons"></i> <a href="${url}" id="chapter${chapter.id}" class="<%=(""+topicId).equals(""+chapter.id)?'orangeText':''%>">${chapter.name}</a> -->
                                        <i class="material-icons"></i> <a href="javascript:getChapterDetails('${chapter.id}','${chapter.name}')" id="chapter${chapter.id}" class="<%=(""+topicId).equals(""+chapter.id)?'orangeText':''%>">${chapter.name}</a>


                                        %{--                                    <ul class="chapter-sections" id="sections-dropdown-${chapter.id}"></ul>--}%
                                    </li>
                                    <%} else {%>
                                    <li class="mdl-list__item"><i class="material-icons">
                                        lock
                                    </i>${chapter.name}&nbsp;</li>
                                    <%}%>
                                </g:each>

                            </ul>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var prepjoySite = "${session['prepjoySite']}";
    function getChapterDetails(chapterId,chapterName){
        $('.read-book-chapters-wrapper li').removeClass('orangeText');
        myChapterId=chapterId;
        noteschapterId=chapterId;
        paramResId = -1;
        selectedchapterName=chapterName;
        $("#htmlreadingcontent").hide();
        $('.chapter-name').removeClass('orangeText')
        $("#chapterName"+previousChapterId).removeClass('orangeText');
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        if(elementExists('sections-dropdown-'+previousChapterId)){
            document.getElementById('sections-dropdown-'+previousChapterId).innerHTML="";
        }
        if(elementExists('content-data-studyset')){
            document.getElementById('content-data-studyset').innerHTML="";
            $("#content-data-studyset-nosets").show();
            $("#study-set-wrapper-container").hide();
        }
        if(elementExists('content-data-userNotes')){
            document.getElementById('content-data-userNotes').innerHTML="";
        }
        if(elementExists('content-data-no-notes')){
            $("#content-data-no-notes").show();
        }

        $('.nav-tabs a[href="#all"]').tab('show');
        if(elementExists('addNotes')){
            $('#addNotes').hide();

        }
        //   $('#chapter-details-tabs a[href="#read"]').tab('show');
        previousChapterId=chapterId;
        $("#chapterName"+previousChapterId).addClass('orangeText');
        <g:remoteFunction controller="funlearn" action="chapterDetails" params="'topicId='+chapterId+'&mode=chapter&fromWeb=true'" onSuccess = "chapterDetailsRecieved(data);"/>
        var chapterNames=document.querySelectorAll('.chapterName');
        for (var a = 0; a < chapterNames.length; a++) {
            chapterNames[a].innerHTML =chapterName;

        }
        $('.resource-contents > .tab-content,.add-tabs').removeClass('d-none');
        $('.side-chapters').addClass('d-none');
        $('#content-data-all').show().removeClass('d-none');
        $('.epub-action').hide();
        showResourceList = true;

        if($(window).width()<=768){
            $('.ebookChapters').removeClass('d-none').addClass('d-block');
            $('.mdl-layout__drawer-button').hide();
            $('.mobile-back-button').show();
            $('.mobile-back-button,.wl-back-button').attr('onclick','mobileBackToChapters()');
            $('.related-book-wrapper').show();
        }


    }

</script>
<script>
    // Setting dynamic height for chapters list
    $(document).ready(function() {
        var sidebarTotalHeight = $('.mdl-tabs.side-column').height();
        var topInfoHeight = $('.book-main-detials').height();
        var buyBtnHeight = $('.preview-book-btns').height();

        var newChapterListHeight = sidebarTotalHeight - (topInfoHeight + buyBtnHeight) ;
        $('#allchapters .mdl-shadow--2dp').css({
            'height': newChapterListHeight + 'px'
        });
    });
</script>
