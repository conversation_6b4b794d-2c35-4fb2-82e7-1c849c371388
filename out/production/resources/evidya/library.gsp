<%@ page import  = 'javax.servlet.http.Cookie' %>

<g:render template="/evidya/navheader_new"></g:render>
<style>
.modal.fade .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(.5);transform: translate(0,0)scale(.5);}
.modal.show .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(1);transform: translate(0,0)scale(1);}
.signupDummy,#agree{
    width: 100%;
    background: #233982;
    color: #ffffff;
}
</style>
<section class="bgBlue">
    <div class="loading-icon">
        <div class="loader-wrapper">
            <div class="loader">Loading</div>
        </div>
    </div>
    <div class="row libraryDimension no-gutters">
        <div class="col-3 bgWhite">
            <%if(session["userdetails"]!=null){%>
            <h4>Welcome&nbsp;<b><%= session["userdetails"].name %></b> <a href="/creation/userProfile"><img src="${assetPath(src: 'evidya/Profile_Edit_Icon.svg')}" class="img-responsive" alt=""></a></h4>
            <%}else{%>
            <h4></h4>
            <%}%>
            <div id="badgeDisplay"></div>
            <ul class="nav nav-tabs" role="tablist" id="libraryTab">
                <li class="nav-item" id="evidyaLibrary">
                    <a class="nav-link active" data-toggle="tab" href="#library"><img src="${assetPath(src: 'evidya/Library_Icon.svg')}" class="img-responsive" alt="" >Library</a>
                </li>
                <%if(session["userdetails"]!=null){%>
                <li class="nav-item" id="userLibrary">
                    <a class="nav-link" data-toggle="tab" href="#mylibrary"><img src="${assetPath(src: 'evidya/My_Library_Icon.svg')}" class="img-responsive" alt="">My Library</a>
                </li>

                <%}else{%>
                <li class="nav-item logOfflibrary" id="userLibrary">
                    <a class="nav-link " data-toggle="tab" onclick="javascript:showLoginModal();"><img src="${assetPath(src: 'evidya/My_Library_Icon.svg')}" class="img-responsive" alt="">My Library</a>
                </li>
                <%  }%>
            </ul>
        </div>
        <div class="col-9">
            <div class="tab-content">
                <div id="library" class="container tab-pane active">
                    <div class="container-wrapper">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <p class="itemLabel" id="topPaginationMessage"></p>
                            </div>
                            <h3 id="libraryDesc">Library</h3>
                            <input type="text" class="search" placeholder="Search(title,subject,author)" id="search-book" autocomplete="off" value="${params.searchString}">

                        </div>
                    </div>
                    <p id="searcherrormsg" style="display: none"></p>
                    <div class="mt-4 bg-gray" id="booksDisplayList">
                    </div>
                    <div class="row ml-0 mr-0 align-items-center mt-4 bg-gray border-pagination" id="bottomPagination" style="display: none">
                        <div class="col-6">
                            <ul class="pagination" id="paginationList">

                            </ul>
                        </div>

                    </div>
                </div>
                <div id="mylibrary" class="container tab-pane fade"><br>
                    <div class="container-wrapper">
                        <div class="d-flex align-items-center justify-content-between">
                            <h3 id="recordsData">My Library </h3>
                            <input type="text" class="search" placeholder="Search(title,subject,author)" id="search-book1" autocomplete="off" value="${params.searchString}">

                        </div>
                    </div>
                    <div class="mt-4 bg-gray" id="booksDisplayListMyLibrary">


                    </div>

                </div>
            </div>
        </div>
    </div>
    <div class="modal" id="readContinue" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header" style="min-height: 50px;">
                    <h4 class="modal-title"></h4>
                    %{--                    <button type="button" class="close" data-dismiss="modal">&</button>--}%
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <p class="text-center" style="font-size: 18px;" id="messageLibrary"></p>
                </div>

                <!-- Modal footer -->
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary btn-primary1" id="readNow">Read now</button>
                    <button type="button" class="btn btn-default" id="closeBook">Close</button>
                </div>

            </div>
        </div>
    </div>
</section>

<div class="modal" id="deleteBook">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header" style="min-height: 50px;">
                <h4 class="modal-title"></h4>

            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <p class="text-center" style="font-size: 18px;" id="remove-msg">Are you sure you want to Remove book from your library?.</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-primary btn-primary1" onclick="javascript:bookDelete();">Yes</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
            </div>

        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="evidyaTCModal" data-keyboard="false" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="evidyaTCModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-zoom" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-center" id="evidyaTCModalLabel">Terms and conditions</h5>
            </div>
            <div class="modal-body">
                <p class="">Our T&C has been revised, please <a href="/evidya/termsCondition" style="font-size: 16px" target="_blank">click</a> to read and accept to enjoy seamless services in your account</p>
                <div class="">
                    <div class="mt-3 d-flex align-items-center">
                        <input class="mr-2" type="checkbox" id="termsConditionCheck" name="termsConditionCheck" required>
                        <label for="termsConditionCheck" class="mb-0">I have read and agree to the  <a class="mt-2" href="/evidya/termsCondition" target="_blank">Terms of Service</a></label>
                    </div>
                    <div id="agreeButtonWrapper"></div>
                    <input type="button" id="agreeDummy" class="btn btn-lg continue mt-3 signupDummy" value="Continue" disabled>
                </div>
            </div>
        </div>
    </div>
</div>

<g:render template="/evidya/footer_new"></g:render>
<g:render template="/wonderpublish/buyOrAdd"></g:render>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>



<script>
    var mylibrarysearcherror=false;
    var intest="";
    var bookssearch=[];
    var  bookssearch1=[];
    var books ;
    var booksTags;

    var searchMode=false;
    var getbookId;
    var books ;
    var booksTags;
    var tempBooks=[];
    var removebook=false;
    var myLibraryMode=false;
    var instituteLibraryCalled=false;
    var numberOfBooksPerPage=10;
    var totalNumberOfPages=0;
    var numberOfPageNumbersPerUnit=10;
    var bookIds=[];
    var libraryBooksData = "";
    var libraryBooksList=",";
    var userLibraryBookIds="";
    var currentPageNo=1;
    var userLibraryPopulated=false;
    function showLoginModal(){
        $('.evidyaLogin').show();
    }
    var librarybook="";
    function getLibraryBooks(){
        intest=true;
        myLibraryMode=false;
        <g:remoteFunction controller="institute" action="getLibraryBooks"  onSuccess='booksListReceived(data);' />
    }

    function getUserLibraryBooks(){
        $('.loading-icon').removeClass('hidden');
        intest=true;
        myLibraryMode=false;
        userLibraryPopulated = true;
        <g:remoteFunction controller="institute" action="getUserLibraryBooks"  onSuccess='userBooksListReceived(data);' />

    }

    function userBooksListReceived(data){
       if(data.status=="OK") {
           books = JSON.parse(data.books);
            displayMyLibraryBooks(books);
        }
    }

    getLibraryBooks();


</script>
<script>
    $('#libraryTab a').click(function(e) {
         e.preventDefault();
        <%if(session["userdetails"]!=null){%>
        if(!userLibraryPopulated) getUserLibraryBooks();
        $(this).tab('show');
        <%  }%>
    });

    // store the currently selected tab in the hash value
    $("ul.nav-tabs > li > a").on("shown.bs.tab", function(e) {
        var id = $(e.target).attr("href").substr(1);
        window.location.hash = id;
    });

    // on load of the page: switch to the currently selected tab
    var hash = window.location.hash;
    if(hash!="") {
        $('#libraryTab a[href="' + hash + '"]').tab('show') ;
        $('.loading-icon').removeClass('hidden');
        getUserLibraryBooks();
    }

    $('#closeBook').on('click',function () {

        document.location.reload();

    });

    $('#search-book').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks2(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch();
        }
    });
    $(document).on("keypress", "#search-book", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                if(document.getElementById("search-book").value == '' || document.getElementById("search-book").value == undefined || document.getElementById("search-book").value == null) location.reload();
                else submitSearch();

            }
        }
    });
    function submitSearch(){
        $('.loading-icon').removeClass('hidden');
        var searchString =encodeURIComponent(document.getElementById("search-book").value);
        <g:remoteFunction controller="discover" action="search"  onSuccess='searchResults(data);'
                params="'searchString='+searchString+'&page=library'" />
    }

    $('#search-book1').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks2(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch1();
        }
    });



    $(document).on("keypress", "#search-book1", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {

                if(document.getElementById("search-book1").value == '' || document.getElementById("search-book1").value == undefined || document.getElementById("search-book1").value == null) location.reload();
                else submitSearch1();
            }
        }
    });



    function submitSearch1(){
        $('.loading-icon').removeClass('hidden');
        var searchString1 =encodeURIComponent(document.getElementById("search-book1").value);

        <g:remoteFunction controller="discover" action="search"  onSuccess='searchResults1(data);'
                params="'searchString='+searchString1" />
    }




    function searchResults1(data){
        var id = $('.tab-content .active').attr('id');
        if(data.status!="Nothing present") {
            var filter=data.books;
            var reptitle12 = [];
            for(var i=0;i<bookssearch1.length;i++){
                for(var j=0;j<filter.length;j++) {
                    var serverbookid=filter[j].id.toString();
                    var mylibrarbookid=bookssearch1[i].id.toString();
                    if (serverbookid==mylibrarbookid) reptitle12.push(bookssearch1[i]);
                }
            }
            $('.loading-icon').addClass('hidden');
     if(reptitle12=='') {
    document.getElementById("booksDisplayListMyLibrary").innerHTML = "No books available for your search";
     }
   else {
    if (data.search) searchMode = true;
    books = reptitle12;
    displayMyLibraryBooks(books);
         }
        }else{
            if(elementExists("recordsData")&&(id=="mylibrary")){
                $('.loading-icon').addClass('hidden');
                document.getElementById("booksDisplayListMyLibrary").innerHTML = "No books available for your search";

            }
            else if(elementExists("libraryDesc")&&(id=="library")) {
                $('.loading-icon').addClass('hidden');
                document.getElementById("libraryDesc").innerHTML = " ";
                document.getElementById("booksDisplayList").innerHTML = "No books available for your search";
            }

        }

    }

    function searchResults(data) {
        $('.loading-icon').addClass('hidden');
        if (data.status != "Nothing present") {
            $("#booksDisplayList").show();
            if (data.search) searchMode = true;
            books = data.books;
            mylibrarysearcherror=true;
            booksTags = data.booksTag;
            displayBooks(books);
        }
        else {
            if (elementExists("searcherrormsg")) {
                document.getElementById("searcherrormsg").innerText = "No books available for your search";
                $("#searcherrormsg").show();
                $("#libraryDesc").hide();
                $("#booksDisplayList").hide();
                $("#paginationList").hide();
                $("#topPaginationMessage").hide();

            }


        }
    }


    function goToPageNumber(pageNo){
        currentPageNo = pageNo;
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="institute" action="getPaginatedLibraryBooks" params="'pageNo='+pageNo" onSuccess = "booksListPagewiseReceived(data);"/>
    }

    function booksListPagewiseReceived(data){
        tempBooks = JSON.parse(data.books);
        var startIndex = ((currentPageNo-1)*numberOfBooksPerPage);
        var endIndex = (currentPageNo)*numberOfBooksPerPage;
        if(endIndex>totalNumberOfBooksPresent) endIndex=tempBooks.length;
        document.getElementById("topPaginationMessage").innerHTML="<span>"+(startIndex+1)+"-"+endIndex+"</span> of <span>"+totalNumberOfBooksPresent+"</span> items";
        displayBooksPagewise(0,tempBooks.length);

    }

    function displayBooksPagewise(startIndex,endIndex){
        $("html, body").animate({ scrollTop: 0 }, "fast");

        $('.loading-icon').removeClass('hidden');
        var htmlStr="";
        var language="";
        var imgSrc="";

        for(var i=startIndex;i<endIndex;i++){
           if(searchMode&&!(libraryBooksList.indexOf(","+tempBooks[i].id+",")>-1)) continue;
            if(tempBooks[i].language==null||""==tempBooks[i].langauge||"null"==tempBooks[i].language) language="English";
            else {
                language=tempBooks[i].language;
            }
            imgSrc = "/funlearn/showProfileImage?id=" + tempBooks[i].id + "&fileName=" + tempBooks[i].coverImage + "&type=books&imgType=passport";
            htmlStr +="<div class=\"d-flex bookContainer flex-wrap flex-md-nowrap\">\n" +
                "                             <div>" ;


                htmlStr +=  "<a class='lib-showcase' href='/library/" + encodeURIComponent(tempBooks[i].title).replace(/'/g,"&#39;").toLowerCase() + "?bookId=" + tempBooks[i].id + "&siteName=${session['entryController']}'>" +
                    "<img src='" + imgSrc + "' class=\"img-responsive bookImage\" alt=\"\">" +
                    "</a>" ;



            htmlStr += "</div>\n"
            htmlStr +=   "                             <div class=\"textWrapper\">\n" +
                "                                 <h3>"+tempBooks[i].title+"</h3>\n" +
                "                                 <p class=\"editBy mt-2\"><span>"+replaceAll(tempBooks[i].authors,/[,-]/,", ")+"</span></p>\n" +

                "                             </div>\n" +
                "                             <div>\n" +
                "<div class='language"+" "+language.toLowerCase()+"'>"+"<img src='/assets/evidya/languagechange.svg'>"+"<span>"+language+"</span>"+"</div>";
            if(libraryBooksData.institutionEmail != undefined && libraryBooksData.institutionEmail != null && libraryBooksData.institutionEmail != '' && !(libraryBooksList.indexOf(","+books[i].id+",")>-1)){
                htmlStr += "<a style='position: absolute;\n" +
                    "  bottom: 30px;\n" +
                    "  right: 10px;' onclick='sendEmail({\"siteName\":\"evidya\"," +
                    "\"toEmail\":\""+libraryBooksData.institutionEmail+"\","+
                    "\"title\":\""+tempBooks[i].title.replace(/'/g,"&#39;")+"\","+
                    "\"id\":\""+tempBooks[i].id+"\","+
                    "\"coverImage\":\""+tempBooks[i].coverImage+"\","+
                    "\"author\":\""+tempBooks[i].authors+"\""+
                    "})' " +
                    "href='#'" +
                    // "\"mailto:"
                    // +libraryBooksData.institutionEmail+"?cc=<EMAIL>&Subject=Recommend Book&body= I%20recommend%20this%20book%20to%20be%20added%20to%20the%20SAGE%20e-Vidya%20collection!%0D%0A"
                    // +tempBooks[i].title+"%20By "+tempBooks[i].authors+"%0D%0A"+"\"" +
                    " target=\"_top\">Suggest This Book</a>" ;
            }
            htmlStr +=  "                             </div>\n" ;

            if(userLibraryBookIds!=null&&userLibraryBookIds.indexOf(","+tempBooks[i].id+",")==-1) {
                htmlStr +="                             <div class=\"addLibrary\">\n" +
                    "                                 <a href=\"javascript:addToMyLibrary(" + tempBooks[i].id + ",0.0,'" + tempBooks[i].title.replace(/'/g,"\\'") + "');\" class=\"d-flex\"><i class=\"material-icons\">\n" +
                    "                                     add_circle_outline\n" +
                    "                                 </i>Add to My Library</a>\n" +
                    "                             </div>\n";
            }
            else if(userLibraryBookIds!=null&&userLibraryBookIds.indexOf(","+tempBooks[i].id+",")>-1) {
                htmlStr +="<div class=\"addLibrary\">\n" +
                    "                                 <a href='#' class=\"d-flex\"><i class=\"material-icons\">\n" +
                    "                                    done_all \n" +
                    "                                 </i>Added to My Library</a>\n" +
                    "                             </div>\n";
            }


            htmlStr +="                         </div>";
        }
        document.getElementById("booksDisplayList").innerHTML=htmlStr;

        $('.loading-icon').addClass('hidden');
    }

    function displayMyLibraryBooks(books){

        bookssearch1 = books;
        $("html, body").animate({ scrollTop: 0 }, "fast");


        var htmlStr="";
        var language="";
        var imgSrc="";
        for(var i=0;i<books.length;i++){

            if(books[i].language==null||""==books[i].langauge||"null"==books[i].language) language="English";
            else {
                language=books[i].language;
            }
            imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=passport";
            htmlStr +="<div class=\"d-flex bookContainer flex-wrap flex-md-nowrap\">\n" +
                "                             <div>" ;

            htmlStr +=  "<a class='lib-showcase' href='/library/" + encodeURIComponent(books[i].title).replace(/'/g,"&#39;").toLowerCase()+ "?bookId=" + books[i].id + "&siteName=${session['entryController']}'>" +
                "<img src='" + imgSrc + "' class=\"img-responsive bookImage\" alt=\"\">" +
                "</a>" ;

            htmlStr += "</div>\n" +
                "                             <div class=\"textWrapper\">\n" +
                "                                 <h3>"+books[i].title+"</h3>\n" +
                "                                 <p class=\"editBy mt-2\"><span>"+replaceAll(books[i].authors,/[,-]/,", ")+"</span></p>\n" +

                "                             </div>\n" +
                "                             <div>\n" +
                "<div class='language"+" "+language.toLowerCase()+"'>"+"<img src='/assets/evidya/languagechange.svg'>"+"<span>"+language+"</span>"+"</div>"+
                "                             </div>\n" ;

            htmlStr +="                             <div class=\"addLibrary\">\n" +
                "                                 <a href=\"javascript:deleteModal(" + books[i].id + ");\" class=\"d-flex\"><i class=\"material-icons\">\n" +
                "                                     remove_circle_outline\n" +
                "                                 </i>Remove from My Library</a>\n" +
                "                             </div>\n";



            htmlStr +="                         </div>";
        }


        document.getElementById("booksDisplayListMyLibrary").innerHTML=htmlStr;


        $('.loading-icon').addClass('hidden');

    }


    function moveForward(currentIndex){
        var startIndex=(currentIndex)*numberOfPageNumbersPerUnit;
        var endIndex=((currentIndex+1)*numberOfPageNumbersPerUnit);
        var pageListStr="";
        if(endIndex>totalNumberOfPages) endIndex=totalNumberOfPages;
        if(currentIndex>0) pageListStr +="<li class='page-item previous'><a class='page-link' href='javascript:moveBackward("+currentIndex+");'><<</a></li>";
        for(var index=startIndex;index<endIndex;index++){
            pageListStr +="<li class=\"page-item \"><a class=\"page-link\" href=\"javascript:goToPageNumber("+(index+1)+");\">"+(index+1)+"</a></li>";
        }
        if(totalNumberOfPages>endIndex){
            currentIndex++;
            pageListStr +="<li class='page-item next'><a class='page-link' href='javascript:moveForward("+currentIndex+");'>>></a></li>";
        }

        document.getElementById("paginationList").innerHTML=pageListStr;

    }

    function moveBackward(currentIndex){
        var startIndex=((currentIndex-1)*numberOfPageNumbersPerUnit);
        var endIndex=((currentIndex)*numberOfPageNumbersPerUnit);
        currentIndex--;
        var pageListStr="";
        if(currentIndex>0) pageListStr +="<li class='page-item previous'><a class='page-link' href='javascript:moveBackward("+currentIndex+");'><<</a></li>";
        for(var index=startIndex;index<endIndex;index++){
            pageListStr +="<li class=\"page-item \"><a class=\"page-link\" href=\"javascript:goToPageNumber("+(index+1)+");\">"+(index+1)+"</a></li>";
        }
        if(totalNumberOfPages>endIndex){
            currentIndex++;
            pageListStr +="<li class='page-item next'><a class='page-link' href='javascript:moveForward("+currentIndex+");'>>></a></li>";
        }

        document.getElementById("paginationList").innerHTML=pageListStr;

    }
    var bookssearch=[];


    function booksListReceived(data){
        if (bookssearch.length == 0) bookssearch = JSON.parse(data.books);
        var params = false;
        var mode = '';
        var modeValue = '';
        books = JSON.parse(data.books);
        var userBooks = null;
        userLibraryBookIds = data.userBookIds;
        if(data.totalBooks!=null) totalNumberOfBooksPresent = data.totalBooks;
        displayBooks(books);
        document.getElementById("badgeDisplay").innerHTML="<p><img src=\"${assetPath(src: 'evidya/Reader_Badge.svg')}\" class=\"img-responsive\" alt=\"\">"+data.instituteName+"</p>";
        if(removebook){
         getUserLibraryBooks();
         removebook=false;
         }
    }

    function displayBooks(books) {

        if (!myLibraryMode) {
            $("#bottomPagination").hide();
            $("#topPaginationMessage").hide();
        }


        tempBooks = [];



        if(elementExists("searcherrormsg")) {
            $("#searcherrormsg").hide();
        }
      if(books!=null) {
        for (var i = 0; i < books.length; i++) {
            if(elementExists("libraryDesc")) {
                document.getElementById("libraryDesc").innerHTML = " ";
            }
           tempBooks.push(books[i]);
            if(!searchMode) {
                libraryBooksList += books[i].id + ",";
            }
            if(mylibrarysearcherror) {
                libraryBooksList += books[i].id + ",";
            }

        }
     }


        totalNumberOfPages  =  Math.floor(totalNumberOfBooksPresent/numberOfBooksPerPage);
        if(totalNumberOfBooksPresent%numberOfBooksPerPage>0) totalNumberOfPages++;

            if(elementExists("libraryDesc")&&!searchMode) {
                if (intest) {
                    document.getElementById("libraryDesc").innerHTML = "Your library now has access to " + totalNumberOfBooksPresent + " titles from evidya store";
                }else{
                    document.getElementById("libraryDesc").innerHTML = " ";
                }
            }

            if(totalNumberOfBooksPresent>numberOfBooksPerPage&&!searchMode) {
                $("#bottomPagination").show();
                document.getElementById("topPaginationMessage").innerHTML="<span>1-"+numberOfBooksPerPage+"</span> of <span>"+totalNumberOfBooksPresent+"</span> items";
                $("#topPaginationMessage").show();

                var pageListStr="";
                var pageNumbersToShow=totalNumberOfPages;
                if(totalNumberOfPages>numberOfPageNumbersPerUnit) pageNumbersToShow=numberOfPageNumbersPerUnit;
                for(var index=0;index<pageNumbersToShow;index++){
                    pageListStr +="<li class=\"page-item \"><a class=\"page-link\" href=\"javascript:goToPageNumber("+(index+1)+");\">"+(index+1)+"</a></li>";
                }
                if(totalNumberOfPages>numberOfPageNumbersPerUnit) {
                    pageListStr +="<li class='page-item next'><a class='page-link' href='javascript:moveForward(1);'>>></a></li>";
                    currentLastPageNumber=numberOfPageNumbersPerUnit;
                }
                document.getElementById("paginationList").innerHTML=pageListStr;
                displayBooksPagewise(0,numberOfBooksPerPage);
            }else{
                if(books!=null) {
                document.getElementById("topPaginationMessage").innerHTML="<span>1-"+books.length+"</span> of <span>"+books.length+"</span> items";
                $("#topPaginationMessage").show();
                displayBooksPagewise(0,tempBooks.length);
                }else{
                    document.getElementById("topPaginationMessage").innerHTML = "<span>0</span> of <span>0</span> items";
                    $("#topPaginationMessage").show();
                    displayBooksPagewise(0, tempBooks.length);
                }
            }




    }



    function bookDelete() {
        <g:remoteFunction controller="log" action="removeBookFromLibrary" params="'bookId='+getbookId" onSuccess = "bookRemoved(data);"/>

    }

    function bookRemoved(data) {
        if (data.status == "Deleted") {
            $('#deleteBook').modal('hide');
            $('.loading-icon').removeClass('hidden');
            removebook=true;
            getLibraryBooks();
        }

    }

    function deleteModal(bookId){
        getbookId=bookId;
        $('#deleteBook').modal('show');
    }

    $(window).on('load', function() {

        <%if(session['userdetails']!=null&&session['userdetails'].username!=null && session['userdetails'].termsCondition!="true"){%>
        $('#evidyaTCModal').modal('show');
        $('#evidyaTCModal').modal({backdrop: 'static', keyboard: false});
        <%} else { %>
        //do nothing..
        <%}%>
    });
    var termsChecked
    $('#termsConditionCheck').on('change', function() {
        termsChecked = this.checked
        if (termsChecked){
            $("#agreeButtonWrapper").html("<input type='button' id='agree' onclick='acceptTerms()' class='btn btn-lg continue mt-3' value='Continue'>")
            $("#agreeDummy").hide();
        }else{
            $("#agreeButtonWrapper").html("");
            $("#agreeDummy").show();
        }
    });

    function acceptTerms(){
        <g:remoteFunction controller="log" action="storeSageTermsDtl"  onSuccess='closeTermsModal(data)'
                params="'termsCondition='+termsChecked" />
    }

    function closeTermsModal(data){
        if(data.status == "OK"){
            $('#evidyaTCModal').modal('hide');
        }
    }
</script>
