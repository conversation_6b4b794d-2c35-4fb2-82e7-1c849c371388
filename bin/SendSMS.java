import java.util.*; 
import com.amazonaws.services.sns.*;
import com.amazonaws.services.sns.model.*;

public class SendSMS {

    public static void main(String[] args) {
        if (args.length < 2) {
            System.out.println("Message: Wrong arguments! Correct usage: java SendSMS mobile message");
            System.exit(0);
        }
        
        AmazonSNSClient snsClient = new AmazonSNSClient();
        String message = args[1];
        String phoneNumber = args[0];

        if(phoneNumber.length()!=10) {
            System.out.println("Message: Please check your phone number and try again. \nPlease enter without the country code. At the moment only valid for Indian mobile numbers");
            System.exit(0);            
        }        
        
        phoneNumber="+91"+phoneNumber;
            
        Properties props = System.getProperties();      
        props.setProperty("aws.accessKeyId","********************");
        props.setProperty("aws.secretKey","2RkbFhc+/JfSrhFpGZM20s7CsISDjfp/JYWPjsIx");
        System.setProperties(props);
        
        Map<String, MessageAttributeValue> smsAttributes = 
                new HashMap<String, MessageAttributeValue>();
            
            
        smsAttributes.put("AWS.SNS.SMS.SenderID", new MessageAttributeValue()
                .withStringValue("mySenderID") //The sender ID shown on the device.
                .withDataType("String"));
        smsAttributes.put("AWS.SNS.SMS.MaxPrice", new MessageAttributeValue()
                .withStringValue("0.50") //Sets the max price to 0.50 USD.
                .withDataType("Number"));
        smsAttributes.put("AWS.SNS.SMS.SMSType", new MessageAttributeValue()
                .withStringValue("Transactional") //Sets the type to promotional.
                .withDataType("String"));                
            
        //<set SMS attributes>
        sendSMSMessage(snsClient, message, phoneNumber, smsAttributes);
    }

    public static void sendSMSMessage(AmazonSNSClient snsClient, String message, 
		String phoneNumber, Map<String, MessageAttributeValue> smsAttributes) {
        PublishResult result = snsClient.publish(new PublishRequest()
                        .withMessage(message)
                        .withPhoneNumber(phoneNumber)
                        .withMessageAttributes(smsAttributes));
        System.out.println(result); // Prints the message ID.
    }
}