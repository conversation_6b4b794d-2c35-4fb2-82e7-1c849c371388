#!/bin/sh

# - Copyright (c) 2019 WonderSlate, Inc. All Rights Reserved. -
#
#  --- Setting local Environment --
HOME=/u01/ws; export HOME;
TOMCAT1=/u01/apache-tomcat-8.5.13; export TOMCAT1;
UPLOAD_HOME=/u01/ws/exports; export UPLOAD_HOME;

CLASSPATH=$TOMCAT1/jars/*:$TOMCAT1/bin/*:$TOMCAT1/bin:$HOME/lib/javax.mail.jar:$HOME/lib/activation.jar:$HOME/bin; export CLASSPATH;

DATE=`date '+%Y-%h-%d'`; export DATE;
LONGDATE=`date '+%Y-%h-%d-%H-%M'`; export LONGDATE;

LOG_DATE=`date -d "5 hours 30 minutes" '+%d-%h-%Y %H:%M'`; export LOG_DATE;
TIMESTAMP=$UPLOAD_HOME/logs/fetch_amazon_$DATE.log;

cleartime() {
 echo "" > $TIMESTAMP ;
}

logtime() {
	echo "$1" >> $TIMESTAMP ;
}

cleartime;

if [ "$1Test" = "Test" ]; then
  echo ""
  echo "Syntax is ./fetch_print_books.d <environment>"
  echo "Parameter environment could be qa, test, live or eu."
  echo ""
  exit 0
fi

if [ "$1" != "qa" ] && [ "$1" != "test" ] && [ "$1" != "live" ] && [ "$1" != "eu" ]; then
  echo ""
  echo "Syntax is ./fetch_print_books.d <environment>"
  echo "Parameter environment could be qa, test, live or eu."
  echo ""
  exit 0
fi

logtime "Beginning the amazon fetch process.";
logtime "";
logtime "Staring at $LOG_DATE";


#sending out email 
java -classpath $CLASSPATH email "<EMAIL>" "Server `cat /u01/ws/bin/servername` amazon fetch begin notification" "`cat $TIMESTAMP`";

java -classpath $CLASSPATH -Dfile.encoding=UTF-8 FetchPrintBooks "$1";

cd $TOMCAT1/bin;
cp amazon_import.txt /efs/

logtime "";
logtime "Done! at `date -d \"5 hours 30 minutes\" '+%d-%h-%Y %H:%M'`";

java -classpath $CLASSPATH email "<EMAIL>" "Server `cat /u01/ws/bin/servername` amazon fetch complete notification" "`cat $TIMESTAMP`";
