#!/bin/sh

# - Copyright (c) 2018 WonderSlate, Inc. All Rights Reserved. -
#
#  --- Setting local Environment --
UPLOAD_HOME=/u01/ws/exports; export UPLOAD_HOME;
HOME=/u01/ws; export HOME;

CLASSPATH=$HOME/lib/javax.mail.jar:$HOME/lib/activation.jar:$HOME/bin; export CLASSPATH;

LOG_DATE=`TZ='Asia/Kolkata' date '+%d %h %Y %H:%M'`; export LOG_DATE;
TIMESTAMP=$UPLOAD_HOME/logs/ws_rsync_db.log;

cleartime() {
 echo "" > $TIMESTAMP;
}

logtime() {
	echo "$1" >> $TIMESTAMP;
}

cleartime;

# -- rsyncing efs --
logtime "Syncing db /u01/mysql to EFS /efs/live/mysql on live server"
logtime "";
logtime "Starting sync at $LOG_DATE";
logtime "";
logtime "";

# please use if want to use backup option before overwriting data on remote machine
#rsync -avzh --stats --backup --backup-dir="backup_$(TZ='Asia/Kolkata' date +\%Y-\%m-\%d_\%H-\%M)" /u01/mysql/ /efs/live/mysql/ >> $TIMESTAMP;

rsync -avzh --stats /u01/mysql/ /efs/live/mysql/ >> $TIMESTAMP;
LOG_DATE=`TZ='Asia/Kolkata' date '+%d %h %Y %H:%M'`; export LOG_DATE;

logtime "";
logtime "";
logtime "Done with sync at $LOG_DATE";
logtime "";

logtime "";
logtime "Syncing is scheduled to run every day every half past an hour Indian Time.";
logtime "";
logtime "This will eventually be synced to backup EFS /efs_backup.";
logtime "";
logtime "Emailing out the notification to following recipients: ";
logtime "<EMAIL>";
logtime "<EMAIL>";
java -classpath $CLASSPATH email "<EMAIL>" "WonderSlate live db sync notification" "`cat $TIMESTAMP`";
#java -classpath $CLASSPATH email "<EMAIL>" "WonderSlate live db sync notification" "`cat $TIMESTAMP`";